using System;
using IdentityService.Application.Interfaces;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace IdentityService.Infrastructure.Services
{
    public class HttpContextUserIdAccessor : IUserIdAccessor
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ILogger<HttpContextUserIdAccessor> _logger;

        public HttpContextUserIdAccessor(IHttpContextAccessor httpContextAccessor, ILogger<HttpContextUserIdAccessor> logger)
        {
            _httpContextAccessor = httpContextAccessor;
            _logger = logger;
        }

        public Guid GetUserId()
        {
            try
            {
                var httpContext = _httpContextAccessor.HttpContext;
                if (httpContext == null)
                {
                    _logger.LogWarning("HttpContext is null");
                    return Guid.Empty;
                }

                if (httpContext.Items.TryGetValue("UserId", out var userIdObj) && userIdObj is Guid userId)
                {
                    _logger.LogInformation("Retrieved user ID from HttpContext.Items: {UserId}", userId);
                    return userId;
                }

                _logger.LogWarning("User ID not found in HttpContext.Items");
                return Guid.Empty;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user ID from HttpContext.Items");
                return Guid.Empty;
            }
        }
    }
}
