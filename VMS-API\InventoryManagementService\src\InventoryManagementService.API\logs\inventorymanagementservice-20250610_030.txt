2025-06-10 13:35:54.998 +05:30 [INF] Starting database operations...
2025-06-10 13:35:56.414 +05:30 [INF] Checking database connection...
2025-06-10 13:35:59.990 +05:30 [INF] Database connection test result: false
2025-06-10 13:36:00.026 +05:30 [WRN] Database is not accessible. Skipping migration and seeding.
2025-06-10 13:36:00.049 +05:30 [INF] Inventory Management Service starting up...
2025-06-10 13:36:00.054 +05:30 [INF] Service will be available at:
2025-06-10 13:36:00.061 +05:30 [INF]   HTTP:  http://localhost:5008
2025-06-10 13:36:00.063 +05:30 [INF]   HTTPS: https://localhost:7008
2025-06-10 13:36:00.066 +05:30 [INF]   Swagger: http://localhost:5008/swagger
2025-06-10 13:36:00.378 +05:30 [DBG] Starting bus instances: IBus
2025-06-10 13:36:00.391 +05:30 [DBG] Starting bus: "rabbitmq://localhost/"
2025-06-10 13:36:00.490 +05:30 [DBG] Connect: guest@localhost:5672/
2025-06-10 13:36:00.593 +05:30 [INF] Now listening on: http://localhost:5008
2025-06-10 13:36:00.593 +05:30 [DBG] Connected: guest@localhost:5672/ (address: amqp://localhost:5672, local: 58570)
2025-06-10 13:36:00.601 +05:30 [INF] Now listening on: https://localhost:7008
2025-06-10 13:36:00.606 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-06-10 13:36:00.608 +05:30 [INF] Hosting environment: Development
2025-06-10 13:36:00.610 +05:30 [INF] Content root path: E:\Shri\AutomobilesGenerative02062025\AutomobilesGenerative\VMS-API\InventoryManagementService\src\InventoryManagementService.API
2025-06-10 13:36:00.636 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/DESKTOPI7D4M6U_InventoryManagementServiceAPI_bus_gytoyyfpkikqdo4cbdq4x7pdyh?temporary=true"
2025-06-10 13:36:00.642 +05:30 [INF] Bus started: "rabbitmq://localhost/"
2025-06-10 13:36:35.845 +05:30 [INF] Application is shutting down...
2025-06-10 13:36:35.918 +05:30 [DBG] Stopping bus instances: IBus
2025-06-10 13:36:35.943 +05:30 [DBG] Stopping bus: "rabbitmq://localhost/"
2025-06-10 13:36:35.976 +05:30 [DBG] Endpoint Stopping: "rabbitmq://localhost/DESKTOPI7D4M6U_InventoryManagementServiceAPI_bus_gytoyyfpkikqdo4cbdq4x7pdyh?temporary=true"
2025-06-10 13:36:35.985 +05:30 [DBG] Endpoint Completed: "rabbitmq://localhost/DESKTOPI7D4M6U_InventoryManagementServiceAPI_bus_gytoyyfpkikqdo4cbdq4x7pdyh?temporary=true"
2025-06-10 13:36:36.028 +05:30 [DBG] Disconnect: guest@localhost:5672/
2025-06-10 13:36:36.037 +05:30 [DBG] Disconnected: guest@localhost:5672/
2025-06-10 13:36:36.041 +05:30 [INF] Bus stopped: "rabbitmq://localhost/"
2025-06-10 13:36:36.048 +05:30 [INF] Inventory Management Service shut down complete
2025-06-10 13:38:04.309 +05:30 [INF] Starting database operations...
2025-06-10 13:38:05.342 +05:30 [INF] Checking database connection...
2025-06-10 13:38:08.097 +05:30 [INF] Database connection test result: false
2025-06-10 13:38:08.119 +05:30 [WRN] Database is not accessible. Skipping migration and seeding.
2025-06-10 13:38:08.182 +05:30 [INF] Inventory Management Service starting up...
2025-06-10 13:38:08.262 +05:30 [INF] Service will be available at:
2025-06-10 13:38:08.265 +05:30 [INF]   HTTP:  http://localhost:5008
2025-06-10 13:38:08.268 +05:30 [INF]   HTTPS: https://localhost:7008
2025-06-10 13:38:08.274 +05:30 [INF]   Swagger: http://localhost:5008/swagger
2025-06-10 13:38:08.496 +05:30 [DBG] Starting bus instances: IBus
2025-06-10 13:38:08.508 +05:30 [DBG] Starting bus: "rabbitmq://localhost/"
2025-06-10 13:38:08.584 +05:30 [DBG] Connect: guest@localhost:5672/
2025-06-10 13:38:08.669 +05:30 [DBG] Connected: guest@localhost:5672/ (address: amqp://localhost:5672, local: 58628)
2025-06-10 13:38:08.669 +05:30 [INF] Now listening on: http://localhost:5008
2025-06-10 13:38:08.675 +05:30 [INF] Now listening on: https://localhost:7008
2025-06-10 13:38:08.678 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-06-10 13:38:08.680 +05:30 [INF] Hosting environment: Development
2025-06-10 13:38:08.683 +05:30 [INF] Content root path: E:\Shri\AutomobilesGenerative02062025\AutomobilesGenerative\VMS-API\InventoryManagementService\src\InventoryManagementService.API
2025-06-10 13:38:08.715 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/DESKTOPI7D4M6U_InventoryManagementServiceAPI_bus_ryiyyyfpkikqdkajbdq4x7xxrf?temporary=true"
2025-06-10 13:38:08.719 +05:30 [INF] Bus started: "rabbitmq://localhost/"
2025-06-10 13:38:15.026 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5008/ - null null
2025-06-10 13:38:15.107 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5008/ - 404 0 null 85.3023ms
2025-06-10 13:38:15.138 +05:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5008/, Response status code: 404
2025-06-10 13:38:41.183 +05:30 [INF] Application is shutting down...
2025-06-10 13:38:41.270 +05:30 [DBG] Stopping bus instances: IBus
2025-06-10 13:38:41.318 +05:30 [DBG] Stopping bus: "rabbitmq://localhost/"
2025-06-10 13:38:41.364 +05:30 [DBG] Endpoint Stopping: "rabbitmq://localhost/DESKTOPI7D4M6U_InventoryManagementServiceAPI_bus_ryiyyyfpkikqdkajbdq4x7xxrf?temporary=true"
2025-06-10 13:38:41.407 +05:30 [DBG] Endpoint Completed: "rabbitmq://localhost/DESKTOPI7D4M6U_InventoryManagementServiceAPI_bus_ryiyyyfpkikqdkajbdq4x7xxrf?temporary=true"
2025-06-10 13:38:41.526 +05:30 [DBG] Disconnect: guest@localhost:5672/
2025-06-10 13:38:41.531 +05:30 [DBG] Disconnected: guest@localhost:5672/
2025-06-10 13:38:41.535 +05:30 [INF] Bus stopped: "rabbitmq://localhost/"
2025-06-10 13:38:41.547 +05:30 [INF] Inventory Management Service shut down complete
