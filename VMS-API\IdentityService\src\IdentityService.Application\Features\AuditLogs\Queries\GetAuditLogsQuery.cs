using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using IdentityService.Application.Common;
using IdentityService.Application.DTOs.AuditLogs;
using IdentityService.Application.Interfaces;
using MediatR;

namespace IdentityService.Application.Features.AuditLogs.Queries;

public class GetAuditLogsQuery : BaseRequest<(List<AuditLogResponse> Items, int TotalCount)>
{
    public AuditLogFilterRequest Filter { get; set; }
}

public class GetAuditLogsQueryHandler : IRequestHandler<GetAuditLogsQuery, (List<AuditLogResponse> Items, int TotalCount)>
{
    private readonly IAuditLogService _auditLogService;

    public GetAuditLogsQueryHandler(IAuditLogService auditLogService)
    {
        _auditLogService = auditLogService;
    }

    public async Task<(List<AuditLogResponse> Items, int TotalCount)> Handle(GetAuditLogsQuery query, CancellationToken cancellationToken)
    {
        var filter = query.Filter;

        var (auditLogs, totalCount) = await _auditLogService.GetFilteredAuditLogsAsync(
            filter.Action,
            filter.EntityName,
            filter.EntityId,
            filter.UserId,
            filter.FromDate,
            filter.ToDate,
            filter.PageNumber,
            filter.PageSize);

        var responses = auditLogs.Select(log => new AuditLogResponse
        {
            Id = log.Id,
            Action = log.Action,
            EntityName = log.EntityName,
            EntityId = log.EntityId,
            OldValues = log.OldValues,
            NewValues = log.NewValues,
            AffectedColumns = log.AffectedColumns,
            IpAddress = log.IpAddress,
            UserAgent = log.UserAgent,
            UserId = log.UserId,
            UserEmail = log.User?.Email ?? "Unknown",
            CreatedAt = log.CreatedAt,
            CreatedBy = log.CreatedBy
        }).ToList();

        return (responses, totalCount);
    }
}
