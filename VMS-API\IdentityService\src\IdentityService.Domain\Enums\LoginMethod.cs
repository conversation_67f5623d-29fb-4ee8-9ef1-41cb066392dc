namespace IdentityService.Domain.Enums;

/// <summary>
/// Represents the method used for login
/// </summary>
public enum LoginMethod
{
    /// <summary>
    /// Email and password login
    /// </summary>
    EmailPassword = 0,
    
    /// <summary>
    /// Phone number and OTP login
    /// </summary>
    PhoneOtp = 1,
    
    /// <summary>
    /// Social login (e.g., Google, Facebook)
    /// </summary>
    Social = 2,
    
    /// <summary>
    /// Single sign-on
    /// </summary>
    Sso = 3,
    
    /// <summary>
    /// API key authentication
    /// </summary>
    ApiKey = 4,
    
    /// <summary>
    /// Other authentication method
    /// </summary>
    Other = 5
}
