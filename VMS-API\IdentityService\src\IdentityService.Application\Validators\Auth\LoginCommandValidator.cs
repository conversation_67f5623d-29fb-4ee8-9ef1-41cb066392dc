using FluentValidation;
using IdentityService.Application.Features.Auth.Commands;

namespace IdentityService.Application.Validators.Auth;

public class LoginCommandValidator : BaseValidator<LoginCommand>
{
    public LoginCommandValidator()
    {
        RuleFor(x => x.Request).NotNull().WithMessage("Request cannot be null.");

        When(x => x.Request != null, () =>
        {
            // Either email login or phone login must be valid
            RuleFor(x => x.Request)
                .Must(r => r.IsEmailLogin || r.IsPhoneLogin)
                .WithMessage("Either email login or phone login credentials must be provided.");

            // Email login validation - only validate these fields when using email login
            When(x => x.Request.IsEmailLogin, () =>
            {
                RuleFor(x => x.Request.Email)
                    .Cascade(CascadeMode.Stop)
                    .NotEmpty().WithMessage("Email is required.")
                    .EmailAddress().WithMessage("A valid email address is required.");

                RuleFor(x => x.Request.Password)
                    .Cascade(CascadeMode.Stop)
                    .NotEmpty().WithMessage("Password is required.");
            });

            // Phone login validation - only validate these fields when using phone login
            When(x => x.Request.IsPhoneLogin, () =>
            {
                RuleFor(x => x.Request.PhoneNumber)
                    .Cascade(CascadeMode.Stop)
                    .NotEmpty().WithMessage("Phone number is required.")
                    .Matches(@"^\+?[0-9\s\-\(\)]+$").WithMessage("A valid phone number is required.");

                RuleFor(x => x.Request.Otp)
                    .Cascade(CascadeMode.Stop)
                    .NotEmpty().WithMessage("OTP is required.");
            });

            // Don't validate PhoneNumber and Otp when using email login
            Unless(x => x.Request.IsPhoneLogin, () =>
            {
                // Skip validation for PhoneNumber and Otp when not using phone login
            });

            // Don't validate Email and Password when using phone login
            Unless(x => x.Request.IsEmailLogin, () =>
            {
                // Skip validation for Email and Password when not using email login
            });
        });
    }
}
