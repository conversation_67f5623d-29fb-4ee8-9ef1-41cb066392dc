# Session Performance Optimization

## Issue Identified

The session cleanup service was causing performance issues due to:

1. **Unnecessary JOINs**: Session cleanup queries were including User data unnecessarily
2. **Complex SQL**: Queries were loading entire User entities including sensitive data like PasswordHash
3. **Missing Composite Indexes**: No optimized indexes for common query patterns

## Log Analysis

The problematic queries were:
```sql
-- Before optimization - Complex JOIN with unnecessary User data
SELECT u."Id", u."CreatedAt", ..., u."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", ..., u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
```

## Optimizations Applied

### 1. Removed Unnecessary Includes

**Files Modified:**
- `UserSessionRepository.cs`

**Changes:**
- Removed `.Include(s => s.User)` from cleanup methods
- Optimized `GetIdleSessionsAsync()` and `GetExpiredSessionsAsync()`
- Optimized `GetActiveSessionsForUserAsync()` for session management

**Before:**
```csharp
return await _dbSet
    .Include(s => s.User)  // ❌ Unnecessary JOIN
    .Where(s => s.Status == SessionStatus.Active && s.ExpiresAt <= now)
    .ToListAsync();
```

**After:**
```csharp
return await _dbSet
    .Where(s => s.Status == SessionStatus.Active && s.ExpiresAt <= now)
    .ToListAsync();
```

### 2. Added Composite Indexes

**Files Modified:**
- `UserSessionConfiguration.cs`

**New Indexes Added:**
```csharp
// Single column indexes
builder.HasIndex(s => s.LastActiveAt);

// Composite indexes for common query patterns
builder.HasIndex(s => new { s.Status, s.ExpiresAt });
builder.HasIndex(s => new { s.Status, s.LastActiveAt, s.ExpiresAt });
builder.HasIndex(s => new { s.UserId, s.Status, s.ExpiresAt });
```

### 3. Query Pattern Optimization

**Idle Sessions Query:**
```sql
-- Optimized query (no JOINs)
SELECT s."Id", s."Status", s."LastActiveAt", s."ExpiresAt", s."UserId", ...
FROM "UserSessions" AS s
WHERE s."Status" = 0 AND s."LastActiveAt" < @__idleThreshold_0 AND s."ExpiresAt" > now()
```

**Expired Sessions Query:**
```sql
-- Optimized query (no JOINs)
SELECT s."Id", s."Status", s."ExpiresAt", s."UserId", ...
FROM "UserSessions" AS s
WHERE s."Status" = 0 AND s."ExpiresAt" <= @__now_0
```

## Performance Benefits

### Before Optimization:
- ❌ Complex JOINs with Users table
- ❌ Loading unnecessary User data including PasswordHash
- ❌ Higher memory usage
- ❌ Slower query execution
- ❌ Potential security risk (loading sensitive data unnecessarily)

### After Optimization:
- ✅ Simple queries on UserSessions table only
- ✅ No unnecessary data loading
- ✅ Reduced memory usage
- ✅ Faster query execution with composite indexes
- ✅ Better security (no sensitive data in cleanup operations)

## Migration Required

To apply the new indexes, run:

```bash
# Create migration
cd src\IdentityService.Infrastructure
dotnet ef migrations add OptimizeSessionIndexes --startup-project ..\IdentityService.API

# Apply migration
dotnet ef database update --startup-project ..\IdentityService.API
```

Or use the provided PowerShell script:
```powershell
.\create-session-indexes-migration.ps1
```

## Expected Results

After applying these optimizations:

1. **Session cleanup logs should be simpler**:
   ```
   [INFO] Expired 0 idle or expired sessions
   [DBG] Session cleanup completed
   ```

2. **No more complex JOIN queries in logs**

3. **Faster session cleanup execution** (every 5 minutes)

4. **Reduced database load** and memory usage

5. **Better overall application performance**

## Monitoring

Monitor the following after deployment:

- Session cleanup execution time
- Database query performance
- Memory usage during cleanup operations
- Application logs for any session-related errors

## Notes

- The optimization maintains all existing functionality
- Only cleanup operations are optimized - other session operations that need User data still include it appropriately
- The changes are backward compatible
- No breaking changes to the API or business logic
