using Microsoft.AspNetCore.Mvc;
using SubscriptionService.Domain.Entities;
using SubscriptionService.Domain.Interfaces;

namespace SubscriptionService.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class InvoiceController : ControllerBase
    {
        private readonly IBillingService _billingService;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<InvoiceController> _logger;

        public InvoiceController(
            IBillingService billingService,
            IUnitOfWork unitOfWork,
            ILogger<InvoiceController> logger)
        {
            _billingService = billingService;
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        [HttpGet("subscription/{subscriptionId}")]
        public async Task<ActionResult<IEnumerable<Invoice>>> GetInvoicesBySubscription(Guid subscriptionId)
        {
            try
            {
                var invoices = await _billingService.GetInvoiceHistoryAsync(subscriptionId);
                return Ok(invoices);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting invoices for subscription {SubscriptionId}", subscriptionId);
                return StatusCode(500, "An error occurred while retrieving invoices");
            }
        }

        [HttpGet("{invoiceNumber}")]
        public async Task<ActionResult<Invoice>> GetInvoiceByNumber(string invoiceNumber)
        {
            try
            {
                var invoice = await _unitOfWork.Invoices.GetInvoiceByNumberAsync(invoiceNumber);
                if (invoice == null)
                {
                    return NotFound($"Invoice with number {invoiceNumber} not found");
                }
                return Ok(invoice);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting invoice {InvoiceNumber}", invoiceNumber);
                return StatusCode(500, "An error occurred while retrieving the invoice");
            }
        }

        [HttpPost("generate")]
        public async Task<ActionResult<Invoice>> GenerateInvoice([FromBody] GenerateInvoiceRequest request)
        {
            try
            {
                var invoice = await _billingService.GenerateInvoiceAsync(
                    request.SubscriptionId,
                    request.Amount,
                    request.TaxAmount,
                    request.DiscountAmount,
                    request.Notes);

                return CreatedAtAction(nameof(GetInvoiceByNumber), new { invoiceNumber = invoice.InvoiceNumber }, invoice);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating invoice for subscription {SubscriptionId}", request.SubscriptionId);
                return StatusCode(500, "An error occurred while generating the invoice");
            }
        }

        [HttpPost("{invoiceNumber}/payment")]
        public async Task<ActionResult<Invoice>> ProcessPayment(string invoiceNumber, [FromBody] ProcessPaymentRequest request)
        {
            try
            {
                var invoice = await _billingService.ProcessPaymentAsync(invoiceNumber, request.PaymentReference);
                return Ok(invoice);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing payment for invoice {InvoiceNumber}", invoiceNumber);
                return StatusCode(500, "An error occurred while processing the payment");
            }
        }

        [HttpPost("{invoiceNumber}/promotion")]
        public async Task<ActionResult<Invoice>> ApplyPromotionCode(string invoiceNumber, [FromBody] ApplyPromotionRequest request)
        {
            try
            {
                var invoice = await _billingService.ApplyPromotionCodeAsync(invoiceNumber, request.PromotionCode);
                return Ok(invoice);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error applying promotion code {PromotionCode} to invoice {InvoiceNumber}",
                    request.PromotionCode, invoiceNumber);
                return StatusCode(500, "An error occurred while applying the promotion code");
            }
        }
    }

    public class GenerateInvoiceRequest
    {
        public Guid SubscriptionId { get; set; }
        public decimal Amount { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal DiscountAmount { get; set; }
        public string? Notes { get; set; }
    }

    public class ProcessPaymentRequest
    {
        public string PaymentReference { get; set; } = string.Empty;
    }

    public class ApplyPromotionRequest
    {
        public string PromotionCode { get; set; } = string.Empty;
    }
}
