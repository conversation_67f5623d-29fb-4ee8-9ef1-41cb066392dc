using System;
using System.Collections.Generic;
using IdentityService.Domain.Common;

namespace IdentityService.Domain.Entities;

public class Menu : BaseEntity
{
    public string Name { get; private set; }
    public string DisplayName { get; private set; }
    public string Path { get; private set; }
    public string Icon { get; private set; }
    public int Order { get; private set; }
    public Guid? ParentId { get; private set; }
    public Menu Parent { get; private set; }
    public List<Menu> Children { get; private set; }
    public List<MenuPermission> MenuPermissions { get; private set; }

    private Menu()
    {
        Children = new List<Menu>();
        MenuPermissions = new List<MenuPermission>();
    }

    public static Menu Create(
        string name,
        string displayName,
        string path,
        string icon,
        int order,
        Guid? parentId,
        string createdBy)
    {
        return new Menu
        {
            Name = name,
            DisplayName = displayName,
            Path = path,
            Icon = icon,
            Order = order,
            ParentId = parentId,
            CreatedBy = createdBy
        };
    }

    public void Update(
        string displayName,
        string path,
        string icon,
        int order,
        Guid? parentId,
        string updatedBy)
    {
        DisplayName = displayName;
        Path = path;
        Icon = icon;
        Order = order;
        ParentId = parentId;
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;
    }

    public void AddPermission(Permission permission, string addedBy)
    {
        MenuPermissions.Add(MenuPermission.Create(this, permission, addedBy));
    }

    public void ClearPermissions()
    {
        MenuPermissions.Clear();
    }
}