using System.ComponentModel.DataAnnotations;

namespace SubscriptionService.API.Models;

/// <summary>
/// Request model for creating new Subscription Terms and Conditions
/// </summary>
public class CreateSubscriptionTermsRequest
{
    [Required(ErrorMessage = "Title is required")]
    [StringLength(200, ErrorMessage = "Title cannot exceed 200 characters")]
    public string Title { get; set; } = string.Empty;

    [Required(ErrorMessage = "Content is required")]
    [MinLength(10, ErrorMessage = "Content must be at least 10 characters")]
    public string Content { get; set; } = string.Empty;

    [Required(ErrorMessage = "Version is required")]
    [StringLength(10, ErrorMessage = "Version cannot exceed 10 characters")]
    public string Version { get; set; } = string.Empty;

    public DateTime? EffectiveDate { get; set; }
}

/// <summary>
/// Request model for updating Subscription Terms and Conditions
/// </summary>
public class UpdateSubscriptionTermsRequest
{
    [Required(ErrorMessage = "Title is required")]
    [StringLength(200, ErrorMessage = "Title cannot exceed 200 characters")]
    public string Title { get; set; } = string.Empty;

    [Required(ErrorMessage = "Content is required")]
    [MinLength(10, ErrorMessage = "Content must be at least 10 characters")]
    public string Content { get; set; } = string.Empty;
}

/// <summary>
/// Request model for accepting Subscription Terms and Conditions
/// </summary>
public class AcceptSubscriptionTermsRequest
{
    [Required(ErrorMessage = "Vendor ID is required")]
    public Guid VendorId { get; set; }
}



/// <summary>
/// Response model for API operations
/// </summary>
public class ApiResponse<T>
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public T? Data { get; set; }
    public List<string> Errors { get; set; } = new();
}

/// <summary>
/// Response model for paginated results
/// </summary>
public class PaginatedResponse<T>
{
    public bool Success { get; set; } = true;
    public string Message { get; set; } = string.Empty;
    public T? Data { get; set; }
    public int TotalCount { get; set; }
    public int PageNumber { get; set; }
    public int PageSize { get; set; }
    public int TotalPages { get; set; }
    public bool HasPreviousPage { get; set; }
    public bool HasNextPage { get; set; }
    public List<string> Errors { get; set; } = new();
}

/// <summary>
/// Query parameters for filtering audit logs
/// </summary>
public class AuditLogQueryParams
{
    public string? Action { get; set; }
    public string? EntityType { get; set; }
    public Guid? EntityId { get; set; }
    public Guid? VendorId { get; set; }
    public string? UserId { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public string SortBy { get; set; } = "Timestamp";
    public string SortDirection { get; set; } = "desc";
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 10;
}

/// <summary>
/// Query parameters for filtering terms list
/// </summary>
public class TermsListQueryParams
{
    public string SortBy { get; set; } = "EffectiveDate";
    public string SortDirection { get; set; } = "desc";
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 10;
}

/// <summary>
/// Query parameters for filtering vendor acceptances
/// </summary>
public class VendorAcceptanceQueryParams
{
    public Guid? VendorId { get; set; }
    public Guid? TermsAndConditionsId { get; set; }
    public string? TermsVersion { get; set; }
    public bool? IsActive { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public string SortBy { get; set; } = "AcceptedAt";
    public string SortDirection { get; set; } = "desc";
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 10;
}
