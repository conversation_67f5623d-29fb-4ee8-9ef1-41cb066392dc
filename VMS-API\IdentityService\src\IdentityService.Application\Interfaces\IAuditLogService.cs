using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using IdentityService.Domain.Entities;

namespace IdentityService.Application.Interfaces;

public interface IAuditLogService
{
    Task<AuditLog> CreateAuditLogAsync(
        string action,
        string entityName,
        string entityId,
        string oldValues,
        string newValues,
        string affectedColumns,
        Guid userId);
        
    Task<(IReadOnlyList<AuditLog> Items, int TotalCount)> GetFilteredAuditLogsAsync(
        string? action = null,
        string? entityName = null,
        string? entityId = null,
        Guid? userId = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        int pageNumber = 1,
        int pageSize = 10);
        
    Task<AuditLog?> GetAuditLogByIdAsync(Guid id);
    Task<byte[]> ExportToCsvAsync(IReadOnlyList<AuditLog> auditLogs);
    Task<byte[]> ExportToPdfAsync(IReadOnlyList<AuditLog> auditLogs);
}
