using System;
using System.Collections.Generic;

namespace IdentityService.Application.DTOs.Menus;

public class MenuResponse
{
    public Guid Id { get; set; }
    public string Name { get; set; }
    public string DisplayName { get; set; }
    public string Path { get; set; }
    public string Icon { get; set; }
    public int Order { get; set; }
    public Guid? ParentId { get; set; }
    public MenuResponse? Parent { get; set; }
    public List<MenuResponse> Children { get; set; } = new List<MenuResponse>();
    public List<MenuPermissionResponse> Permissions { get; set; } = new List<MenuPermissionResponse>();
    public DateTime CreatedAt { get; set; }
    public string CreatedBy { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string? UpdatedBy { get; set; }
}

public class MenuPermissionResponse
{
    public Guid Id { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
    public string Resource { get; set; }
    public string Action { get; set; }
}
