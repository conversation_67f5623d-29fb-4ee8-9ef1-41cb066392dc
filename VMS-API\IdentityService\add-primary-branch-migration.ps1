# Script to add the PrimaryBranchId migration to the Identity Service

# Set the Infrastructure project directory
$infraDir = Join-Path $PSScriptRoot "src\IdentityService.Infrastructure"
Write-Host "Infrastructure directory: $infraDir" -ForegroundColor Cyan

# Navigate to the Infrastructure project directory
Set-Location $infraDir

# Create a new migration for adding PrimaryBranchId
Write-Host "Creating migration for adding PrimaryBranchId column..." -ForegroundColor Yellow
dotnet ef migrations add AddPrimaryBranchIdToUser

# Apply the migration
Write-Host "Applying the migration..." -ForegroundColor Yellow
dotnet ef database update

Write-Host "Migration completed." -ForegroundColor Green
