using System;
using System.Threading;
using System.Threading.Tasks;
using IdentityService.Application.Common;
using IdentityService.Application.DTOs.Auth;
using IdentityService.Application.Interfaces;
using IdentityService.Domain.Interfaces;
using MediatR;
using Microsoft.Extensions.Logging;

namespace IdentityService.Application.Features.Auth.Commands;

public class ResetPasswordCommand : BaseRequest<bool>
{
    public ResetPasswordRequest Request { get; set; }
}

public class ResetPasswordCommandHandler : IRequestHandler<ResetPasswordCommand, bool>
{
    private readonly IUserRepository _userRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<ResetPasswordCommandHandler> _logger;
    private readonly IPasswordHasher _passwordHasher;

    public ResetPasswordCommandHandler(
        IUserRepository userRepository,
        IUnitOfWork unitOfWork,
        ILogger<ResetPasswordCommandHandler> logger,
        IPasswordHasher passwordHasher)
    {
        _userRepository = userRepository;
        _unitOfWork = unitOfWork;
        _logger = logger;
        _passwordHasher = passwordHasher;
    }

    public async Task<bool> Handle(ResetPasswordCommand request, CancellationToken cancellationToken)
    {
        var user = await _userRepository.GetByEmailAsync(request.Request.Email);
        if (user == null)
        {
            throw new ArgumentException("Invalid request");
        }

        var resetToken = user.PasswordResetTokens
            .Find(t => t.Token == request.Request.Token && t.IsValid());

        if (resetToken == null)
        {
            throw new ArgumentException("Invalid or expired reset token");
        }

        var newPasswordHash = _passwordHasher.HashPassword(request.Request.NewPassword);
        user.UpdatePassword(newPasswordHash, request.UserId.ToString() ?? user.Id.ToString());
        resetToken.MarkAsUsed();

        try
        {
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Password reset successfully for user {UserId}", user.Id);
            return true;
        }
        catch (InvalidOperationException ex) when (ex.Message.Contains("deleted by another user"))
        {
            // If there's a concurrency conflict with the token, it might have been used already
            _logger.LogWarning(ex, "Concurrency conflict when resetting password for user {UserId}", user.Id);
            throw new ArgumentException("Invalid or expired reset token. Please request a new password reset link.");
        }
    }
}