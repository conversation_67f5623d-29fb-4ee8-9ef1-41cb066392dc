2025-06-10 13:31:38.894 +05:30 [INF] Starting database operations...
2025-06-10 13:31:39.511 +05:30 [INF] Checking database connection...
2025-06-10 13:31:40.973 +05:30 [INF] Database connection test result: false
2025-06-10 13:31:40.981 +05:30 [WRN] Database is not accessible. Skipping migration and seeding.
2025-06-10 13:31:40.987 +05:30 [INF] Inventory Management Service starting up...
2025-06-10 13:31:40.989 +05:30 [INF] Service will be available at:
2025-06-10 13:31:40.992 +05:30 [INF]   HTTP:  http://localhost:5008
2025-06-10 13:31:40.994 +05:30 [INF]   HTTPS: https://localhost:7008
2025-06-10 13:31:40.996 +05:30 [INF]   Swagger: http://localhost:5008/swagger
2025-06-10 13:31:41.563 +05:30 [INF] Bus started: "rabbitmq://localhost/"
2025-06-10 13:32:40.746 +05:30 [INF] Bus stopped: "rabbitmq://localhost/"
2025-06-10 13:32:40.756 +05:30 [INF] Inventory Management Service shut down complete
2025-06-10 13:35:54.998 +05:30 [INF] Starting database operations...
2025-06-10 13:35:56.414 +05:30 [INF] Checking database connection...
2025-06-10 13:35:59.990 +05:30 [INF] Database connection test result: false
2025-06-10 13:36:00.026 +05:30 [WRN] Database is not accessible. Skipping migration and seeding.
2025-06-10 13:36:00.049 +05:30 [INF] Inventory Management Service starting up...
2025-06-10 13:36:00.054 +05:30 [INF] Service will be available at:
2025-06-10 13:36:00.061 +05:30 [INF]   HTTP:  http://localhost:5008
2025-06-10 13:36:00.063 +05:30 [INF]   HTTPS: https://localhost:7008
2025-06-10 13:36:00.066 +05:30 [INF]   Swagger: http://localhost:5008/swagger
2025-06-10 13:36:00.378 +05:30 [DBG] Starting bus instances: IBus
2025-06-10 13:36:00.391 +05:30 [DBG] Starting bus: "rabbitmq://localhost/"
2025-06-10 13:36:00.490 +05:30 [DBG] Connect: guest@localhost:5672/
2025-06-10 13:36:00.593 +05:30 [INF] Now listening on: http://localhost:5008
2025-06-10 13:36:00.593 +05:30 [DBG] Connected: guest@localhost:5672/ (address: amqp://localhost:5672, local: 58570)
2025-06-10 13:36:00.601 +05:30 [INF] Now listening on: https://localhost:7008
2025-06-10 13:36:00.606 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-06-10 13:36:00.608 +05:30 [INF] Hosting environment: Development
2025-06-10 13:36:00.610 +05:30 [INF] Content root path: E:\Shri\AutomobilesGenerative02062025\AutomobilesGenerative\VMS-API\InventoryManagementService\src\InventoryManagementService.API
2025-06-10 13:36:00.636 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/DESKTOPI7D4M6U_InventoryManagementServiceAPI_bus_gytoyyfpkikqdo4cbdq4x7pdyh?temporary=true"
2025-06-10 13:36:00.642 +05:30 [INF] Bus started: "rabbitmq://localhost/"
2025-06-10 13:36:35.845 +05:30 [INF] Application is shutting down...
2025-06-10 13:36:35.918 +05:30 [DBG] Stopping bus instances: IBus
2025-06-10 13:36:35.943 +05:30 [DBG] Stopping bus: "rabbitmq://localhost/"
2025-06-10 13:36:35.976 +05:30 [DBG] Endpoint Stopping: "rabbitmq://localhost/DESKTOPI7D4M6U_InventoryManagementServiceAPI_bus_gytoyyfpkikqdo4cbdq4x7pdyh?temporary=true"
2025-06-10 13:36:35.985 +05:30 [DBG] Endpoint Completed: "rabbitmq://localhost/DESKTOPI7D4M6U_InventoryManagementServiceAPI_bus_gytoyyfpkikqdo4cbdq4x7pdyh?temporary=true"
2025-06-10 13:36:36.028 +05:30 [DBG] Disconnect: guest@localhost:5672/
2025-06-10 13:36:36.037 +05:30 [DBG] Disconnected: guest@localhost:5672/
2025-06-10 13:36:36.041 +05:30 [INF] Bus stopped: "rabbitmq://localhost/"
2025-06-10 13:36:36.048 +05:30 [INF] Inventory Management Service shut down complete
