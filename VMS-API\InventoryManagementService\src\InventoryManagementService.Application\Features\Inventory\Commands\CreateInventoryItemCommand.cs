using AutoMapper;
using FluentValidation;
using InventoryManagementService.Application.DTOs;
using InventoryManagementService.Domain.Entities;
using InventoryManagementService.Domain.Interfaces;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using VMS.Contracts.Common.Authorization;

namespace InventoryManagementService.Application.Features.Inventory.Commands;

public class CreateInventoryItemCommand : IRequest<InventoryItemDto>
{
    public CreateInventoryItemDto InventoryItem { get; set; } = null!;
    public string CreatedBy { get; set; } = string.Empty;
}

public class CreateInventoryItemCommandValidator : AbstractValidator<CreateInventoryItemCommand>
{
    public CreateInventoryItemCommandValidator()
    {
        RuleFor(x => x.InventoryItem.PartId)
            .NotEmpty().WithMessage("Part ID is required");

        RuleFor(x => x.InventoryItem.BranchId)
            .NotEmpty().WithMessage("Branch ID is required");

        RuleFor(x => x.InventoryItem.VendorId)
            .NotEmpty().WithMessage("Vendor ID is required");

        RuleFor(x => x.InventoryItem.InitialStock)
            .GreaterThanOrEqualTo(0).WithMessage("Initial stock cannot be negative");

        RuleFor(x => x.InventoryItem.MinimumStock)
            .GreaterThanOrEqualTo(0).WithMessage("Minimum stock cannot be negative");

        RuleFor(x => x.InventoryItem.MaximumStock)
            .GreaterThan(0).WithMessage("Maximum stock must be greater than 0");

        RuleFor(x => x.InventoryItem.ReorderLevel)
            .GreaterThanOrEqualTo(0).WithMessage("Reorder level cannot be negative");

        RuleFor(x => x.InventoryItem.StorageLocation)
            .NotEmpty().WithMessage("Storage location is required")
            .MaximumLength(100).WithMessage("Storage location cannot exceed 100 characters");

        RuleFor(x => x.CreatedBy)
            .NotEmpty().WithMessage("CreatedBy is required");

        RuleFor(x => x.InventoryItem)
            .Must(x => x.MaximumStock >= x.MinimumStock)
            .WithMessage("Maximum stock must be greater than or equal to minimum stock");

        RuleFor(x => x.InventoryItem)
            .Must(x => x.ReorderLevel >= x.MinimumStock)
            .WithMessage("Reorder level should be greater than or equal to minimum stock");
    }
}

public class CreateInventoryItemCommandHandler : IRequestHandler<CreateInventoryItemCommand, InventoryItemDto>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly ILogger<CreateInventoryItemCommandHandler> _logger;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly IEventPublisher _eventPublisher;

    public CreateInventoryItemCommandHandler(
        IUnitOfWork unitOfWork,
        IMapper mapper,
        ILogger<CreateInventoryItemCommandHandler> logger,
        IHttpContextAccessor httpContextAccessor,
        IEventPublisher eventPublisher)
    {
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _logger = logger;
        _httpContextAccessor = httpContextAccessor;
        _eventPublisher = eventPublisher;
    }

    public async Task<InventoryItemDto> Handle(CreateInventoryItemCommand request, CancellationToken cancellationToken)
    {
        // Check permissions - Admin roles bypass, others need explicit permission
        var user = _httpContextAccessor.HttpContext?.User;
        if (!PermissionHelper.IsAdmin(user))
        {
            PermissionHelper.ValidateAccess(user, "Inventory.Create");
        }

        _logger.LogInformation("Creating inventory item for Part: {PartId} at Branch: {BranchId}", 
            request.InventoryItem.PartId, request.InventoryItem.BranchId);

        // Check if part exists
        if (!await _unitOfWork.Parts.ExistsAsync(request.InventoryItem.PartId))
        {
            throw new KeyNotFoundException($"Part with ID {request.InventoryItem.PartId} not found");
        }

        // Check if inventory item already exists for this part and branch
        if (await _unitOfWork.Inventory.ExistsForPartAndBranchAsync(
            request.InventoryItem.PartId, 
            request.InventoryItem.BranchId))
        {
            throw new InvalidOperationException(
                $"Inventory item already exists for Part {request.InventoryItem.PartId} at Branch {request.InventoryItem.BranchId}");
        }

        var inventoryItem = InventoryItem.Create(
            request.InventoryItem.PartId,
            request.InventoryItem.BranchId,
            request.InventoryItem.VendorId,
            request.InventoryItem.InitialStock,
            request.InventoryItem.MinimumStock,
            request.InventoryItem.MaximumStock,
            request.InventoryItem.ReorderLevel,
            request.InventoryItem.StorageLocation,
            request.CreatedBy,
            request.InventoryItem.Bin,
            request.InventoryItem.Shelf);

        await _unitOfWork.Inventory.AddAsync(inventoryItem);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("Inventory item created successfully with ID: {InventoryItemId}", inventoryItem.Id);

        // Publish event
        await _eventPublisher.PublishInventoryCreatedEvent(
            inventoryItem.Id,
            inventoryItem.PartId,
            inventoryItem.BranchId,
            inventoryItem.CurrentStock);

        return _mapper.Map<InventoryItemDto>(inventoryItem);
    }
}
