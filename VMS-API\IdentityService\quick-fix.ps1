# Quick fix script for Identity Service migration and seeding issues
Write-Host "Quick Fix for Identity Service" -ForegroundColor Cyan

# Get the script directory and navigate to the API project
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$apiProjectPath = Join-Path $scriptDir "src\IdentityService.API"

Write-Host "Script directory: $scriptDir" -ForegroundColor Gray
Write-Host "API project path: $apiProjectPath" -ForegroundColor Gray

if (Test-Path $apiProjectPath) {
    Set-Location $apiProjectPath
    Write-Host "Changed to API project directory" -ForegroundColor Green
} else {
    Write-Host "API project directory not found: $apiProjectPath" -ForegroundColor Red
    exit 1
}

# Clean and build
Write-Host "Cleaning project..." -ForegroundColor Yellow
dotnet clean

Write-Host "Building project..." -ForegroundColor Yellow
dotnet build

if ($LASTEXITCODE -eq 0) {
    Write-Host "Build successful!" -ForegroundColor Green

    Write-Host "Starting application..." -ForegroundColor Yellow
    Write-Host "Let it run for about 30 seconds to complete seeding..." -ForegroundColor Cyan
    Write-Host "Watch the console output for seeding progress..." -ForegroundColor Cyan
    Write-Host "Press Ctrl+C to stop when seeding is complete" -ForegroundColor Yellow

    dotnet run
} else {
    Write-Host "Build failed!" -ForegroundColor Red
}
