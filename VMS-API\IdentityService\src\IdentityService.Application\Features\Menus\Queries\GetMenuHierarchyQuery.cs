using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using IdentityService.Application.Common;
using IdentityService.Application.DTOs.Menus;
using IdentityService.Domain.Interfaces;
using MediatR;

namespace IdentityService.Application.Features.Menus.Queries;

public class GetMenuHierarchyQuery : BaseRequest<List<MenuResponse>>
{
}

public class GetMenuHierarchyQueryHandler : IRequestHandler<GetMenuHierarchyQuery, List<MenuResponse>>
{
    private readonly IUnitOfWork _unitOfWork;

    public GetMenuHierarchyQueryHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
    }

    public async Task<List<MenuResponse>> Handle(GetMenuHierarchyQuery query, CancellationToken cancellationToken)
    {
        var allMenus = await _unitOfWork.MenuRepository.GetAllAsync();
        var rootMenus = allMenus.Where(m => m.ParentId == null).OrderBy(m => m.Order).ToList();

        var menuResponses = new List<MenuResponse>();

        foreach (var rootMenu in rootMenus)
        {
            var menuResponse = await BuildMenuHierarchy(rootMenu, allMenus);
            menuResponses.Add(menuResponse);
        }

        return menuResponses;
    }

    private async Task<MenuResponse> BuildMenuHierarchy(Domain.Entities.Menu menu, IEnumerable<Domain.Entities.Menu> allMenus)
    {
        var menuResponse = new MenuResponse
        {
            Id = menu.Id,
            Name = menu.Name,
            DisplayName = menu.DisplayName,
            Path = menu.Path,
            Icon = menu.Icon,
            Order = menu.Order,
            ParentId = menu.ParentId,
            CreatedAt = menu.CreatedAt,
            CreatedBy = menu.CreatedBy,
            UpdatedAt = menu.UpdatedAt,
            UpdatedBy = menu.UpdatedBy,
            Permissions = new List<MenuPermissionResponse>(),
            Children = new List<MenuResponse>()
        };

        // Add permissions
        foreach (var mp in menu.MenuPermissions)
        {
            if (mp.PermissionId.HasValue)
            {
                var permission = await _unitOfWork.PermissionRepository.GetByIdAsync(mp.PermissionId.Value);
                if (permission != null)
                {
                    menuResponse.Permissions.Add(new MenuPermissionResponse
                    {
                        Id = permission.Id,
                        Name = permission.Name,
                        Description = permission.Description,
                        Resource = permission.Resource,
                        Action = permission.Action
                    });
                }
            }
            else
            {
                menuResponse.Permissions.Add(new MenuPermissionResponse
                {
                    Id = System.Guid.Empty,
                    Name = "Public Access",
                    Description = "Accessible to all authenticated users",
                    Resource = "Menu",
                    Action = "View"
                });
            }
        }

        // Add children recursively
        var children = allMenus.Where(m => m.ParentId == menu.Id).OrderBy(m => m.Order).ToList();
        foreach (var childMenu in children)
        {
            var childResponse = await BuildMenuHierarchy(childMenu, allMenus);
            menuResponse.Children.Add(childResponse);
        }

        return menuResponse;
    }
}
