using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using IdentityService.Application.Interfaces;
using IdentityService.Domain.Entities;
using IdentityService.Domain.Interfaces;
using IdentityService.Infrastructure.Messaging.Events;
using MassTransit;
using Microsoft.Extensions.Logging;
using VMSContracts.ServiceClients;
using VMSContracts.Tenant.Events;
using Microsoft.Extensions.Configuration;
using System.Text;
using Microsoft.AspNetCore.WebUtilities;
using System.Net;
using System.Net.Mail;

namespace IdentityService.Infrastructure.Messaging.Consumers;

/// <summary>
/// Consumer for VendorRegistered events
/// </summary>
public class VendorRegisteredConsumer : IConsumer<VendorRegistered>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IPasswordHasher _passwordHasher;
    private readonly IEmailService _emailService;
    private readonly IEventPublisher _eventPublisher;
    private readonly ITenantManagementServiceClient _tenantManagementServiceClient;
    private readonly ILogger<VendorRegisteredConsumer> _logger;
    private readonly IConfiguration _configuration;
    private readonly ITokenService _tokenService;

    public VendorRegisteredConsumer(
        IUnitOfWork unitOfWork,
        IPasswordHasher passwordHasher,
        IEmailService emailService,
        IEventPublisher eventPublisher,
        ITenantManagementServiceClient tenantManagementServiceClient,
        ILogger<VendorRegisteredConsumer> logger,
        IConfiguration configuration,
        ITokenService tokenService)
    {

        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _passwordHasher = passwordHasher ?? throw new ArgumentNullException(nameof(passwordHasher));
        _emailService = emailService ?? throw new ArgumentNullException(nameof(emailService));
        _eventPublisher = eventPublisher ?? throw new ArgumentNullException(nameof(eventPublisher));
        _tenantManagementServiceClient = tenantManagementServiceClient ?? throw new ArgumentNullException(nameof(tenantManagementServiceClient));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _tokenService = tokenService ?? throw new ArgumentNullException(nameof(tokenService));
    }

    public async Task Consume(ConsumeContext<VendorRegistered> context)
    {
        var @event = context.Message;

        _logger.LogInformation("Received VendorRegistered event for vendor {VendorId} (EventId: {EventId})",
            @event.VendorId, @event.EventId);

        try
        {
            // Check if user with this email already exists
            var existingUser = await _unitOfWork.UserRepository.GetByEmailAsync(@event.Email);
            if (existingUser != null)
            {
                _logger.LogWarning("User with email {Email} already exists for vendor {VendorId}",
                    @event.Email, @event.VendorId);
                return;
            }
            string? passwordHash = null;
            if (!string.IsNullOrEmpty(@event.Email))
            {
                // Generate a temporary password
                string tempPassword = Guid.NewGuid().ToString().Substring(0, 8); // Generate a random password
                passwordHash = _passwordHasher.HashPassword(tempPassword);

                // Log the temporary password for testing purposes only
                _logger.LogInformation("Generated temporary password for user {Email}: {Password}",
                    @event.Email, tempPassword);
            }

            // Create a new user for the vendor
            var user = User.Create(
                @event.Email,
                @event.PhoneNumber,
                passwordHash,
                "System");

            // Add the user to the database
            await _unitOfWork.UserRepository.AddAsync(user);

            // Get the Vendor role
            var vendorRole = await GetOrCreateVendorRole();
            if (vendorRole != null)
            {
                // Assign the Vendor role to the user
                user.AddRole(vendorRole, "System");
                _logger.LogInformation("Assigned Vendor role to user {UserId} for vendor {VendorId}",
                    user.Id, @event.VendorId);
            }
            else
            {
                _logger.LogWarning("Vendor role not found and could not be created for vendor {VendorId}",
                    @event.VendorId);
            }

            // Save changes
            await _unitOfWork.SaveChangesAsync();
            _logger.LogInformation("Created user {UserId} for vendor {VendorId}", user.Id, @event.VendorId);

            // Update the vendor with the user ID
            try
            {
                _logger.LogInformation("Updating vendor {VendorId} with user ID {UserId}", @event.VendorId, user.Id);

                var success = await _tenantManagementServiceClient.UpdateVendorUserIdAsync(@event.VendorId, user.Id);

                if (success)
                {
                    _logger.LogInformation("Successfully updated vendor {VendorId} with user ID {UserId}",
                        @event.VendorId, user.Id);
                }
                else
                {
                    _logger.LogWarning("Failed to update vendor {VendorId} with user ID {UserId}",
                        @event.VendorId, user.Id);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating vendor {VendorId} with user ID {UserId}",
                    @event.VendorId, user.Id);
                // Continue processing even if this fails
            }

            // Send email with credentials
            await SendCredentialsEmail(@event.Email, @event.BusinessName, @event.Email);

            // Publish UserCreated event
            await PublishUserCreatedEvent(user);

            _logger.LogInformation("Successfully processed VendorRegistered event for vendor {VendorId}",
                @event.VendorId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing VendorRegistered event for vendor {VendorId} (EventId: {EventId})",
                @event.VendorId, @event.EventId);
            throw; // Rethrow to trigger retry
        }
    }

    private async Task<Role> GetOrCreateVendorRole()
    {
        try
        {
            // Try to get the Vendor role
            var roles = await _unitOfWork.RoleRepository.GetAsync(r => r.Name == "Vendor");
            if (roles.Count > 0)
            {
                return roles[0];
            }

            _logger.LogInformation("Creating Vendor role as it does not exist");

            // If the role doesn't exist, create it
            var vendorRole = Role.Create("Vendor", "Vendor role for accessing vendor-specific features", "System");
            await _unitOfWork.RoleRepository.AddAsync(vendorRole);

            // Get basic permissions for vendors
            var permissions = await _unitOfWork.PermissionRepository.GetAllAsync();
            foreach (var permission in permissions)
            {
                if (permission.Name.StartsWith("Vendor.") ||
                    permission.Name == "Profile.View" ||
                    permission.Name == "Profile.Edit")
                {
                    vendorRole.AddPermission(permission, "System");
                }
            }

            await _unitOfWork.SaveChangesAsync();
            _logger.LogInformation("Successfully created Vendor role with permissions");

            return vendorRole;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating Vendor role");
            return null;
        }
    }

    private string GenerateRandomPassword()
    {
        // Generate a random password with at least one uppercase letter, one lowercase letter,
        // one digit, and one special character
        const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()";
        var random = new Random();
        var password = new char[12];

        // Ensure at least one of each required character type
        password[0] = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"[random.Next(26)]; // Uppercase
        password[1] = "abcdefghijklmnopqrstuvwxyz"[random.Next(26)]; // Lowercase
        password[2] = "0123456789"[random.Next(10)]; // Digit
        password[3] = "!@#$%^&*()"[random.Next(10)]; // Special

        // Fill the rest with random characters
        for (int i = 4; i < password.Length; i++)
        {
            password[i] = chars[random.Next(chars.Length)];
        }

        // Shuffle the password
        for (int i = 0; i < password.Length; i++)
        {
            int swapIndex = random.Next(password.Length);
            char temp = password[i];
            password[i] = password[swapIndex];
            password[swapIndex] = temp;
        }

        return new string(password);
    }

    private async Task SendCredentialsEmail(string email, string businessName, string username)
    {
        try
        {
            // var resultUrl = _configuration["Vendor"] + "setpassword";
            // var user = await _unitOfWork.UserRepository.GetByEmailAsync(email);
            // //var code = await _tokenService.GenerateTokensAsync(user);
            // var code = await this._userManager.GeneratePasswordResetTokenAsync(user);
            // var encodedCode = Encoding.UTF8.GetBytes(code);
            // var validToken = WebEncoders.Base64UrlEncode(encodedCode);
            // var tempUrl = $"{resultUrl}?email={email}&token={encodedCode}";
            // var callbackUrl = resultUrl + tempUrl.ToString().Substring(tempUrl.ToString().IndexOf("?"));
            // string setthepassword = "Set Password";
            // var trimmedUserEmail = email?.Trim();
            // if (user != null)
            // {
            //     user.PasswordToken = validToken;
            //     await _unitOfWork.UserRepository.UpdateAsync(user);
            // }
            var smtpConfig = _configuration.GetSection("MailConfig");
            string supportEmail = smtpConfig["FromAddress"];
            string subject;
            string body;
            subject = "Registration Confirmation - VMS";
            body = $@"
            <html>
            <body>
                <h2>Registration Confirmation</h2>
                <p>Dear {businessName},</p>
                <p>Thank you for registering with our Vendor Management System. Your registration has been received and is pending verification by our admin team.</p>
                <p><strong>Login Credentials:</strong></p>
                <p><strong>Username:</strong> {businessName}</p>
                <p><strong>Password:</strong> {businessName}</p>
                <p>You will receive another email once your registration has been verified.</p>
                <p>Best regards,<br/>VMS Team</p>
            </body>
            </html>";

            using var smtpClient = new SmtpClient
            {
                Host = smtpConfig["Host"],
                Port = int.Parse(smtpConfig["Port"]),
                EnableSsl = true,
                DeliveryMethod = SmtpDeliveryMethod.Network,
                UseDefaultCredentials = false,
                Credentials = new NetworkCredential(smtpConfig["Username"], smtpConfig["Password"])
            };

            var mailMessage = new MailMessage
            {
                From = new MailAddress(smtpConfig["FromAddress"], smtpConfig["FromName"]),
                Subject = subject,
                Body = body,
                IsBodyHtml = true
            };
            mailMessage.To.Add(email);

            await smtpClient.SendMailAsync(mailMessage);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending credentials email to {Email}", email);
            // Continue processing even if email fails
        }
    }

    private async Task PublishUserCreatedEvent(User user)
    {
        try
        {
            var userCreatedEvent = new UserCreatedEvent(
                user.Id,
                user.Email,
                user.PhoneNumber,
                user.EmailVerified,
                user.PhoneNumberVerified);

            await _eventPublisher.PublishAsync(userCreatedEvent);
            _logger.LogInformation("Published UserCreated event for user {UserId}", user.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error publishing UserCreated event for user {UserId}", user.Id);
            // Continue processing even if event publishing fails
        }
    }
}
