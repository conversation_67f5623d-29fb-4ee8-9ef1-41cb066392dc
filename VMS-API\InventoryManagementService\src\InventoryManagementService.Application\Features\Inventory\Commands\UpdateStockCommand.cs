using FluentValidation;
using InventoryManagementService.Domain.Interfaces;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using VMS.Contracts.Common.Authorization;

namespace InventoryManagementService.Application.Features.Inventory.Commands;

public class UpdateStockCommand : IRequest
{
    public Guid InventoryItemId { get; set; }
    public int NewQuantity { get; set; }
    public string Reason { get; set; } = string.Empty;
    public string UpdatedBy { get; set; } = string.Empty;
}

public class UpdateStockCommandValidator : AbstractValidator<UpdateStockCommand>
{
    public UpdateStockCommandValidator()
    {
        RuleFor(x => x.InventoryItemId)
            .NotEmpty().WithMessage("Inventory item ID is required");

        RuleFor(x => x.NewQuantity)
            .GreaterThanOrEqualTo(0).WithMessage("Quantity cannot be negative");

        RuleFor(x => x.Reason)
            .NotEmpty().WithMessage("Reason is required")
            .MaximumLength(200).WithMessage("Reason cannot exceed 200 characters");

        RuleFor(x => x.UpdatedBy)
            .NotEmpty().WithMessage("UpdatedBy is required");
    }
}

public class UpdateStockCommandHandler : IRequestHandler<UpdateStockCommand>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<UpdateStockCommandHandler> _logger;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly IEventPublisher _eventPublisher;

    public UpdateStockCommandHandler(
        IUnitOfWork unitOfWork,
        ILogger<UpdateStockCommandHandler> logger,
        IHttpContextAccessor httpContextAccessor,
        IEventPublisher eventPublisher)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
        _httpContextAccessor = httpContextAccessor;
        _eventPublisher = eventPublisher;
    }

    public async Task Handle(UpdateStockCommand request, CancellationToken cancellationToken)
    {
        // Check permissions - Admin roles bypass, others need explicit permission
        var user = _httpContextAccessor.HttpContext?.User;
        if (!PermissionHelper.IsAdmin(user))
        {
            PermissionHelper.ValidateAccess(user, "Inventory.Update");
        }

        _logger.LogInformation("Updating stock for inventory item: {InventoryItemId} to {NewQuantity}", 
            request.InventoryItemId, request.NewQuantity);

        var inventoryItem = await _unitOfWork.Inventory.GetByIdAsync(request.InventoryItemId);

        if (inventoryItem == null)
        {
            throw new KeyNotFoundException($"Inventory item with ID {request.InventoryItemId} not found");
        }

        var previousStock = inventoryItem.CurrentStock;
        inventoryItem.UpdateStock(request.NewQuantity, request.UpdatedBy, request.Reason);

        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("Stock updated successfully for inventory item {InventoryItemId}. Previous: {PreviousStock}, New: {NewStock}", 
            inventoryItem.Id, previousStock, inventoryItem.CurrentStock);

        // Publish stock updated event
        await _eventPublisher.PublishStockUpdatedEvent(
            inventoryItem.Id,
            inventoryItem.PartId,
            inventoryItem.BranchId,
            previousStock,
            inventoryItem.CurrentStock,
            request.Reason);

        // Check if item is now low stock and publish alert if needed
        if (inventoryItem.IsLowStock)
        {
            await _eventPublisher.PublishLowStockAlertEvent(
                inventoryItem.Id,
                inventoryItem.PartId,
                inventoryItem.BranchId,
                inventoryItem.CurrentStock,
                inventoryItem.ReorderLevel);
        }
    }
}
