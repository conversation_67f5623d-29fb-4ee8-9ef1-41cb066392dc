using InventoryManagementService.Domain.Common;
using InventoryManagementService.Domain.Enums;

namespace InventoryManagementService.Domain.Entities;

public class InventoryTransaction : BaseEntity
{
    public Guid InventoryItemId { get; private set; }
    public TransactionType TransactionType { get; private set; }
    public int Quantity { get; private set; }
    public int PreviousStock { get; private set; }
    public int NewStock { get; private set; }
    public string Reason { get; private set; } = string.Empty;
    public string? Reference { get; private set; }
    public Guid? RelatedTransactionId { get; private set; }
    public DateTime TransactionDate { get; private set; }

    // Navigation properties
    public InventoryItem InventoryItem { get; private set; } = null!;

    private InventoryTransaction() { }

    public static InventoryTransaction Create(
        Guid inventoryItemId,
        TransactionType transactionType,
        int quantity,
        int previousStock,
        int newStock,
        string reason,
        string createdBy,
        string? reference = null,
        Guid? relatedTransactionId = null)
    {
        return new InventoryTransaction
        {
            InventoryItemId = inventoryItemId,
            TransactionType = transactionType,
            Quantity = quantity,
            PreviousStock = previousStock,
            NewStock = newStock,
            Reason = reason.Trim(),
            Reference = reference?.Trim(),
            RelatedTransactionId = relatedTransactionId,
            TransactionDate = DateTime.UtcNow,
            CreatedBy = createdBy
        };
    }
}
