using System;
using System.Collections.Generic;
using IdentityService.Domain.Common;
using IdentityService.Domain.Enums;

namespace IdentityService.Domain.Entities;

public class UserSubscription : BaseEntity
{
    public Guid UserId { get; private set; }
    public User User { get; private set; }
    public Guid SubscriptionId { get; private set; }
    public string PlanName { get; private set; }
    public SubscriptionTier Tier { get; private set; }
    public DateTime StartDate { get; private set; }
    public DateTime EndDate { get; private set; }
    public DateTime? TrialEndDate { get; private set; }
    public SubscriptionStatus Status { get; private set; }
    public List<SubscriptionFeature> Features { get; private set; }

    private UserSubscription()
    {
        Features = new List<SubscriptionFeature>();
    }

    public static UserSubscription Create(
        Guid userId,
        User user,
        Guid subscriptionId,
        string planName,
        SubscriptionTier tier,
        DateTime startDate,
        DateTime endDate,
        DateTime? trialEndDate,
        SubscriptionStatus status,
        string createdBy)
    {
        return new UserSubscription
        {
            UserId = userId,
            User = user,
            SubscriptionId = subscriptionId,
            PlanName = planName,
            Tier = tier,
            StartDate = startDate,
            EndDate = endDate,
            TrialEndDate = trialEndDate,
            Status = status,
            CreatedBy = createdBy,
            Features = new List<SubscriptionFeature>()
        };
    }

    public void UpdateStatus(SubscriptionStatus status, string updatedBy)
    {
        Status = status;
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;
    }

    public void UpdateDates(DateTime endDate, DateTime? trialEndDate, string updatedBy)
    {
        EndDate = endDate;
        TrialEndDate = trialEndDate;
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;
    }

    public void AddFeature(string featureName, bool isEnabled, int? usageLimit, string addedBy)
    {
        Features.Add(SubscriptionFeature.Create(this.Id, featureName, isEnabled, usageLimit, addedBy));
    }

    public void RemoveFeature(string featureName)
    {
        Features.RemoveAll(f => f.FeatureName == featureName);
    }

    public bool HasFeature(string featureName)
    {
        return Features.Any(f => f.FeatureName == featureName && f.IsEnabled);
    }

    public bool IsActive()
    {
        return Status == SubscriptionStatus.Active || 
               (Status == SubscriptionStatus.Trial && TrialEndDate.HasValue && TrialEndDate.Value >= DateTime.UtcNow);
    }
}
