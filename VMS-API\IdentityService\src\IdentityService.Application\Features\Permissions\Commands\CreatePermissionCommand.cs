using System;
using System.Threading;
using System.Threading.Tasks;
using IdentityService.Application.DTOs.Permissions;
using IdentityService.Application.Interfaces;
using IdentityService.Domain.Entities;
using IdentityService.Domain.Interfaces;
using MediatR;
using Microsoft.AspNetCore.Http;
using IdentityService.Application.Common.Authorization;

namespace IdentityService.Application.Features.Permissions.Commands;

public class CreatePermissionCommand : IRequest<PermissionResponse>
{
    public CreatePermissionRequest Request { get; set; }
}

public class CreatePermissionCommandHandler : IRequestHandler<CreatePermissionCommand, PermissionResponse>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICurrentUserService _currentUserService;
    private readonly IAuditLogService _auditLogService;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public CreatePermissionCommandHandler(
        IUnitOfWork unitOfWork,
        ICurrentUserService currentUserService,
        IAuditLogService auditLogService,
        IHttpContextAccessor httpContextAccessor)
    {
        _unitOfWork = unitOfWork;
        _currentUserService = currentUserService;
        _auditLogService = auditLogService;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<PermissionResponse> Handle(CreatePermissionCommand command, CancellationToken cancellationToken)
    {
        // Check permissions - Admin roles bypass, others need explicit permission
        var user = _httpContextAccessor.HttpContext?.User;
        if (!PermissionHelper.IsAdmin(user))
        {
            await PermissionHelper.ValidateAccess(user, "Permissions.Create", _unitOfWork.UserRepository);
        }

        var request = command.Request;
        var createdBy = _currentUserService.UserId.ToString() ?? "System";

        // Check if permission with same name already exists
        var permissions = await _unitOfWork.PermissionRepository.GetAllAsync();
        if (permissions.Any(p => p.Name.Equals(request.Name, StringComparison.OrdinalIgnoreCase)))
            throw new InvalidOperationException($"Permission with name '{request.Name}' already exists");
        
        // Create permission
        var permission = Permission.Create(
            request.Name,
            request.Description,
            request.Resource,
            request.Action,
            createdBy);
        
        // Save permission
        await _unitOfWork.PermissionRepository.AddAsync(permission);
        await _unitOfWork.SaveChangesAsync(cancellationToken);
        
        // Create audit log
        await _auditLogService.CreateAuditLogAsync(
            "Create",
            "Permission",
            permission.Id.ToString(),
            string.Empty,
            System.Text.Json.JsonSerializer.Serialize(new { 
                permission.Id, 
                permission.Name, 
                permission.Description, 
                permission.Resource, 
                permission.Action 
            }),
            "Id,Name,Description,Resource,Action",
            _currentUserService.UserId ?? Guid.Empty);
        
        // Create response
        var response = new PermissionResponse
        {
            Id = permission.Id,
            Name = permission.Name,
            Description = permission.Description,
            Resource = permission.Resource,
            Action = permission.Action,
            CreatedAt = permission.CreatedAt,
            CreatedBy = permission.CreatedBy,
            UpdatedAt = permission.UpdatedAt,
            UpdatedBy = permission.UpdatedBy
        };
        
        return response;
    }
}
