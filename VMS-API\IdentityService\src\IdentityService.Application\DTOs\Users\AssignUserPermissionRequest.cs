using System.ComponentModel.DataAnnotations;
using IdentityService.Domain.Enums;

namespace IdentityService.Application.DTOs.Users;

/// <summary>
/// Request to assign a permission to a user
/// </summary>
public class AssignUserPermissionRequest
{
    /// <summary>
    /// Permission ID to assign
    /// </summary>
    [Required]
    public Guid PermissionId { get; set; }

    /// <summary>
    /// Type of permission (Grant or Deny)
    /// </summary>
    [Required]
    public PermissionType Type { get; set; }

    /// <summary>
    /// Optional expiration date for the permission
    /// </summary>
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// Reason for assigning this permission
    /// </summary>
    [MaxLength(500)]
    public string? Reason { get; set; }
}
