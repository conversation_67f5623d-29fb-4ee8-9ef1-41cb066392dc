using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace IdentityService.Application.Behaviors;

public class ValidationBehavior<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse>
    where TRequest : IRequest<TResponse>
{
    private readonly IEnumerable<IValidator<TRequest>> _validators;
    private readonly ILogger<ValidationBehavior<TRequest, TResponse>> _logger;

    public ValidationBehavior(
        IEnumerable<IValidator<TRequest>> validators,
        ILogger<ValidationBehavior<TRequest, TResponse>> logger)
    {
        _validators = validators;
        _logger = logger;
    }

    public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
    {
        // Log the request type and content for debugging
        _logger.LogInformation("Validating request of type {RequestType}", typeof(TRequest).Name);

        try
        {
            // Log the request properties for debugging
            var requestJson = JsonSerializer.Serialize(request, new JsonSerializerOptions
            {
                WriteIndented = true,
                ReferenceHandler = System.Text.Json.Serialization.ReferenceHandler.IgnoreCycles
            });
            _logger.LogInformation("Request content: {RequestContent}", requestJson);

            // Check if the request has a UserId property
            var userIdProperty = typeof(TRequest).GetProperty("UserId");
            if (userIdProperty != null)
            {
                var userId = userIdProperty.GetValue(request);
                _logger.LogInformation("Request UserId property value: {UserId}", userId);

                // If UserId is empty GUID, log a warning
                if (userId is Guid guidValue && guidValue == Guid.Empty)
                {
                    _logger.LogWarning("UserId is empty GUID (00000000-0000-0000-0000-000000000000)");
                }
            }
            else
            {
                _logger.LogInformation("Request does not have a UserId property");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error logging request details");
        }

        if (_validators.Any())
        {
            var context = new ValidationContext<TRequest>(request);

            // Run all validators
            var validationResults = await Task.WhenAll(
                _validators.Select(v => v.ValidateAsync(context, cancellationToken)));

            // Combine validation failures
            var failures = validationResults
                .SelectMany(r => r.Errors)
                .Where(f => f != null)
                .ToList();

            if (failures.Count != 0)
            {
                // Log validation failures
                foreach (var failure in failures)
                {
                    _logger.LogWarning("Validation failure: {PropertyName} - {ErrorMessage}",
                        failure.PropertyName, failure.ErrorMessage);
                }

                // Throw a validation exception with all validation errors
                throw new ValidationException(failures);
            }
        }

        // Continue with the pipeline if validation passes
        _logger.LogInformation("Validation passed for request of type {RequestType}", typeof(TRequest).Name);
        return await next();
    }
}
