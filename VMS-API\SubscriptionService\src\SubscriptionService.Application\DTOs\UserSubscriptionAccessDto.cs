using SubscriptionService.Domain.Enums;

namespace SubscriptionService.Application.DTOs
{
    public class UserSubscriptionAccessDto
    {
        public Guid VendorId { get; set; }
        public Guid SubscriptionPlanId { get; set; }
        public string? PlanName { get; set; }
        public SubscriptionTier Tier { get; set; }
        public bool IsTrial { get; set; }
        public DateTime? TrialEndDate { get; set; }
        public List<MenuVisibilityDto> MenuVisibility { get; set; } = new();
        public List<PermissionAccessDto> PermissionAccesses { get; set; } = new();
        public SubscriptionAccessSummary AccessSummary { get; set; } = new();
    }

    public class MenuVisibilityDto
    {
        public Guid MenuId { get; set; }
        public string? MenuName { get; set; }
        public string? MenuPath { get; set; }
        public string? Icon { get; set; }
        public int Order { get; set; }
        public Guid? ParentId { get; set; }
        public bool IsVisible { get; set; }
        public bool CanView { get; set; }
        public bool CanEdit { get; set; }
        public bool CanCreate { get; set; }
        public bool CanDelete { get; set; }
        public AccessType AccessType { get; set; }
        public List<MenuVisibilityDto> Children { get; set; } = new();
    }

    public class SubscriptionAccessSummary
    {
        public int TotalMenusAvailable { get; set; }
        public int VisibleMenus { get; set; }
        public int EditableMenus { get; set; }
        public int TotalPermissionsAvailable { get; set; }
        public int GrantedPermissions { get; set; }
        public int ViewOnlyPermissions { get; set; }
        public int EditPermissions { get; set; }
        public int FullAccessPermissions { get; set; }
    }

    public class MenuVisibilityRequest
    {
        public Guid VendorId { get; set; }
        public bool IncludeChildren { get; set; } = true;
        public bool IncludePermissions { get; set; } = false;
    }
}
