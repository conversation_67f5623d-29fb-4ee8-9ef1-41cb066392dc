using System;
using System.Collections.Generic;
using IdentityService.Domain.Common;

namespace IdentityService.Domain.Entities;

public class Permission : BaseEntity
{
    public required string Name { get; set; }
    public required string Description { get; set; }
    public required string Resource { get; set; }
    public required string Action { get; set; }
    public List<RolePermission> RolePermissions { get; private set; }
    public List<UserPermission> UserPermissions { get; private set; }

    private Permission()
    {
        RolePermissions = new List<RolePermission>();
        UserPermissions = new List<UserPermission>();
    }

    public static Permission Create(
        string name,
        string description,
        string resource,
        string action,
        string createdBy)
    {
        return new Permission
        {
            Name = name,
            Description = description,
            Resource = resource,
            Action = action,
            CreatedBy = createdBy
        };
    }

    public void Update(
        string name,
        string description,
        string resource,
        string action,
        string updatedBy)
    {
        Name = name;
        Description = description;
        Resource = resource;
        Action = action;
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;
    }
}