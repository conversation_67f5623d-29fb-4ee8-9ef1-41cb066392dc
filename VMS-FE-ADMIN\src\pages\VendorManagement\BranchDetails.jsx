import React, { useState } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate, useLocation } from "react-router-dom";
import { Container, Row, Col } from "reactstrap";
import { MainHeading } from "../../components/common";
import { useFilter } from "../../contexts/FilterContext";
import BranchEditForm from "./components/BranchEditForm";
import ServiceTaskCard from "./components/ServiceTaskCard";
import CategoryCard from "./components/CategoryCard";
import CategoryCreateForm from "./components/CategoryCreateForm";
import CategoryEditForm from "./components/CategoryEditForm";
import ServiceTaskCreateForm from "./components/ServiceTaskCreateForm";
import ServiceTaskEditForm from "./components/ServiceTaskEditForm";
import DeleteConfirmationModal from "./components/DeleteConfirmationModal";
import BranchSidebar from "./components/BranchSidebar";
import JobCardDetails from "./components/JobCardDetails";
import {
  FaChevronRight,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>aE<PERSON>,
  <PERSON>a<PERSON><PERSON><PERSON>,
  <PERSON>a<PERSON>ar,
  <PERSON>a<PERSON><PERSON><PERSON>,
  Fa<PERSON>lipboard<PERSON>ist,
  Fa<PERSON>ye,
  FaChevronLeft,
  FaList,
  FaFileAlt
} from "react-icons/fa";
import toast from "react-hot-toast";

// CSS moved to styles/custom.css - use vm-branch-details-* classes
import {
  useBranchDetailsWithVehicles,
  useServiceTasksAccessible,
  useCategoriesAccessible,
  useDeleteCategory,
  useDeleteServiceTask,
} from "@api/useTenantHooks";
import ROUTES from "@constants/routes";

const BranchDetails = () => {
  const { branchId } = useParams();
  const navigate = useNavigate();
  const { openSidebar, closeSidebar } = useFilter();
  const location = useLocation();
  const branchDetails = location.state?.branch;

  // State for selected category
  const [selectedCategory, setSelectedCategory] = useState(null);

  // State for active tab
  const [activeTab, setActiveTab] = useState("categories");
  const [isTabLoading, setIsTabLoading] = useState(false);

  // Tab configuration with icons
  const tabs = [
    {
      id: "categories",
      name: "Service Categories",
    
      key: "categories"
    },
    {
      id: "vehicles",
      name: "Vehicles",
    
      key: "vehicles"
    },
    {
      id: "jobcards",
      name: "Job Cards",
     
      key: "jobcards"
    }
  ];

  // Tab navigation functions
  const handleTabChange = (tabId) => {
    if (tabId === activeTab) return;

    setIsTabLoading(true);
    setTimeout(() => {
      setActiveTab(tabId);
      setIsTabLoading(false);
    }, 150);
  };

  // State for expanded categories in list view
  const [expandedCategories, setExpandedCategories] = useState(new Set());

  // State for delete modal
  const [deleteModal, setDeleteModal] = useState({
    isOpen: false,
    category: null,
    isLoading: false,
  });

  // State for service task delete modal
  const [serviceTaskDeleteModal, setServiceTaskDeleteModal] = useState({
    isOpen: false,
    serviceTask: null,
    isLoading: false
  });

  // State for selected job card
  const [selectedJobCard, setSelectedJobCard] = useState(null);

  // Delete category mutation
  const deleteCategoryMutation = useDeleteCategory({
    onSuccess: () => {
      toast.success("Category deleted successfully!");
      setDeleteModal({ isOpen: false, category: null, isLoading: false });
      refetchCategories(); // Refresh the categories data after deletion
    },
    onError: (error) => {
      const errorMessage =
        error.response?.data?.message || "Failed to delete category";
      toast.error(errorMessage);
      setDeleteModal((prev) => ({ ...prev, isLoading: false }));
    },
  });

  // Delete service task mutation
  const deleteServiceTaskMutation = useDeleteServiceTask({
    onSuccess: () => {
      toast.success("Service task deleted successfully!");
      setServiceTaskDeleteModal({ isOpen: false, serviceTask: null, isLoading: false });
      refetchServiceTasks(); // Refresh the service tasks data after deletion
    },
    onError: (error) => {
      const errorMessage = error.response?.data?.message || "Failed to delete service task";
      toast.error(errorMessage);
      setServiceTaskDeleteModal(prev => ({ ...prev, isLoading: false }));
    },
  });

  // Note: Carousel refs removed as we're using the new VendorDetails layout pattern

  // Fetch branch details and vehicles from API
  const {
    data: branchVehiclesData,
    isLoading: branchVehiclesLoading,
    error: branchVehiclesError,
    refetch: refetchBranchVehicles,
  } = useBranchDetailsWithVehicles({ branchId: branchDetails?.id });
 // Fetch categories accessible by vendor and branch
  const {
    data: categoriesData,
    isLoading: categoriesLoading,
    error: categoriesError,
    refetch: refetchCategories,
  } = useCategoriesAccessible(
    branchDetails?.vendorId || vehiclesData?.vendorId,
    branchDetails?.id,
    {
      enabled:
        !!(branchDetails?.vendorId || vehiclesData?.vendorId) &&
        !!branchDetails?.id,
    }
  );
  // Extract vehicles data from API response
  const vehiclesData = branchVehiclesData || [];



 

  // Fetch service tasks for selected category
  const {
    data: serviceTasksData,
    isLoading: serviceTasksLoading,
    error: serviceTasksError,
    refetch: refetchServiceTasks,
  } = useServiceTasksAccessible(
    branchDetails?.vendorId || vehiclesData?.vendorId,
    branchDetails?.id,
    selectedCategory?.id,
    {
      enabled:
        !!(branchDetails?.vendorId || vehiclesData?.vendorId) &&
        !!branchDetails?.id &&
        !!selectedCategory?.id,
    }
  );

  // Error handling
  if (branchVehiclesError) {
    return (
      <div className="branch-details">
        <MainHeading
          title="Error Loading Branch"
          subtitle="Unable to fetch branch details"
          showBackButton={true}
          onBackClick={() => navigate(-1)}
          showFilter={false}
        />
        <div className="error-message">
          <p>Error: {branchVehiclesError.message}</p>
          <button
            onClick={() => refetchBranchVehicles()}
            className="retry-button"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  // Loading state
  if (branchVehiclesLoading) {
    return (
      <div className="branch-details">
        <MainHeading
          title="Loading..."
          subtitle="Fetching branch details"
          showBackButton={true}
          onBackClick={() => navigate(-1)}
          showFilter={false}
        />
      </div>
    );
  }

  // Handle edit branch
  const handleEditBranch = () => {
    const editForm = (
      <BranchEditForm
        branchData={vehiclesData}
        onSubmit={(updatedData) => {
          console.log("Branch updated:", updatedData);
          refetchBranchVehicles(); // Refresh the data after update
          closeSidebar();
        }}
        onCancel={() => {
          closeSidebar();
        }}
      />
    );
    openSidebar(editForm, "Edit Branch Details", "edit");
  };

  // Handle create category
  const handleCreateCategory = () => {
    const createForm = (
      <CategoryCreateForm
        branchId={branchDetails?.id}
        onSubmit={(createdCategory) => {
          console.log("Category created:", createdCategory);
          refetchCategories(); // Refresh the categories data after creation
          closeSidebar();
        }}
        onCancel={() => {
          closeSidebar();
        }}
      />
    );
    openSidebar(createForm, "Create New Category", "plus");
  };

  // Handle create service task
  const handleCreateServiceTask = (categoryId = null) => {
    const createForm = (
      <ServiceTaskCreateForm
        categoryId={categoryId || selectedCategory?.id}
        onSubmit={(createdServiceTask) => {
          console.log("Service task created:", createdServiceTask);
          refetchServiceTasks(); // Refresh the service tasks data after creation
          closeSidebar();
        }}
        onCancel={() => {
          closeSidebar();
        }}
      />
    );
    openSidebar(createForm, "Create New Service Task", "plus");
  };

  // Handle edit category
  const handleEditCategory = (category) => {
    const editForm = (
      <CategoryEditForm
        categoryData={category}
        onSubmit={(updatedCategory) => {
          console.log("Category updated:", updatedCategory);
          // TODO: Add API call to update category
          refetchCategories(); // Refresh the categories data after update
          closeSidebar();
        }}
        onCancel={() => {
          closeSidebar();
        }}
      />
    );
    openSidebar(editForm, "Edit Category", "edit");
  };

  // Handle delete category
  const handleDeleteCategory = (category) => {
    setDeleteModal({
      isOpen: true,
      category: category,
      isLoading: false,
    });
  };

  // Handle delete confirmation
  const handleDeleteConfirm = async () => {
    setDeleteModal((prev) => ({ ...prev, isLoading: true }));

    try {
      await deleteCategoryMutation.mutateAsync(deleteModal.category.id);
    } catch (error) {
      console.error("Error deleting category:", error);
    }
  };

  // Handle delete cancel
  const handleDeleteCancel = () => {
    setDeleteModal({ isOpen: false, category: null, isLoading: false });
  };

  // Handle edit service task
  const handleEditServiceTask = (serviceTask) => {
    const editForm = (
      <ServiceTaskEditForm
        serviceTaskData={serviceTask}
        onSubmit={(updatedServiceTask) => {
          console.log("Service task updated:", updatedServiceTask);
          refetchServiceTasks(); // Refresh the service tasks data after update
          closeSidebar();
        }}
        onCancel={() => {
          closeSidebar();
        }}
      />
    );
    openSidebar(editForm, "Edit Service Task", "edit");
  };

  // Handle delete service task
  const handleDeleteServiceTask = (serviceTask) => {
    setServiceTaskDeleteModal({
      isOpen: true,
      serviceTask: serviceTask,
      isLoading: false
    });
  };

  // Handle service task delete confirmation
  const handleServiceTaskDeleteConfirm = async () => {
    setServiceTaskDeleteModal(prev => ({ ...prev, isLoading: true }));

    try {
      await deleteServiceTaskMutation.mutateAsync(serviceTaskDeleteModal.serviceTask.id);
    } catch (error) {
      console.error("Error deleting service task:", error);
    }
  };

  // Handle service task delete cancel
  const handleServiceTaskDeleteCancel = () => {
    setServiceTaskDeleteModal({ isOpen: false, serviceTask: null, isLoading: false });
  };

  const handleBackClick = () => {
    navigate(-1);
  };



  // Handle toggle category expansion
  const toggleCategoryExpansion = (categoryId) => {
    setExpandedCategories(prev => {
      const newSet = new Set(prev);
      if (newSet.has(categoryId)) {
        newSet.delete(categoryId);
      } else {
        newSet.add(categoryId);
      }
      return newSet;
    });
  };

  // Component to render service tasks for a specific category
  const CategoryTasksList = ({ categoryId }) => {
    const {
      data: categoryServiceTasks,
      isLoading: categoryTasksLoading,
      error: categoryTasksError,
    } = useServiceTasksAccessible(
      branchDetails?.vendorId || vehiclesData?.vendorId,
      branchDetails?.id,
      categoryId,
      {
        enabled:
          !!(branchDetails?.vendorId || vehiclesData?.vendorId) &&
          !!branchDetails?.id &&
          !!categoryId,
      }
    );

    if (categoryTasksLoading) {
      return (
        <div className="vm-category-tasks-loading">
          <FaSpinner className="vm-loading-spinner" />
          <span>Loading tasks...</span>
        </div>
      );
    }

    if (categoryTasksError) {
      return (
        <div className="vm-category-tasks-error">
          <p>Error loading tasks: {categoryTasksError.message}</p>
        </div>
      );
    }

    if (!categoryServiceTasks || categoryServiceTasks.length === 0) {
      return (
        <div className="vm-category-tasks-empty">
          <p>No service tasks available in this category.</p>
          <button
            className="vm-create-task-btn"
            onClick={() => {
              handleCreateServiceTask(categoryId);
            }}
          >
            Create First Task
          </button>
        </div>
      );
    }

    return (
      <div className="vm-category-tasks-table">
        <div className="vm-tasks-table-header">
          <div className="vm-task-col-name">Task Name</div>
          <div className="vm-task-col-price">Price</div>
          <div className="vm-task-col-duration">Duration</div>
          <div className="vm-task-col-type">Type</div>
          <div className="vm-task-col-status">Status</div>
          <div className="vm-task-col-actions">Actions</div>
        </div>
        <div className="vm-tasks-table-body">
          {categoryServiceTasks.map((task) => (
            <div key={task.id} className="vm-task-row">
              <div className="vm-task-col-name">
                <div className="vm-task-name">{task.name}</div>
                <div className="vm-task-description">{task.description || "No description"}</div>
              </div>
              <div className="vm-task-col-price">
                <span className="vm-task-price">₹{task.price || "0"}</span>
              </div>
              <div className="vm-task-col-duration">
                <span className="vm-task-duration">{task.estimatedDuration || "N/A"}</span>
              </div>
              <div className="vm-task-col-type">
                <span className="vm-task-type">{task.serviceType || "General"}</span>
              </div>
              <div className="vm-task-col-status">
                <span className={`vm-task-status ${task.isActive ? 'active' : 'inactive'}`}>
                  {task.isActive ? "Active" : "Inactive"}
                </span>
              </div>
              <div className="vm-task-col-actions">
                <button
                  className="vm-task-action-btn"
                  title="Edit"
                  onClick={() => handleEditServiceTask(task)}
                >
                  <FaEdit />
                </button>
                <button
                  className="vm-task-action-btn delete"
                  title="Delete"
                  onClick={() => handleDeleteServiceTask(task)}
                >
                  <FaChevronRight />
                </button>
              </div>
            </div>
          ))}
        </div>
        <div className="vm-tasks-table-footer">
          <button
            className="vm-add-task-btn"
            onClick={() => {
              handleCreateServiceTask(categoryId);
            }}
          >
            Add New Task
          </button>
        </div>
      </div>
    );
  };

  // Render Branch Information Section - Simple & Compact Design
  const renderBranchInformationSection = () => (
    <div className="vm-branch-details-card">
      <div className="vm-branch-card-header">
        <div className="vm-branch-header-left">
         
          <h3>Branch Information</h3>
        </div>
        <button className="vm-branch-edit-btn" onClick={handleEditBranch}>
          <FaEdit />
          Edit
        </button>
      </div>

      <div className="vm-branch-card-body">
        <div className="vm-branch-info-grid">
          <div className="vm-branch-field">
            <label>Branch Name</label>
            <span>{vehiclesData?.name?.trim() || "Not provided"}</span>
          </div>
          <div className="vm-branch-field">
            <label>Branch Code</label>
            <span>{vehiclesData?.code || "Not provided"}</span>
          </div>
          <div className="vm-branch-field">
            <label>Address</label>
            <span>{vehiclesData?.address || "Not provided"}</span>
          </div>
          <div className="vm-branch-field">
            <label>City</label>
            <span>{vehiclesData?.city || "Not provided"}</span>
          </div>
          <div className="vm-branch-field">
            <label>State</label>
            <span>{vehiclesData?.state || "Not provided"}</span>
          </div>
          <div className="vm-branch-field">
            <label>Country</label>
            <span>{vehiclesData?.country || "Not provided"}</span>
          </div>
          <div className="vm-branch-field">
            <label>Pin Code</label>
            <span>{vehiclesData?.pinCode || "Not provided"}</span>
          </div>
          <div className="vm-branch-field">
            <label>Contact Number</label>
            <span>{vehiclesData?.contactNumber || "Not provided"}</span>
          </div>
          <div className="vm-branch-field">
            <label>Email</label>
            <span>{vehiclesData?.email || "Not provided"}</span>
          </div>
        
        </div>
      </div>
    </div>
  );



  // Render Service Categories Section - Simple Working Version
  const renderServiceCategoriesSection = () => (
    <div className="vm-tab-panel">
      <div >
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
          <div>
            <h3>Service Categories</h3>
            <p>Manage service categories for this branch</p>
          </div>
          <button className="btn btn-primary" onClick={handleCreateCategory}>
            Create Category
          </button>
        </div>

        {categoriesLoading ? (
          <div style={{ textAlign: 'center', padding: '2rem' }}>
            <FaSpinner style={{ animation: 'spin 1s linear infinite' }} />
            <p>Loading categories...</p>
          </div>
        ) : categoriesError ? (
          <div style={{ textAlign: 'center', padding: '2rem' }}>
            <p>Error loading categories: {categoriesError.message}</p>
            <button onClick={() => refetchCategories()} className="btn btn-secondary">
              Retry
            </button>
          </div>
        ) : !categoriesData || categoriesData.length === 0 ? (
          <div style={{ textAlign: 'center', padding: '2rem' }}>
            <h4>No Categories Available</h4>
            <p>No service categories are currently available for this branch.</p>
            <button className="btn btn-primary" onClick={handleCreateCategory}>
              Create First Category
            </button>
          </div>
        ) : (
          <div style={{ background: 'white', border: '1px solid #e5e7eb', borderRadius: '8px', overflow: 'hidden' }}>
            <table style={{ width: '100%', borderCollapse: 'collapse' }}>
              <thead style={{ background: '#f8fafc' }}>
                <tr>
                  <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #e5e7eb' }}>Category Name</th>
                  <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #e5e7eb' }}>Description</th>
                  <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #e5e7eb' }}>Vehicle Type</th>
                  <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #e5e7eb' }}>Tasks</th>
                  <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #e5e7eb' }}>Status</th>
                  <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #e5e7eb' }}>Actions</th>
                </tr>
              </thead>
              <tbody>
                {categoriesData.map((category) => (
                  <tr key={category.id} style={{ borderBottom: '1px solid #f3f4f6' }}>
                    <td style={{ padding: '12px' }}>
                      <strong>{category.name}</strong>
                    </td>
                    <td style={{ padding: '12px' }}>
                      {category.description || "No description"}
                    </td>
                    <td style={{ padding: '12px' }}>
                      {category.vehicleType || "All Types"}
                    </td>
                    <td style={{ padding: '12px' }}>
                      {category.tasksCount || 0}
                    </td>
                    <td style={{ padding: '12px' }}>
                      <span style={{
                        padding: '4px 8px',
                        borderRadius: '12px',
                        fontSize: '12px',
                        fontWeight: '600',
                        background: category.isActive ? '#dcfce7' : '#fef2f2',
                        color: category.isActive ? '#166534' : '#dc2626'
                      }}>
                        {category.isActive ? 'Active' : 'Inactive'}
                      </span>
                    </td>
                    <td style={{ padding: '12px' }}>
                      <div style={{ display: 'flex', gap: '8px' }}>
                        <button
                          style={{ padding: '4px 8px', border: '1px solid #d1d5db', borderRadius: '4px', background: 'white' }}
                          title="View Tasks"
                          onClick={() => navigate(`/vendor-management/branch/${branchDetails?.id}/categories/${category.id}`)}
                        >
                          <FaEye />
                        </button>
                        <button
                          style={{ padding: '4px 8px', border: '1px solid #d1d5db', borderRadius: '4px', background: 'white' }}
                          title="Edit"
                          onClick={() => handleEditCategory(category)}
                        >
                          <FaEdit />
                        </button>
                        <button
                          style={{ padding: '4px 8px', border: '1px solid #d1d5db', borderRadius: '4px', background: 'white' }}
                          title="Delete"
                          onClick={() => handleDeleteCategory(category)}
                        >
                          <FaChevronRight />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );

  // Render Vehicles Section - Using same layout as VendorDetails
  const renderVehiclesSection = () => (
    <div className="vm-vendor-profile-content">
      <div className="vm-profile-header">
        <div className="vm-profile-title">
          <h2>Vehicles</h2>
          <p>All vehicles registered under this branch</p>
        </div>
      </div>

      <div className="vm-section-content">
        <div className="vm-vehicles-content">
          {vehiclesData?.vehicleDetails?.length > 0 ? (
            <div className="vm-vehicles-list">
              {vehiclesData.vehicleDetails.map((vehicle, index) => (
                <div
                  key={vehicle.id || index}
                  className="vm-vehicle-item"
                  onClick={() =>
                    navigate(ROUTES?.VEHICLE_DETAILS, {
                      state: { vehicle },
                    })
                  }
                >
                  <div className="vm-vehicle-icon">
                    <FaCar />
                  </div>
                  <div className="vm-vehicle-info">
                    <h4>{vehicle.registrationNumber || vehicle.vehicleNumber || "N/A"}</h4>
                    <span className="vm-vehicle-meta">
                      {vehicle.model} • {vehicle.make || "Unknown Make"}
                    </span>
                  </div>
                  <span className={`vm-vehicle-status ${(vehicle.status || "Active").toLowerCase()}`}>
                    {vehicle.status || "Active"}
                  </span>
                  <div className="vm-vehicle-stats">
                    <span>{vehicle.ownerName || "N/A"}</span>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="vm-empty-state">
              <FaCar className="vm-empty-icon" />
              <h4>No Vehicles Found</h4>
              <p>No vehicles are currently registered under this branch.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );

  // Dummy Job Cards Data
  const dummyJobCards = [
    {
      id: "JC001",
      jobCardNumber: "JC-2024-001",
      vehicleNumber: "KA01AB1234",
      customerName: "Rajesh Kumar",
      serviceType: "Regular Service",
      status: "Completed",
      createdDate: "2024-01-15",
      completedDate: "2024-01-15",
      totalAmount: 2500,
      technician: "Suresh M",
      services: ["Oil Change", "Brake Check", "Tire Rotation"]
    },
    {
      id: "JC002",
      jobCardNumber: "JC-2024-002",
      vehicleNumber: "KA02CD5678",
      customerName: "Priya Sharma",
      serviceType: "Repair",
      status: "In Progress",
      createdDate: "2024-01-16",
      completedDate: null,
      totalAmount: 4200,
      technician: "Ramesh K",
      services: ["Engine Repair", "AC Service"]
    },
    {
      id: "JC003",
      jobCardNumber: "JC-2024-003",
      vehicleNumber: "KA03EF9012",
      customerName: "Amit Patel",
      serviceType: "Inspection",
      status: "Pending",
      createdDate: "2024-01-17",
      completedDate: null,
      totalAmount: 800,
      technician: "Vinod S",
      services: ["Safety Inspection", "Emission Test"]
    },
    {
      id: "JC004",
      jobCardNumber: "JC-2024-004",
      vehicleNumber: "KA04GH3456",
      customerName: "Sneha Reddy",
      serviceType: "Emergency",
      status: "Completed",
      createdDate: "2024-01-18",
      completedDate: "2024-01-18",
      totalAmount: 1500,
      technician: "Manoj T",
      services: ["Battery Replacement", "Starter Motor Fix"]
    },
    {
      id: "JC005",
      jobCardNumber: "JC-2024-005",
      vehicleNumber: "KA05IJ7890",
      customerName: "Vikram Singh",
      serviceType: "Regular Service",
      status: "Cancelled",
      createdDate: "2024-01-19",
      completedDate: null,
      totalAmount: 0,
      technician: "N/A",
      services: ["Full Service"]
    }
  ];

  // Render Job Cards Section - Using same layout as VendorDetails
  const renderJobCardsSection = () => (
    <div className="vm-vendor-profile-content">
      <div className="vm-profile-header">
        <div className="vm-profile-title">
          <h2>Job Cards</h2>
          <p>Service records and job card management</p>
        </div>
      </div>

      <div className="vm-section-content">
        <div className="vm-jobcards-content">
          {dummyJobCards.length > 0 ? (
            <div className="vm-jobcards-list">
              {dummyJobCards.map((jobCard, index) => (
                <div
                  key={jobCard.id || index}
                  className="vm-jobcard-item"
                  onClick={() => {
                    setSelectedJobCard(jobCard);
                  }}
                >
                  <div className="vm-jobcard-icon">
                    <FaTasks />
                  </div>
                  <div className="vm-jobcard-info">
                    <h4>{jobCard.jobCardNumber}</h4>
                    <span className="vm-jobcard-meta">
                      {jobCard.vehicleNumber} • {jobCard.customerName}
                    </span>
                    <span className="vm-jobcard-meta">
                      {jobCard.serviceType} • {jobCard.technician}
                    </span>
                  </div>
                  <div className="vm-jobcard-stats">
                    <span className="vm-jobcard-amount">
                      ₹{jobCard.totalAmount.toLocaleString()}
                    </span>
                    <span className="vm-jobcard-date">
                      {new Date(jobCard.createdDate).toLocaleDateString('en-IN')}
                    </span>
                  </div>
                  <span className={`vm-jobcard-status ${jobCard.status.toLowerCase().replace(' ', '-')}`}>
                    {jobCard.status}
                  </span>
                </div>
              ))}
            </div>
          ) : (
            <div className="vm-empty-state">
              <FaTasks className="vm-empty-icon" />
              <h4>No Job Cards Found</h4>
              <p>No job cards are currently available for this branch.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );

  // Handle back to categories
  const handleBackToCategories = () => {
    setSelectedCategory(null);
  };

  // Note: Carousel functions removed as we're using the new VendorDetails layout pattern

  // If a job card is selected, show the detailed view
  if (selectedJobCard) {
    return (
      <JobCardDetails
        jobCard={selectedJobCard}
        onBack={() => setSelectedJobCard(null)}
      />
    );
  }

  return (
    <div className="page-with-header">
      <MainHeading
        title={"Branch Details"}
        subtitle="View and manage branch information"
        showBackButton={true}
        onBackClick={handleBackClick}
        showFilter={false}
      />
      <div className="page-content-scrollable">
        <div className="vm-branch-details-container">
          {/* Branch Header */}
         

          {/* Branch Information Card */}
          {renderBranchInformationSection()}

          {/* Tab Navigation */}
          {/* Enhanced Tabbed Interface - Same as Subscription Plan Details */}
          <div className="vm-plan-tabs-container">
            {/* Tab Navigation */}
            <div className="vm-tab-navigation">
              <div className="vm-tab-buttons-container">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    className={`vm-tab-button ${activeTab === tab.id ? 'vm-active' : ''}`}
                    onClick={() => handleTabChange(tab.id)}
                  >
                   
                    {tab.name}
                  </button>
                ))}
              </div>

              {/* Navigation Arrows */}
              <div className="vm-tab-arrows">
                <button
                  className="vm-tab-nav-arrow"
                  onClick={() => {
                    const currentIndex = tabs.findIndex(tab => tab.id === activeTab);
                    const prevIndex = currentIndex > 0 ? currentIndex - 1 : tabs.length - 1;
                    handleTabChange(tabs[prevIndex].id);
                  }}
                  title="Previous Tab"
                >
                  <FaChevronLeft />
                </button>
                <button
                  className="vm-tab-nav-arrow"
                  onClick={() => {
                    const currentIndex = tabs.findIndex(tab => tab.id === activeTab);
                    const nextIndex = currentIndex < tabs.length - 1 ? currentIndex + 1 : 0;
                    handleTabChange(tabs[nextIndex].id);
                  }}
                  title="Next Tab"
                >
                  <FaChevronRight />
                </button>
              </div>
            </div>

            {/* Tab Content */}
            <div className="vm-tab-content">
              {isTabLoading ? (
                <div className="vm-tab-loading">
                  <FaSpinner className="vm-loading-spinner" />
                  <span>Loading...</span>
                </div>
              ) : (
                <>
                  {/* Tab Panels */}
                  <div className={`vm-tab-panel ${activeTab === 'categories' ? 'vm-active' : ''}`}>
                    {activeTab === 'categories' && renderServiceCategoriesSection()}
                  </div>
                  <div className={`vm-tab-panel ${activeTab === 'vehicles' ? 'vm-active' : ''}`}>
                    {activeTab === 'vehicles' && renderVehiclesSection()}
                  </div>
                  <div className={`vm-tab-panel ${activeTab === 'jobcards' ? 'vm-active' : ''}`}>
                    {activeTab === 'jobcards' && renderJobCardsSection()}
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        isOpen={deleteModal.isOpen}
        onClose={handleDeleteCancel}
        onConfirm={handleDeleteConfirm}
        title="Delete Category"
        message="Are you sure you want to delete this category?"
        itemName={deleteModal.category?.name}
        isLoading={deleteModal.isLoading}
      />

      {/* Service Task Delete Confirmation Modal */}
      <DeleteConfirmationModal
        isOpen={serviceTaskDeleteModal.isOpen}
        onClose={handleServiceTaskDeleteCancel}
        onConfirm={handleServiceTaskDeleteConfirm}
        title="Delete Service Task"
        message="Are you sure you want to delete this service task?"
        itemName={serviceTaskDeleteModal.serviceTask?.name}
        isLoading={serviceTaskDeleteModal.isLoading}
      />
    </div>
  );
};

export default BranchDetails;
