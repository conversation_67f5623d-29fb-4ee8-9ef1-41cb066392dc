using AutoMapper;
using FluentValidation;
using InventoryManagementService.Application.DTOs;
using InventoryManagementService.Domain.Interfaces;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using VMS.Contracts.Common.Authorization;

namespace InventoryManagementService.Application.Features.Parts.Commands;

public class UpdatePartCommand : IRequest<PartDto>
{
    public Guid PartId { get; set; }
    public UpdatePartDto Part { get; set; } = null!;
    public string UpdatedBy { get; set; } = string.Empty;
}

public class UpdatePartCommandValidator : AbstractValidator<UpdatePartCommand>
{
    public UpdatePartCommandValidator()
    {
        RuleFor(x => x.PartId)
            .NotEmpty().WithMessage("Part ID is required");

        RuleFor(x => x.Part.Name)
            .NotEmpty().WithMessage("Name is required")
            .MaximumLength(200).WithMessage("Name cannot exceed 200 characters");

        RuleFor(x => x.Part.Description)
            .NotEmpty().WithMessage("Description is required")
            .MaximumLength(1000).WithMessage("Description cannot exceed 1000 characters");

        RuleFor(x => x.Part.Manufacturer)
            .NotEmpty().WithMessage("Manufacturer is required")
            .MaximumLength(100).WithMessage("Manufacturer cannot exceed 100 characters");

        RuleFor(x => x.Part.CostPrice)
            .GreaterThan(0).WithMessage("Cost price must be greater than 0");

        RuleFor(x => x.Part.RetailPrice)
            .GreaterThan(0).WithMessage("Retail price must be greater than 0");

        RuleFor(x => x.Part.Category)
            .IsInEnum().WithMessage("Invalid category");

        RuleFor(x => x.UpdatedBy)
            .NotEmpty().WithMessage("UpdatedBy is required");
    }
}

public class UpdatePartCommandHandler : IRequestHandler<UpdatePartCommand, PartDto>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly ILogger<UpdatePartCommandHandler> _logger;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly IEventPublisher _eventPublisher;

    public UpdatePartCommandHandler(
        IUnitOfWork unitOfWork,
        IMapper mapper,
        ILogger<UpdatePartCommandHandler> logger,
        IHttpContextAccessor httpContextAccessor,
        IEventPublisher eventPublisher)
    {
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _logger = logger;
        _httpContextAccessor = httpContextAccessor;
        _eventPublisher = eventPublisher;
    }

    public async Task<PartDto> Handle(UpdatePartCommand request, CancellationToken cancellationToken)
    {
        // Check permissions - Admin roles bypass, others need explicit permission
        var user = _httpContextAccessor.HttpContext?.User;
        if (!PermissionHelper.IsAdmin(user))
        {
            PermissionHelper.ValidateAccess(user, "Parts.Update");
        }

        _logger.LogInformation("Updating part with ID: {PartId}", request.PartId);

        var part = await _unitOfWork.Parts.GetByIdAsync(request.PartId);

        if (part == null)
        {
            throw new KeyNotFoundException($"Part with ID {request.PartId} not found");
        }

        part.Update(
            request.Part.Name,
            request.Part.Description,
            request.Part.Manufacturer,
            request.Part.CostPrice,
            request.Part.RetailPrice,
            request.Part.Category,
            request.UpdatedBy,
            request.Part.PartNumber,
            request.Part.Barcode,
            request.Part.WarrantyMonths,
            request.Part.ImageUrl,
            request.Part.Notes);

        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("Part updated successfully with ID: {PartId}", part.Id);

        // Publish event
        await _eventPublisher.PublishPartUpdatedEvent(
            part.Id,
            part.SKU,
            part.Name,
            part.Manufacturer);

        return _mapper.Map<PartDto>(part);
    }
}
