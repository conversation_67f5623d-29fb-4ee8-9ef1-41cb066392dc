using InventoryManagementService.Application.DTOs;
using InventoryManagementService.Application.Features.Inventory.Commands;
using InventoryManagementService.Application.Features.Inventory.Queries;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace InventoryManagementService.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class InventoryController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<InventoryController> _logger;

    public InventoryController(IMediator mediator, ILogger<InventoryController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Get inventory item by ID
    /// </summary>
    [HttpGet("{id}")]
    [Authorize(Policy = "Inventory.View")]
    public async Task<ActionResult<InventoryItemDto>> GetInventoryItem(Guid id)
    {
        try
        {
            var query = new GetInventoryItemByIdQuery { InventoryItemId = id };
            var inventoryItem = await _mediator.Send(query);

            if (inventoryItem == null)
            {
                return NotFound($"Inventory item with ID {id} not found");
            }

            return Ok(inventoryItem);
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access attempt to get inventory item {InventoryItemId}", id);
            return Forbid(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving inventory item {InventoryItemId}", id);
            return StatusCode(500, new { message = "An error occurred while retrieving the inventory item" });
        }
    }

    /// <summary>
    /// Get inventory items by branch
    /// </summary>
    [HttpGet("branch/{branchId}")]
    [Authorize(Policy = "Inventory.View")]
    public async Task<ActionResult<List<InventoryItemDto>>> GetInventoryByBranch(Guid branchId)
    {
        try
        {
            var query = new GetInventoryByBranchQuery { BranchId = branchId };
            var inventoryItems = await _mediator.Send(query);
            return Ok(inventoryItems);
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access attempt to get inventory for branch {BranchId}", branchId);
            return Forbid(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving inventory for branch {BranchId}", branchId);
            return StatusCode(500, new { message = "An error occurred while retrieving inventory" });
        }
    }

    /// <summary>
    /// Get inventory items by part
    /// </summary>
    [HttpGet("part/{partId}")]
    [Authorize(Policy = "Inventory.View")]
    public async Task<ActionResult<List<InventoryItemDto>>> GetInventoryByPart(Guid partId)
    {
        try
        {
            var query = new GetInventoryByPartQuery { PartId = partId };
            var inventoryItems = await _mediator.Send(query);
            return Ok(inventoryItems);
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access attempt to get inventory for part {PartId}", partId);
            return Forbid(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving inventory for part {PartId}", partId);
            return StatusCode(500, new { message = "An error occurred while retrieving inventory" });
        }
    }

    /// <summary>
    /// Get low stock items for a branch
    /// </summary>
    [HttpGet("branch/{branchId}/low-stock")]
    [Authorize(Policy = "Inventory.View")]
    public async Task<ActionResult<List<InventoryItemDto>>> GetLowStockItems(Guid branchId)
    {
        try
        {
            var query = new GetLowStockItemsQuery { BranchId = branchId };
            var inventoryItems = await _mediator.Send(query);
            return Ok(inventoryItems);
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access attempt to get low stock items for branch {BranchId}", branchId);
            return Forbid(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving low stock items for branch {BranchId}", branchId);
            return StatusCode(500, new { message = "An error occurred while retrieving low stock items" });
        }
    }

    /// <summary>
    /// Create a new inventory item
    /// </summary>
    [HttpPost]
    [Authorize(Policy = "Inventory.Create")]
    public async Task<ActionResult<InventoryItemDto>> CreateInventoryItem([FromBody] CreateInventoryItemDto createInventoryItemDto)
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "System";
            
            var command = new CreateInventoryItemCommand
            {
                InventoryItem = createInventoryItemDto,
                CreatedBy = userId
            };

            var inventoryItem = await _mediator.Send(command);
            return CreatedAtAction(nameof(GetInventoryItem), new { id = inventoryItem.Id }, inventoryItem);
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access attempt to create inventory item");
            return Forbid(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation during inventory item creation");
            return BadRequest(new { message = ex.Message });
        }
        catch (KeyNotFoundException ex)
        {
            _logger.LogWarning(ex, "Referenced entity not found during inventory item creation");
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating inventory item");
            return StatusCode(500, new { message = "An error occurred while creating the inventory item" });
        }
    }

    /// <summary>
    /// Add stock to an inventory item
    /// </summary>
    [HttpPost("{id}/add-stock")]
    [Authorize(Policy = "Inventory.Update")]
    public async Task<ActionResult> AddStock(Guid id, [FromBody] UpdateInventoryStockDto updateStockDto)
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "System";
            
            var command = new AddStockCommand
            {
                InventoryItemId = id,
                Quantity = updateStockDto.Quantity,
                Reason = updateStockDto.Reason,
                UpdatedBy = userId
            };

            await _mediator.Send(command);
            return NoContent();
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access attempt to add stock to inventory item {InventoryItemId}", id);
            return Forbid(ex.Message);
        }
        catch (KeyNotFoundException ex)
        {
            _logger.LogWarning(ex, "Inventory item not found for adding stock: {InventoryItemId}", id);
            return NotFound(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding stock to inventory item {InventoryItemId}", id);
            return StatusCode(500, new { message = "An error occurred while adding stock" });
        }
    }

    /// <summary>
    /// Remove stock from an inventory item
    /// </summary>
    [HttpPost("{id}/remove-stock")]
    [Authorize(Policy = "Inventory.Update")]
    public async Task<ActionResult> RemoveStock(Guid id, [FromBody] UpdateInventoryStockDto updateStockDto)
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "System";
            
            var command = new RemoveStockCommand
            {
                InventoryItemId = id,
                Quantity = updateStockDto.Quantity,
                Reason = updateStockDto.Reason,
                UpdatedBy = userId
            };

            await _mediator.Send(command);
            return NoContent();
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access attempt to remove stock from inventory item {InventoryItemId}", id);
            return Forbid(ex.Message);
        }
        catch (KeyNotFoundException ex)
        {
            _logger.LogWarning(ex, "Inventory item not found for removing stock: {InventoryItemId}", id);
            return NotFound(new { message = ex.Message });
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation during stock removal from inventory item {InventoryItemId}", id);
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing stock from inventory item {InventoryItemId}", id);
            return StatusCode(500, new { message = "An error occurred while removing stock" });
        }
    }

    /// <summary>
    /// Update stock quantity with reason
    /// </summary>
    [HttpPut("{id}/stock")]
    [Authorize(Policy = "Inventory.Update")]
    public async Task<ActionResult> UpdateStock(Guid id, [FromBody] UpdateInventoryStockDto updateStockDto)
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "System";
            
            var command = new UpdateStockCommand
            {
                InventoryItemId = id,
                NewQuantity = updateStockDto.Quantity,
                Reason = updateStockDto.Reason,
                UpdatedBy = userId
            };

            await _mediator.Send(command);
            return NoContent();
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access attempt to update stock for inventory item {InventoryItemId}", id);
            return Forbid(ex.Message);
        }
        catch (KeyNotFoundException ex)
        {
            _logger.LogWarning(ex, "Inventory item not found for updating stock: {InventoryItemId}", id);
            return NotFound(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating stock for inventory item {InventoryItemId}", id);
            return StatusCode(500, new { message = "An error occurred while updating stock" });
        }
    }

    /// <summary>
    /// Update storage location
    /// </summary>
    [HttpPut("{id}/storage-location")]
    [Authorize(Policy = "Inventory.Update")]
    public async Task<ActionResult> UpdateStorageLocation(Guid id, [FromBody] UpdateStorageLocationDto updateLocationDto)
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "System";
            
            var command = new UpdateStorageLocationCommand
            {
                InventoryItemId = id,
                StorageLocation = updateLocationDto.StorageLocation,
                Bin = updateLocationDto.Bin,
                Shelf = updateLocationDto.Shelf,
                UpdatedBy = userId
            };

            await _mediator.Send(command);
            return NoContent();
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access attempt to update storage location for inventory item {InventoryItemId}", id);
            return Forbid(ex.Message);
        }
        catch (KeyNotFoundException ex)
        {
            _logger.LogWarning(ex, "Inventory item not found for updating storage location: {InventoryItemId}", id);
            return NotFound(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating storage location for inventory item {InventoryItemId}", id);
            return StatusCode(500, new { message = "An error occurred while updating storage location" });
        }
    }
}
