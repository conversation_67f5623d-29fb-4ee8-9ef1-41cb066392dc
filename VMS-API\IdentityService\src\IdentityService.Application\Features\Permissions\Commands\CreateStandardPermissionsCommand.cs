using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using IdentityService.Application.Common;
using IdentityService.Application.DTOs.Permissions;
using IdentityService.Application.Interfaces;
using IdentityService.Domain.Entities;
using IdentityService.Domain.Interfaces;
using MediatR;

namespace IdentityService.Application.Features.Permissions.Commands;

public class CreateStandardPermissionsCommand : BaseRequest<List<PermissionResponse>>
{
    public CreateStandardPermissionsRequest Request { get; set; }
}

public class CreateStandardPermissionsCommandHandler : IRequestHandler<CreateStandardPermissionsCommand, List<PermissionResponse>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICurrentUserService _currentUserService;
    private readonly IAuditLogService _auditLogService;

    public CreateStandardPermissionsCommandHandler(
        IUnitOfWork unitOfWork,
        ICurrentUserService currentUserService,
        IAuditLogService auditLogService)
    {
        _unitOfWork = unitOfWork;
        _currentUserService = currentUserService;
        _auditLogService = auditLogService;
    }

    public async Task<List<PermissionResponse>> Handle(CreateStandardPermissionsCommand command, CancellationToken cancellationToken)
    {
        var request = command.Request;
        var createdBy = _currentUserService.UserId.ToString() ?? "System";

        // Define standard actions
        var standardActions = new[] { "View", "Create", "Update", "Delete" };

        // Check if any of the standard permissions already exist
        var existingPermissions = await _unitOfWork.PermissionRepository.GetAllAsync();
        foreach (var action in standardActions)
        {
            var permissionName = $"{request.Resource}.{action}";
            if (existingPermissions.Any(p => p.Name.Equals(permissionName, StringComparison.OrdinalIgnoreCase)))
                throw new InvalidOperationException($"Permission with name '{permissionName}' already exists");
        }

        // Create standard permissions
        var permissions = new List<Permission>();
        foreach (var action in standardActions)
        {
            var permissionName = $"{request.Resource}.{action}";
            var permissionDescription = $"{action} {request.Description.ToLower()}";

            var permission = Permission.Create(
                permissionName,
                permissionDescription,
                request.Resource,
                action,
                createdBy);

            permissions.Add(permission);

            // Save permission
            await _unitOfWork.PermissionRepository.AddAsync(permission);

            // Create audit log
            await _auditLogService.CreateAuditLogAsync(
                "Create",
                "Permission",
                permission.Id.ToString(),
                string.Empty,
                System.Text.Json.JsonSerializer.Serialize(new {
                    permission.Id,
                    permission.Name,
                    permission.Description,
                    permission.Resource,
                    permission.Action
                }),
                "Id,Name,Description,Resource,Action",
                _currentUserService.UserId ?? Guid.Empty);
        }

        await _unitOfWork.SaveChangesAsync(cancellationToken);

        // Create response
        var response = new List<PermissionResponse>();
        foreach (var permission in permissions)
        {
            response.Add(new PermissionResponse
            {
                Id = permission.Id,
                Name = permission.Name,
                Description = permission.Description,
                Resource = permission.Resource,
                Action = permission.Action,
                CreatedAt = permission.CreatedAt,
                CreatedBy = permission.CreatedBy,
                UpdatedAt = permission.UpdatedAt,
                UpdatedBy = permission.UpdatedBy
            });
        }

        return response;
    }
}
