using InventoryManagementService.Domain.Enums;

namespace InventoryManagementService.Application.DTOs;

public class InventoryItemDto
{
    public Guid Id { get; set; }
    public Guid PartId { get; set; }
    public Guid BranchId { get; set; }
    public Guid VendorId { get; set; }
    public int CurrentStock { get; set; }
    public int MinimumStock { get; set; }
    public int MaximumStock { get; set; }
    public int ReorderLevel { get; set; }
    public string StorageLocation { get; set; } = string.Empty;
    public string? Bin { get; set; }
    public string? Shelf { get; set; }
    public DateTime? LastStockUpdate { get; set; }
    public bool IsLowStock { get; set; }
    public bool IsOutOfStock { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public DateTime? UpdatedAt { get; set; }
    public string? UpdatedBy { get; set; }
    
    public PartDto? Part { get; set; }
}

public class InventoryTransactionDto
{
    public Guid Id { get; set; }
    public Guid InventoryItemId { get; set; }
    public TransactionType TransactionType { get; set; }
    public int Quantity { get; set; }
    public int PreviousStock { get; set; }
    public int NewStock { get; set; }
    public string Reason { get; set; } = string.Empty;
    public string? Reference { get; set; }
    public Guid? RelatedTransactionId { get; set; }
    public DateTime TransactionDate { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
}

public class CreateInventoryItemDto
{
    public Guid PartId { get; set; }
    public Guid BranchId { get; set; }
    public Guid VendorId { get; set; }
    public int InitialStock { get; set; }
    public int MinimumStock { get; set; }
    public int MaximumStock { get; set; }
    public int ReorderLevel { get; set; }
    public string StorageLocation { get; set; } = string.Empty;
    public string? Bin { get; set; }
    public string? Shelf { get; set; }
}

public class UpdateInventoryStockDto
{
    public int Quantity { get; set; }
    public string Reason { get; set; } = string.Empty;
}

public class UpdateStorageLocationDto
{
    public string StorageLocation { get; set; } = string.Empty;
    public string? Bin { get; set; }
    public string? Shelf { get; set; }
}

public class UpdateStockLevelsDto
{
    public int MinimumStock { get; set; }
    public int MaximumStock { get; set; }
    public int ReorderLevel { get; set; }
}
