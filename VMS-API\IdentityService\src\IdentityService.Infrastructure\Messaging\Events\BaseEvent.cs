using VMSContracts.Common;

namespace IdentityService.Infrastructure.Messaging.Events;

/// <summary>
/// Base implementation for all events
/// </summary>
public abstract class BaseEvent : IEvent
{
    /// <inheritdoc />
    public Guid EventId { get; }
    
    /// <inheritdoc />
    public DateTime Timestamp { get; }
    
    /// <inheritdoc />
    public string Publisher { get; }

    protected BaseEvent()
    {
        EventId = Guid.NewGuid();
        Timestamp = DateTime.UtcNow;
        Publisher = "IdentityService";
    }
}
