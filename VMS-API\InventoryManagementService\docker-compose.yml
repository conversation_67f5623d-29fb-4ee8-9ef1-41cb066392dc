version: '3.8'

services:
  inventorymanagementservice-api:
    build:
      context: .
      dockerfile: docker/Dockerfile
    ports:
      - "5006:8080"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__DefaultConnection=Host=inventorymanagementservice-db;Port=5432;Database=InventoryManagementDB;Username=postgres;Password=postgres
      - RabbitMQ__Host=rabbitmq
      - RabbitMQ__Port=5672
      - RabbitMQ__Username=guest
      - RabbitMQ__Password=guest
    depends_on:
      - inventorymanagementservice-db
      - rabbitmq
    networks:
      - vms-network

  inventorymanagementservice-db:
    image: postgres:16
    environment:
      POSTGRES_DB: InventoryManagementDB
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5438:5432"
    volumes:
      - inventorymanagementservice_postgres_data:/var/lib/postgresql/data
    networks:
      - vms-network

  rabbitmq:
    image: rabbitmq:3-management
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      RABBITMQ_DEFAULT_USER: guest
      RABBITMQ_DEFAULT_PASS: guest
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - vms-network

volumes:
  inventorymanagementservice_postgres_data:
  rabbitmq_data:

networks:
  vms-network:
    driver: bridge
