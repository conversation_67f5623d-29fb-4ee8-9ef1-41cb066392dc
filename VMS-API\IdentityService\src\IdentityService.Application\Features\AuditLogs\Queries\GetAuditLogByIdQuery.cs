using System;
using System.Threading;
using System.Threading.Tasks;
using IdentityService.Application.Common;
using IdentityService.Application.DTOs.AuditLogs;
using IdentityService.Application.Interfaces;
using MediatR;

namespace IdentityService.Application.Features.AuditLogs.Queries;

public class GetAuditLogByIdQuery : BaseRequest<AuditLogResponse>
{
    public Guid AuditLogId { get; set; }
}

public class GetAuditLogByIdQueryHandler : IRequestHandler<GetAuditLogByIdQuery, AuditLogResponse>
{
    private readonly IAuditLogService _auditLogService;

    public GetAuditLogByIdQueryHandler(IAuditLogService auditLogService)
    {
        _auditLogService = auditLogService;
    }

    public async Task<AuditLogResponse> Handle(GetAuditLogByIdQuery query, CancellationToken cancellationToken)
    {
        var auditLog = await _auditLogService.GetAuditLogByIdAsync(query.AuditLogId);
        if (auditLog == null)
            throw new InvalidOperationException($"Audit log with ID {query.AuditLogId} not found");

        return new AuditLogResponse
        {
            Id = auditLog.Id,
            Action = auditLog.Action,
            EntityName = auditLog.EntityName,
            EntityId = auditLog.EntityId,
            OldValues = auditLog.OldValues,
            NewValues = auditLog.NewValues,
            AffectedColumns = auditLog.AffectedColumns,
            IpAddress = auditLog.IpAddress,
            UserAgent = auditLog.UserAgent,
            UserId = auditLog.UserId,
            UserEmail = auditLog.User?.Email ?? "Unknown",
            CreatedAt = auditLog.CreatedAt,
            CreatedBy = auditLog.CreatedBy
        };
    }
}
