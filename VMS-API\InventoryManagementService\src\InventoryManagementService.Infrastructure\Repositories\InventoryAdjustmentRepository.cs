using InventoryManagementService.Domain.Entities;
using InventoryManagementService.Domain.Enums;
using InventoryManagementService.Domain.Interfaces;
using InventoryManagementService.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;

namespace InventoryManagementService.Infrastructure.Repositories;

public class InventoryAdjustmentRepository : IInventoryAdjustmentRepository
{
    private readonly InventoryDbContext _context;

    public InventoryAdjustmentRepository(InventoryDbContext context)
    {
        _context = context;
    }

    public async Task<InventoryAdjustment?> GetByIdAsync(Guid id)
    {
        return await _context.InventoryAdjustments
            .Include(ia => ia.Items)
                .ThenInclude(iai => iai.Part)
            .FirstOrDefaultAsync(ia => ia.Id == id);
    }

    public async Task<InventoryAdjustment?> GetByAdjustmentNumberAsync(string adjustmentNumber)
    {
        return await _context.InventoryAdjustments
            .Include(ia => ia.Items)
                .ThenInclude(iai => iai.Part)
            .FirstOrDefaultAsync(ia => ia.AdjustmentNumber == adjustmentNumber);
    }

    public async Task<List<InventoryAdjustment>> GetAllAsync()
    {
        return await _context.InventoryAdjustments
            .Include(ia => ia.Items)
                .ThenInclude(iai => iai.Part)
            .Where(ia => ia.IsActive)
            .OrderByDescending(ia => ia.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<InventoryAdjustment>> GetByBranchAsync(Guid branchId)
    {
        return await _context.InventoryAdjustments
            .Include(ia => ia.Items)
                .ThenInclude(iai => iai.Part)
            .Where(ia => ia.BranchId == branchId && ia.IsActive)
            .OrderByDescending(ia => ia.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<InventoryAdjustment>> GetByVendorAsync(Guid vendorId)
    {
        return await _context.InventoryAdjustments
            .Include(ia => ia.Items)
                .ThenInclude(iai => iai.Part)
            .Where(ia => ia.VendorId == vendorId && ia.IsActive)
            .OrderByDescending(ia => ia.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<InventoryAdjustment>> GetByReasonAsync(AdjustmentReason reason)
    {
        return await _context.InventoryAdjustments
            .Include(ia => ia.Items)
                .ThenInclude(iai => iai.Part)
            .Where(ia => ia.Reason == reason && ia.IsActive)
            .OrderByDescending(ia => ia.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<InventoryAdjustment>> GetPendingAdjustmentsAsync()
    {
        return await _context.InventoryAdjustments
            .Include(ia => ia.Items)
                .ThenInclude(iai => iai.Part)
            .Where(ia => !ia.IsApproved && ia.IsActive)
            .OrderBy(ia => ia.AdjustmentDate)
            .ToListAsync();
    }

    public async Task<List<InventoryAdjustment>> GetApprovedAdjustmentsAsync()
    {
        return await _context.InventoryAdjustments
            .Include(ia => ia.Items)
                .ThenInclude(iai => iai.Part)
            .Where(ia => ia.IsApproved && ia.IsActive)
            .OrderByDescending(ia => ia.ApprovedDate)
            .ToListAsync();
    }

    public async Task<List<InventoryAdjustment>> GetAsync(Expression<Func<InventoryAdjustment, bool>> predicate)
    {
        return await _context.InventoryAdjustments
            .Include(ia => ia.Items)
                .ThenInclude(iai => iai.Part)
            .Where(predicate)
            .OrderByDescending(ia => ia.CreatedAt)
            .ToListAsync();
    }

    public async Task AddAsync(InventoryAdjustment inventoryAdjustment)
    {
        await _context.InventoryAdjustments.AddAsync(inventoryAdjustment);
    }

    public async Task UpdateAsync(InventoryAdjustment inventoryAdjustment)
    {
        _context.InventoryAdjustments.Update(inventoryAdjustment);
        await Task.CompletedTask;
    }

    public async Task DeleteAsync(Guid id)
    {
        var inventoryAdjustment = await _context.InventoryAdjustments.FindAsync(id);
        if (inventoryAdjustment != null)
        {
            _context.InventoryAdjustments.Remove(inventoryAdjustment);
        }
    }

    public async Task<bool> ExistsAsync(Guid id)
    {
        return await _context.InventoryAdjustments.AnyAsync(ia => ia.Id == id);
    }

    public async Task<bool> AdjustmentNumberExistsAsync(string adjustmentNumber, Guid? excludeId = null)
    {
        var query = _context.InventoryAdjustments.Where(ia => ia.AdjustmentNumber == adjustmentNumber);
        
        if (excludeId.HasValue)
        {
            query = query.Where(ia => ia.Id != excludeId.Value);
        }

        return await query.AnyAsync();
    }
}
