using System;
using System.Threading.Tasks;
using IdentityService.Domain.Enums;
using IdentityService.Domain.Interfaces;
using MassTransit;
using Microsoft.Extensions.Logging;
using VMSContracts.Subscription.Events;

namespace IdentityService.Infrastructure.Messaging.Consumers;

/// <summary>
/// Consumer for SubscriptionStatusChanged events
/// </summary>
public class SubscriptionStatusChangedConsumer : IConsumer<SubscriptionStatusChanged>
{
    private readonly ILogger<SubscriptionStatusChangedConsumer> _logger;
    private readonly IUnitOfWork _unitOfWork;

    public SubscriptionStatusChangedConsumer(
        ILogger<SubscriptionStatusChangedConsumer> logger,
        IUnitOfWork unitOfWork)
    {
        _logger = logger;
        _unitOfWork = unitOfWork;
    }

    public async Task Consume(ConsumeContext<SubscriptionStatusChanged> context)
    {
        var @event = context.Message;

        _logger.LogInformation(
            "Received SubscriptionStatusChanged event for user {UserId}, subscription {SubscriptionId}, status changed from {PreviousStatus} to {NewStatus}",
            @event.UserId, @event.SubscriptionId, @event.PreviousStatus, @event.NewStatus);

        try
        {
            // Get the user
            var user = await _unitOfWork.UserRepository.GetByIdAsync(@event.UserId);
            if (user == null)
            {
                _logger.LogWarning("User with ID {@UserId} not found", @event.UserId);
                return;
            }

            // Convert subscription status
            var status = ConvertSubscriptionStatus(@event.NewStatus);

            // Update subscription status
            user.UpdateSubscriptionStatus(@event.SubscriptionId, status, "System");

            // Save changes
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation(
                "Successfully updated subscription {SubscriptionId} status to {Status} for user {UserId}",
                @event.SubscriptionId, status, @event.UserId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex,
                "Error processing SubscriptionStatusChanged event for user {UserId}, subscription {SubscriptionId}",
                @event.UserId, @event.SubscriptionId);
        }
    }

    private IdentityService.Domain.Enums.SubscriptionStatus ConvertSubscriptionStatus(VMSContracts.Subscription.Enums.SubscriptionStatus status)
    {
        // Convert based on the integer value of the enum
        return (int)status switch
        {
            0 => IdentityService.Domain.Enums.SubscriptionStatus.Trial,
            1 => IdentityService.Domain.Enums.SubscriptionStatus.Active,
            2 => IdentityService.Domain.Enums.SubscriptionStatus.GracePeriod,
            3 => IdentityService.Domain.Enums.SubscriptionStatus.SoftLocked,
            4 => IdentityService.Domain.Enums.SubscriptionStatus.HardLocked,
            5 => IdentityService.Domain.Enums.SubscriptionStatus.Cancelled,
            6 => IdentityService.Domain.Enums.SubscriptionStatus.Expired,
            _ => IdentityService.Domain.Enums.SubscriptionStatus.Cancelled
        };
    }
}
