using InventoryManagementService.Domain.Interfaces;
using InventoryManagementService.Infrastructure.Repositories;
using Microsoft.EntityFrameworkCore.Storage;

namespace InventoryManagementService.Infrastructure.Persistence;

public class UnitOfWork : IUnitOfWork
{
    private readonly InventoryDbContext _context;
    private IDbContextTransaction? _transaction;
    private IPartRepository? _partRepository;
    private IInventoryRepository? _inventoryRepository;
    private IStockTransferRepository? _stockTransferRepository;
    private IInventoryAdjustmentRepository? _inventoryAdjustmentRepository;
    private bool _disposed;

    public UnitOfWork(InventoryDbContext context)
    {
        _context = context;
    }

    public IPartRepository Parts => _partRepository ??= new PartRepository(_context);
    public IInventoryRepository Inventory => _inventoryRepository ??= new InventoryRepository(_context);
    public IStockTransferRepository StockTransfers => _stockTransferRepository ??= new StockTransferRepository(_context);
    public IInventoryAdjustmentRepository InventoryAdjustments => _inventoryAdjustmentRepository ??= new InventoryAdjustmentRepository(_context);

    public async Task<int> SaveChangesAsync()
    {
        return await _context.SaveChangesAsync();
    }

    public async Task BeginTransactionAsync()
    {
        _transaction = await _context.Database.BeginTransactionAsync();
    }

    public async Task CommitTransactionAsync()
    {
        if (_transaction != null)
        {
            await _transaction.CommitAsync();
            await _transaction.DisposeAsync();
            _transaction = null;
        }
    }

    public async Task RollbackTransactionAsync()
    {
        if (_transaction != null)
        {
            await _transaction.RollbackAsync();
            await _transaction.DisposeAsync();
            _transaction = null;
        }
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed && disposing)
        {
            _transaction?.Dispose();
            _context.Dispose();
        }
        _disposed = true;
    }
}
