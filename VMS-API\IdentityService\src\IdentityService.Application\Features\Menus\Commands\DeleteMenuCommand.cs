using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using IdentityService.Application.Interfaces;
using IdentityService.Domain.Interfaces;
using MediatR;

namespace IdentityService.Application.Features.Menus.Commands;

public class DeleteMenuCommand : IRequest<Unit>
{
    public Guid MenuId { get; set; }
}

public class DeleteMenuCommandHandler : IRequestHandler<DeleteMenuCommand, Unit>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICurrentUserService _currentUserService;
    private readonly IAuditLogService _auditLogService;

    public DeleteMenuCommandHandler(
        IUnitOfWork unitOfWork,
        ICurrentUserService currentUserService,
        IAuditLogService auditLogService)
    {
        _unitOfWork = unitOfWork;
        _currentUserService = currentUserService;
        _auditLogService = auditLogService;
    }

    public async Task<Unit> Handle(DeleteMenuCommand command, CancellationToken cancellationToken)
    {
        var menu = await _unitOfWork.MenuRepository.GetByIdAsync(command.MenuId);
        if (menu == null)
            throw new InvalidOperationException($"Menu with ID {command.MenuId} not found");
            
        // Check if menu has children
        var allMenus = await _unitOfWork.MenuRepository.GetAllAsync();
        var hasChildren = allMenus.Any(m => m.ParentId == command.MenuId);
        if (hasChildren)
            throw new InvalidOperationException("Cannot delete a menu that has child menus. Delete child menus first.");
            
        // Track old values for audit log
        var oldValues = System.Text.Json.JsonSerializer.Serialize(new { 
            menu.Id, 
            menu.Name, 
            menu.DisplayName, 
            menu.Path, 
            menu.Icon, 
            menu.Order, 
            menu.ParentId 
        });
        
        // Delete menu
        await _unitOfWork.MenuRepository.DeleteAsync(menu);
        await _unitOfWork.SaveChangesAsync(cancellationToken);
        
        // Create audit log
        await _auditLogService.CreateAuditLogAsync(
            "Delete",
            "Menu",
            menu.Id.ToString(),
            oldValues,
            string.Empty,
            "Id,Name,DisplayName,Path,Icon,Order,ParentId",
            _currentUserService.UserId ?? Guid.Empty);
        
        return Unit.Value;
    }
}
