import React, { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { Container, Row, Col } from "reactstrap";
import toast from "react-hot-toast";
import { MainHeading } from "../../../components/common";
import { useCreateUser, useUpdateUser } from "../hooks/useUMSUsers";
import { useGetAllMenus } from "../hooks/useUMSMenus";
import { useGetAllPermissions } from "../hooks/useUMSPermissions";

const MenuPermissionPage = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const createUserMutation = useCreateUser();
  const updateUserMutation = useUpdateUser();


    const { basicInfo, userId, isEditing, userData } = location.state || {};

  // Fetch menus from API
  const { data: menusData, isLoading: menusLoading, error: menusError } = useGetAllMenus();

  // Fetch permissions from API
  const { data: permissionsData, isLoading: permissionsLoading, error: permissionsError } = useGetAllPermissions();
  // Process permissions data - extract name and id from response
  const processPermissions = (permissions) => {
    if (!permissions || !Array.isArray(permissions)) return [];

    return permissions.map(permission => ({
      key: permission.name?.toLowerCase() || 'unknown',
      label: permission.name || 'Unknown',
      id: permission.id,
      originalData: permission 
    }));
  };

  const permissionTypes = processPermissions(permissionsData);

  // State for checkbox-based permissions - {menuId: {view: true, create: false, etc}}
  const [selectedPermissions, setSelectedPermissions] = useState({});

  // Handle checkbox change for permissions
  const handlePermissionChange = (menuId, permissionType, isChecked) => {


    setSelectedPermissions(prev => {
      const updated = {
        ...prev,
        [menuId]: {
          ...prev[menuId],
          [permissionType]: isChecked
        }
      };

      return updated;
    });
  };
  const processMenus = (menus) => {
    if (!menus || !Array.isArray(menus)) return [];

    const flattenMenus = (menuList, result = []) => {
      menuList.forEach(menu => {
        // Add main menu - structure to match userFormik.js expectations
        result.push({
          id: menu.id, // Keep for internal use
          value: menu.id, // For userFormik.js compatibility
          name: menu.displayName || menu.name || 'Unnamed Menu',
          label: menu.displayName || menu.name || 'Unnamed Menu', // For userFormik.js compatibility
          originalMenuData: menu // Keep original menu data for payload
        });

        // Add submenus if they exist
        if (menu.subMenus && Array.isArray(menu.subMenus)) {
          flattenMenus(menu.subMenus, result);
        }
      });
      return result;
    };

    return flattenMenus(menus);
  };

  const menus = processMenus(menusData);

  // Debug: Show structure of first menu and permission for reference
  if (menus.length > 0) {
    console.log("Sample Menu Structure:", menus[0]);
  }
  if (permissionTypes.length > 0) {
    console.log("Sample Permission Structure:", permissionTypes[0]);
  }

  // Create initial state based on available permissions


  // Function to populate existing user permissions
  const populateExistingPermissions = React.useCallback(() => {
    if (!userData || !userData.roles || userData.roles.length === 0) {
      return;
    }

    const userRole = userData.roles[0];
    if (!userRole.menu || userRole.menu.length === 0) {
      return;
    }

    const newSelectedPermissions = {};

    userRole.menu.forEach(menuItem => {
      newSelectedPermissions[menuItem.menuId] = {};

      menuItem.permission.forEach(permission => {
        const permissionKey = permission.name.toLowerCase();
        newSelectedPermissions[menuItem.menuId][permissionKey] = true;
      });
    });

    console.log("Populated permissions:", newSelectedPermissions);
    setSelectedPermissions(newSelectedPermissions);
  }, [userData]);

  // Populate existing permissions when editing
  useEffect(() => {
    if (isEditing && userData && permissionsData && menusData) {
      console.log("Effect triggered - populating existing permissions");
      populateExistingPermissions();
    }
  }, [isEditing, userData, permissionsData, menusData, populateExistingPermissions]);

  const [isSubmitting, setIsSubmitting] = useState(false);

  // Handle cancel/clear functionality
  const handleCancel = () => {
    navigate(-1);
  };





  // Redirect if no basic info
  useEffect(() => {
    if (!basicInfo) {
      toast.error("Basic information is required. Please start from the beginning.");
      navigate('/ums');
    }
  }, [basicInfo, navigate]);



  // Handle form submission
  const handleSubmit = async () => {
    try {
      setIsSubmitting(true);

      // Transform menu permissions to the required API format
      const menuPermissionsArray = [];

      // Group permissions by menu
      const menuPermissionMap = new Map();

      // Transform checkbox selections to API format
      Object.entries(selectedPermissions).forEach(([menuId, permissions]) => {
        const selectedPerms = Object.entries(permissions)
          .filter(([_, isSelected]) => isSelected)
          .map(([permissionType, _]) => {
            const permissionObj = permissionTypes.find(p => p.key === permissionType);
            return {
              permissionId: permissionObj?.id || "00000000-0000-0000-0000-000000000000",
              name: permissionObj?.label || permissionType,
              resource: permissionObj?.originalData?.resource || `${permissionType.toUpperCase()}`
            };
          });

        if (selectedPerms.length > 0) {
          const menu = menus.find(m => m.id === menuId);
          if (menu) {
            menuPermissionsArray.push({
              menuId: menuId,
              menuName: menu.name,
              subMenuId: "00000000-0000-0000-0000-000000000000",
              subMenuName: "Default",
              permission: selectedPerms
            });
          }
        }
      });

      // Convert map to array
      menuPermissionsArray.push(...menuPermissionMap.values());

      // Create the payload in the ORIGINAL required format
      const payload = {
        email: basicInfo.email,
        firstName: basicInfo.firstName,
        lastName: basicInfo.lastName,
        phoneNumber: basicInfo.phoneNumber,
        userType: 3, // AdminUms = 3 (default)
        // vendorId and branchId are not needed as per requirement
        roles: [
          {
            roleId: basicInfo.selectedRole?.value || "00000000-0000-0000-0000-000000000000",
            name: basicInfo.selectedRole?.label || "Default Role",
            menu: menuPermissionsArray
          }
        ]
      };

      

      if (isEditing && userId) {
        await updateUserMutation.mutateAsync({ userId, userData: payload });
        toast.success("User updated successfully!");
      } else {
        await createUserMutation.mutateAsync(payload);
        toast.success("User created successfully!");
      }

      navigate('/ums');
    } catch (error) {
      toast.error(error.message || "Failed to save user. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!basicInfo) {
    return null;
  }

  // Show loading state
  if (menusLoading || permissionsLoading) {
    return (
      <div className="page-with-header">
        <MainHeading
          title={isEditing ? "Edit User Permissions" : "Assign User Permissions"}
          subtitle="Loading menus..."
          showBackButton={true}
          onBackClick={handleCancel}
          showFilter={false}
        />
        <div className="page-content-scrollable">
          <div className="vms-admin-loading-container">
            <div className="vms-admin-loading-center">
              <div className="spinner-border text-primary" role="status">
                <span className="visually-hidden">Loading...</span>
              </div>
              <p className="vms-admin-loading-text">Loading menus...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Show error state
  if (menusError || permissionsError) {
    return (
      <div className="page-with-header">
        <MainHeading
          title={isEditing ? "Edit User Permissions" : "Assign User Permissions"}
          subtitle="Error loading data"
          showBackButton={true}
          onBackClick={handleCancel}
          showFilter={false}
        />
        <div className="page-content-scrollable">
          <div className="vms-admin-loading-container">
            <div className="vms-admin-loading-center">
              <div className="alert alert-danger">
                <h5>Failed to load {menusError ? 'menus' : 'permissions'}</h5>
                <p>Please try refreshing the page or contact support if the problem persists.</p>
                <button
                  className="btn btn-primary"
                  onClick={() => window.location.reload()}
                >
                  Refresh Page
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="page-with-header">
      <MainHeading
        title={isEditing ? "Edit User Permissions" : "Assign User Permissions"}
        subtitle={`Setting permissions for ${basicInfo.firstName} ${basicInfo.lastName}`}
        showBackButton={true}
        onBackClick={handleCancel}
        showFilter={false}
      />

      <div className="page-content-scrollable p-0">
        <Container fluid className="vm-two-column-layout p-0">
          <Row className="h-100 g-3">
            {/* Left Column: User Profile and Permissions Table */}
            <Col md={8} className="vm-left-column">
              <div className="vm-left-column-content">
                {/* User Profile Section */}
                <div className="vm-user-profile-section">
                  <div className="vm-compact-user-info">
                    <div className="vm-user-info-grid">
                      <div className="vm-info-item">
                        <div className="vm-info-label">FIRST NAME</div>
                        <div className="vm-info-value">{basicInfo?.firstName || 'N/A'}</div>
                      </div>
                      <div className="vm-info-item">
                        <div className="vm-info-label">LAST NAME</div>
                        <div className="vm-info-value">{basicInfo?.lastName || 'N/A'}</div>
                      </div>
                      <div className="vm-info-item">
                        <div className="vm-info-label">EMAIL</div>
                        <div className="vm-info-value">{basicInfo?.email || 'N/A'}</div>
                      </div>
                      <div className="vm-info-item">
                        <div className="vm-info-label">PHONE</div>
                        <div className="vm-info-value">{basicInfo?.phoneNumber || 'N/A'}</div>
                      </div>
                      <div className="vm-info-item">
                        <div className="vm-info-label">ROLE</div>
                        <div className="vm-info-value">{basicInfo?.selectedRole?.label || 'N/A'}</div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Permissions Table Section */}
                <div className="vm-permissions-table-section">
                  <div className="vm-section-card">
                    <div className="vm-section-header sticky-top">
                      <h6>Assign Permissions</h6>
                      <p>Check boxes to assign permissions to menus</p>
                    </div>
                    <div className="vm-permission-table-container">
                      <div className="vm-permission-table p-2">
                        <div className="vm-permission-table-header sticky-top">
                          <div className="vm-permission-menu-col">Menu</div>
                          {permissionTypes.map((permission) => (
                            <div key={permission.key} className="vm-permission-col">
                              {permission.label}
                            </div>
                          ))}
                        </div>
                        <div className="vm-table-body">
                          {menus.map((menu) => (
                            <div key={menu.id} className="vm-permission-table-row">
                              <div className="vm-menu-name">{menu.name}</div>
                              {permissionTypes.map((permission) => (
                                <div key={permission.key} className="vm-checkbox-cell">
                                  <input
                                    type="checkbox"
                                    id={`${menu.id}-${permission.key}`}
                                    checked={selectedPermissions[menu.id]?.[permission.key] || false}
                                    onChange={(e) => handlePermissionChange(menu.id, permission.key, e.target.checked)}
                                    className="vm-permission-checkbox"
                                  />
                                </div>
                              ))}
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </Col>

            {/* Right Column: Update Actions and Permission Hierarchy */}
            <Col md={4} className="vm-right-column">
              <div className="vm-right-column-content">
               

                {/* Permission Hierarchy Section */}
                <div className="vm-hierarchy-section">
                  <div className="vm-section-card">
                    <div className="vm-section-header d-flex justify-content-between align-items-center">
                      <div>
                        <h6 className="mb-0">Permission Hierarchy</h6>
                        <p className="mb-0 text-muted small">Current selections</p>
                      </div>
                       <button
                        type="button"
                        onClick={handleSubmit}
                        disabled={isSubmitting}
                        className="btn btn-dark"
                      >
                        {isSubmitting ? "Saving..." : (isEditing ? "Update" : "Create")}
                      </button>
                    </div>
                    <div className="vm-hierarchy-display-simple">
                      {(() => {
                        const currentMenus = [];

                        // Map the actual permission keys to display order (matching API order)
                        const permissionMapping = {
                          'create': { order: 1, icon: '➕', color: '#1f2937', display: 'Create' },
                          'update': { order: 2, icon: '✏️', color: '#1e293b', display: 'Update' },
                          'view': { order: 3, icon: '👁️', color: '#374151', display: 'View' },
                          'delete': { order: 4, icon: '🗑️', color: '#4b5563', display: 'Delete' }
                        };

                        Object.entries(selectedPermissions).forEach(([menuId, permissions]) => {
                          console.log("🏗️ Processing hierarchy for menu:", menuId, "permissions:", permissions);

                          const selectedPerms = Object.entries(permissions)
                            .filter(([_, isSelected]) => {
                              console.log("🔍 Checking permission:", _, "isSelected:", isSelected);
                              return isSelected === true;
                            })
                            .map(([permKey, _]) => {
                              console.log("🎯 Mapping permission key:", permKey);
                              const mapping = permissionMapping[permKey.toLowerCase()];
                              const result = {
                                name: mapping ? mapping.display : permKey.charAt(0).toUpperCase() + permKey.slice(1),
                                type: permKey.toLowerCase(),
                                order: mapping ? mapping.order : 999,
                                icon: mapping ? mapping.icon : '⚙️',
                                color: mapping ? mapping.color : '#6b7280'
                              };
                              console.log("🎯 Mapped to:", result);
                              return result;
                            })
                            .sort((a, b) => a.order - b.order); // Sort by order

                          console.log("✅ Final selected permissions for menu:", menuId, selectedPerms);

                          if (selectedPerms.length > 0) {
                            const menu = menus.find(m => m.id === menuId);
                            if (menu) {
                              currentMenus.unshift({  // Use unshift to add at the beginning
                                menuName: menu.name,
                                permission: selectedPerms
                              });
                            }
                          }
                        });

                        return (
                          <div className="vm-hierarchy-content">
                            {currentMenus.length > 0 ? (
                              <div className="vm-tree-hierarchy">
                                {currentMenus.map((menuItem, index) => (
                                  <div key={index} className="vm-tree-node">
                                    <div className="vm-tree-menu">
                                      <span className="vm-tree-toggle">▼</span>
                                      <span className="vm-tree-menu-name">{menuItem.menuName}</span>
                                    </div>
                                    <div className="vm-tree-permissions">
                                      {menuItem.permission.map((perm, permIndex) => (
                                        <div key={permIndex} className="vm-tree-permission">
                                          <span className="vm-tree-line"></span>
                                          <span className="vm-tree-bullet">●</span>
                                          <span className="vm-tree-permission-text" style={{ color: perm.color }}>
                                            {perm.name}
                                          </span>
                                        </div>
                                      ))}
                                    </div>
                                  </div>
                                ))}
                              </div>
                            ) : (
                              <div className="vm-empty-hierarchy-simple">
                                <p>No permissions selected</p>
                              </div>
                            )}
                          </div>
                        );
                      })()}
                    </div>
                  </div>
                </div>
              </div>
            </Col>
          </Row>
        </Container>
      </div>
    </div>
  );
};

export default MenuPermissionPage;
