using System.ComponentModel.DataAnnotations;
using IdentityService.Application.DTOs.Users;
using IdentityService.Application.Interfaces;
using IdentityService.Domain.Enums;
using IdentityService.Domain.Interfaces;
using IdentityService.Domain.Services;
using MediatR;
using Microsoft.Extensions.Logging;

namespace IdentityService.Application.Features.Users.Commands;

/// <summary>
/// Command to assign a permission directly to a user
/// </summary>
public class AssignUserPermissionCommand : IRequest<UserPermissionResponse>
{
    public Guid UserId { get; set; }
    public AssignUserPermissionRequest Request { get; set; } = new();
}

/// <summary>
/// Handler for assigning user permissions
/// </summary>
public class AssignUserPermissionCommandHandler : IRequestHandler<AssignUserPermissionCommand, UserPermissionResponse>
{
    private readonly IPermissionService _permissionService;
    private readonly ICurrentUserService _currentUserService;
    private readonly ILogger<AssignUserPermissionCommandHandler> _logger;

    public AssignUserPermissionCommandHandler(
        IPermissionService permissionService,
        ICurrentUserService currentUserService,
        ILogger<AssignUserPermissionCommandHandler> logger)
    {
        _permissionService = permissionService;
        _currentUserService = currentUserService;
        _logger = logger;
    }

    public async Task<UserPermissionResponse> Handle(AssignUserPermissionCommand command, CancellationToken cancellationToken)
    {
        try
        {
            var request = command.Request;
            var assignedBy = _currentUserService.UserId.ToString() ?? "System";

            // Validate expiration date
            if (request.ExpiresAt.HasValue && request.ExpiresAt.Value <= DateTime.UtcNow)
            {
                throw new ArgumentException("Expiration date must be in the future");
            }

            // Assign the permission
            await _permissionService.AssignUserPermissionAsync(
                command.UserId,
                request.PermissionId,
                request.Type,
                request.ExpiresAt,
                request.Reason,
                assignedBy);

            // Get the assigned permission details
            var userPermissions = await _permissionService.GetUserSpecificPermissionsAsync(command.UserId);
            var assignedPermission = userPermissions
                .FirstOrDefault(up => up.PermissionId == request.PermissionId);

            if (assignedPermission == null)
            {
                throw new InvalidOperationException("Failed to retrieve assigned permission");
            }

            _logger.LogInformation("Assigned {Type} permission {PermissionId} to user {UserId} by {AssignedBy}",
                request.Type, request.PermissionId, command.UserId, assignedBy);

            return new UserPermissionResponse
            {
                Id = assignedPermission.Id,
                UserId = assignedPermission.UserId,
                PermissionId = assignedPermission.PermissionId,
                PermissionName = assignedPermission.Permission.Name,
                Type = assignedPermission.Type,
                ExpiresAt = assignedPermission.ExpiresAt,
                Reason = assignedPermission.Reason,
                IsActive = assignedPermission.IsCurrentlyActive(),
                CreatedAt = assignedPermission.CreatedAt,
                CreatedBy = assignedPermission.CreatedBy,
                UpdatedAt = assignedPermission.UpdatedAt,
                UpdatedBy = assignedPermission.UpdatedBy
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assigning permission {PermissionId} to user {UserId}",
                command.Request.PermissionId, command.UserId);
            throw;
        }
    }
}
