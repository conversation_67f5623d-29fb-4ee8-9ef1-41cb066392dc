using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using IdentityService.Application.DTOs.Users;
using IdentityService.Application.Features.Users.Queries;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace IdentityService.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class LoginHistoryController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<LoginHistoryController> _logger;

    public LoginHistoryController(
        IMediator mediator,
        ILogger<LoginHistoryController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    [HttpGet]
    public async Task<ActionResult<List<UserLoginHistoryResponse>>> GetLoginHistory(
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        [FromQuery] bool? isSuccessful = null,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10)
    {
        try
        {
            var query = new GetUserLoginHistoryQuery
            {
                FromDate = fromDate,
                ToDate = toDate,
                IsSuccessful = isSuccessful,
                PageNumber = pageNumber,
                PageSize = pageSize
            };

            var loginHistory = await _mediator.Send(query);
            return Ok(loginHistory);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation during login history retrieval");
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving login history");
            return StatusCode(500, new { message = "An error occurred while retrieving login history" });
        }
    }

    [HttpGet("user/{userId}")]
    [Authorize(Policy = "Users.View")]
    public async Task<ActionResult<List<UserLoginHistoryResponse>>> GetLoginHistoryForUser(
        Guid userId,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        [FromQuery] bool? isSuccessful = null,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10)
    {
        try
        {
            var query = new GetUserLoginHistoryQuery
            {
                UserId = userId,
                FromDate = fromDate,
                ToDate = toDate,
                IsSuccessful = isSuccessful,
                PageNumber = pageNumber,
                PageSize = pageSize
            };

            var loginHistory = await _mediator.Send(query);
            return Ok(loginHistory);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation during login history retrieval");
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving login history");
            return StatusCode(500, new { message = "An error occurred while retrieving login history" });
        }
    }
}
