-- Fix migration state for Identity Service
-- This script manually marks problematic migrations as completed and ensures proper database state

-- Step 1: Mark the problematic migration as completed if it's not already
DO $$
BEGIN
    -- Check if the migration is already marked as completed
    IF NOT EXISTS (
        SELECT 1 FROM "__EFMigrationsHistory" 
        WHERE "MigrationId" = '20250606075256_IdentityInitialCreate'
    ) THEN
        INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
        VALUES ('20250606075256_IdentityInitialCreate', '8.0.0');
        RAISE NOTICE 'Marked IdentityInitialCreate migration as completed';
    ELSE
        RAISE NOTICE 'IdentityInitialCreate migration already marked as completed';
    END IF;
END $$;

-- Step 2: Ensure all required tables exist
DO $$
BEGIN
    -- Ensure Vendor table exists
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.tables 
        WHERE table_name = 'Vendor'
    ) THEN
        CREATE TABLE "Vendor" (
            "Id" uuid NOT NULL,
            "Name" text NOT NULL,
            "Email" text NOT NULL,
            "PhoneNumber" text NOT NULL,
            "IsActive" boolean NOT NULL,
            "CreatedAt" timestamp with time zone NOT NULL,
            "UpdatedAt" timestamp with time zone NULL,
            "CreatedBy" text NOT NULL,
            "UpdatedBy" text NULL,
            "IsDeleted" boolean NOT NULL,
            "RowVersion" bytea NULL,
            CONSTRAINT "PK_Vendor" PRIMARY KEY ("Id")
        );
        RAISE NOTICE 'Created Vendor table';
    ELSE
        RAISE NOTICE 'Vendor table already exists';
    END IF;

    -- Ensure UserPermission table exists
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.tables 
        WHERE table_name = 'UserPermission'
    ) THEN
        CREATE TABLE "UserPermission" (
            "Id" uuid NOT NULL,
            "UserId" uuid NOT NULL,
            "PermissionId" uuid NOT NULL,
            "Type" integer NOT NULL,
            "ExpiresAt" timestamp with time zone NULL,
            "Reason" text NULL,
            "IsActive" boolean NOT NULL,
            "CreatedAt" timestamp with time zone NOT NULL,
            "UpdatedAt" timestamp with time zone NULL,
            "CreatedBy" text NOT NULL,
            "UpdatedBy" text NULL,
            "IsDeleted" boolean NOT NULL,
            "RowVersion" bytea NULL,
            CONSTRAINT "PK_UserPermission" PRIMARY KEY ("Id"),
            CONSTRAINT "FK_UserPermission_Permissions_PermissionId" FOREIGN KEY ("PermissionId") REFERENCES "Permissions" ("Id") ON DELETE CASCADE,
            CONSTRAINT "FK_UserPermission_Users_UserId" FOREIGN KEY ("UserId") REFERENCES "Users" ("Id") ON DELETE CASCADE
        );
        RAISE NOTICE 'Created UserPermission table';
    ELSE
        RAISE NOTICE 'UserPermission table already exists';
    END IF;
END $$;

-- Step 3: Ensure all required columns exist
DO $$
BEGIN
    -- Ensure PrimaryBranchId column exists
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'Users' 
        AND column_name = 'PrimaryBranchId'
    ) THEN
        ALTER TABLE "Users" ADD COLUMN "PrimaryBranchId" uuid NULL;
        RAISE NOTICE 'Added PrimaryBranchId column to Users table';
    ELSE
        RAISE NOTICE 'PrimaryBranchId column already exists in Users table';
    END IF;

    -- Ensure VendorId column exists
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'Users' 
        AND column_name = 'VendorId'
    ) THEN
        ALTER TABLE "Users" ADD COLUMN "VendorId" uuid NULL;
        RAISE NOTICE 'Added VendorId column to Users table';
    ELSE
        RAISE NOTICE 'VendorId column already exists in Users table';
    END IF;
END $$;

-- Step 4: Ensure all required indexes exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'IX_Users_VendorId') THEN
        CREATE INDEX "IX_Users_VendorId" ON "Users" ("VendorId");
        RAISE NOTICE 'Created index IX_Users_VendorId';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'IX_UserSessions_LastActiveAt') THEN
        CREATE INDEX "IX_UserSessions_LastActiveAt" ON "UserSessions" ("LastActiveAt");
        RAISE NOTICE 'Created index IX_UserSessions_LastActiveAt';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'IX_UserSessions_Status_ExpiresAt') THEN
        CREATE INDEX "IX_UserSessions_Status_ExpiresAt" ON "UserSessions" ("Status", "ExpiresAt");
        RAISE NOTICE 'Created index IX_UserSessions_Status_ExpiresAt';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'IX_UserSessions_Status_LastActiveAt_ExpiresAt') THEN
        CREATE INDEX "IX_UserSessions_Status_LastActiveAt_ExpiresAt" ON "UserSessions" ("Status", "LastActiveAt", "ExpiresAt");
        RAISE NOTICE 'Created index IX_UserSessions_Status_LastActiveAt_ExpiresAt';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'IX_UserSessions_UserId_Status_ExpiresAt') THEN
        CREATE INDEX "IX_UserSessions_UserId_Status_ExpiresAt" ON "UserSessions" ("UserId", "Status", "ExpiresAt");
        RAISE NOTICE 'Created index IX_UserSessions_UserId_Status_ExpiresAt';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'IX_UserPermission_PermissionId') THEN
        CREATE INDEX "IX_UserPermission_PermissionId" ON "UserPermission" ("PermissionId");
        RAISE NOTICE 'Created index IX_UserPermission_PermissionId';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'IX_UserPermission_UserId') THEN
        CREATE INDEX "IX_UserPermission_UserId" ON "UserPermission" ("UserId");
        RAISE NOTICE 'Created index IX_UserPermission_UserId';
    END IF;
END $$;

-- Step 5: Ensure foreign key constraints exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.table_constraints 
        WHERE constraint_name = 'FK_Users_Vendor_VendorId'
    ) THEN
        ALTER TABLE "Users" ADD CONSTRAINT "FK_Users_Vendor_VendorId" 
        FOREIGN KEY ("VendorId") REFERENCES "Vendor" ("Id");
        RAISE NOTICE 'Added foreign key constraint FK_Users_Vendor_VendorId';
    ELSE
        RAISE NOTICE 'Foreign key constraint FK_Users_Vendor_VendorId already exists';
    END IF;
END $$;

-- Step 6: Show current migration status
SELECT "MigrationId", "ProductVersion" 
FROM "__EFMigrationsHistory" 
ORDER BY "MigrationId";

RAISE NOTICE 'Migration state fix completed successfully!';
