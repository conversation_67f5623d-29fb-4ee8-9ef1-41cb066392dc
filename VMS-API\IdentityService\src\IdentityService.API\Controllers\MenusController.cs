using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using IdentityService.Application.DTOs.Menus;
using IdentityService.Application.Features.Menus.Commands;
using IdentityService.Application.Features.Menus.Queries;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace IdentityService.API.Controllers;

[ApiController]
[Route("api/[controller]")]
//[Authorize]
public class MenusController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<MenusController> _logger;

    public MenusController(IMediator mediator, ILogger<MenusController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    [HttpGet]
    //[Authorize(Policy = "Menus.View")]
    public async Task<ActionResult<List<MenuResponse>>> GetMenus()
    {
        try
        {
            var query = new GetMenusQuery();
            var menus = await _mediator.Send(query);
            return Ok(menus);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving menus");
            return StatusCode(500, new { message = "An error occurred while retrieving menus" });
        }
    }

    [HttpGet("{id}")]
    //[Authorize(Policy = "Menus.View")]
    public async Task<ActionResult<MenuResponse>> GetMenuById(Guid id)
    {
        try
        {
            var query = new GetMenuByIdQuery { MenuId = id };
            var menu = await _mediator.Send(query);
            return Ok(menu);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Menu not found");
            return NotFound(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving menu");
            return StatusCode(500, new { message = "An error occurred while retrieving the menu" });
        }
    }

    [HttpGet("hierarchy")]
    //[Authorize(Policy = "Menus.View")]
    public async Task<ActionResult<List<MenuResponse>>> GetMenuHierarchy()
    {
        try
        {
            var query = new GetMenuHierarchyQuery();
            var menus = await _mediator.Send(query);
            return Ok(menus);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving menu hierarchy");
            return StatusCode(500, new { message = "An error occurred while retrieving menu hierarchy" });
        }
    }

    [HttpPost]
    //[Authorize(Policy = "Menus.Create")]
    public async Task<ActionResult<MenuResponse>> CreateMenu([FromBody] CreateMenuRequest request)
    {
        try
        {
            var command = new CreateMenuCommand { Request = request };
            var menu = await _mediator.Send(command);
            return CreatedAtAction(nameof(GetMenuById), new { id = menu.Id }, menu);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation during menu creation");
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating menu");
            return StatusCode(500, new { message = "An error occurred while creating the menu" });
        }
    }

    [HttpPut("{id}")]
   // [Authorize(Policy = "Menus.Update")]
    public async Task<ActionResult<MenuResponse>> UpdateMenu(Guid id, [FromBody] UpdateMenuRequest request)
    {
        try
        {
            var command = new UpdateMenuCommand { MenuId = id, Request = request };
            var menu = await _mediator.Send(command);
            return Ok(menu);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation during menu update");
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating menu");
            return StatusCode(500, new { message = "An error occurred while updating the menu" });
        }
    }

    [HttpDelete("{id}")]
    //[Authorize(Policy = "Menus.Delete")]
    public async Task<ActionResult> DeleteMenu(Guid id)
    {
        try
        {
            var command = new DeleteMenuCommand { MenuId = id };
            await _mediator.Send(command);
            return NoContent();
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation during menu deletion");
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting menu");
            return StatusCode(500, new { message = "An error occurred while deleting the menu" });
        }
    }

    [HttpPost("{id}/permissions")]
    //[Authorize(Policy = "Menus.Update")]
    public async Task<ActionResult<MenuResponse>> AssignMenuPermissions(Guid id, [FromBody] AssignMenuPermissionRequest request)
    {
        try
        {
            var command = new AssignMenuPermissionCommand { MenuId = id, Request = request };
            var menu = await _mediator.Send(command);
            return Ok(menu);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation during menu permission assignment");
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assigning menu permissions");
            return StatusCode(500, new { message = "An error occurred while assigning menu permissions" });
        }
    }

    /// <summary>
    /// Get user-specific menu structure
    /// </summary>
    /// <returns>Menu structure based on user permissions</returns>
    [HttpGet("user-menu")]
    public async Task<ActionResult<List<MenuItemResponse>>> GetUserMenu()
    {
        try
        {
            _logger.LogInformation("Getting user menu");

            var query = new GetUserMenuQuery();
            var result = await _mediator.Send(query);

            _logger.LogInformation("Successfully retrieved user menu with {Count} items", result.Count);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user menu");
            return StatusCode(500, new { message = "An error occurred while retrieving user menu" });
        }
    }
}
