using System;
using System.Threading;
using System.Threading.Tasks;
using IdentityService.Application.Common;
using IdentityService.Application.DTOs.Users;
using IdentityService.Application.Interfaces;
using IdentityService.Domain.Interfaces;
using MediatR;

namespace IdentityService.Application.Features.Users.Commands;

public class UpdateUserCommand : BaseRequest<UserResponse>
{
    // This UserId is the target user's ID, not the current user's ID
    public new Guid UserId { get; set; }
    public UpdateUserRequest Request { get; set; }
}

public class UpdateUserCommandHandler : IRequestHandler<UpdateUserCommand, UserResponse>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICurrentUserService _currentUserService;
    private readonly IAuditLogService _auditLogService;

    public UpdateUserCommandHandler(
        IUnitOfWork unitOfWork,
        ICurrentUserService currentUserService,
        IAuditLogService auditLogService)
    {
        _unitOfWork = unitOfWork;
        _currentUserService = currentUserService;
        _auditLogService = auditLogService;
    }

    public async Task<UserResponse> Handle(UpdateUserCommand command, CancellationToken cancellationToken)
    {
        var user = await _unitOfWork.UserRepository.GetByIdAsync(command.UserId);
        if (user == null)
            throw new InvalidOperationException($"User with ID {command.UserId} not found");

        var request = command.Request;
        var updatedBy = _currentUserService.UserId.ToString() ?? "System";

        // Track changes for audit log
        var oldValues = System.Text.Json.JsonSerializer.Serialize(new
        {
            user.Email,
            user.PhoneNumber,
            IsActive = !user.IsDeleted
        });

        var affectedColumns = new List<string>();

        // Update email if provided and different
        if (!string.IsNullOrEmpty(request.Email) && request.Email != user.Email)
        {
            // Check if email is already in use
            var emailExists = await _unitOfWork.UserRepository.ExistsByEmailAsync(request.Email);
            if (emailExists)
                throw new InvalidOperationException($"Email {request.Email} is already in use");

            user.UpdateEmail(request.Email, updatedBy);
            affectedColumns.Add("Email");
        }

        // Update phone number if provided and different
        if (!string.IsNullOrEmpty(request.PhoneNumber) && request.PhoneNumber != user.PhoneNumber)
        {
            // Check if phone number is already in use
            var phoneExists = await _unitOfWork.UserRepository.ExistsByPhoneNumberAsync(request.PhoneNumber);
            if (phoneExists)
                throw new InvalidOperationException($"Phone number {request.PhoneNumber} is already in use");

            user.UpdatePhoneNumber(request.PhoneNumber, updatedBy);
            affectedColumns.Add("PhoneNumber");
        }

        // Update active status if provided
        if (request.IsActive.HasValue)
        {
            if (request.IsActive.Value && user.IsDeleted)
            {
                user.Restore(updatedBy);
                affectedColumns.Add("IsDeleted");
            }
            else if (!request.IsActive.Value && !user.IsDeleted)
            {
                user.Delete(updatedBy);
                affectedColumns.Add("IsDeleted");
            }
        }

        // Save changes
        await _unitOfWork.UserRepository.UpdateAsync(user);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        // Create audit log
        var newValues = System.Text.Json.JsonSerializer.Serialize(new
        {
            user.Email,
            user.PhoneNumber,
            IsActive = !user.IsDeleted
        });

        await _auditLogService.CreateAuditLogAsync(
            "Update",
            "User",
            user.Id.ToString(),
            oldValues,
            newValues,
            string.Join(",", affectedColumns),
            _currentUserService.UserId ?? Guid.Empty);

        // Get user roles for response
        var roles = await _unitOfWork.UserRepository.GetUserRolesAsync(user.Id);

        // Create response
        var response = new UserResponse
        {
            Id = user.Id,
            Email = user.Email,
            PhoneNumber = user.PhoneNumber,
            EmailVerified = user.EmailVerified,
            PhoneNumberVerified = user.PhoneNumberVerified,
            IsActive = !user.IsDeleted,
            LastLoginAt = user.LastLoginAt,
            CreatedAt = user.CreatedAt,
            CreatedBy = user.CreatedBy,
            UpdatedAt = user.UpdatedAt,
            UpdatedBy = user.UpdatedBy,
            Roles = new List<UserRoleResponse>()
        };

        return response;
    }
}
