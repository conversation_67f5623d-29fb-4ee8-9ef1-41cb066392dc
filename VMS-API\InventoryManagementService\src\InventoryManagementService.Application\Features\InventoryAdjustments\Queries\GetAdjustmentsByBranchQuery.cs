using AutoMapper;
using InventoryManagementService.Application.DTOs;
using InventoryManagementService.Domain.Interfaces;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using VMS.Contracts.Common.Authorization;

namespace InventoryManagementService.Application.Features.InventoryAdjustments.Queries;

public class GetAdjustmentsByBranchQuery : IRequest<List<InventoryAdjustmentDto>>
{
    public Guid BranchId { get; set; }
}

public class GetAdjustmentsByBranchQueryHandler : IRequestHandler<GetAdjustmentsByBranchQuery, List<InventoryAdjustmentDto>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly ILogger<GetAdjustmentsByBranchQueryHandler> _logger;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public GetAdjustmentsByBranchQueryHandler(
        IUnitOfWork unitOfWork,
        IMapper mapper,
        ILogger<GetAdjustmentsByBranchQueryHandler> logger,
        IHttpContextAccessor httpContextAccessor)
    {
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _logger = logger;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<List<InventoryAdjustmentDto>> Handle(GetAdjustmentsByBranchQuery request, CancellationToken cancellationToken)
    {
        // Check permissions - Admin roles bypass, others need explicit permission
        var user = _httpContextAccessor.HttpContext?.User;
        if (!PermissionHelper.IsAdmin(user))
        {
            PermissionHelper.ValidateAccess(user, "Adjustments.View");
        }

        _logger.LogInformation("Getting inventory adjustments for branch: {BranchId}", request.BranchId);

        var adjustments = await _unitOfWork.InventoryAdjustments.GetByBranchAsync(request.BranchId);

        _logger.LogInformation("Found {Count} inventory adjustments for branch {BranchId}", adjustments.Count, request.BranchId);

        return _mapper.Map<List<InventoryAdjustmentDto>>(adjustments);
    }
}
