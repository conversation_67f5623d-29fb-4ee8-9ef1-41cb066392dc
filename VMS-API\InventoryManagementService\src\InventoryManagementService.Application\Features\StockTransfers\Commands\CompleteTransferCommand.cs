using FluentValidation;
using InventoryManagementService.Application.DTOs;
using InventoryManagementService.Domain.Interfaces;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using VMS.Contracts.Common.Authorization;

namespace InventoryManagementService.Application.Features.StockTransfers.Commands;

public class CompleteTransferCommand : IRequest
{
    public Guid TransferId { get; set; }
    public List<CompleteTransferItemDto> Items { get; set; } = new();
    public string ReceivedBy { get; set; } = string.Empty;
}

public class CompleteTransferCommandValidator : AbstractValidator<CompleteTransferCommand>
{
    public CompleteTransferCommandValidator()
    {
        RuleFor(x => x.TransferId)
            .NotEmpty().WithMessage("Transfer ID is required");

        RuleFor(x => x.Items)
            .NotEmpty().WithMessage("At least one item is required");

        RuleForEach(x => x.Items).ChildRules(item =>
        {
            item.RuleFor(x => x.ItemId)
                .NotEmpty().WithMessage("Item ID is required");

            item.RuleFor(x => x.ReceivedQuantity)
                .GreaterThanOrEqualTo(0).WithMessage("Received quantity cannot be negative");
        });

        RuleFor(x => x.ReceivedBy)
            .NotEmpty().WithMessage("ReceivedBy is required");
    }
}

public class CompleteTransferCommandHandler : IRequestHandler<CompleteTransferCommand>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<CompleteTransferCommandHandler> _logger;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly IEventPublisher _eventPublisher;

    public CompleteTransferCommandHandler(
        IUnitOfWork unitOfWork,
        ILogger<CompleteTransferCommandHandler> logger,
        IHttpContextAccessor httpContextAccessor,
        IEventPublisher eventPublisher)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
        _httpContextAccessor = httpContextAccessor;
        _eventPublisher = eventPublisher;
    }

    public async Task Handle(CompleteTransferCommand request, CancellationToken cancellationToken)
    {
        // Check permissions - Admin roles bypass, others need explicit permission
        var user = _httpContextAccessor.HttpContext?.User;
        if (!PermissionHelper.IsAdmin(user))
        {
            PermissionHelper.ValidateAccess(user, "Transfers.Update");
        }

        _logger.LogInformation("Completing transfer {TransferId}", request.TransferId);

        await _unitOfWork.BeginTransactionAsync();

        try
        {
            var transfer = await _unitOfWork.StockTransfers.GetByIdAsync(request.TransferId);

            if (transfer == null)
            {
                throw new KeyNotFoundException($"Stock transfer with ID {request.TransferId} not found");
            }

            // Update received quantities for each item
            foreach (var itemDto in request.Items)
            {
                var transferItem = transfer.Items.FirstOrDefault(i => i.Id == itemDto.ItemId);
                if (transferItem == null)
                {
                    throw new KeyNotFoundException($"Transfer item with ID {itemDto.ItemId} not found");
                }

                if (itemDto.ReceivedQuantity > (transferItem.ShippedQuantity ?? transferItem.RequestedQuantity))
                {
                    var maxQuantity = transferItem.ShippedQuantity ?? transferItem.RequestedQuantity;
                    throw new InvalidOperationException($"Received quantity ({itemDto.ReceivedQuantity}) cannot exceed shipped quantity ({maxQuantity})");
                }

                transferItem.SetReceivedQuantity(itemDto.ReceivedQuantity, request.ReceivedBy);

                // Add stock to destination branch
                if (itemDto.ReceivedQuantity > 0)
                {
                    var inventoryItem = await _unitOfWork.Inventory.GetByPartAndBranchAsync(transferItem.PartId, transfer.DestinationBranchId);
                    if (inventoryItem != null)
                    {
                        inventoryItem.AddStock(itemDto.ReceivedQuantity, request.ReceivedBy, $"Transfer from branch {transfer.SourceBranchId} - {transfer.TransferNumber}");
                    }
                    else
                    {
                        // If inventory item doesn't exist at destination, we might need to create it
                        // For now, we'll log a warning - this should be handled based on business rules
                        _logger.LogWarning("Inventory item for part {PartId} not found at destination branch {BranchId}", 
                            transferItem.PartId, transfer.DestinationBranchId);
                    }
                }
            }

            transfer.Complete(request.ReceivedBy);

            await _unitOfWork.SaveChangesAsync();
            await _unitOfWork.CommitTransactionAsync();

            _logger.LogInformation("Transfer {TransferId} completed successfully", transfer.Id);

            // Publish event
            await _eventPublisher.PublishTransferCompletedEvent(
                transfer.Id,
                transfer.TransferNumber,
                transfer.ReceivedDate!.Value);
        }
        catch
        {
            await _unitOfWork.RollbackTransactionAsync();
            throw;
        }
    }
}
