using InventoryManagementService.Domain.Enums;

namespace InventoryManagementService.Application.DTOs;

public class StockTransferDto
{
    public Guid Id { get; set; }
    public Guid VendorId { get; set; }
    public Guid SourceBranchId { get; set; }
    public Guid DestinationBranchId { get; set; }
    public string TransferNumber { get; set; } = string.Empty;
    public TransferStatus Status { get; set; }
    public DateTime RequestedDate { get; set; }
    public DateTime? ShippedDate { get; set; }
    public DateTime? ReceivedDate { get; set; }
    public string? Notes { get; set; }
    public string? ShippedBy { get; set; }
    public string? ReceivedBy { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public DateTime? UpdatedAt { get; set; }
    public string? UpdatedBy { get; set; }
    
    public List<StockTransferItemDto> Items { get; set; } = new();
}

public class StockTransferItemDto
{
    public Guid Id { get; set; }
    public Guid StockTransferId { get; set; }
    public Guid PartId { get; set; }
    public int RequestedQuantity { get; set; }
    public int? ShippedQuantity { get; set; }
    public int? ReceivedQuantity { get; set; }
    public string? Notes { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    
    public PartDto? Part { get; set; }
}

public class CreateStockTransferDto
{
    public Guid VendorId { get; set; }
    public Guid SourceBranchId { get; set; }
    public Guid DestinationBranchId { get; set; }
    public string? Notes { get; set; }
}

public class AddTransferItemDto
{
    public Guid PartId { get; set; }
    public int Quantity { get; set; }
}

public class UpdateTransferNotesDto
{
    public string Notes { get; set; } = string.Empty;
}

public class ShipTransferDto
{
    public List<ShipTransferItemDto> Items { get; set; } = new();
}

public class ShipTransferItemDto
{
    public Guid ItemId { get; set; }
    public int ShippedQuantity { get; set; }
}

public class CompleteTransferDto
{
    public List<CompleteTransferItemDto> Items { get; set; } = new();
}

public class CompleteTransferItemDto
{
    public Guid ItemId { get; set; }
    public int ReceivedQuantity { get; set; }
}

public class CancelTransferDto
{
    public string Reason { get; set; } = string.Empty;
}
