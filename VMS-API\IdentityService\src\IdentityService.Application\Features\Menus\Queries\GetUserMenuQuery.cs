using IdentityService.Application.Interfaces;
using IdentityService.Domain.Interfaces;
using IdentityService.Domain.Services;
using MediatR;
using Microsoft.Extensions.Logging;

namespace IdentityService.Application.Features.Menus.Queries;

/// <summary>
/// Query to get user-specific menu structure
/// </summary>
public class GetUserMenuQuery : IRequest<List<MenuItemResponse>>
{
    public Guid? UserId { get; set; } // If null, uses current user
}

/// <summary>
/// Response model for menu items
/// </summary>
public class MenuItemResponse
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Path { get; set; } = string.Empty;
    public string Icon { get; set; } = string.Empty;
    public int Order { get; set; }
    public Guid? ParentId { get; set; }
    public bool IsVisible { get; set; }
    public bool HasAccess { get; set; }
    public List<MenuItemResponse> Children { get; set; } = new();
    public List<string> RequiredPermissions { get; set; } = new();
}

/// <summary>
/// Handler for getting user menu
/// </summary>
public class GetUserMenuQueryHandler : IRequestHandler<GetUserMenuQuery, List<MenuItemResponse>>
{
    private readonly IMenuService _menuService;
    private readonly ICurrentUserService _currentUserService;
    private readonly ILogger<GetUserMenuQueryHandler> _logger;

    public GetUserMenuQueryHandler(
        IMenuService menuService,
        ICurrentUserService currentUserService,
        ILogger<GetUserMenuQueryHandler> logger)
    {
        _menuService = menuService;
        _currentUserService = currentUserService;
        _logger = logger;
    }

    public async Task<List<MenuItemResponse>> Handle(GetUserMenuQuery query, CancellationToken cancellationToken)
    {
        try
        {
            var userId = query.UserId ?? _currentUserService.UserId ?? throw new UnauthorizedAccessException("User not authenticated");

            var menuItems = await _menuService.GetUserMenuAsync(userId);

            var response = menuItems.Select(MapToResponse).ToList();

            _logger.LogDebug("Retrieved {Count} menu items for user {UserId}", response.Count, userId);

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving user menu for user {UserId}", query.UserId);
            throw;
        }
    }

    private static MenuItemResponse MapToResponse(MenuItemDto menuItem)
    {
        return new MenuItemResponse
        {
            Id = menuItem.Id,
            Name = menuItem.Name,
            DisplayName = menuItem.DisplayName,
            Path = menuItem.Path,
            Icon = menuItem.Icon,
            Order = menuItem.Order,
            ParentId = menuItem.ParentId,
            IsVisible = menuItem.IsVisible,
            HasAccess = menuItem.HasAccess,
            RequiredPermissions = menuItem.RequiredPermissions,
            Children = menuItem.Children.Select(MapToResponse).ToList()
        };
    }
}
