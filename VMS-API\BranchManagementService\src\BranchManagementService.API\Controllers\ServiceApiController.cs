using BranchManagementService.Application.DTOs;
using BranchManagementService.Application.Features.Branches.Queries;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BranchManagementService.API.Controllers
{
    [ApiController]
    [Route("api/service")]
    [EnableCors("AllowAll")]
    [Authorize]
    public class ServiceApiController : ControllerBase
    {
        private readonly IMediator _mediator;
        private readonly ILogger<ServiceApiController> _logger;

        public ServiceApiController(IMediator mediator, ILogger<ServiceApiController> logger)
        {
            _mediator = mediator;
            _logger = logger;
        }

        /// <summary>
        /// Gets branches for a vendor
        /// </summary>
        /// <param name="vendorId">The vendor ID</param>
        /// <returns>The list of branches</returns>
        [HttpGet("vendors/{vendorId}/branches")]
        public async Task<ActionResult<List<BranchDto>>> GetBranchesByVendorId(Guid vendorId)
        {
            try
            {
                _logger.LogInformation("Service API: Getting branches for vendor {VendorId}", vendorId);

                var query = new GetBranchesByVendorIdQuery { VendorId = vendorId };
                var branches = await _mediator.Send(query);

                return Ok(branches);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting branches for vendor {VendorId}", vendorId);
                return StatusCode(500, new { message = "An error occurred while retrieving branches" });
            }
        }

        /// <summary>
        /// Gets a branch by ID
        /// </summary>
        /// <param name="id">The branch ID</param>
        /// <returns>The branch details</returns>
        [HttpGet("branches/{id}")]
        public async Task<ActionResult<BranchDto>> GetBranchById(Guid id)
        {
            try
            {
                _logger.LogInformation("Service API: Getting branch with ID {BranchId}", id);

                var query = new GetBranchByIdQuery { Id = id };
                var branch = await _mediator.Send(query);

                if (branch == null)
                {
                    return NotFound();
                }

                return Ok(branch);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting branch with ID {BranchId}", id);
                return StatusCode(500, new { message = "An error occurred while retrieving the branch" });
            }
        }
    }
}
