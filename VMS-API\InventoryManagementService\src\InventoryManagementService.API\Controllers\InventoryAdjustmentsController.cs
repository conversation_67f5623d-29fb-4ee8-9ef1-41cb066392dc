using InventoryManagementService.Application.DTOs;
using InventoryManagementService.Application.Features.InventoryAdjustments.Commands;
using InventoryManagementService.Application.Features.InventoryAdjustments.Queries;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace InventoryManagementService.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class InventoryAdjustmentsController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<InventoryAdjustmentsController> _logger;

    public InventoryAdjustmentsController(IMediator mediator, ILogger<InventoryAdjustmentsController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Get inventory adjustment by ID
    /// </summary>
    [HttpGet("{id}")]
    [Authorize(Policy = "Adjustments.View")]
    public async Task<ActionResult<InventoryAdjustmentDto>> GetInventoryAdjustment(Guid id)
    {
        try
        {
            var query = new GetInventoryAdjustmentByIdQuery { AdjustmentId = id };
            var adjustment = await _mediator.Send(query);

            if (adjustment == null)
            {
                return NotFound($"Inventory adjustment with ID {id} not found");
            }

            return Ok(adjustment);
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access attempt to get inventory adjustment {AdjustmentId}", id);
            return Forbid(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving inventory adjustment {AdjustmentId}", id);
            return StatusCode(500, new { message = "An error occurred while retrieving the inventory adjustment" });
        }
    }

    /// <summary>
    /// Get inventory adjustments by branch
    /// </summary>
    [HttpGet("branch/{branchId}")]
    [Authorize(Policy = "Adjustments.View")]
    public async Task<ActionResult<List<InventoryAdjustmentDto>>> GetAdjustmentsByBranch(Guid branchId)
    {
        try
        {
            var query = new GetAdjustmentsByBranchQuery { BranchId = branchId };
            var adjustments = await _mediator.Send(query);
            return Ok(adjustments);
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access attempt to get adjustments for branch {BranchId}", branchId);
            return Forbid(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving adjustments for branch {BranchId}", branchId);
            return StatusCode(500, new { message = "An error occurred while retrieving adjustments" });
        }
    }

    /// <summary>
    /// Get pending inventory adjustments
    /// </summary>
    [HttpGet("pending")]
    [Authorize(Policy = "Adjustments.View")]
    public async Task<ActionResult<List<InventoryAdjustmentDto>>> GetPendingAdjustments()
    {
        try
        {
            var query = new GetPendingAdjustmentsQuery();
            var adjustments = await _mediator.Send(query);
            return Ok(adjustments);
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access attempt to get pending adjustments");
            return Forbid(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving pending adjustments");
            return StatusCode(500, new { message = "An error occurred while retrieving pending adjustments" });
        }
    }

    /// <summary>
    /// Create a new inventory adjustment
    /// </summary>
    [HttpPost]
    [Authorize(Policy = "Adjustments.Create")]
    public async Task<ActionResult<InventoryAdjustmentDto>> CreateInventoryAdjustment([FromBody] CreateInventoryAdjustmentDto createAdjustmentDto)
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "System";
            
            var command = new CreateInventoryAdjustmentCommand
            {
                Adjustment = createAdjustmentDto,
                CreatedBy = userId
            };

            var adjustment = await _mediator.Send(command);
            return CreatedAtAction(nameof(GetInventoryAdjustment), new { id = adjustment.Id }, adjustment);
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access attempt to create inventory adjustment");
            return Forbid(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation during inventory adjustment creation");
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating inventory adjustment");
            return StatusCode(500, new { message = "An error occurred while creating the inventory adjustment" });
        }
    }

    /// <summary>
    /// Add item to inventory adjustment
    /// </summary>
    [HttpPost("{id}/items")]
    [Authorize(Policy = "Adjustments.Update")]
    public async Task<ActionResult> AddAdjustmentItem(Guid id, [FromBody] AddAdjustmentItemDto addItemDto)
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "System";
            
            var command = new AddAdjustmentItemCommand
            {
                AdjustmentId = id,
                PartId = addItemDto.PartId,
                PreviousQuantity = addItemDto.PreviousQuantity,
                NewQuantity = addItemDto.NewQuantity,
                Notes = addItemDto.Notes,
                CreatedBy = userId
            };

            await _mediator.Send(command);
            return NoContent();
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access attempt to add item to adjustment {AdjustmentId}", id);
            return Forbid(ex.Message);
        }
        catch (KeyNotFoundException ex)
        {
            _logger.LogWarning(ex, "Adjustment not found for adding item: {AdjustmentId}", id);
            return NotFound(new { message = ex.Message });
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation during item addition to adjustment {AdjustmentId}", id);
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding item to adjustment {AdjustmentId}", id);
            return StatusCode(500, new { message = "An error occurred while adding item to adjustment" });
        }
    }

    /// <summary>
    /// Approve inventory adjustment
    /// </summary>
    [HttpPost("{id}/approve")]
    [Authorize(Policy = "Adjustments.Approve")]
    public async Task<ActionResult> ApproveAdjustment(Guid id)
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "System";
            
            var command = new ApproveAdjustmentCommand
            {
                AdjustmentId = id,
                ApprovedBy = userId
            };

            await _mediator.Send(command);
            return NoContent();
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access attempt to approve adjustment {AdjustmentId}", id);
            return Forbid(ex.Message);
        }
        catch (KeyNotFoundException ex)
        {
            _logger.LogWarning(ex, "Adjustment not found for approval: {AdjustmentId}", id);
            return NotFound(new { message = ex.Message });
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation during adjustment approval {AdjustmentId}", id);
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error approving adjustment {AdjustmentId}", id);
            return StatusCode(500, new { message = "An error occurred while approving the adjustment" });
        }
    }

    /// <summary>
    /// Update adjustment notes
    /// </summary>
    [HttpPut("{id}/notes")]
    [Authorize(Policy = "Adjustments.Update")]
    public async Task<ActionResult> UpdateAdjustmentNotes(Guid id, [FromBody] UpdateAdjustmentNotesDto updateNotesDto)
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "System";
            
            var command = new UpdateAdjustmentNotesCommand
            {
                AdjustmentId = id,
                Notes = updateNotesDto.Notes,
                UpdatedBy = userId
            };

            await _mediator.Send(command);
            return NoContent();
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access attempt to update notes for adjustment {AdjustmentId}", id);
            return Forbid(ex.Message);
        }
        catch (KeyNotFoundException ex)
        {
            _logger.LogWarning(ex, "Adjustment not found for updating notes: {AdjustmentId}", id);
            return NotFound(new { message = ex.Message });
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation during notes update for adjustment {AdjustmentId}", id);
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating notes for adjustment {AdjustmentId}", id);
            return StatusCode(500, new { message = "An error occurred while updating adjustment notes" });
        }
    }
}
