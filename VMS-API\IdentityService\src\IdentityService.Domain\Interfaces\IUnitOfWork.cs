using System;
using System.Threading;
using System.Threading.Tasks;
using IdentityService.Domain.Entities;

namespace IdentityService.Domain.Interfaces;

public interface IUnitOfWork : IDisposable
{
    IUserRepository UserRepository { get; }
    IRepository<Role> RoleRepository { get; }
    IRepository<Permission> PermissionRepository { get; }
    IRepository<UserRole> UserRoleRepository { get; }
    IRepository<RolePermission> RolePermissionRepository { get; }
    IRepository<PasswordResetToken> PasswordResetTokenRepository { get; }
    IAuditLogRepository AuditLogRepository { get; }
    IRepository<Menu> MenuRepository { get; }
    IRepository<MenuPermission> MenuPermissionRepository { get; }
    IUserSubscriptionRepository UserSubscriptionRepository { get; }
    IRepository<SubscriptionFeature> SubscriptionFeatureRepository { get; }
    IUserSessionRepository UserSessionRepository { get; }
    IRepository<UserLoginHistory> UserLoginHistoryRepository { get; }

    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
    Task BeginTransactionAsync();
    Task CommitTransactionAsync();
    Task RollbackTransactionAsync();
    void ClearChangeTracker();
}