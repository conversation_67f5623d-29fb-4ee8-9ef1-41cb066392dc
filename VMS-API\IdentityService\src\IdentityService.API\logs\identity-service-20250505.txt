2025-05-05 11:53:00.920 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'AuditLog'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-05 11:53:01.001 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'PasswordResetToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-05 11:53:01.007 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserBranch'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-05 11:53:01.009 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserRole'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-05 11:53:01.011 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserSubscription'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-05 11:53:01.453 +05:30 [ERR] An error occurred while migrating the database.
Npgsql.PostgresException (0x80004005): 28P01: password authentication failed for user "postgres"
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.Internal.NpgsqlConnector.AuthenticateSASL(List`1 mechanisms, String username, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.Authenticate(String username, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|213_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken, Boolean isFirstAttempt)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.UnpooledDataSource.Get(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.Open()
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenDbConnection(Boolean errorsExpected)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenInternal(Boolean errorsExpected)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.Open(Boolean errorsExpected)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlDatabaseCreator.Exists(Boolean async, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlDatabaseCreator.Exists(Boolean async, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlDatabaseCreator.Exists()
   at Microsoft.EntityFrameworkCore.Migrations.HistoryRepository.Exists()
   at Microsoft.EntityFrameworkCore.Migrations.HistoryRepository.GetAppliedMigrations()
   at Npgsql.EntityFrameworkCore.PostgreSQL.Migrations.Internal.NpgsqlMigrator.Migrate(String targetMigration)
   at Microsoft.EntityFrameworkCore.RelationalDatabaseFacadeExtensions.Migrate(DatabaseFacade databaseFacade)
   at Program.<Main>$(String[] args) in E:\Shri\Automobiles\VMS-API\IdentityService\src\IdentityService.API\Program.cs:line 299
  Exception data:
    Severity: FATAL
    SqlState: 28P01
    MessageText: password authentication failed for user "postgres"
    File: auth.c
    Line: 324
    Routine: auth_failed
2025-05-05 11:53:01.564 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-05 11:53:01.753 +05:30 [INF] Configured endpoint SubscriptionCreated, Consumer: IdentityService.Infrastructure.Messaging.Consumers.SubscriptionCreatedConsumer
2025-05-05 11:53:01.759 +05:30 [INF] Configured endpoint SubscriptionStatusChanged, Consumer: IdentityService.Infrastructure.Messaging.Consumers.SubscriptionStatusChangedConsumer
2025-05-05 11:53:01.964 +05:30 [DBG] Starting bus instances: IBus
2025-05-05 11:53:01.970 +05:30 [DBG] Starting bus: "rabbitmq://localhost/"
2025-05-05 11:53:02.029 +05:30 [DBG] Connect: guest@localhost:5672/
2025-05-05 11:53:02.086 +05:30 [INF] Now listening on: http://localhost:5263
2025-05-05 11:53:02.109 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-05-05 11:53:02.112 +05:30 [INF] Hosting environment: Development
2025-05-05 11:53:02.118 +05:30 [INF] Content root path: E:\Shri\Automobiles\VMS-API\IdentityService\src\IdentityService.API
2025-05-05 11:53:02.159 +05:30 [DBG] Connected: guest@localhost:5672/ (address: amqp://localhost:5672, local: 65485)
2025-05-05 11:53:02.248 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/DESKTOPI7D4M6U_IdentityServiceAPI_bus_ntpyyyfpkikqn13abdqaz8kjrq?temporary=true"
2025-05-05 11:53:02.324 +05:30 [DBG] Declare queue: name: SubscriptionStatusChanged, durable, consumer-count: 0 message-count: 0
2025-05-05 11:53:02.324 +05:30 [DBG] Declare queue: name: SubscriptionCreated, durable, consumer-count: 0 message-count: 0
2025-05-05 11:53:02.367 +05:30 [DBG] Declare exchange: name: SubscriptionStatusChanged, type: fanout, durable
2025-05-05 11:53:02.370 +05:30 [DBG] Declare exchange: name: SubscriptionCreated, type: fanout, durable
2025-05-05 11:53:02.429 +05:30 [DBG] Declare exchange: name: VMSContracts.Subscription.Events:SubscriptionStatusChanged, type: fanout, durable
2025-05-05 11:53:02.429 +05:30 [DBG] Declare exchange: name: VMSContracts.Subscription.Events:SubscriptionCreated, type: fanout, durable
2025-05-05 11:53:02.462 +05:30 [DBG] Bind queue: source: SubscriptionCreated, destination: SubscriptionCreated
2025-05-05 11:53:02.462 +05:30 [DBG] Bind queue: source: SubscriptionStatusChanged, destination: SubscriptionStatusChanged
2025-05-05 11:53:02.524 +05:30 [DBG] Bind exchange: source: VMSContracts.Subscription.Events:SubscriptionCreated, destination: SubscriptionCreated
2025-05-05 11:53:02.524 +05:30 [DBG] Bind exchange: source: VMSContracts.Subscription.Events:SubscriptionStatusChanged, destination: SubscriptionStatusChanged
2025-05-05 11:53:02.711 +05:30 [DBG] Consumer Ok: "rabbitmq://localhost/SubscriptionCreated" - amq.ctag-B9ouv57HGR_HM5JL0QTKbA
2025-05-05 11:53:02.862 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/SubscriptionCreated"
2025-05-05 11:53:02.890 +05:30 [DBG] Consumer Ok: "rabbitmq://localhost/SubscriptionStatusChanged" - amq.ctag-BPhXuTTexcRF0m7SFrPDRw
2025-05-05 11:53:03.093 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/SubscriptionStatusChanged"
2025-05-05 11:53:03.107 +05:30 [INF] Bus started: "rabbitmq://localhost/"
2025-05-05 11:53:05.234 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/ - null null
2025-05-05 11:53:05.302 +05:30 [INF] Request was redirected to /swagger
2025-05-05 11:53:05.312 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/ - 302 0 null 83.9842ms
2025-05-05 11:53:05.333 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/swagger - null null
2025-05-05 11:53:05.360 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/swagger - 301 0 null 28.2023ms
2025-05-05 11:53:05.366 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/swagger/index.html - null null
2025-05-05 11:53:05.457 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/swagger/index.html - 200 null text/html;charset=utf-8 90.8933ms
2025-05-05 11:53:05.479 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/swagger/swagger-ui-bundle.js - null null
2025-05-05 11:53:05.479 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/swagger/swagger-ui.css - null null
2025-05-05 11:53:05.480 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/swagger/swagger-ui-standalone-preset.js - null null
2025-05-05 11:53:05.517 +05:30 [INF] Sending file. Request path: '/swagger-ui.css'. Physical path: 'N/A'
2025-05-05 11:53:05.534 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/swagger/swagger-ui.css - 200 143943 text/css 55.0113ms
2025-05-05 11:53:05.537 +05:30 [INF] Sending file. Request path: '/swagger-ui-standalone-preset.js'. Physical path: 'N/A'
2025-05-05 11:53:05.554 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/swagger/swagger-ui-standalone-preset.js - 200 339486 text/javascript 74.4275ms
2025-05-05 11:53:05.556 +05:30 [INF] Sending file. Request path: '/swagger-ui-bundle.js'. Physical path: 'N/A'
2025-05-05 11:53:05.563 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/swagger/swagger-ui-bundle.js - 200 1096145 text/javascript 83.5598ms
2025-05-05 11:53:05.835 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/swagger/v1/swagger.json - null null
2025-05-05 11:53:05.872 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/swagger/favicon-32x32.png - null null
2025-05-05 11:53:05.876 +05:30 [INF] Sending file. Request path: '/favicon-32x32.png'. Physical path: 'N/A'
2025-05-05 11:53:05.880 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/swagger/favicon-32x32.png - 200 628 image/png 8.0213ms
2025-05-05 11:53:06.061 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 226.312ms
2025-05-05 11:53:12.454 +05:30 [INF] Application is shutting down...
2025-05-05 11:53:12.465 +05:30 [DBG] Stopping bus instances: IBus
2025-05-05 11:53:12.470 +05:30 [DBG] Stopping bus: "rabbitmq://localhost/"
2025-05-05 11:53:12.478 +05:30 [DBG] Endpoint Stopping: "rabbitmq://localhost/SubscriptionCreated"
2025-05-05 11:53:12.487 +05:30 [DBG] Consumer Stopping: "rabbitmq://localhost/SubscriptionCreated" (Stop Receive Transport)
2025-05-05 11:53:12.493 +05:30 [DBG] Consumer Cancel Ok: "rabbitmq://localhost/SubscriptionCreated" - amq.ctag-B9ouv57HGR_HM5JL0QTKbA
2025-05-05 11:53:12.494 +05:30 [DBG] Endpoint Stopping: "rabbitmq://localhost/SubscriptionStatusChanged"
2025-05-05 11:53:12.496 +05:30 [DBG] Consumer Stopping: "rabbitmq://localhost/SubscriptionStatusChanged" (Stop Receive Transport)
2025-05-05 11:53:12.499 +05:30 [DBG] Endpoint Completed: "rabbitmq://localhost/SubscriptionCreated"
2025-05-05 11:53:12.505 +05:30 [DBG] Consumer Cancel Ok: "rabbitmq://localhost/SubscriptionStatusChanged" - amq.ctag-BPhXuTTexcRF0m7SFrPDRw
2025-05-05 11:53:12.507 +05:30 [DBG] Endpoint Completed: "rabbitmq://localhost/SubscriptionStatusChanged"
2025-05-05 11:53:12.512 +05:30 [DBG] Consumer Completed: "rabbitmq://localhost/SubscriptionStatusChanged": 0 received, 0 concurrent, amq.ctag-BPhXuTTexcRF0m7SFrPDRw
2025-05-05 11:53:12.513 +05:30 [DBG] Consumer Completed: "rabbitmq://localhost/SubscriptionCreated": 0 received, 0 concurrent, amq.ctag-B9ouv57HGR_HM5JL0QTKbA
2025-05-05 11:53:12.529 +05:30 [DBG] Endpoint Stopping: "rabbitmq://localhost/DESKTOPI7D4M6U_IdentityServiceAPI_bus_ntpyyyfpkikqn13abdqaz8kjrq?temporary=true"
2025-05-05 11:53:12.535 +05:30 [DBG] Endpoint Completed: "rabbitmq://localhost/DESKTOPI7D4M6U_IdentityServiceAPI_bus_ntpyyyfpkikqn13abdqaz8kjrq?temporary=true"
2025-05-05 11:53:12.539 +05:30 [DBG] Disconnect: guest@localhost:5672/
2025-05-05 11:53:12.542 +05:30 [DBG] Disconnected: guest@localhost:5672/
2025-05-05 11:53:12.544 +05:30 [INF] Bus stopped: "rabbitmq://localhost/"
2025-05-05 12:37:27.763 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'AuditLog'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-05 12:37:27.828 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'PasswordResetToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-05 12:37:27.831 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserBranch'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-05 12:37:27.833 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserRole'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-05 12:37:27.838 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserSubscription'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-05 12:37:28.733 +05:30 [INF] Executed DbCommand (62ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-05-05 12:37:28.776 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-05-05 12:37:28.872 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-05-05 12:37:28.958 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-05-05 12:37:28.962 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-05-05 12:37:28.985 +05:30 [INF] No migrations were applied. The database is already up to date.
2025-05-05 12:37:29.015 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-05 12:37:29.211 +05:30 [INF] Configured endpoint SubscriptionCreated, Consumer: IdentityService.Infrastructure.Messaging.Consumers.SubscriptionCreatedConsumer
2025-05-05 12:37:29.217 +05:30 [INF] Configured endpoint SubscriptionStatusChanged, Consumer: IdentityService.Infrastructure.Messaging.Consumers.SubscriptionStatusChangedConsumer
2025-05-05 12:37:29.397 +05:30 [DBG] Starting bus instances: IBus
2025-05-05 12:37:29.403 +05:30 [DBG] Starting bus: "rabbitmq://localhost/"
2025-05-05 12:37:29.462 +05:30 [DBG] Connect: guest@localhost:5672/
2025-05-05 12:37:29.511 +05:30 [INF] Now listening on: http://localhost:5263
2025-05-05 12:37:29.513 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-05-05 12:37:29.514 +05:30 [INF] Hosting environment: Development
2025-05-05 12:37:29.515 +05:30 [INF] Content root path: E:\Shri\Automobiles\VMS-API\IdentityService\src\IdentityService.API
2025-05-05 12:37:29.545 +05:30 [DBG] Connected: guest@localhost:5672/ (address: amqp://localhost:5672, local: 50637)
2025-05-05 12:37:29.583 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/DESKTOPI7D4M6U_IdentityServiceAPI_bus_josyyyfpkikqdr3kbdqaze59d5?temporary=true"
2025-05-05 12:37:29.606 +05:30 [DBG] Declare queue: name: SubscriptionCreated, durable, consumer-count: 0 message-count: 0
2025-05-05 12:37:29.606 +05:30 [DBG] Declare queue: name: SubscriptionStatusChanged, durable, consumer-count: 0 message-count: 0
2025-05-05 12:37:29.623 +05:30 [DBG] Declare exchange: name: SubscriptionStatusChanged, type: fanout, durable
2025-05-05 12:37:29.623 +05:30 [DBG] Declare exchange: name: SubscriptionCreated, type: fanout, durable
2025-05-05 12:37:29.630 +05:30 [DBG] Declare exchange: name: VMSContracts.Subscription.Events:SubscriptionCreated, type: fanout, durable
2025-05-05 12:37:29.630 +05:30 [DBG] Declare exchange: name: VMSContracts.Subscription.Events:SubscriptionStatusChanged, type: fanout, durable
2025-05-05 12:37:29.639 +05:30 [DBG] Bind queue: source: SubscriptionStatusChanged, destination: SubscriptionStatusChanged
2025-05-05 12:37:29.639 +05:30 [DBG] Bind queue: source: SubscriptionCreated, destination: SubscriptionCreated
2025-05-05 12:37:29.674 +05:30 [DBG] Bind exchange: source: VMSContracts.Subscription.Events:SubscriptionStatusChanged, destination: SubscriptionStatusChanged
2025-05-05 12:37:29.674 +05:30 [DBG] Bind exchange: source: VMSContracts.Subscription.Events:SubscriptionCreated, destination: SubscriptionCreated
2025-05-05 12:37:29.718 +05:30 [DBG] Consumer Ok: "rabbitmq://localhost/SubscriptionStatusChanged" - amq.ctag-yyC1RCmJw4blAa89yZeszw
2025-05-05 12:37:29.718 +05:30 [DBG] Consumer Ok: "rabbitmq://localhost/SubscriptionCreated" - amq.ctag-ltrALsm7Lvn__YD9sHoOdg
2025-05-05 12:37:29.723 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/SubscriptionStatusChanged"
2025-05-05 12:37:29.723 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/SubscriptionCreated"
2025-05-05 12:37:29.727 +05:30 [INF] Bus started: "rabbitmq://localhost/"
2025-05-05 12:37:37.477 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/ - null null
2025-05-05 12:37:37.609 +05:30 [INF] Request was redirected to /swagger
2025-05-05 12:37:37.624 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/ - 302 0 null 154.0061ms
2025-05-05 12:37:37.711 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/swagger/index.html - null null
2025-05-05 12:37:37.971 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/swagger/index.html - 200 null text/html;charset=utf-8 260.7268ms
2025-05-05 12:37:38.262 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/swagger/v1/swagger.json - null null
2025-05-05 12:37:38.460 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 197.9913ms
2025-05-05 12:41:30.317 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5263/api/Users - application/json 221
2025-05-05 12:41:30.326 +05:30 [WRN] Failed to determine the https port for redirect.
2025-05-05 12:41:30.395 +05:30 [INF] Token received and is being processed
2025-05-05 12:41:30.421 +05:30 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
ClaimsAuthorizationRequirement:Claim.Type=permission and Claim.Value is one of the following values: (Users.Create)
2025-05-05 12:41:30.425 +05:30 [WRN] OnChallenge: null
2025-05-05 12:41:30.428 +05:30 [INF] AuthenticationScheme: Bearer was challenged.
2025-05-05 12:41:30.434 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5263/api/Users - 401 0 null 116.6326ms
2025-05-05 13:20:16.516 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'AuditLog'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-05 13:20:16.572 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'PasswordResetToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-05 13:20:16.574 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserBranch'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-05 13:20:16.581 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserRole'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-05 13:20:16.585 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserSubscription'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-05 13:20:17.293 +05:30 [INF] Executed DbCommand (62ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-05-05 13:20:17.327 +05:30 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-05-05 13:20:17.435 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-05-05 13:20:17.525 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-05-05 13:20:17.531 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-05-05 13:20:17.550 +05:30 [INF] No migrations were applied. The database is already up to date.
2025-05-05 13:20:17.580 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-05 13:20:17.788 +05:30 [INF] Configured endpoint SubscriptionCreated, Consumer: IdentityService.Infrastructure.Messaging.Consumers.SubscriptionCreatedConsumer
2025-05-05 13:20:17.796 +05:30 [INF] Configured endpoint SubscriptionStatusChanged, Consumer: IdentityService.Infrastructure.Messaging.Consumers.SubscriptionStatusChangedConsumer
2025-05-05 13:20:17.992 +05:30 [DBG] Starting bus instances: IBus
2025-05-05 13:20:17.998 +05:30 [DBG] Starting bus: "rabbitmq://localhost/"
2025-05-05 13:20:18.071 +05:30 [DBG] Connect: guest@localhost:5672/
2025-05-05 13:20:18.096 +05:30 [INF] Now listening on: http://localhost:5263
2025-05-05 13:20:18.098 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-05-05 13:20:18.100 +05:30 [INF] Hosting environment: Development
2025-05-05 13:20:18.103 +05:30 [INF] Content root path: E:\Shri\Automobiles\VMS-API\IdentityService\src\IdentityService.API
2025-05-05 13:20:18.160 +05:30 [DBG] Connected: guest@localhost:5672/ (address: amqp://localhost:5672, local: 51294)
2025-05-05 13:20:18.194 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/DESKTOPI7D4M6U_IdentityServiceAPI_bus_8o9oyyfpkikqnji1bdqazkm4da?temporary=true"
2025-05-05 13:20:18.221 +05:30 [DBG] Declare queue: name: SubscriptionCreated, durable, consumer-count: 0 message-count: 0
2025-05-05 13:20:18.221 +05:30 [DBG] Declare queue: name: SubscriptionStatusChanged, durable, consumer-count: 0 message-count: 0
2025-05-05 13:20:18.251 +05:30 [DBG] Declare exchange: name: SubscriptionStatusChanged, type: fanout, durable
2025-05-05 13:20:18.251 +05:30 [DBG] Declare exchange: name: SubscriptionCreated, type: fanout, durable
2025-05-05 13:20:18.260 +05:30 [DBG] Declare exchange: name: VMSContracts.Subscription.Events:SubscriptionStatusChanged, type: fanout, durable
2025-05-05 13:20:18.260 +05:30 [DBG] Declare exchange: name: VMSContracts.Subscription.Events:SubscriptionCreated, type: fanout, durable
2025-05-05 13:20:18.268 +05:30 [DBG] Bind queue: source: SubscriptionCreated, destination: SubscriptionCreated
2025-05-05 13:20:18.268 +05:30 [DBG] Bind queue: source: SubscriptionStatusChanged, destination: SubscriptionStatusChanged
2025-05-05 13:20:18.297 +05:30 [DBG] Bind exchange: source: VMSContracts.Subscription.Events:SubscriptionStatusChanged, destination: SubscriptionStatusChanged
2025-05-05 13:20:18.297 +05:30 [DBG] Bind exchange: source: VMSContracts.Subscription.Events:SubscriptionCreated, destination: SubscriptionCreated
2025-05-05 13:20:18.346 +05:30 [DBG] Consumer Ok: "rabbitmq://localhost/SubscriptionStatusChanged" - amq.ctag-4sDv7sREun1o9jz-H2DcPA
2025-05-05 13:20:18.346 +05:30 [DBG] Consumer Ok: "rabbitmq://localhost/SubscriptionCreated" - amq.ctag-9pQJd12K1GwA-JKLQBixEQ
2025-05-05 13:20:18.350 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/SubscriptionStatusChanged"
2025-05-05 13:20:18.351 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/SubscriptionCreated"
2025-05-05 13:20:18.357 +05:30 [INF] Bus started: "rabbitmq://localhost/"
2025-05-05 13:20:22.637 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/ - null null
2025-05-05 13:20:22.732 +05:30 [INF] Request was redirected to /swagger
2025-05-05 13:20:22.738 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/ - 302 0 null 108.9945ms
2025-05-05 13:20:22.751 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/swagger/index.html - null null
2025-05-05 13:20:22.869 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/swagger/index.html - 200 null text/html;charset=utf-8 117.9485ms
2025-05-05 13:20:23.005 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/swagger/v1/swagger.json - null null
2025-05-05 13:20:23.187 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 181.3961ms
2025-05-05 13:21:31.868 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5263/api/Users - application/json 221
2025-05-05 13:21:31.878 +05:30 [WRN] Failed to determine the https port for redirect.
2025-05-05 13:21:31.921 +05:30 [INF] Token received and is being processed
2025-05-05 13:21:31.946 +05:30 [WRN] User is null or not authenticated
2025-05-05 13:21:31.950 +05:30 [WRN] No user ID found in token
2025-05-05 13:21:31.953 +05:30 [INF] Executing endpoint 'IdentityService.API.Controllers.UsersController.CreateUser (IdentityService.API)'
2025-05-05 13:21:31.983 +05:30 [INF] Route matched with {action = "CreateUser", controller = "Users"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[IdentityService.Application.DTOs.Users.UserResponse]] CreateUser(IdentityService.Application.DTOs.Users.CreateUserRequest) on controller IdentityService.API.Controllers.UsersController (IdentityService.API).
2025-05-05 13:21:32.043 +05:30 [WRN] User ID not found in HttpContext.Items
2025-05-05 13:21:32.045 +05:30 [INF] Setting UserId "00000000-0000-0000-0000-000000000000" on request of type CreateUserCommand
2025-05-05 13:21:32.047 +05:30 [WRN] User ID not found in HttpContext.Items
2025-05-05 13:21:32.054 +05:30 [INF] Validating request of type CreateUserCommand
2025-05-05 13:21:32.071 +05:30 [INF] Request content: {
  "Request": {
    "Email": "<EMAIL>",
    "PhoneNumber": "8088020878",
    "Password": "Admin@123",
    "ConfirmPassword": "Admin@123",
    "RoleIds": [
      "3fa85f64-5717-4562-b3fc-2c963f66afa6"
    ],
    "IsActive": true
  },
  "UserId": "00000000-0000-0000-0000-000000000000"
}
2025-05-05 13:21:32.077 +05:30 [INF] Request UserId property value: "00000000-0000-0000-0000-000000000000"
2025-05-05 13:21:32.079 +05:30 [WRN] UserId is empty GUID (00000000-0000-0000-0000-000000000000)
2025-05-05 13:21:32.080 +05:30 [INF] Validation passed for request of type CreateUserCommand
2025-05-05 13:21:32.407 +05:30 [INF] Executed DbCommand (17ms) [Parameters=[@__email_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__email_0)
2025-05-05 13:21:32.432 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__phoneNumber_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."PhoneNumber" = @__phoneNumber_0)
2025-05-05 13:21:32.547 +05:30 [WRN] User is null or not authenticated
2025-05-05 13:21:32.656 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__get_Item_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."Description", r."IsDeleted", r."Name", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "Roles" AS r
WHERE r."Id" = @__get_Item_0
LIMIT 1
2025-05-05 13:21:32.843 +05:30 [WRN] User is null or not authenticated
2025-05-05 13:21:32.955 +05:30 [INF] Executed DbCommand (12ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = DateTime), @p2='?', @p3='?', @p4='?' (DbType = DateTime), @p5='?', @p6='?', @p7='?' (DbType = DateTime), @p8='?' (DbType = DateTime), @p9='?' (DbType = DateTime), @p10='?', @p11='?'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Users" ("Id", "CreatedAt", "CreatedBy", "Email", "LastLoginAt", "PhoneNumber", "RefreshToken", "RefreshTokenExpiryTime", "SecurityStamp", "UpdatedAt", "UpdatedBy", "PasswordHash")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11)
RETURNING "EmailVerified", "IsDeleted", "PhoneNumberVerified", "RememberMe", "RowVersion";
2025-05-05 13:21:33.021 +05:30 [INF] Executed DbCommand (4ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."Description", r."IsDeleted", r."Name", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "UserRoles" AS u
INNER JOIN "Roles" AS r ON u."RoleId" = r."Id"
WHERE u."UserId" = @__userId_0
2025-05-05 13:21:33.035 +05:30 [INF] Executing CreatedAtActionResult, writing value of type 'IdentityService.Application.DTOs.Users.UserResponse'.
2025-05-05 13:21:33.076 +05:30 [INF] Executed action IdentityService.API.Controllers.UsersController.CreateUser (IdentityService.API) in 1084.7554ms
2025-05-05 13:21:33.078 +05:30 [INF] Executed endpoint 'IdentityService.API.Controllers.UsersController.CreateUser (IdentityService.API)'
2025-05-05 13:21:33.088 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5263/api/Users - 201 null application/json; charset=utf-8 1219.5705ms
2025-05-05 13:22:05.244 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/ - null null
2025-05-05 13:22:05.248 +05:30 [INF] Request was redirected to /swagger
2025-05-05 13:22:05.249 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/ - 302 0 null 4.7666ms
2025-05-05 13:22:05.269 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/swagger/index.html - null null
2025-05-05 13:22:05.273 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/swagger/index.html - 200 null text/html;charset=utf-8 4.2986ms
2025-05-05 13:22:05.458 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/swagger/v1/swagger.json - null null
2025-05-05 13:22:05.531 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 72.4672ms
2025-05-05 13:23:27.750 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5263/api/Permissions - application/json 95
2025-05-05 13:23:27.757 +05:30 [INF] Token received and is being processed
2025-05-05 13:23:27.849 +05:30 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-05-05 13:23:27.853 +05:30 [WRN] OnChallenge: null
2025-05-05 13:23:27.854 +05:30 [INF] AuthenticationScheme: Bearer was challenged.
2025-05-05 13:23:27.859 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5263/api/Permissions - 401 0 null 108.7861ms
2025-05-05 13:24:14.666 +05:30 [INF] Application is shutting down...
2025-05-05 13:24:14.693 +05:30 [DBG] Stopping bus instances: IBus
2025-05-05 13:24:14.706 +05:30 [DBG] Stopping bus: "rabbitmq://localhost/"
2025-05-05 13:24:14.728 +05:30 [DBG] Endpoint Stopping: "rabbitmq://localhost/SubscriptionCreated"
2025-05-05 13:24:14.744 +05:30 [DBG] Consumer Stopping: "rabbitmq://localhost/SubscriptionCreated" (Stop Receive Transport)
2025-05-05 13:24:14.771 +05:30 [DBG] Endpoint Stopping: "rabbitmq://localhost/SubscriptionStatusChanged"
2025-05-05 13:24:14.798 +05:30 [DBG] Consumer Cancel Ok: "rabbitmq://localhost/SubscriptionCreated" - amq.ctag-9pQJd12K1GwA-JKLQBixEQ
2025-05-05 13:24:14.824 +05:30 [DBG] Endpoint Completed: "rabbitmq://localhost/SubscriptionCreated"
2025-05-05 13:24:14.869 +05:30 [DBG] Consumer Stopping: "rabbitmq://localhost/SubscriptionStatusChanged" (Stop Receive Transport)
2025-05-05 13:24:14.878 +05:30 [DBG] Consumer Cancel Ok: "rabbitmq://localhost/SubscriptionStatusChanged" - amq.ctag-4sDv7sREun1o9jz-H2DcPA
2025-05-05 13:24:14.882 +05:30 [DBG] Endpoint Completed: "rabbitmq://localhost/SubscriptionStatusChanged"
2025-05-05 13:24:14.892 +05:30 [DBG] Consumer Completed: "rabbitmq://localhost/SubscriptionCreated": 0 received, 0 concurrent, amq.ctag-9pQJd12K1GwA-JKLQBixEQ
2025-05-05 13:24:14.892 +05:30 [DBG] Consumer Completed: "rabbitmq://localhost/SubscriptionStatusChanged": 0 received, 0 concurrent, amq.ctag-4sDv7sREun1o9jz-H2DcPA
2025-05-05 13:24:14.898 +05:30 [DBG] Endpoint Stopping: "rabbitmq://localhost/DESKTOPI7D4M6U_IdentityServiceAPI_bus_8o9oyyfpkikqnji1bdqazkm4da?temporary=true"
2025-05-05 13:24:14.901 +05:30 [DBG] Endpoint Completed: "rabbitmq://localhost/DESKTOPI7D4M6U_IdentityServiceAPI_bus_8o9oyyfpkikqnji1bdqazkm4da?temporary=true"
2025-05-05 13:24:14.905 +05:30 [DBG] Disconnect: guest@localhost:5672/
2025-05-05 13:24:14.910 +05:30 [DBG] Disconnected: guest@localhost:5672/
2025-05-05 13:24:14.912 +05:30 [INF] Bus stopped: "rabbitmq://localhost/"
2025-05-05 13:24:37.955 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'AuditLog'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-05 13:24:38.051 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'PasswordResetToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-05 13:24:38.055 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserBranch'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-05 13:24:38.067 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserRole'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-05 13:24:38.070 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserSubscription'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-05 13:24:39.009 +05:30 [INF] Executed DbCommand (120ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-05-05 13:24:39.044 +05:30 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-05-05 13:24:39.169 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-05-05 13:24:39.266 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-05-05 13:24:39.272 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-05-05 13:24:39.292 +05:30 [INF] No migrations were applied. The database is already up to date.
2025-05-05 13:24:39.328 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-05 13:24:39.566 +05:30 [INF] Configured endpoint SubscriptionCreated, Consumer: IdentityService.Infrastructure.Messaging.Consumers.SubscriptionCreatedConsumer
2025-05-05 13:24:39.574 +05:30 [INF] Configured endpoint SubscriptionStatusChanged, Consumer: IdentityService.Infrastructure.Messaging.Consumers.SubscriptionStatusChangedConsumer
2025-05-05 13:24:39.807 +05:30 [DBG] Starting bus instances: IBus
2025-05-05 13:24:39.815 +05:30 [DBG] Starting bus: "rabbitmq://localhost/"
2025-05-05 13:24:39.879 +05:30 [DBG] Connect: guest@localhost:5672/
2025-05-05 13:24:39.935 +05:30 [INF] Now listening on: http://localhost:5263
2025-05-05 13:24:39.945 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-05-05 13:24:39.948 +05:30 [INF] Hosting environment: Development
2025-05-05 13:24:39.949 +05:30 [INF] Content root path: E:\Shri\Automobiles\VMS-API\IdentityService\src\IdentityService.API
2025-05-05 13:24:39.976 +05:30 [DBG] Connected: guest@localhost:5672/ (address: amqp://localhost:5672, local: 51363)
2025-05-05 13:24:40.016 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/DESKTOPI7D4M6U_IdentityServiceAPI_bus_xofyyyfpkikqdfyrbdqazkosd6?temporary=true"
2025-05-05 13:24:40.048 +05:30 [DBG] Declare queue: name: SubscriptionCreated, durable, consumer-count: 0 message-count: 0
2025-05-05 13:24:40.048 +05:30 [DBG] Declare queue: name: SubscriptionStatusChanged, durable, consumer-count: 0 message-count: 0
2025-05-05 13:24:40.066 +05:30 [DBG] Declare exchange: name: SubscriptionCreated, type: fanout, durable
2025-05-05 13:24:40.066 +05:30 [DBG] Declare exchange: name: SubscriptionStatusChanged, type: fanout, durable
2025-05-05 13:24:40.073 +05:30 [DBG] Declare exchange: name: VMSContracts.Subscription.Events:SubscriptionStatusChanged, type: fanout, durable
2025-05-05 13:24:40.073 +05:30 [DBG] Declare exchange: name: VMSContracts.Subscription.Events:SubscriptionCreated, type: fanout, durable
2025-05-05 13:24:40.084 +05:30 [DBG] Bind queue: source: SubscriptionStatusChanged, destination: SubscriptionStatusChanged
2025-05-05 13:24:40.084 +05:30 [DBG] Bind queue: source: SubscriptionCreated, destination: SubscriptionCreated
2025-05-05 13:24:40.117 +05:30 [DBG] Bind exchange: source: VMSContracts.Subscription.Events:SubscriptionStatusChanged, destination: SubscriptionStatusChanged
2025-05-05 13:24:40.117 +05:30 [DBG] Bind exchange: source: VMSContracts.Subscription.Events:SubscriptionCreated, destination: SubscriptionCreated
2025-05-05 13:24:40.168 +05:30 [DBG] Consumer Ok: "rabbitmq://localhost/SubscriptionStatusChanged" - amq.ctag-Q3S0Q0XZNc-VURO6vgcVKA
2025-05-05 13:24:40.168 +05:30 [DBG] Consumer Ok: "rabbitmq://localhost/SubscriptionCreated" - amq.ctag-q5e-UxaiOpGYwsWacPXptg
2025-05-05 13:24:40.171 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/SubscriptionCreated"
2025-05-05 13:24:40.171 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/SubscriptionStatusChanged"
2025-05-05 13:24:40.180 +05:30 [INF] Bus started: "rabbitmq://localhost/"
2025-05-05 13:24:47.145 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5263/api/Permissions - application/json 95
2025-05-05 13:24:47.183 +05:30 [WRN] Failed to determine the https port for redirect.
2025-05-05 13:24:47.284 +05:30 [INF] Token received and is being processed
2025-05-05 13:24:47.370 +05:30 [WRN] User is null or not authenticated
2025-05-05 13:24:47.371 +05:30 [WRN] No user ID found in token
2025-05-05 13:24:47.377 +05:30 [INF] Executing endpoint 'IdentityService.API.Controllers.PermissionsController.CreatePermission (IdentityService.API)'
2025-05-05 13:24:47.403 +05:30 [INF] Route matched with {action = "CreatePermission", controller = "Permissions"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[IdentityService.Application.DTOs.Permissions.PermissionResponse]] CreatePermission(IdentityService.Application.DTOs.Permissions.CreatePermissionRequest) on controller IdentityService.API.Controllers.PermissionsController (IdentityService.API).
2025-05-05 13:24:47.482 +05:30 [INF] Request of type CreatePermissionCommand is not a BaseRequest, skipping UserId propagation
2025-05-05 13:24:47.487 +05:30 [INF] Validating request of type CreatePermissionCommand
2025-05-05 13:24:47.496 +05:30 [INF] Request content: {
  "Request": {
    "Name": "Create",
    "Description": "Create",
    "Resource": "Create",
    "Action": "Create"
  }
}
2025-05-05 13:24:47.497 +05:30 [INF] Request does not have a UserId property
2025-05-05 13:24:47.499 +05:30 [INF] Validation passed for request of type CreatePermissionCommand
2025-05-05 13:24:47.503 +05:30 [WRN] User is null or not authenticated
2025-05-05 13:24:47.735 +05:30 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
2025-05-05 13:24:47.882 +05:30 [WRN] User is null or not authenticated
2025-05-05 13:24:47.991 +05:30 [INF] Executed DbCommand (17ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?' (DbType = DateTime), @p3='?', @p4='?', @p5='?' (DbType = Boolean), @p6='?', @p7='?', @p8='?' (DbType = Binary), @p9='?' (DbType = DateTime), @p10='?'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Permissions" ("Id", "Action", "CreatedAt", "CreatedBy", "Description", "IsDeleted", "Name", "Resource", "RowVersion", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10);
2025-05-05 13:24:48.030 +05:30 [WRN] User is null or not authenticated
2025-05-05 13:24:48.168 +05:30 [INF] Executed DbCommand (4ms) [Parameters=[@__get_Item_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."Email", u."EmailVerified", u."IsDeleted", u."LastLoginAt", u."PhoneNumber", u."PhoneNumberVerified", u."RefreshToken", u."RefreshTokenExpiryTime", u."RememberMe", u."RowVersion", u."SecurityStamp", u."UpdatedAt", u."UpdatedBy", u."PasswordHash"
FROM "Users" AS u
WHERE NOT (u."IsDeleted") AND u."Id" = @__get_Item_0
LIMIT 1
2025-05-05 13:24:48.210 +05:30 [ERR] Error creating permission
System.ArgumentException: User with ID 00000000-0000-0000-0000-000000000000 not found
   at IdentityService.Infrastructure.Services.AuditLogService.CreateAuditLogAsync(String action, String entityName, String entityId, String oldValues, String newValues, String affectedColumns, Guid userId) in E:\Shri\Automobiles\VMS-API\IdentityService\src\IdentityService.Infrastructure\Services\AuditLogService.cs:line 38
   at IdentityService.Application.Features.Permissions.Commands.CreatePermissionCommandHandler.Handle(CreatePermissionCommand command, CancellationToken cancellationToken) in E:\Shri\Automobiles\VMS-API\IdentityService\src\IdentityService.Application\Features\Permissions\Commands\CreatePermissionCommand.cs:line 56
   at IdentityService.Application.Behaviors.ValidationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in E:\Shri\Automobiles\VMS-API\IdentityService\src\IdentityService.Application\Behaviors\ValidationBehavior.cs:line 95
   at IdentityService.Application.Behaviors.UserIdPropagationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in E:\Shri\Automobiles\VMS-API\IdentityService\src\IdentityService.Application\Behaviors\UserIdPropagationBehavior.cs:line 39
   at MediatR.Pipeline.RequestExceptionProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestExceptionProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestExceptionActionProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestExceptionActionProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestPostProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestPreProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at IdentityService.API.Controllers.PermissionsController.CreatePermission(CreatePermissionRequest request) in E:\Shri\Automobiles\VMS-API\IdentityService\src\IdentityService.API\Controllers\PermissionsController.cs:line 74
2025-05-05 13:24:48.282 +05:30 [INF] Executing ObjectResult, writing value of type '<>f__AnonymousType0`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-05-05 13:24:48.309 +05:30 [INF] Executed action IdentityService.API.Controllers.PermissionsController.CreatePermission (IdentityService.API) in 894.0423ms
2025-05-05 13:24:48.320 +05:30 [INF] Executed endpoint 'IdentityService.API.Controllers.PermissionsController.CreatePermission (IdentityService.API)'
2025-05-05 13:24:48.347 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5263/api/Permissions - 500 null application/json; charset=utf-8 1201.0797ms
2025-05-05 13:25:41.731 +05:30 [INF] Application is shutting down...
2025-05-05 13:25:41.742 +05:30 [DBG] Stopping bus instances: IBus
2025-05-05 13:25:41.747 +05:30 [DBG] Stopping bus: "rabbitmq://localhost/"
2025-05-05 13:25:41.754 +05:30 [DBG] Endpoint Stopping: "rabbitmq://localhost/SubscriptionCreated"
2025-05-05 13:25:41.759 +05:30 [DBG] Consumer Stopping: "rabbitmq://localhost/SubscriptionCreated" (Stop Receive Transport)
2025-05-05 13:25:41.767 +05:30 [DBG] Consumer Cancel Ok: "rabbitmq://localhost/SubscriptionCreated" - amq.ctag-q5e-UxaiOpGYwsWacPXptg
2025-05-05 13:25:41.769 +05:30 [DBG] Endpoint Stopping: "rabbitmq://localhost/SubscriptionStatusChanged"
2025-05-05 13:25:41.771 +05:30 [DBG] Consumer Stopping: "rabbitmq://localhost/SubscriptionStatusChanged" (Stop Receive Transport)
2025-05-05 13:25:41.772 +05:30 [DBG] Endpoint Completed: "rabbitmq://localhost/SubscriptionCreated"
2025-05-05 13:25:41.774 +05:30 [DBG] Consumer Cancel Ok: "rabbitmq://localhost/SubscriptionStatusChanged" - amq.ctag-Q3S0Q0XZNc-VURO6vgcVKA
2025-05-05 13:25:41.777 +05:30 [DBG] Endpoint Completed: "rabbitmq://localhost/SubscriptionStatusChanged"
2025-05-05 13:25:41.789 +05:30 [DBG] Consumer Completed: "rabbitmq://localhost/SubscriptionCreated": 0 received, 0 concurrent, amq.ctag-q5e-UxaiOpGYwsWacPXptg
2025-05-05 13:25:41.789 +05:30 [DBG] Consumer Completed: "rabbitmq://localhost/SubscriptionStatusChanged": 0 received, 0 concurrent, amq.ctag-Q3S0Q0XZNc-VURO6vgcVKA
2025-05-05 13:25:41.804 +05:30 [DBG] Endpoint Stopping: "rabbitmq://localhost/DESKTOPI7D4M6U_IdentityServiceAPI_bus_xofyyyfpkikqdfyrbdqazkosd6?temporary=true"
2025-05-05 13:25:41.807 +05:30 [DBG] Endpoint Completed: "rabbitmq://localhost/DESKTOPI7D4M6U_IdentityServiceAPI_bus_xofyyyfpkikqdfyrbdqazkosd6?temporary=true"
2025-05-05 13:25:41.810 +05:30 [DBG] Disconnect: guest@localhost:5672/
2025-05-05 13:25:41.816 +05:30 [DBG] Disconnected: guest@localhost:5672/
2025-05-05 13:25:41.819 +05:30 [INF] Bus stopped: "rabbitmq://localhost/"
2025-05-05 13:26:00.188 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'AuditLog'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-05 13:26:00.284 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'PasswordResetToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-05 13:26:00.287 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserBranch'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-05 13:26:00.290 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserRole'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-05 13:26:00.294 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserSubscription'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-05 13:26:01.266 +05:30 [INF] Executed DbCommand (67ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-05-05 13:26:01.306 +05:30 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-05-05 13:26:01.418 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-05-05 13:26:01.504 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-05-05 13:26:01.508 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-05-05 13:26:01.523 +05:30 [INF] No migrations were applied. The database is already up to date.
2025-05-05 13:26:01.552 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-05 13:26:01.755 +05:30 [INF] Configured endpoint SubscriptionCreated, Consumer: IdentityService.Infrastructure.Messaging.Consumers.SubscriptionCreatedConsumer
2025-05-05 13:26:01.760 +05:30 [INF] Configured endpoint SubscriptionStatusChanged, Consumer: IdentityService.Infrastructure.Messaging.Consumers.SubscriptionStatusChangedConsumer
2025-05-05 13:26:01.961 +05:30 [DBG] Starting bus instances: IBus
2025-05-05 13:26:01.966 +05:30 [DBG] Starting bus: "rabbitmq://localhost/"
2025-05-05 13:26:02.022 +05:30 [DBG] Connect: guest@localhost:5672/
2025-05-05 13:26:02.065 +05:30 [INF] Now listening on: http://localhost:5263
2025-05-05 13:26:02.073 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-05-05 13:26:02.075 +05:30 [INF] Hosting environment: Development
2025-05-05 13:26:02.077 +05:30 [INF] Content root path: E:\Shri\Automobiles\VMS-API\IdentityService\src\IdentityService.API
2025-05-05 13:26:02.108 +05:30 [DBG] Connected: guest@localhost:5672/ (address: amqp://localhost:5672, local: 51408)
2025-05-05 13:26:02.146 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/DESKTOPI7D4M6U_IdentityServiceAPI_bus_do4yyyfpkikqn698bdqazk18d7?temporary=true"
2025-05-05 13:26:02.172 +05:30 [DBG] Declare queue: name: SubscriptionCreated, durable, consumer-count: 0 message-count: 0
2025-05-05 13:26:02.172 +05:30 [DBG] Declare queue: name: SubscriptionStatusChanged, durable, consumer-count: 0 message-count: 0
2025-05-05 13:26:02.199 +05:30 [DBG] Declare exchange: name: SubscriptionStatusChanged, type: fanout, durable
2025-05-05 13:26:02.199 +05:30 [DBG] Declare exchange: name: SubscriptionCreated, type: fanout, durable
2025-05-05 13:26:02.210 +05:30 [DBG] Declare exchange: name: VMSContracts.Subscription.Events:SubscriptionStatusChanged, type: fanout, durable
2025-05-05 13:26:02.210 +05:30 [DBG] Declare exchange: name: VMSContracts.Subscription.Events:SubscriptionCreated, type: fanout, durable
2025-05-05 13:26:02.225 +05:30 [DBG] Bind queue: source: SubscriptionStatusChanged, destination: SubscriptionStatusChanged
2025-05-05 13:26:02.225 +05:30 [DBG] Bind queue: source: SubscriptionCreated, destination: SubscriptionCreated
2025-05-05 13:26:02.261 +05:30 [DBG] Bind exchange: source: VMSContracts.Subscription.Events:SubscriptionCreated, destination: SubscriptionCreated
2025-05-05 13:26:02.261 +05:30 [DBG] Bind exchange: source: VMSContracts.Subscription.Events:SubscriptionStatusChanged, destination: SubscriptionStatusChanged
2025-05-05 13:26:02.312 +05:30 [DBG] Consumer Ok: "rabbitmq://localhost/SubscriptionCreated" - amq.ctag-Kpx_RKktsj07FTfKSK-8iQ
2025-05-05 13:26:02.312 +05:30 [DBG] Consumer Ok: "rabbitmq://localhost/SubscriptionStatusChanged" - amq.ctag-TomIwDYFsgJBJ0fCP89S6g
2025-05-05 13:26:02.317 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/SubscriptionCreated"
2025-05-05 13:26:02.318 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/SubscriptionStatusChanged"
2025-05-05 13:26:02.326 +05:30 [INF] Bus started: "rabbitmq://localhost/"
2025-05-05 13:26:11.485 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5263/api/Permissions - application/json 95
2025-05-05 13:26:11.544 +05:30 [WRN] Failed to determine the https port for redirect.
2025-05-05 13:26:11.568 +05:30 [INF] Token received and is being processed
2025-05-05 13:26:11.603 +05:30 [WRN] User is null or not authenticated
2025-05-05 13:26:11.605 +05:30 [WRN] No user ID found in token
2025-05-05 13:26:11.610 +05:30 [INF] Executing endpoint 'IdentityService.API.Controllers.PermissionsController.CreatePermission (IdentityService.API)'
2025-05-05 13:26:11.640 +05:30 [INF] Route matched with {action = "CreatePermission", controller = "Permissions"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[IdentityService.Application.DTOs.Permissions.PermissionResponse]] CreatePermission(IdentityService.Application.DTOs.Permissions.CreatePermissionRequest) on controller IdentityService.API.Controllers.PermissionsController (IdentityService.API).
2025-05-05 13:26:11.709 +05:30 [INF] Request of type CreatePermissionCommand is not a BaseRequest, skipping UserId propagation
2025-05-05 13:26:11.714 +05:30 [INF] Validating request of type CreatePermissionCommand
2025-05-05 13:26:11.719 +05:30 [INF] Request content: {
  "Request": {
    "Name": "Create",
    "Description": "Create",
    "Resource": "Create",
    "Action": "Create"
  }
}
2025-05-05 13:26:11.721 +05:30 [INF] Request does not have a UserId property
2025-05-05 13:26:11.724 +05:30 [INF] Validation passed for request of type CreatePermissionCommand
2025-05-05 13:26:11.731 +05:30 [WRN] User is null or not authenticated
2025-05-05 13:26:11.965 +05:30 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
2025-05-05 13:26:12.072 +05:30 [WRN] Invalid operation during permission creation
System.InvalidOperationException: Permission with name 'Create' already exists
   at IdentityService.Application.Features.Permissions.Commands.CreatePermissionCommandHandler.Handle(CreatePermissionCommand command, CancellationToken cancellationToken) in E:\Shri\Automobiles\VMS-API\IdentityService\src\IdentityService.Application\Features\Permissions\Commands\CreatePermissionCommand.cs:line 41
   at IdentityService.Application.Behaviors.ValidationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in E:\Shri\Automobiles\VMS-API\IdentityService\src\IdentityService.Application\Behaviors\ValidationBehavior.cs:line 95
   at IdentityService.Application.Behaviors.UserIdPropagationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in E:\Shri\Automobiles\VMS-API\IdentityService\src\IdentityService.Application\Behaviors\UserIdPropagationBehavior.cs:line 39
   at MediatR.Pipeline.RequestExceptionProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestExceptionProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestExceptionActionProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestExceptionActionProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestPostProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestPreProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at IdentityService.API.Controllers.PermissionsController.CreatePermission(CreatePermissionRequest request) in E:\Shri\Automobiles\VMS-API\IdentityService\src\IdentityService.API\Controllers\PermissionsController.cs:line 74
2025-05-05 13:26:12.101 +05:30 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-05-05 13:26:12.117 +05:30 [INF] Executed action IdentityService.API.Controllers.PermissionsController.CreatePermission (IdentityService.API) in 468.131ms
2025-05-05 13:26:12.122 +05:30 [INF] Executed endpoint 'IdentityService.API.Controllers.PermissionsController.CreatePermission (IdentityService.API)'
2025-05-05 13:26:12.131 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5263/api/Permissions - 400 null application/json; charset=utf-8 647.3565ms
2025-05-05 13:28:19.191 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5263/api/Roles - application/json 116
2025-05-05 13:28:19.225 +05:30 [INF] Token received and is being processed
2025-05-05 13:28:19.301 +05:30 [WRN] User is null or not authenticated
2025-05-05 13:28:19.302 +05:30 [WRN] No user ID found in token
2025-05-05 13:28:19.303 +05:30 [INF] Executing endpoint 'IdentityService.API.Controllers.RolesController.CreateRole (IdentityService.API)'
2025-05-05 13:28:19.308 +05:30 [INF] Route matched with {action = "CreateRole", controller = "Roles"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[IdentityService.Application.DTOs.Roles.RoleResponse]] CreateRole(IdentityService.Application.DTOs.Roles.CreateRoleRequest) on controller IdentityService.API.Controllers.RolesController (IdentityService.API).
2025-05-05 13:28:19.333 +05:30 [WRN] User ID not found in HttpContext.Items
2025-05-05 13:28:19.335 +05:30 [INF] Setting UserId "00000000-0000-0000-0000-000000000000" on request of type CreateRoleCommand
2025-05-05 13:28:19.337 +05:30 [WRN] User ID not found in HttpContext.Items
2025-05-05 13:28:19.338 +05:30 [INF] Validating request of type CreateRoleCommand
2025-05-05 13:28:19.349 +05:30 [INF] Request content: {
  "Request": {
    "Name": "Admin",
    "Description": "Admin",
    "PermissionIds": [
      "4519a6bc-8648-4fde-b6f1-aae923418b87"
    ]
  },
  "UserId": "00000000-0000-0000-0000-000000000000"
}
2025-05-05 13:28:19.352 +05:30 [INF] Request UserId property value: "00000000-0000-0000-0000-000000000000"
2025-05-05 13:28:19.353 +05:30 [WRN] UserId is empty GUID (00000000-0000-0000-0000-000000000000)
2025-05-05 13:28:19.354 +05:30 [INF] Validation passed for request of type CreateRoleCommand
2025-05-05 13:28:19.357 +05:30 [WRN] User is null or not authenticated
2025-05-05 13:28:19.497 +05:30 [INF] Executed DbCommand (8ms) [Parameters=[@__get_Item_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Id" = @__get_Item_0
LIMIT 1
2025-05-05 13:28:19.646 +05:30 [WRN] User is null or not authenticated
2025-05-05 13:28:19.647 +05:30 [WRN] User is null or not authenticated
2025-05-05 13:28:19.757 +05:30 [INF] Executed DbCommand (10ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = DateTime), @p2='?', @p3='?', @p4='?' (DbType = Boolean), @p5='?', @p6='?' (DbType = Binary), @p7='?' (DbType = DateTime), @p8='?', @p9='?' (DbType = Guid), @p10='?' (DbType = DateTime), @p11='?', @p12='?' (DbType = Boolean), @p13='?' (DbType = Guid), @p14='?' (DbType = Guid), @p15='?' (DbType = Binary), @p16='?' (DbType = DateTime), @p17='?'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Roles" ("Id", "CreatedAt", "CreatedBy", "Description", "IsDeleted", "Name", "RowVersion", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8);
INSERT INTO "RolePermissions" ("Id", "CreatedAt", "CreatedBy", "IsDeleted", "PermissionId", "RoleId", "RowVersion", "UpdatedAt", "UpdatedBy")
VALUES (@p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17);
2025-05-05 13:28:19.787 +05:30 [INF] Executing CreatedAtActionResult, writing value of type 'IdentityService.Application.DTOs.Roles.RoleResponse'.
2025-05-05 13:28:19.821 +05:30 [INF] Executed action IdentityService.API.Controllers.RolesController.CreateRole (IdentityService.API) in 506.7955ms
2025-05-05 13:28:19.823 +05:30 [INF] Executed endpoint 'IdentityService.API.Controllers.RolesController.CreateRole (IdentityService.API)'
2025-05-05 13:28:19.825 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5263/api/Roles - 201 null application/json; charset=utf-8 634.28ms
2025-05-05 13:31:42.681 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5263/api/Auth/login - application/json 143
2025-05-05 13:31:42.686 +05:30 [INF] Token received and is being processed
2025-05-05 13:31:42.695 +05:30 [WRN] User is null or not authenticated
2025-05-05 13:31:42.697 +05:30 [WRN] No user ID found in token
2025-05-05 13:31:42.702 +05:30 [INF] Executing endpoint 'IdentityService.API.Controllers.AuthController.Login (IdentityService.API)'
2025-05-05 13:31:42.713 +05:30 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[IdentityService.Application.DTOs.Auth.LoginResponse]] Login(IdentityService.Application.DTOs.Auth.LoginRequest) on controller IdentityService.API.Controllers.AuthController (IdentityService.API).
2025-05-05 13:31:42.762 +05:30 [WRN] User ID not found in HttpContext.Items
2025-05-05 13:31:42.764 +05:30 [INF] Setting UserId "00000000-0000-0000-0000-000000000000" on request of type LoginCommand
2025-05-05 13:31:42.766 +05:30 [WRN] User ID not found in HttpContext.Items
2025-05-05 13:31:42.770 +05:30 [INF] Validating request of type LoginCommand
2025-05-05 13:31:42.774 +05:30 [INF] Request content: {
  "Request": {
    "Email": "<EMAIL>",
    "Password": "Admin@123",
    "PhoneNumber": "8088020878",
    "Otp": "1234",
    "RememberMe": true,
    "IsEmailLogin": true,
    "IsPhoneLogin": true
  },
  "UserId": "00000000-0000-0000-0000-000000000000"
}
2025-05-05 13:31:42.776 +05:30 [INF] Request UserId property value: "00000000-0000-0000-0000-000000000000"
2025-05-05 13:31:42.777 +05:30 [WRN] UserId is empty GUID (00000000-0000-0000-0000-000000000000)
2025-05-05 13:31:42.795 +05:30 [INF] Validation passed for request of type LoginCommand
2025-05-05 13:31:42.895 +05:30 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-05-05 13:31:43.011 +05:30 [INF] Executed DbCommand (10ms) [Parameters=[@__email_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."PasswordHash", t0."Id", t0."CreatedAt", t0."CreatedBy", t0."IsDeleted", t0."RoleId", t0."RowVersion", t0."UpdatedAt", t0."UpdatedBy", t0."UserId", t0."Id0", t0."CreatedAt0", t0."CreatedBy0", t0."Description", t0."IsDeleted0", t0."Name", t0."RowVersion0", t0."UpdatedAt0", t0."UpdatedBy0", t0."Id1", t0."CreatedAt1", t0."CreatedBy1", t0."IsDeleted1", t0."PermissionId", t0."RoleId0", t0."RowVersion1", t0."UpdatedAt1", t0."UpdatedBy1", t0."Id00", t0."Action", t0."CreatedAt00", t0."CreatedBy00", t0."Description0", t0."IsDeleted00", t0."Name0", t0."Resource", t0."RowVersion00", t0."UpdatedAt00", t0."UpdatedBy00"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."Email", u."EmailVerified", u."IsDeleted", u."LastLoginAt", u."PhoneNumber", u."PhoneNumberVerified", u."RefreshToken", u."RefreshTokenExpiryTime", u."RememberMe", u."RowVersion", u."SecurityStamp", u."UpdatedAt", u."UpdatedBy", u."PasswordHash"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__email_0
    LIMIT 1
) AS t
LEFT JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."IsDeleted", u0."RoleId", u0."RowVersion", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", r."Id" AS "Id0", r."CreatedAt" AS "CreatedAt0", r."CreatedBy" AS "CreatedBy0", r."Description", r."IsDeleted" AS "IsDeleted0", r."Name", r."RowVersion" AS "RowVersion0", r."UpdatedAt" AS "UpdatedAt0", r."UpdatedBy" AS "UpdatedBy0", t1."Id" AS "Id1", t1."CreatedAt" AS "CreatedAt1", t1."CreatedBy" AS "CreatedBy1", t1."IsDeleted" AS "IsDeleted1", t1."PermissionId", t1."RoleId" AS "RoleId0", t1."RowVersion" AS "RowVersion1", t1."UpdatedAt" AS "UpdatedAt1", t1."UpdatedBy" AS "UpdatedBy1", t1."Id0" AS "Id00", t1."Action", t1."CreatedAt0" AS "CreatedAt00", t1."CreatedBy0" AS "CreatedBy00", t1."Description" AS "Description0", t1."IsDeleted0" AS "IsDeleted00", t1."Name" AS "Name0", t1."Resource", t1."RowVersion0" AS "RowVersion00", t1."UpdatedAt0" AS "UpdatedAt00", t1."UpdatedBy0" AS "UpdatedBy00"
    FROM "UserRoles" AS u0
    INNER JOIN "Roles" AS r ON u0."RoleId" = r."Id"
    LEFT JOIN (
        SELECT r0."Id", r0."CreatedAt", r0."CreatedBy", r0."IsDeleted", r0."PermissionId", r0."RoleId", r0."RowVersion", r0."UpdatedAt", r0."UpdatedBy", p."Id" AS "Id0", p."Action", p."CreatedAt" AS "CreatedAt0", p."CreatedBy" AS "CreatedBy0", p."Description", p."IsDeleted" AS "IsDeleted0", p."Name", p."Resource", p."RowVersion" AS "RowVersion0", p."UpdatedAt" AS "UpdatedAt0", p."UpdatedBy" AS "UpdatedBy0"
        FROM "RolePermissions" AS r0
        INNER JOIN "Permissions" AS p ON r0."PermissionId" = p."Id"
    ) AS t1 ON r."Id" = t1."RoleId"
) AS t0 ON t."Id" = t0."UserId"
ORDER BY t."Id", t0."Id", t0."Id0", t0."Id1"
2025-05-05 13:31:43.243 +05:30 [WRN] User is null or not authenticated
2025-05-05 13:31:43.259 +05:30 [INF] Executed DbCommand (5ms) [Parameters=[@p6='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?', @p2='?' (DbType = DateTime), @p3='?' (DbType = Boolean), @p7='?' (DbType = Binary), @p4='?' (DbType = DateTime), @p5='?'], CommandType='"Text"', CommandTimeout='30']
UPDATE "Users" SET "LastLoginAt" = @p0, "RefreshToken" = @p1, "RefreshTokenExpiryTime" = @p2, "RememberMe" = @p3, "UpdatedAt" = @p4, "UpdatedBy" = @p5
WHERE "Id" = @p6 AND "RowVersion" = @p7
RETURNING "RowVersion";
2025-05-05 13:31:43.297 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."Description", r."IsDeleted", r."Name", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "UserRoles" AS u
INNER JOIN "Roles" AS r ON u."RoleId" = r."Id"
WHERE u."UserId" = @__userId_0
2025-05-05 13:31:43.310 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."Description", r."IsDeleted", r."Name", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "UserRoles" AS u
INNER JOIN "Roles" AS r ON u."RoleId" = r."Id"
WHERE u."UserId" = @__userId_0
2025-05-05 13:31:43.314 +05:30 [INF] Executing OkObjectResult, writing value of type 'IdentityService.Application.DTOs.Auth.LoginResponse'.
2025-05-05 13:31:43.324 +05:30 [INF] Executed action IdentityService.API.Controllers.AuthController.Login (IdentityService.API) in 605.9585ms
2025-05-05 13:31:43.326 +05:30 [INF] Executed endpoint 'IdentityService.API.Controllers.AuthController.Login (IdentityService.API)'
2025-05-05 13:31:43.327 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5263/api/Auth/login - 200 null application/json; charset=utf-8 646.4571ms
2025-05-05 13:35:28.805 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/api/Subscriptions/active - null null
2025-05-05 13:35:28.812 +05:30 [INF] Token received and is being processed
2025-05-05 13:35:28.862 +05:30 [INF] Token validated successfully with claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: fb5f1ac4-b4c4-4f61-b0a5-bec888ca00c3, security_stamp: 638820282925527280, permission: test.permission, nbf: 1746432103, exp: 1746435703, iss: identity-service, aud: vms-api
2025-05-05 13:35:28.864 +05:30 [INF] NameIdentifier claim found: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e
2025-05-05 13:35:28.895 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__get_Item_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."Email", u."EmailVerified", u."IsDeleted", u."LastLoginAt", u."PhoneNumber", u."PhoneNumberVerified", u."RefreshToken", u."RefreshTokenExpiryTime", u."RememberMe", u."RowVersion", u."SecurityStamp", u."UpdatedAt", u."UpdatedBy", u."PasswordHash"
FROM "Users" AS u
WHERE NOT (u."IsDeleted") AND u."Id" = @__get_Item_0
LIMIT 1
2025-05-05 13:35:28.903 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: fb5f1ac4-b4c4-4f61-b0a5-bec888ca00c3, security_stamp: 638820282925527280, permission: test.permission, nbf: 1746432103, exp: 1746435703, iss: identity-service, aud: vms-api
2025-05-05 13:35:28.906 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-05 13:35:28.907 +05:30 [INF] User ID from token: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-05 13:35:28.909 +05:30 [INF] Executing endpoint 'IdentityService.API.Controllers.SubscriptionsController.GetActiveSubscription (IdentityService.API)'
2025-05-05 13:35:28.913 +05:30 [INF] Route matched with {action = "GetActiveSubscription", controller = "Subscriptions"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[IdentityService.Application.DTOs.Subscriptions.UserSubscriptionResponse]] GetActiveSubscription() on controller IdentityService.API.Controllers.SubscriptionsController (IdentityService.API).
2025-05-05 13:35:28.918 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: fb5f1ac4-b4c4-4f61-b0a5-bec888ca00c3, security_stamp: 638820282925527280, permission: test.permission, nbf: 1746432103, exp: 1746435703, iss: identity-service, aud: vms-api
2025-05-05 13:35:28.920 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-05 13:35:28.923 +05:30 [INF] Retrieved user ID from HttpContext.Items: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-05 13:35:28.925 +05:30 [INF] Setting UserId "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e" on request of type GetActiveSubscriptionQuery
2025-05-05 13:35:28.927 +05:30 [INF] Retrieved user ID from HttpContext.Items: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-05 13:35:28.929 +05:30 [INF] Validating request of type GetActiveSubscriptionQuery
2025-05-05 13:35:28.935 +05:30 [INF] Request content: {
  "UserId": "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
}
2025-05-05 13:35:28.937 +05:30 [INF] Request UserId property value: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-05 13:35:28.938 +05:30 [INF] Validation passed for request of type GetActiveSubscriptionQuery
2025-05-05 13:35:28.964 +05:30 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-05-05 13:35:28.977 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."PasswordHash", t0."Id", t0."CreatedAt", t0."CreatedBy", t0."EndDate", t0."IsDeleted", t0."PlanName", t0."RowVersion", t0."StartDate", t0."Status", t0."SubscriptionId", t0."Tier", t0."TrialEndDate", t0."UpdatedAt", t0."UpdatedBy", t0."UserId", t0."Id0", t0."CreatedAt0", t0."CreatedBy0", t0."FeatureName", t0."IsDeleted0", t0."IsEnabled", t0."RowVersion0", t0."UpdatedAt0", t0."UpdatedBy0", t0."UsageLimit", t0."UserSubscriptionId"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."Email", u."EmailVerified", u."IsDeleted", u."LastLoginAt", u."PhoneNumber", u."PhoneNumberVerified", u."RefreshToken", u."RefreshTokenExpiryTime", u."RememberMe", u."RowVersion", u."SecurityStamp", u."UpdatedAt", u."UpdatedBy", u."PasswordHash"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Id" = @__userId_0
    LIMIT 1
) AS t
LEFT JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."EndDate", u0."IsDeleted", u0."PlanName", u0."RowVersion", u0."StartDate", u0."Status", u0."SubscriptionId", u0."Tier", u0."TrialEndDate", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", s."Id" AS "Id0", s."CreatedAt" AS "CreatedAt0", s."CreatedBy" AS "CreatedBy0", s."FeatureName", s."IsDeleted" AS "IsDeleted0", s."IsEnabled", s."RowVersion" AS "RowVersion0", s."UpdatedAt" AS "UpdatedAt0", s."UpdatedBy" AS "UpdatedBy0", s."UsageLimit", s."UserSubscriptionId"
    FROM "UserSubscriptions" AS u0
    LEFT JOIN "SubscriptionFeatures" AS s ON u0."Id" = s."UserSubscriptionId"
) AS t0 ON t."Id" = t0."UserId"
ORDER BY t."Id", t0."Id"
2025-05-05 13:35:28.984 +05:30 [INF] Executing NotFoundObjectResult, writing value of type '<>f__AnonymousType0`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-05-05 13:35:28.987 +05:30 [INF] Executed action IdentityService.API.Controllers.SubscriptionsController.GetActiveSubscription (IdentityService.API) in 71.8603ms
2025-05-05 13:35:28.991 +05:30 [INF] Executed endpoint 'IdentityService.API.Controllers.SubscriptionsController.GetActiveSubscription (IdentityService.API)'
2025-05-05 13:35:28.992 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/api/Subscriptions/active - 404 null application/json; charset=utf-8 186.7209ms
2025-05-05 13:35:39.556 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/api/Subscriptions - null null
2025-05-05 13:35:39.559 +05:30 [INF] Token received and is being processed
2025-05-05 13:35:39.561 +05:30 [INF] Token validated successfully with claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: fb5f1ac4-b4c4-4f61-b0a5-bec888ca00c3, security_stamp: 638820282925527280, permission: test.permission, nbf: 1746432103, exp: 1746435703, iss: identity-service, aud: vms-api
2025-05-05 13:35:39.564 +05:30 [INF] NameIdentifier claim found: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e
2025-05-05 13:35:39.595 +05:30 [INF] Executed DbCommand (24ms) [Parameters=[@__get_Item_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."Email", u."EmailVerified", u."IsDeleted", u."LastLoginAt", u."PhoneNumber", u."PhoneNumberVerified", u."RefreshToken", u."RefreshTokenExpiryTime", u."RememberMe", u."RowVersion", u."SecurityStamp", u."UpdatedAt", u."UpdatedBy", u."PasswordHash"
FROM "Users" AS u
WHERE NOT (u."IsDeleted") AND u."Id" = @__get_Item_0
LIMIT 1
2025-05-05 13:35:39.609 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: fb5f1ac4-b4c4-4f61-b0a5-bec888ca00c3, security_stamp: 638820282925527280, permission: test.permission, nbf: 1746432103, exp: 1746435703, iss: identity-service, aud: vms-api
2025-05-05 13:35:39.611 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-05 13:35:39.612 +05:30 [INF] User ID from token: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-05 13:35:39.612 +05:30 [INF] Executing endpoint 'IdentityService.API.Controllers.SubscriptionsController.GetUserSubscriptions (IdentityService.API)'
2025-05-05 13:35:39.622 +05:30 [INF] Route matched with {action = "GetUserSubscriptions", controller = "Subscriptions"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.List`1[IdentityService.Application.DTOs.Subscriptions.UserSubscriptionResponse]]] GetUserSubscriptions() on controller IdentityService.API.Controllers.SubscriptionsController (IdentityService.API).
2025-05-05 13:35:39.626 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: fb5f1ac4-b4c4-4f61-b0a5-bec888ca00c3, security_stamp: 638820282925527280, permission: test.permission, nbf: 1746432103, exp: 1746435703, iss: identity-service, aud: vms-api
2025-05-05 13:35:39.627 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-05 13:35:39.630 +05:30 [INF] Request of type GetUserSubscriptionsQuery is not a BaseRequest, skipping UserId propagation
2025-05-05 13:35:39.635 +05:30 [INF] Validating request of type GetUserSubscriptionsQuery
2025-05-05 13:35:39.637 +05:30 [INF] Request content: {
  "UserId": "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
}
2025-05-05 13:35:39.638 +05:30 [INF] Request UserId property value: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-05 13:35:39.640 +05:30 [INF] Validation passed for request of type GetUserSubscriptionsQuery
2025-05-05 13:35:39.660 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."EndDate", u."IsDeleted", u."PlanName", u."RowVersion", u."StartDate", u."Status", u."SubscriptionId", u."Tier", u."TrialEndDate", u."UpdatedAt", u."UpdatedBy", u."UserId", s."Id", s."CreatedAt", s."CreatedBy", s."FeatureName", s."IsDeleted", s."IsEnabled", s."RowVersion", s."UpdatedAt", s."UpdatedBy", s."UsageLimit", s."UserSubscriptionId"
FROM "UserSubscriptions" AS u
LEFT JOIN "SubscriptionFeatures" AS s ON u."Id" = s."UserSubscriptionId"
WHERE u."UserId" = @__userId_0
ORDER BY u."Id"
2025-05-05 13:35:39.668 +05:30 [INF] Executing OkObjectResult, writing value of type 'System.Collections.Generic.List`1[[IdentityService.Application.DTOs.Subscriptions.UserSubscriptionResponse, IdentityService.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-05 13:35:39.692 +05:30 [INF] Executed action IdentityService.API.Controllers.SubscriptionsController.GetUserSubscriptions (IdentityService.API) in 66.5523ms
2025-05-05 13:35:39.693 +05:30 [INF] Executed endpoint 'IdentityService.API.Controllers.SubscriptionsController.GetUserSubscriptions (IdentityService.API)'
2025-05-05 13:35:39.694 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/api/Subscriptions - 200 null application/json; charset=utf-8 138.9041ms
