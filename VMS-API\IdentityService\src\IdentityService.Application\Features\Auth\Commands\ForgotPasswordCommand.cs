using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using IdentityService.Application.Common;
using IdentityService.Application.DTOs.Auth;
using IdentityService.Application.Interfaces;
using IdentityService.Domain.Entities;
using IdentityService.Domain.Interfaces;
using MediatR;
using Microsoft.Extensions.Logging;

namespace IdentityService.Application.Features.Auth.Commands;

public class ForgotPasswordCommand : BaseRequest<bool>
{
    public ForgotPasswordRequest Request { get; set; }
}

public class ForgotPasswordCommandHandler : IRequestHandler<ForgotPasswordCommand, bool>
{
    private readonly IUserRepository _userRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<ForgotPasswordCommandHandler> _logger;
    private readonly IEmailService _emailService;

    public ForgotPasswordCommandHandler(
        IUserRepository userRepository,
        IUnitOfWork unitOfWork,
        ILogger<ForgotPasswordCommandHandler> logger,
        IEmailService emailService)
    {
        _userRepository = userRepository;
        _unitOfWork = unitOfWork;
        _logger = logger;
        _emailService = emailService;
    }

    public async Task<bool> Handle(ForgotPasswordCommand request, CancellationToken cancellationToken)
    {
        var user = await _userRepository.GetByEmailAsync(request.Request.Email);
        if (user == null)
        {
            // Return true to prevent email enumeration attacks
            return true;
        }

        // Note: Token cleanup will be done in the retry loop with fresh user data

        var token = Guid.NewGuid().ToString("N");
        var expiryTime = DateTime.UtcNow.AddHours(24);
        var createdBy = request.UserId.ToString() ?? "System";

        // Retry logic for concurrency conflicts
        const int maxRetries = 3;
        for (int attempt = 1; attempt <= maxRetries; attempt++)
        {
            try
            {
                // Clear any pending changes to avoid tracking conflicts
                _unitOfWork.ClearChangeTracker();

                // Reload user to get fresh entity with current RowVersion
                user = await _userRepository.GetByEmailAsync(request.Request.Email);
                if (user == null)
                {
                    _logger.LogWarning("User was deleted during password reset attempt for email {Email}", request.Request.Email);
                    return true;
                }

                // Clean up expired and used tokens
                var expiredTokensToRemove = user.PasswordResetTokens
                    .Where(t => t.IsUsed || t.ExpiryTime <= DateTime.UtcNow)
                    .ToList();

                foreach (var expiredToken in expiredTokensToRemove)
                {
                    user.PasswordResetTokens.Remove(expiredToken);
                }

                // Also clean up any tokens older than 7 days regardless of status
                var oldTokensToRemove = user.PasswordResetTokens
                    .Where(t => t.CreatedAt <= DateTime.UtcNow.AddDays(-7))
                    .ToList();

                foreach (var oldToken in oldTokensToRemove)
                {
                    user.PasswordResetTokens.Remove(oldToken);
                }

                // Check if there are any remaining active tokens (to prevent spam)
                var activeTokensToInvalidate = user.PasswordResetTokens
                    .Where(t => !t.IsUsed && t.ExpiryTime > DateTime.UtcNow)
                    .ToList();

                if (activeTokensToInvalidate.Count > 0)
                {
                    _logger.LogInformation("User {UserId} already has {ActiveTokenCount} active password reset tokens. Invalidating them.",
                        user.Id, activeTokensToInvalidate.Count);

                    // Mark existing active tokens as used to prevent abuse
                    foreach (var activeToken in activeTokensToInvalidate)
                    {
                        activeToken.MarkAsUsed();
                    }
                }

                _logger.LogInformation("Cleaned up {ExpiredCount} expired tokens and {OldCount} old tokens for user {UserId}",
                    expiredTokensToRemove.Count, oldTokensToRemove.Count, user.Id);

                // Create fresh token with current user entity
                var resetToken = PasswordResetToken.Create(token, expiryTime, user.Id, user, createdBy);
                user.PasswordResetTokens.Add(resetToken);

                await _unitOfWork.SaveChangesAsync(cancellationToken);

                var resetLink = $"https://your-app-url/reset-password?token={token}&email={user.Email}";
                await _emailService.SendPasswordResetEmailAsync(user.Email, resetLink);

                _logger.LogInformation("Password reset token generated for user {UserId}", user.Id);
                return true;
            }
            catch (Exception ex) when (ex is InvalidOperationException ||
                                        ex.GetType().Name == "DbUpdateConcurrencyException" ||
                                        ex.Message.Contains("concurrency") ||
                                        ex.Message.Contains("RowVersion") ||
                                        ex.Message.Contains("deleted by another user"))
            {
                // Handle any concurrency-related exceptions
                _logger.LogWarning(ex, "Concurrency conflict when generating password reset token for user {UserId}, attempt {Attempt}/{MaxRetries}",
                    user?.Id, attempt, maxRetries);

                if (attempt == maxRetries)
                {
                    // On final attempt, still return true to prevent email enumeration attacks
                    _logger.LogError(ex, "Failed to generate password reset token after {MaxRetries} attempts for user {UserId}",
                        maxRetries, user?.Id);
                    return true;
                }

                // Wait a short time before retry to reduce contention
                await Task.Delay(100 * attempt, cancellationToken);

                // The next iteration will reload the user and clear tracking
            }
        }

        // This should never be reached due to the retry logic, but included for completeness
        return true;
    }
}