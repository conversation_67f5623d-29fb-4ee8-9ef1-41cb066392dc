using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using MediatR;
using IdentityService.Application.Features.Users.Commands;
using IdentityService.Application.Features.Users.Queries;
using IdentityService.Application.DTOs.Users;
using Microsoft.Extensions.Logging;

namespace IdentityService.API.Controllers;

/// <summary>
/// Controller for managing user-specific permissions
/// </summary>
[ApiController]
[Route("api/users/{userId:guid}/permissions")]
[Authorize]
public class UserPermissionsController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<UserPermissionsController> _logger;

    public UserPermissionsController(
        IMediator mediator,
        ILogger<UserPermissionsController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Assign a permission to a user
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="request">Permission assignment request</param>
    /// <returns>User permission response</returns>
    [HttpPost]
    [ProducesResponseType(typeof(UserPermissionResponse), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(401)]
    [ProducesResponseType(403)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<UserPermissionResponse>> AssignPermission(
        Guid userId,
        [FromBody] AssignUserPermissionRequest request)
    {
        try
        {
            _logger.LogInformation("Assigning permission {PermissionId} to user {UserId}", 
                request.PermissionId, userId);

            var command = new AssignUserPermissionCommand
            {
                UserId = userId,
                Request = request
            };

            var result = await _mediator.Send(command);
            
            _logger.LogInformation("Successfully assigned permission {PermissionId} to user {UserId}", 
                request.PermissionId, userId);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assigning permission {PermissionId} to user {UserId}", 
                request.PermissionId, userId);
            throw;
        }
    }

    /// <summary>
    /// Get user permission analysis
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <returns>User permission analysis</returns>
    [HttpGet("analysis")]
    [ProducesResponseType(typeof(UserPermissionAnalysisResponse), 200)]
    [ProducesResponseType(401)]
    [ProducesResponseType(403)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<UserPermissionAnalysisResponse>> GetPermissionAnalysis(Guid userId)
    {
        try
        {
            _logger.LogInformation("Getting permission analysis for user {UserId}", userId);

            var query = new GetUserPermissionAnalysisQuery { UserId = userId };
            var result = await _mediator.Send(query);

            _logger.LogInformation("Successfully retrieved permission analysis for user {UserId}", userId);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting permission analysis for user {UserId}", userId);
            throw;
        }
    }

    /// <summary>
    /// Revoke a user permission
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="permissionId">Permission ID</param>
    /// <returns>Success response</returns>
    [HttpDelete("{permissionId:guid}")]
    [ProducesResponseType(200)]
    [ProducesResponseType(401)]
    [ProducesResponseType(403)]
    [ProducesResponseType(404)]
    public async Task<ActionResult> RevokePermission(Guid userId, Guid permissionId)
    {
        try
        {
            _logger.LogInformation("Revoking permission {PermissionId} from user {UserId}", 
                permissionId, userId);

            var command = new RevokeUserPermissionCommand
            {
                UserId = userId,
                PermissionId = permissionId
            };

            await _mediator.Send(command);
            
            _logger.LogInformation("Successfully revoked permission {PermissionId} from user {UserId}", 
                permissionId, userId);

            return Ok(new { Message = "Permission revoked successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error revoking permission {PermissionId} from user {UserId}", 
                permissionId, userId);
            throw;
        }
    }

    /// <summary>
    /// Update user permission (extend expiration, change type, etc.)
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="permissionId">Permission ID</param>
    /// <param name="request">Update request</param>
    /// <returns>Updated user permission response</returns>
    [HttpPut("{permissionId:guid}")]
    [ProducesResponseType(typeof(UserPermissionResponse), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(401)]
    [ProducesResponseType(403)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<UserPermissionResponse>> UpdatePermission(
        Guid userId,
        Guid permissionId,
        [FromBody] UpdateUserPermissionRequest request)
    {
        try
        {
            _logger.LogInformation("Updating permission {PermissionId} for user {UserId}", 
                permissionId, userId);

            var command = new UpdateUserPermissionCommand
            {
                UserId = userId,
                PermissionId = permissionId,
                Request = request
            };

            var result = await _mediator.Send(command);
            
            _logger.LogInformation("Successfully updated permission {PermissionId} for user {UserId}", 
                permissionId, userId);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating permission {PermissionId} for user {UserId}", 
                permissionId, userId);
            throw;
        }
    }
}
