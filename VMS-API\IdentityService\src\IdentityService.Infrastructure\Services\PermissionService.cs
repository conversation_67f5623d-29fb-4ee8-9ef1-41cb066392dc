using IdentityService.Domain.Entities;
using IdentityService.Domain.Enums;
using IdentityService.Domain.Interfaces;
using IdentityService.Domain.Services;
using Microsoft.Extensions.Logging;

namespace IdentityService.Infrastructure.Services;

/// <summary>
/// Implementation of advanced permission resolution service
/// </summary>
public class PermissionService : IPermissionService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<PermissionService> _logger;

    public PermissionService(IUnitOfWork unitOfWork, ILogger<PermissionService> logger)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    /// <summary>
    /// Resolves effective permissions with precedence rules:
    /// 1. Direct DENY permissions override everything
    /// 2. Direct GRANT permissions override role permissions
    /// 3. Role permissions are the baseline
    /// </summary>
    public async Task<List<Permission>> GetEffectivePermissionsAsync(Guid userId)
    {
        try
        {
            var user = await _unitOfWork.UserRepository.GetByIdAsync(userId);
            if (user == null)
            {
                _logger.LogWarning("User {UserId} not found", userId);
                return new List<Permission>();
            }

            // Get role-based permissions
            var rolePermissions = await GetRoleBasedPermissionsAsync(userId);
            var effectivePermissions = new List<Permission>(rolePermissions);

            // Get user-specific permissions
            var userPermissions = await GetUserSpecificPermissionsAsync(userId);
            var activeUserPermissions = userPermissions.Where(up => up.IsCurrentlyActive()).ToList();

            // Apply user-specific permissions with precedence rules
            foreach (var userPermission in activeUserPermissions)
            {
                var permission = userPermission.Permission;
                
                if (userPermission.Type == PermissionType.Grant)
                {
                    // Add if not already present from roles
                    if (!effectivePermissions.Any(p => p.Id == permission.Id))
                    {
                        effectivePermissions.Add(permission);
                    }
                }
                else if (userPermission.Type == PermissionType.Deny)
                {
                    // Remove if present (DENY overrides everything)
                    effectivePermissions.RemoveAll(p => p.Id == permission.Id);
                }
            }

            _logger.LogDebug("Resolved {Count} effective permissions for user {UserId}", 
                effectivePermissions.Count, userId);

            return effectivePermissions.Distinct().ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resolving effective permissions for user {UserId}", userId);
            throw;
        }
    }

    public async Task<bool> HasPermissionAsync(Guid userId, string permissionName)
    {
        try
        {
            // First check for explicit DENY (highest precedence)
            var userPermissions = await GetUserSpecificPermissionsAsync(userId);
            var denyPermission = userPermissions.FirstOrDefault(up => 
                up.Permission.Name == permissionName && 
                up.DeniesAccess());

            if (denyPermission != null)
            {
                _logger.LogDebug("User {UserId} explicitly denied permission {Permission}", userId, permissionName);
                return false;
            }

            // Check for explicit GRANT
            var grantPermission = userPermissions.FirstOrDefault(up => 
                up.Permission.Name == permissionName && 
                up.GrantsAccess());

            if (grantPermission != null)
            {
                _logger.LogDebug("User {UserId} explicitly granted permission {Permission}", userId, permissionName);
                return true;
            }

            // Fall back to role-based permissions
            var rolePermissions = await GetRoleBasedPermissionsAsync(userId);
            var hasRolePermission = rolePermissions.Any(p => p.Name == permissionName);

            _logger.LogDebug("User {UserId} has role-based permission {Permission}: {HasPermission}", 
                userId, permissionName, hasRolePermission);

            return hasRolePermission;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking permission {Permission} for user {UserId}", permissionName, userId);
            return false;
        }
    }

    public async Task<List<Permission>> GetRoleBasedPermissionsAsync(Guid userId)
    {
        return await _unitOfWork.UserRepository.GetUserPermissionsAsync(userId);
    }

    public async Task<List<UserPermission>> GetUserSpecificPermissionsAsync(Guid userId)
    {
        var user = await _unitOfWork.UserRepository.GetByIdAsync(userId);
        return user?.UserPermissions ?? new List<UserPermission>();
    }

    public async Task AssignUserPermissionAsync(Guid userId, Guid permissionId, PermissionType type, 
        DateTime? expiresAt, string? reason, string assignedBy)
    {
        try
        {
            var user = await _unitOfWork.UserRepository.GetByIdAsync(userId);
            if (user == null)
                throw new InvalidOperationException($"User {userId} not found");

            var permission = await _unitOfWork.PermissionRepository.GetByIdAsync(permissionId);
            if (permission == null)
                throw new InvalidOperationException($"Permission {permissionId} not found");

            user.AddPermission(permission, type, expiresAt, reason, assignedBy);
            
            await _unitOfWork.UserRepository.UpdateAsync(user);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Assigned {Type} permission {Permission} to user {UserId} by {AssignedBy}", 
                type, permission.Name, userId, assignedBy);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assigning permission {PermissionId} to user {UserId}", permissionId, userId);
            throw;
        }
    }

    public async Task RemoveUserPermissionAsync(Guid userId, Guid permissionId)
    {
        try
        {
            var user = await _unitOfWork.UserRepository.GetByIdAsync(userId);
            if (user == null)
                throw new InvalidOperationException($"User {userId} not found");

            user.RemovePermission(permissionId);
            
            await _unitOfWork.UserRepository.UpdateAsync(user);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Removed user permission {PermissionId} from user {UserId}", permissionId, userId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing permission {PermissionId} from user {UserId}", permissionId, userId);
            throw;
        }
    }

    public async Task<PermissionAnalysis> GetPermissionAnalysisAsync(Guid userId)
    {
        try
        {
            var analysis = new PermissionAnalysis { UserId = userId };
            
            // Get role-based permissions
            var rolePermissions = await GetRoleBasedPermissionsAsync(userId);
            var userRoles = await _unitOfWork.UserRepository.GetUserRolesAsync(userId);
            
            foreach (var permission in rolePermissions)
            {
                analysis.Permissions.Add(new PermissionSource
                {
                    PermissionName = permission.Name,
                    Source = $"Role: {string.Join(", ", userRoles)}",
                    IsActive = true,
                    IsEffective = true
                });
            }

            // Get user-specific permissions
            var userPermissions = await GetUserSpecificPermissionsAsync(userId);
            
            foreach (var userPermission in userPermissions)
            {
                var isActive = userPermission.IsCurrentlyActive();
                var isEffective = isActive;
                
                // Check for conflicts
                var existingRolePermission = analysis.Permissions
                    .FirstOrDefault(p => p.PermissionName == userPermission.Permission.Name);
                
                if (existingRolePermission != null && isActive)
                {
                    analysis.ConflictingPermissions.Add(userPermission.Permission.Name);
                    
                    // Apply precedence rules
                    if (userPermission.Type == PermissionType.Deny)
                    {
                        existingRolePermission.IsEffective = false;
                    }
                }

                analysis.Permissions.Add(new PermissionSource
                {
                    PermissionName = userPermission.Permission.Name,
                    Source = "Direct",
                    Type = userPermission.Type,
                    ExpiresAt = userPermission.ExpiresAt,
                    IsActive = isActive,
                    IsEffective = isEffective
                });

                // Track expired permissions
                if (!isActive && userPermission.ExpiresAt.HasValue && userPermission.ExpiresAt < DateTime.UtcNow)
                {
                    analysis.ExpiredPermissions.Add(userPermission.Permission.Name);
                }
            }

            return analysis;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing permissions for user {UserId}", userId);
            throw;
        }
    }

    public async Task BulkAssignUserPermissionsAsync(Guid userId, List<UserPermissionAssignment> permissionAssignments)
    {
        try
        {
            var user = await _unitOfWork.UserRepository.GetByIdAsync(userId);
            if (user == null)
                throw new InvalidOperationException($"User {userId} not found");

            foreach (var assignment in permissionAssignments)
            {
                var permission = await _unitOfWork.PermissionRepository.GetByIdAsync(assignment.PermissionId);
                if (permission != null)
                {
                    user.AddPermission(permission, assignment.Type, assignment.ExpiresAt, assignment.Reason, "System");
                }
            }

            await _unitOfWork.UserRepository.UpdateAsync(user);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Bulk assigned {Count} permissions to user {UserId}", 
                permissionAssignments.Count, userId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error bulk assigning permissions to user {UserId}", userId);
            throw;
        }
    }

    public async Task CleanupExpiredPermissionsAsync()
    {
        try
        {
            // This would typically be implemented as a background service
            // For now, we'll just log the intent
            _logger.LogInformation("Cleanup expired permissions task executed");
            
            // Implementation would query for expired UserPermissions and mark them as inactive
            // or remove them based on business requirements
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cleaning up expired permissions");
            throw;
        }
    }
}
