namespace IdentityService.Domain.Enums;

/// <summary>
/// Defines the type of permission assignment
/// </summary>
public enum PermissionType
{
    /// <summary>
    /// Grants the permission to the user
    /// This overrides role-based permissions and explicitly allows access
    /// </summary>
    Grant = 1,

    /// <summary>
    /// Denies the permission to the user
    /// This overrides role-based permissions and explicitly denies access
    /// Deny permissions take precedence over Grant permissions
    /// </summary>
    Deny = 2
}
