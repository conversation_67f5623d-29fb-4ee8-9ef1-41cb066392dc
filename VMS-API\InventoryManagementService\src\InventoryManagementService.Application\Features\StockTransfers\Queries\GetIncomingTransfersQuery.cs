using AutoMapper;
using InventoryManagementService.Application.DTOs;
using InventoryManagementService.Domain.Interfaces;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using VMS.Contracts.Common.Authorization;

namespace InventoryManagementService.Application.Features.StockTransfers.Queries;

public class GetIncomingTransfersQuery : IRequest<List<StockTransferDto>>
{
    public Guid DestinationBranchId { get; set; }
}

public class GetIncomingTransfersQueryHandler : IRequestHandler<GetIncomingTransfersQuery, List<StockTransferDto>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly ILogger<GetIncomingTransfersQueryHandler> _logger;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public GetIncomingTransfersQueryHandler(
        IUnitOfWork unitOfWork,
        IMapper mapper,
        ILogger<GetIncomingTransfersQueryHandler> logger,
        IHttpContextAccessor httpContextAccessor)
    {
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _logger = logger;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<List<StockTransferDto>> Handle(GetIncomingTransfersQuery request, CancellationToken cancellationToken)
    {
        // Check permissions - Admin roles bypass, others need explicit permission
        var user = _httpContextAccessor.HttpContext?.User;
        if (!PermissionHelper.IsAdmin(user))
        {
            PermissionHelper.ValidateAccess(user, "Transfers.View");
        }

        _logger.LogInformation("Getting incoming transfers for branch: {DestinationBranchId}", request.DestinationBranchId);

        var transfers = await _unitOfWork.StockTransfers.GetByDestinationBranchAsync(request.DestinationBranchId);

        _logger.LogInformation("Found {Count} incoming transfers for branch {DestinationBranchId}", transfers.Count, request.DestinationBranchId);

        return _mapper.Map<List<StockTransferDto>>(transfers);
    }
}
