2025-06-10 11:34:38.955 +05:30 [INF] Database migration completed successfully
2025-06-10 11:34:39.233 +05:30 [INF] Starting inventory database seeding...
2025-06-10 11:34:39.679 +05:30 [INF] Database already contains data. Skipping seeding.
2025-06-10 11:34:39.695 +05:30 [INF] Database seeding completed successfully
2025-06-10 11:34:39.722 +05:30 [INF] Inventory Management Service starting up...
2025-06-10 11:34:40.003 +05:30 [ERR] Hosting failed to start
System.IO.IOException: Failed to bind to address http://127.0.0.1:5000: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: Only one usage of each socket address (protocol/network address/port) is normally permitted.
 ---> System.Net.Sockets.SocketException (10048): Only one usage of each socket address (protocol/network address/port) is normally permitted.
   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.DefaultAddressStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
2025-06-10 11:34:40.127 +05:30 [WRN] Failed to stop bus: "rabbitmq://localhost/DESKTOPI7D4M6U_InventoryManagementServiceAPI_bus_no1oyyfpkikqd9ambdq4x3fxfc?temporary=true" (Not Started)
2025-06-10 11:34:40.278 +05:30 [INF] Bus started: "rabbitmq://localhost/"
2025-06-10 12:47:06.238 +05:30 [INF] Starting database operations...
2025-06-10 12:47:06.573 +05:30 [INF] Checking database connection...
2025-06-10 12:47:09.832 +05:30 [WRN] Database is not accessible. Skipping migration and seeding.
2025-06-10 12:47:09.873 +05:30 [INF] Inventory Management Service starting up...
2025-06-10 12:47:09.891 +05:30 [INF] Service will be available at:
2025-06-10 12:47:09.927 +05:30 [INF]   HTTP:  http://localhost:5008
2025-06-10 12:47:09.941 +05:30 [INF]   HTTPS: https://localhost:7008
2025-06-10 12:47:09.964 +05:30 [INF]   Swagger: http://localhost:5008/swagger
2025-06-10 12:47:10.503 +05:30 [INF] Bus started: "rabbitmq://localhost/"
