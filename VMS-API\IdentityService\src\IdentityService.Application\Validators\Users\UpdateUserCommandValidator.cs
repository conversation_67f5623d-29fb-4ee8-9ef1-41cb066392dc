using FluentValidation;
using IdentityService.Application.Features.Users.Commands;

namespace IdentityService.Application.Validators.Users;

public class UpdateUserCommandValidator : BaseValidator<UpdateUserCommand>
{
    public UpdateUserCommandValidator()
    {
        RuleFor(x => x.UserId)
            .NotEmpty().WithMessage("User ID is required.");
            
        RuleFor(x => x.Request).NotNull().WithMessage("Request cannot be null.");
        
        When(x => x.Request != null, () =>
        {
            When(x => !string.IsNullOrEmpty(x.Request.Email), () =>
            {
                RuleFor(x => x.Request.Email)
                    .EmailAddress().WithMessage("A valid email address is required.")
                    .MaximumLength(256).WithMessage("Email cannot exceed 256 characters.");
            });
            
            When(x => !string.IsNullOrEmpty(x.Request.PhoneNumber), () =>
            {
                RuleFor(x => x.Request.PhoneNumber)
                    .Matches(@"^\+?[0-9\s\-\(\)]+$").WithMessage("A valid phone number is required.")
                    .MaximumLength(20).WithMessage("Phone number cannot exceed 20 characters.");
            });
        });
    }
}
