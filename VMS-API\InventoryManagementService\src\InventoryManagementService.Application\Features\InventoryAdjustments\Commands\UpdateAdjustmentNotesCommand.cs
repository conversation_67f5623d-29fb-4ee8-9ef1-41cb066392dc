using FluentValidation;
using InventoryManagementService.Domain.Interfaces;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using VMS.Contracts.Common.Authorization;

namespace InventoryManagementService.Application.Features.InventoryAdjustments.Commands;

public class UpdateAdjustmentNotesCommand : IRequest
{
    public Guid AdjustmentId { get; set; }
    public string Notes { get; set; } = string.Empty;
    public string UpdatedBy { get; set; } = string.Empty;
}

public class UpdateAdjustmentNotesCommandValidator : AbstractValidator<UpdateAdjustmentNotesCommand>
{
    public UpdateAdjustmentNotesCommandValidator()
    {
        RuleFor(x => x.AdjustmentId)
            .NotEmpty().WithMessage("Adjustment ID is required");

        RuleFor(x => x.Notes)
            .MaximumLength(1000).WithMessage("Notes cannot exceed 1000 characters");

        RuleFor(x => x.UpdatedBy)
            .NotEmpty().WithMessage("UpdatedBy is required");
    }
}

public class UpdateAdjustmentNotesCommandHandler : IRequestHandler<UpdateAdjustmentNotesCommand>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<UpdateAdjustmentNotesCommandHandler> _logger;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public UpdateAdjustmentNotesCommandHandler(
        IUnitOfWork unitOfWork,
        ILogger<UpdateAdjustmentNotesCommandHandler> logger,
        IHttpContextAccessor httpContextAccessor)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task Handle(UpdateAdjustmentNotesCommand request, CancellationToken cancellationToken)
    {
        // Check permissions - Admin roles bypass, others need explicit permission
        var user = _httpContextAccessor.HttpContext?.User;
        if (!PermissionHelper.IsAdmin(user))
        {
            PermissionHelper.ValidateAccess(user, "Adjustments.Update");
        }

        _logger.LogInformation("Updating notes for adjustment {AdjustmentId}", request.AdjustmentId);

        var adjustment = await _unitOfWork.InventoryAdjustments.GetByIdAsync(request.AdjustmentId);

        if (adjustment == null)
        {
            throw new KeyNotFoundException($"Inventory adjustment with ID {request.AdjustmentId} not found");
        }

        adjustment.UpdateNotes(request.Notes, request.UpdatedBy);

        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("Notes updated successfully for adjustment {AdjustmentId}", adjustment.Id);
    }
}
