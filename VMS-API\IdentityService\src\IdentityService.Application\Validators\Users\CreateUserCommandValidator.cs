using FluentValidation;
using IdentityService.Application.Features.Users.Commands;

namespace IdentityService.Application.Validators.Users;

public class CreateUserCommandValidator : BaseValidator<CreateUserCommand>
{
    public CreateUserCommandValidator()
    {
        RuleFor(x => x.Request).NotNull().WithMessage("Request cannot be null.");
        
        When(x => x.Request != null, () =>
        {
            RuleFor(x => x.Request.Email)
                .Cascade(CascadeMode.Stop)
                .NotEmpty().WithMessage("Email is required.")
                .EmailAddress().WithMessage("A valid email address is required.")
                .MaximumLength(256).WithMessage("Email cannot exceed 256 characters.");
                
            RuleFor(x => x.Request.PhoneNumber)
                .Cascade(CascadeMode.Stop)
                .NotEmpty().WithMessage("Phone number is required.")
                .Matches(@"^\+?[0-9\s\-\(\)]+$").WithMessage("A valid phone number is required.")
                .MaximumLength(20).WithMessage("Phone number cannot exceed 20 characters.");
                
            RuleFor(x => x.Request.Password)
                .Cascade(CascadeMode.Stop)
                .NotEmpty().WithMessage("Password is required.")
                .MinimumLength(8).WithMessage("Password must be at least 8 characters long.");
                
            RuleFor(x => x.Request.ConfirmPassword)
                .Cascade(CascadeMode.Stop)
                .NotEmpty().WithMessage("Confirm password is required.")
                .Equal(x => x.Request.Password).WithMessage("Passwords do not match.");
        });
    }
}
