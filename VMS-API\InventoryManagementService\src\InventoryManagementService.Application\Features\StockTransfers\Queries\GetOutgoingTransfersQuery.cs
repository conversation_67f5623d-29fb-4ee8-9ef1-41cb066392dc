using AutoMapper;
using InventoryManagementService.Application.DTOs;
using InventoryManagementService.Domain.Interfaces;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using VMS.Contracts.Common.Authorization;

namespace InventoryManagementService.Application.Features.StockTransfers.Queries;

public class GetOutgoingTransfersQuery : IRequest<List<StockTransferDto>>
{
    public Guid SourceBranchId { get; set; }
}

public class GetOutgoingTransfersQueryHandler : IRequestHandler<GetOutgoingTransfersQuery, List<StockTransferDto>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly ILogger<GetOutgoingTransfersQueryHandler> _logger;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public GetOutgoingTransfersQueryHandler(
        IUnitOfWork unitOfWork,
        IMapper mapper,
        ILogger<GetOutgoingTransfersQueryHandler> logger,
        IHttpContextAccessor httpContextAccessor)
    {
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _logger = logger;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<List<StockTransferDto>> Handle(GetOutgoingTransfersQuery request, CancellationToken cancellationToken)
    {
        // Check permissions - Admin roles bypass, others need explicit permission
        var user = _httpContextAccessor.HttpContext?.User;
        if (!PermissionHelper.IsAdmin(user))
        {
            PermissionHelper.ValidateAccess(user, "Transfers.View");
        }

        _logger.LogInformation("Getting outgoing transfers for branch: {SourceBranchId}", request.SourceBranchId);

        var transfers = await _unitOfWork.StockTransfers.GetBySourceBranchAsync(request.SourceBranchId);

        _logger.LogInformation("Found {Count} outgoing transfers for branch {SourceBranchId}", transfers.Count, request.SourceBranchId);

        return _mapper.Map<List<StockTransferDto>>(transfers);
    }
}
