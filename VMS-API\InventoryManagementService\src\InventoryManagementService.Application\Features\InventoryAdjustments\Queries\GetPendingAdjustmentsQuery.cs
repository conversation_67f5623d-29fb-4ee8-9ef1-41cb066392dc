using AutoMapper;
using InventoryManagementService.Application.DTOs;
using InventoryManagementService.Domain.Interfaces;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using VMS.Contracts.Common.Authorization;

namespace InventoryManagementService.Application.Features.InventoryAdjustments.Queries;

public class GetPendingAdjustmentsQuery : IRequest<List<InventoryAdjustmentDto>>
{
}

public class GetPendingAdjustmentsQueryHandler : IRequestHandler<GetPendingAdjustmentsQuery, List<InventoryAdjustmentDto>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly ILogger<GetPendingAdjustmentsQueryHandler> _logger;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public GetPendingAdjustmentsQueryHandler(
        IUnitOfWork unitOfWork,
        IMapper mapper,
        ILogger<GetPendingAdjustmentsQueryHandler> logger,
        IHttpContextAccessor httpContextAccessor)
    {
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _logger = logger;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<List<InventoryAdjustmentDto>> Handle(GetPendingAdjustmentsQuery request, CancellationToken cancellationToken)
    {
        // Check permissions - Admin roles bypass, others need explicit permission
        var user = _httpContextAccessor.HttpContext?.User;
        if (!PermissionHelper.IsAdmin(user))
        {
            PermissionHelper.ValidateAccess(user, "Adjustments.View");
        }

        _logger.LogInformation("Getting pending inventory adjustments");

        var adjustments = await _unitOfWork.InventoryAdjustments.GetPendingAdjustmentsAsync();

        _logger.LogInformation("Found {Count} pending inventory adjustments", adjustments.Count);

        return _mapper.Map<List<InventoryAdjustmentDto>>(adjustments);
    }
}
