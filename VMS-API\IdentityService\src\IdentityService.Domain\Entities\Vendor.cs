using System;
using System.Collections.Generic;
using IdentityService.Domain.Common;

namespace IdentityService.Domain.Entities;

public class Vendor : BaseEntity
{
    public string Name { get; private set; }
    public string Email { get; private set; }
    public string PhoneNumber { get; private set; }
    public bool IsActive { get; private set; }
    public List<User> Users { get; private set; }

    private Vendor()
    {
        Users = new List<User>();
    }

    public static Vendor Create(
        string name,
        string email,
        string phoneNumber,
        string createdBy)
    {
        return new Vendor
        {
            Name = name,
            Email = email,
            PhoneNumber = phoneNumber,
            IsActive = true,
            CreatedBy = createdBy
        };
    }

    public void Update(
        string name,
        string email,
        string phoneNumber,
        string updatedBy)
    {
        Name = name;
        Email = email;
        PhoneNumber = phoneNumber;
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;
    }

    public void Activate(string updatedBy)
    {
        IsActive = true;
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;
    }

    public void Deactivate(string updatedBy)
    {
        IsActive = false;
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;
    }

    public void AddUser(User user, string addedBy)
    {
        Users.Add(user);
        user.SetVendor(Id, addedBy);
    }
}
