namespace IdentityService.Application.Features.Menus.Queries;

/// <summary>
/// Response containing menu structure with breadcrumbs
/// </summary>
public class MenuWithBreadcrumbsResponse
{
    /// <summary>
    /// Menu structure
    /// </summary>
    public List<MenuItemResponse> Menu { get; set; } = new();

    /// <summary>
    /// Breadcrumb trail for current path
    /// </summary>
    public List<BreadcrumbItem> Breadcrumbs { get; set; } = new();
}

/// <summary>
/// Breadcrumb item
/// </summary>
public class BreadcrumbItem
{
    /// <summary>
    /// Display name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Path/URL
    /// </summary>
    public string Path { get; set; } = string.Empty;

    /// <summary>
    /// Whether this is the current/active item
    /// </summary>
    public bool IsActive { get; set; }
}
