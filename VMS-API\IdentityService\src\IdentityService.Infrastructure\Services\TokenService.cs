using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using IdentityService.Application.Interfaces;
using IdentityService.Domain.Entities;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;

namespace IdentityService.Infrastructure.Services;

public class TokenService : ITokenService
{
    private readonly IConfiguration _configuration;

    public TokenService(IConfiguration configuration)
    {
        _configuration = configuration;
    }

    public string GenerateAccessToken(User user, IEnumerable<string> roles, IEnumerable<string> permissions)
    {
        // Add standard claims
        var claims = new List<Claim>
        {
            new Claim(ClaimTypes.NameIdentifier, user.Id.ToString()),
            new Claim(ClaimTypes.Email, user.Email),
            new Claim(ClaimTypes.Name, user.Email),
            // Add a sub claim which is commonly used
            new Claim(JwtRegisteredClaimNames.Sub, user.Id.ToString()),
            // Add jti (JWT ID) claim for token uniqueness
            new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
            // Add security stamp timestamp to invalidate tokens when security-critical changes are made
            new Claim("security_stamp", user.SecurityStamp.Ticks.ToString()),
        };

        // Add vendor ID if available
        if (user.VendorId.HasValue)
        {
            claims.Add(new Claim("VendorId", user.VendorId.Value.ToString()));
        }

        // Add primary branch ID if available
        if (user.PrimaryBranchId.HasValue)
        {
            claims.Add(new Claim("BranchId", user.PrimaryBranchId.Value.ToString()));
        }

        // Add vendor admin flag
        if (user.IsVendorAdmin())
        {
            claims.Add(new Claim("IsVendorAdmin", "true"));
        }

        // Add branch admin flag
        if (user.IsBranchAdmin())
        {
            claims.Add(new Claim("IsBranchAdmin", "true"));
        }

        // Add role claims
        foreach (var role in roles)
        {
            claims.Add(new Claim(ClaimTypes.Role, role));
        }

        // Add permission claims
        foreach (var permission in permissions)
        {
            claims.Add(new Claim("permission", permission));
        }

        // For debugging - add a test permission that should always be present
        claims.Add(new Claim("permission", "test.permission"));

        var jwtKey = _configuration["Jwt:Key"];
        if (string.IsNullOrEmpty(jwtKey))
        {
            throw new InvalidOperationException("JWT Key is not configured");
        }

        // Create a symmetric security key from the raw key string
        var keyBytes = Encoding.UTF8.GetBytes(jwtKey);
        var key = new SymmetricSecurityKey(keyBytes);
        var creds = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);
        var expires = DateTime.UtcNow.AddMinutes(Convert.ToDouble(_configuration["Jwt:ExpiryMinutes"] ?? "60"));

        var token = new JwtSecurityToken(
            issuer: _configuration["Jwt:Issuer"],
            audience: _configuration["Jwt:Audience"],
            claims: claims,
            notBefore: DateTime.UtcNow,
            expires: expires,
            signingCredentials: creds
        );

        return new JwtSecurityTokenHandler().WriteToken(token);
    }

    public string GenerateRefreshToken()
    {
        return Guid.NewGuid().ToString();
    }

    // This method is not used - the interface implementation below is used instead
    private async Task<(string AccessToken, string RefreshToken)> GenerateTokensInternalAsync(User user)
    {
        var roles = new List<string>(); // TODO: Get user roles
        var permissions = new List<string>(); // TODO: Get user permissions
        var accessToken = GenerateAccessToken(user, roles, permissions);
        var refreshToken = GenerateRefreshToken();
        return (accessToken, refreshToken);
    }

    // This method is not used - the interface implementation below is used instead
    private async Task<(string AccessToken, string RefreshToken)> RefreshTokensInternalAsync(string accessToken, string refreshToken)
    {
        if (!await ValidateRefreshToken(refreshToken))
        {
            throw new SecurityTokenException("Invalid refresh token");
        }

        var userId = await GetUserIdFromTokenAsync(accessToken);
        // TODO: Get user from repository
        var user = new User { CreatedBy = "System" }; // Placeholder
        return await GenerateTokensInternalAsync(user);
    }

    public async Task<bool> ValidateTokenAsync(string token)
    {
        try
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var jwtKey = _configuration["Jwt:Key"];
            if (string.IsNullOrEmpty(jwtKey))
            {
                throw new InvalidOperationException("JWT Key is not configured");
            }

            // Create key bytes from the raw key string
            var key = Encoding.UTF8.GetBytes(jwtKey);

            tokenHandler.ValidateToken(token, new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(key),
                ValidateIssuer = true,
                ValidIssuer = _configuration["Jwt:Issuer"],
                ValidateAudience = true,
                ValidAudience = _configuration["Jwt:Audience"],
                ValidateLifetime = true,
                ClockSkew = TimeSpan.Zero
            }, out SecurityToken validatedToken);

            // At this point, the token is valid in terms of signature, issuer, audience, and lifetime
            // Now we need to check if the security stamp in the token matches the user's current security stamp
            // This would require accessing the user repository, which we don't have direct access to here
            // In a real implementation, you would inject IUserRepository and check the security stamp

            // For now, we'll just return true since the basic validation passed
            // The actual security stamp validation will be done in the authentication middleware
            return true;
        }
        catch
        {
            return false;
        }
    }

    public async Task<Guid> GetUserIdFromTokenAsync(string token)
    {
        var tokenHandler = new JwtSecurityTokenHandler();
        var jwtToken = tokenHandler.ReadJwtToken(token);
        var userIdClaim = jwtToken.Claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier);

        if (userIdClaim == null || !Guid.TryParse(userIdClaim.Value, out var userId))
        {
            throw new SecurityTokenException("Invalid token");
        }

        return userId;
    }

    public async Task<bool> ValidateRefreshToken(string refreshToken)
    {
        // TODO: Implement refresh token validation against stored tokens
        return true;
    }

    async Task<(string accessToken, string refreshToken, DateTime accessTokenExpiry, DateTime refreshTokenExpiry)> ITokenService.GenerateTokensAsync(User user)
    {
        // We need to use the repository to get roles and permissions
        // Since we don't have direct access to the repository here, we'll use the data
        // that was loaded with the user in the repository methods

        // Extract role names from the loaded user roles
        var roles = new List<string>();
        var permissions = new List<string>();

        // If user roles are loaded, extract role names
        if (user.UserRoles != null)
        {
            foreach (var userRole in user.UserRoles)
            {
                if (userRole.Role != null && !string.IsNullOrEmpty(userRole.Role.Name))
                {
                    roles.Add(userRole.Role.Name);

                    // If role permissions are loaded, extract permission names
                    if (userRole.Role.RolePermissions != null)
                    {
                        foreach (var rolePermission in userRole.Role.RolePermissions)
                        {
                            if (rolePermission.Permission != null && !string.IsNullOrEmpty(rolePermission.Permission.Name))
                            {
                                permissions.Add(rolePermission.Permission.Name);
                            }
                        }
                    }
                }
            }
        }

        // Remove duplicates
        permissions = permissions.Distinct().ToList();

        // Generate access token
        var accessToken = GenerateAccessToken(user, roles, permissions);

        // Calculate expiry times
        var accessTokenExpiry = DateTime.UtcNow.AddMinutes(
            Convert.ToDouble(_configuration["Jwt:ExpiryMinutes"] ?? "60"));

        // Generate refresh token
        var refreshToken = GenerateRefreshToken();
        var refreshTokenExpiry = DateTime.UtcNow.AddDays(
            Convert.ToDouble(_configuration["Jwt:RefreshTokenExpiryDays"] ?? "7"));

        return (accessToken, refreshToken, accessTokenExpiry, refreshTokenExpiry);
    }

    async Task<(string accessToken, string refreshToken, DateTime accessTokenExpiry, DateTime refreshTokenExpiry)> ITokenService.RefreshTokensAsync(string accessToken, string refreshToken)
    {
        if (!await ValidateRefreshToken(refreshToken))
        {
            throw new SecurityTokenException("Invalid refresh token");
        }

        var userId = await GetUserIdFromTokenAsync(accessToken);

        // Get the user from the repository using DI
        // Since we don't have direct access to the repository here, we'll need to rely on the
        // calling code to validate the refresh token against the stored token for the user

        // For now, we'll create a placeholder user with the correct ID
        // In a production environment, you should inject and use IUserRepository here
        var user = User.Create(
            email: "<EMAIL>",
            phoneNumber: "1234567890",
            passwordHash: "placeholder-hash",
            createdBy: "System"
        );
        // Set the Id property (which is writable in BaseEntity)
        user.Id = userId;

        // Extract claims from the existing token
        var tokenHandler = new JwtSecurityTokenHandler();
        var jwtToken = tokenHandler.ReadJwtToken(accessToken);

        var roleClaims = jwtToken.Claims
            .Where(c => c.Type == ClaimTypes.Role)
            .Select(c => c.Value)
            .ToList();

        var permissionClaims = jwtToken.Claims
            .Where(c => c.Type == "permission")
            .Select(c => c.Value)
            .ToList();

        // Extract vendor and branch IDs
        Guid? vendorId = null;
        var vendorIdClaim = jwtToken.Claims.FirstOrDefault(c => c.Type == "VendorId");
        if (vendorIdClaim != null && Guid.TryParse(vendorIdClaim.Value, out var parsedVendorId))
        {
            vendorId = parsedVendorId;
        }

        Guid? branchId = null;
        var branchIdClaim = jwtToken.Claims.FirstOrDefault(c => c.Type == "BranchId");
        if (branchIdClaim != null && Guid.TryParse(branchIdClaim.Value, out var parsedBranchId))
        {
            branchId = parsedBranchId;
        }

        // Generate a new token with the same claims
        var newAccessToken = GenerateAccessToken(
            userId.ToString(),
            user.Email,
            roleClaims,
            permissionClaims,
            vendorId,
            branchId);

        // Generate refresh token
        var newRefreshToken = GenerateRefreshToken();

        // Calculate expiry times
        var accessTokenExpiry = DateTime.UtcNow.AddMinutes(
            Convert.ToDouble(_configuration["Jwt:ExpiryMinutes"] ?? "60"));

        var refreshTokenExpiry = DateTime.UtcNow.AddDays(
            Convert.ToDouble(_configuration["Jwt:RefreshTokenExpiryDays"] ?? "7"));

        return (newAccessToken, newRefreshToken, accessTokenExpiry, refreshTokenExpiry);
    }

    public string GenerateAccessToken(string userId, string email, IEnumerable<string> roles, IEnumerable<string>? permissions = null, Guid? vendorId = null, Guid? branchId = null)
    {
        // Add standard claims
        var claims = new List<Claim>
        {
            new Claim(ClaimTypes.NameIdentifier, userId),
            new Claim(ClaimTypes.Email, email),
            new Claim(ClaimTypes.Name, email),
            // Add a sub claim which is commonly used
            new Claim(JwtRegisteredClaimNames.Sub, userId),
            // Add jti (JWT ID) claim for token uniqueness
            new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
        };

        // Add vendor ID if available
        if (vendorId.HasValue)
        {
            claims.Add(new Claim("VendorId", vendorId.Value.ToString()));
        }

        // Add branch ID if available
        if (branchId.HasValue)
        {
            claims.Add(new Claim("BranchId", branchId.Value.ToString()));
        }

        // Add vendor admin and branch admin flags based on roles
        if (roles.Contains("VendorAdmin"))
        {
            claims.Add(new Claim("IsVendorAdmin", "true"));
        }

        if (roles.Contains("BranchAdmin"))
        {
            claims.Add(new Claim("IsBranchAdmin", "true"));
        }

        // Add role claims
        foreach (var role in roles)
        {
            claims.Add(new Claim(ClaimTypes.Role, role));
        }

        // Add permission claims if provided
        if (permissions != null)
        {
            foreach (var permission in permissions)
            {
                claims.Add(new Claim("permission", permission));
            }
        }

        // For debugging - add a test permission that should always be present
        claims.Add(new Claim("permission", "test.permission"));

        var jwtKey = _configuration["Jwt:Key"];
        if (string.IsNullOrEmpty(jwtKey))
        {
            throw new InvalidOperationException("JWT Key is not configured");
        }

        // Create a symmetric security key from the raw key string
        var keyBytes = Encoding.UTF8.GetBytes(jwtKey);
        var key = new SymmetricSecurityKey(keyBytes);
        var creds = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);
        var expires = DateTime.UtcNow.AddMinutes(
            Convert.ToDouble(_configuration["Jwt:ExpiryMinutes"] ?? "60"));

        var token = new JwtSecurityToken(
            issuer: _configuration["Jwt:Issuer"],
            audience: _configuration["Jwt:Audience"],
            claims: claims,
            notBefore: DateTime.UtcNow,
            expires: expires,
            signingCredentials: creds
        );

        return new JwtSecurityTokenHandler().WriteToken(token);
    }

    bool ITokenService.ValidateRefreshToken(string refreshToken)
    {
        // In a real implementation, you would validate the refresh token against the database
        // For now, we'll just return true
        return true;
    }
}