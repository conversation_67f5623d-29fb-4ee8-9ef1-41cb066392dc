using System;
using IdentityService.Domain.Common;
using IdentityService.Domain.Enums;

namespace IdentityService.Domain.Entities;

/// <summary>
/// Represents a user session in the system
/// </summary>
public class UserSession : BaseEntity
{
    /// <summary>
    /// The ID of the user this session belongs to
    /// </summary>
    public Guid UserId { get; private set; }
    
    /// <summary>
    /// Reference to the user
    /// </summary>
    public User User { get; private set; }
    
    /// <summary>
    /// The token associated with this session
    /// </summary>
    public string Token { get; private set; }
    
    /// <summary>
    /// The refresh token associated with this session
    /// </summary>
    public string RefreshToken { get; private set; }
    
    /// <summary>
    /// When the session was started
    /// </summary>
    public DateTime StartedAt { get; private set; }
    
    /// <summary>
    /// When the session was last active
    /// </summary>
    public DateTime LastActiveAt { get; private set; }
    
    /// <summary>
    /// When the session expires
    /// </summary>
    public DateTime ExpiresAt { get; private set; }
    
    /// <summary>
    /// The IP address of the client
    /// </summary>
    public string IpAddress { get; private set; }
    
    /// <summary>
    /// The user agent of the client
    /// </summary>
    public string UserAgent { get; private set; }
    
    /// <summary>
    /// The device information of the client
    /// </summary>
    public string DeviceInfo { get; private set; }
    
    /// <summary>
    /// The current status of the session
    /// </summary>
    public SessionStatus Status { get; private set; }
    
    /// <summary>
    /// When the session was ended (if applicable)
    /// </summary>
    public DateTime? EndedAt { get; private set; }
    
    /// <summary>
    /// The reason for ending the session (if applicable)
    /// </summary>
    public string EndReason { get; private set; }

    // Private constructor for EF Core
    private UserSession()
    {
        Token = string.Empty;
        RefreshToken = string.Empty;
        IpAddress = string.Empty;
        UserAgent = string.Empty;
        DeviceInfo = string.Empty;
        EndReason = string.Empty;
    }

    /// <summary>
    /// Creates a new user session
    /// </summary>
    public static UserSession Create(
        Guid userId,
        User user,
        string token,
        string refreshToken,
        DateTime expiresAt,
        string ipAddress,
        string userAgent,
        string deviceInfo,
        string createdBy)
    {
        return new UserSession
        {
            UserId = userId,
            User = user,
            Token = token,
            RefreshToken = refreshToken,
            StartedAt = DateTime.UtcNow,
            LastActiveAt = DateTime.UtcNow,
            ExpiresAt = expiresAt,
            IpAddress = ipAddress,
            UserAgent = userAgent,
            DeviceInfo = deviceInfo,
            Status = SessionStatus.Active,
            CreatedBy = createdBy
        };
    }

    /// <summary>
    /// Updates the last active time of the session
    /// </summary>
    public void UpdateLastActive()
    {
        LastActiveAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Extends the session expiry time
    /// </summary>
    public void ExtendExpiry(DateTime newExpiryTime)
    {
        ExpiresAt = newExpiryTime;
        UpdateLastActive();
    }

    /// <summary>
    /// Ends the session
    /// </summary>
    public void End(string reason, string updatedBy)
    {
        Status = SessionStatus.Ended;
        EndedAt = DateTime.UtcNow;
        EndReason = reason;
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Expires the session
    /// </summary>
    public void Expire(string updatedBy)
    {
        Status = SessionStatus.Expired;
        EndedAt = DateTime.UtcNow;
        EndReason = "Session expired";
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Terminates the session due to security reasons
    /// </summary>
    public void Terminate(string reason, string updatedBy)
    {
        Status = SessionStatus.Terminated;
        EndedAt = DateTime.UtcNow;
        EndReason = reason;
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Checks if the session is active
    /// </summary>
    public bool IsActive()
    {
        return Status == SessionStatus.Active && DateTime.UtcNow < ExpiresAt;
    }

    /// <summary>
    /// Checks if the session is idle (no activity for a specified period)
    /// </summary>
    public bool IsIdle(int idleTimeoutMinutes)
    {
        return Status == SessionStatus.Active && 
               DateTime.UtcNow > LastActiveAt.AddMinutes(idleTimeoutMinutes);
    }
}
