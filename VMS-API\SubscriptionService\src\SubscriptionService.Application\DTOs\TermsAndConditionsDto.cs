using System;
using SubscriptionService.Domain.Entities;

namespace SubscriptionService.Application.DTOs;

public class TermsAndConditionsDto
{
    public Guid Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public bool IsCurrentVersion { get; set; }
    public DateTime EffectiveDate { get; set; }
    public TermsType Type { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public DateTime? UpdatedAt { get; set; }
    public string? UpdatedBy { get; set; }
}
