using System;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using VMSContracts.Common.Context;

namespace BranchManagementService.Infrastructure.Services
{
    /// <summary>
    /// Implementation of the branch context service
    /// </summary>
    public class BranchContextService : IBranchContextService
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ILogger<BranchContextService> _logger;

        public BranchContextService(IHttpContextAccessor httpContextAccessor, ILogger<BranchContextService> logger)
        {
            _httpContextAccessor = httpContextAccessor;
            _logger = logger;
        }

        /// <inheritdoc />
        public BranchContext GetCurrentBranchContext()
        {
            try
            {
                var httpContext = _httpContextAccessor.HttpContext;
                if (httpContext == null)
                {
                    _logger.LogWarning("HttpContext is null");
                    return new BranchContext();
                }

                if (httpContext.Items.TryGetValue("BranchContext", out var contextObj) && 
                    contextObj is BranchContext branchContext)
                {
                    return branchContext;
                }

                _logger.LogWarning("Branch context not found in HttpContext.Items");
                return new BranchContext();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting branch context from HttpContext.Items");
                return new BranchContext();
            }
        }

        /// <inheritdoc />
        public bool HasAccessToBranch(Guid branchId)
        {
            var context = GetCurrentBranchContext();
            return context.HasAccessToBranch(branchId);
        }

        /// <inheritdoc />
        public bool HasVendorAccess()
        {
            var context = GetCurrentBranchContext();
            return context.HasVendorAccess();
        }

        /// <inheritdoc />
        public bool HasBranchAccess()
        {
            var context = GetCurrentBranchContext();
            return context.HasBranchAccess();
        }

        /// <inheritdoc />
        public Guid GetVendorId()
        {
            var context = GetCurrentBranchContext();
            return context.VendorId;
        }

        /// <inheritdoc />
        public Guid? GetBranchId()
        {
            var context = GetCurrentBranchContext();
            return context.BranchId;
        }

        /// <inheritdoc />
        public bool IsVendorAdmin()
        {
            var context = GetCurrentBranchContext();
            return context.IsVendorAdmin;
        }

        /// <inheritdoc />
        public bool IsBranchAdmin()
        {
            var context = GetCurrentBranchContext();
            return context.IsBranchAdmin;
        }
    }
}
