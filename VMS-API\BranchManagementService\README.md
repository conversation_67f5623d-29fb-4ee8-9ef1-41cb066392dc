# Branch Management Service

This microservice is part of the Vehicle Management System and handles branch management for automobile service garages.

## Features

- Add/manage multiple branches based on subscription limits
- Branch details entry with required fields
- Vehicle type selection (Car/Bike/Both) per branch
- Branch settings modification
- Branch switching to view/manage operations
- Subscription integration with branch limits

## Prerequisites

- .NET 8.0 SDK
- Docker and Docker Compose
- PostgreSQL 16
- RabbitMQ

## Getting Started

1. Clone the repository
2. Navigate to the project directory
3. Run the following commands:

```bash
# Build and run the service using Docker Compose
docker-compose up --build
```

The service will be available at:
- API: http://localhost:5004
- Swagger UI: http://localhost:5004/swagger

## Project Structure

```
BranchManagementService/
├── docker/
├── src/
│   ├── BranchManagementService.API/
│   ├── BranchManagementService.Application/
│   ├── BranchManagementService.Domain/
│   ├── BranchManagementService.Infrastructure/
│   └── BranchManagementService.Shared/
└── tests/
```

## Technology Stack

- .NET 8.0
- Entity Framework Core with PostgreSQL
- MediatR for CQRS pattern
- FluentValidation for validation
- AutoMap<PERSON> for object mapping
- MassTransit with RabbitMQ for messaging
- <PERSON>ilo<PERSON> for logging
- Swagger for API documentation

## API Endpoints

### Branch Management

- `GET /api/branches?vendorId={vendorId}` - Get all branches for a vendor
- `GET /api/branches/{id}` - Get a branch by ID
- `POST /api/branches` - Create a new branch
- `PUT /api/branches/{id}` - Update a branch

### Service API (for inter-service communication)

- `GET /api/service/vendors/{vendorId}/branches` - Get all branches for a vendor
- `GET /api/service/branches/{id}` - Get a branch by ID

## Development

### Running Locally

```bash
cd src/BranchManagementService.API
dotnet run
```

### Database Migrations

```bash
cd src/BranchManagementService.API
dotnet ef migrations add InitialCreate
dotnet ef database update
```

## Testing

```bash
dotnet test
```
