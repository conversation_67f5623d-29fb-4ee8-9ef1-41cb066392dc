using System;

namespace IdentityService.Domain.ValueObjects;

public class PasswordHash
{
    public string Hash { get; }

    public PasswordHash(string hash)
    {
        if (string.IsNullOrWhiteSpace(hash))
            throw new ArgumentException("Password hash cannot be empty", nameof(hash));

        Hash = hash;
    }
    public static PasswordHash? FromNullableString(string? hash)
    {
        return string.IsNullOrWhiteSpace(hash) ? null : new PasswordHash(hash);
    }

    public override bool Equals(object obj)
    {
        if (obj is PasswordHash other)
            return Hash.Equals(other.Hash);

        return false;
    }

    public override int GetHashCode()
    {
        return Hash.GetHashCode();
    }
}