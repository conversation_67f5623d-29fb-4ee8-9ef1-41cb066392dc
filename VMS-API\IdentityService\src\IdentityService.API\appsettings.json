{"AllowedOrigins": ["http://localhost:3000", "http://localhost:3001", "http://localhost:3002"], "ConnectionStrings": {"DefaultConnection": "Host=**************;Port=5432;Database=vms_identityservice;Username=postgres;Password=************"}, "Jwt": {"Key": "12345678901234567890123456789012", "Issuer": "identity-service", "Audience": "vms-api", "ExpiryMinutes": 60, "RefreshTokenExpiryDays": 7}, "Email": {"SmtpServer": "smtp.gmail.com", "SmtpPort": 587, "SmtpUsername": "<EMAIL>", "SmtpPassword": "testingteamvo@12345", "FromEmail": "<EMAIL>", "FromName": "VMS Team"}, "RabbitMQ": {"HostName": "localhost", "UserName": "guest", "Password": "guest", "VirtualHost": "/"}, "PasswordComplexity": {"MinimumLength": 8, "RequireUppercase": true, "RequireLowercase": true, "RequireDigit": true, "RequireSpecialCharacter": true, "MaximumRepeatedCharacters": 3, "PreventCommonPasswords": true, "PreventUsernameInPassword": true}, "SessionManagement": {"IdleTimeoutMinutes": 30, "AbsoluteTimeoutHours": 24, "MaxConcurrentSessions": 5, "PreventConcurrentLogin": false}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}}, "ServiceUrls": {"TenantManagementService": "http://localhost:5003", "SubscriptionService": "http://localhost:5114"}, "AllowedHosts": "*", "Vendor": "http://**************:105/"}