using System.ComponentModel.DataAnnotations;
using IdentityService.Application.DTOs.Users;
using IdentityService.Application.Interfaces;
using IdentityService.Domain.Interfaces;
using IdentityService.Domain.Services;
using MediatR;
using Microsoft.Extensions.Logging;

namespace IdentityService.Application.Features.Users.Commands;

/// <summary>
/// Command to update a user permission
/// </summary>
public class UpdateUserPermissionCommand : IRequest<UserPermissionResponse>
{
    /// <summary>
    /// User ID
    /// </summary>
    [Required]
    public Guid UserId { get; set; }

    /// <summary>
    /// Permission ID to update
    /// </summary>
    [Required]
    public Guid PermissionId { get; set; }

    /// <summary>
    /// Update request
    /// </summary>
    [Required]
    public UpdateUserPermissionRequest Request { get; set; }
}

/// <summary>
/// Handler for updating user permission
/// </summary>
public class UpdateUserPermissionCommandHandler : IRequestHandler<UpdateUserPermissionCommand, UserPermissionResponse>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IPermissionService _permissionService;
    private readonly ICurrentUserService _currentUserService;
    private readonly ILogger<UpdateUserPermissionCommandHandler> _logger;

    public UpdateUserPermissionCommandHandler(
        IUnitOfWork unitOfWork,
        IPermissionService permissionService,
        ICurrentUserService currentUserService,
        ILogger<UpdateUserPermissionCommandHandler> logger)
    {
        _unitOfWork = unitOfWork;
        _permissionService = permissionService;
        _currentUserService = currentUserService;
        _logger = logger;
    }

    public async Task<UserPermissionResponse> Handle(UpdateUserPermissionCommand command, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Updating permission {PermissionId} for user {UserId}",
                command.PermissionId, command.UserId);

            // Get the user
            var user = await _unitOfWork.UserRepository.GetByIdAsync(command.UserId);
            if (user == null)
            {
                throw new ArgumentException($"User with ID {command.UserId} not found");
            }

            // Get the permission
            var permission = await _unitOfWork.PermissionRepository.GetByIdAsync(command.PermissionId);
            if (permission == null)
            {
                throw new ArgumentException($"Permission with ID {command.PermissionId} not found");
            }

            // Find the user permission
            var userPermission = user.UserPermissions
                .FirstOrDefault(up => up.PermissionId == command.PermissionId && up.IsCurrentlyActive());

            if (userPermission == null)
            {
                throw new ArgumentException($"Active permission {command.PermissionId} not found for user {command.UserId}");
            }

            // Update the permission
            var currentUserName = _currentUserService.UserName ?? "System";
            userPermission.Update(
                command.Request.Type,
                command.Request.ExpiresAt,
                command.Request.Reason,
                currentUserName);

            // Save changes
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Successfully updated permission {PermissionId} for user {UserId}",
                command.PermissionId, command.UserId);

            // Return response
            return new UserPermissionResponse
            {
                Id = userPermission.Id,
                UserId = userPermission.UserId,
                PermissionId = userPermission.PermissionId,
                PermissionName = permission.Name,
                Type = userPermission.Type,
                ExpiresAt = userPermission.ExpiresAt,
                Reason = userPermission.Reason,
                IsActive = userPermission.IsCurrentlyActive(),
                CreatedAt = userPermission.CreatedAt,
                CreatedBy = userPermission.CreatedBy,
                UpdatedAt = userPermission.UpdatedAt,
                UpdatedBy = userPermission.UpdatedBy
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating permission {PermissionId} for user {UserId}",
                command.PermissionId, command.UserId);
            throw;
        }
    }
}
