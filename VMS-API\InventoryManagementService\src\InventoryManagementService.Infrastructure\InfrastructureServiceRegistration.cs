using InventoryManagementService.Domain.Interfaces;
using InventoryManagementService.Infrastructure.Messaging;
using InventoryManagementService.Infrastructure.Persistence;
using InventoryManagementService.Infrastructure.Repositories;
using InventoryManagementService.Infrastructure.Services;
using MassTransit;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using VMS.Contracts.Common.Context;

namespace InventoryManagementService.Infrastructure;

public static class InfrastructureServiceRegistration
{
    public static IServiceCollection AddInfrastructureServices(this IServiceCollection services, IConfiguration configuration)
    {
        // Add DbContext
        services.AddDbContext<InventoryDbContext>(options =>
            options.UseNpgsql(configuration.GetConnectionString("DefaultConnection")));

        // Add repositories
        services.AddScoped<IPartRepository, PartRepository>();
        services.AddScoped<IInventoryRepository, InventoryRepository>();
        services.AddScoped<IStockTransferRepository, StockTransferRepository>();
        services.AddScoped<IInventoryAdjustmentRepository, InventoryAdjustmentRepository>();

        // Add Unit of Work
        services.AddScoped<IUnitOfWork, UnitOfWork>();

        // Add event publisher
        services.AddScoped<IEventPublisher, EventPublisher>();

        // Add branch context service
        services.AddScoped<IBranchContextService, BranchContextService>();

        // Add MassTransit with RabbitMQ
        services.AddMassTransitWithRabbitMq(configuration);

        return services;
    }

    private static IServiceCollection AddMassTransitWithRabbitMq(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddMassTransit(x =>
        {
            x.UsingRabbitMq((context, cfg) =>
            {
                var rabbitMqSettings = configuration.GetSection("RabbitMQ");
                var host = rabbitMqSettings["Host"] ?? "localhost";
                var port = rabbitMqSettings.GetValue<int>("Port", 5672);
                var username = rabbitMqSettings["Username"] ?? "guest";
                var password = rabbitMqSettings["Password"] ?? "guest";

                cfg.Host(host, h =>
                {
                    h.Username(username);
                    h.Password(password);
                });

                cfg.ConfigureEndpoints(context);
            });
        });

        return services;
    }

    public static async Task InitializeDatabaseAsync(IServiceProvider serviceProvider)
    {
        using var scope = serviceProvider.CreateScope();
        var services = scope.ServiceProvider;
        var logger = services.GetRequiredService<ILogger<InventoryDbContext>>();
        var context = services.GetRequiredService<InventoryDbContext>();

        try
        {
            // Ensure database exists first
            logger.LogInformation("Ensuring database exists...");
            try
            {
                await context.Database.EnsureCreatedAsync();
                logger.LogInformation("Database ensured successfully");
            }
            catch (Exception ex)
            {
                logger.LogWarning(ex, "EnsureCreated failed, but continuing: {Message}", ex.Message);
            }

            // Check if the database connection is working
            logger.LogInformation("Checking database connection...");
            try
            {
                var canConnect = await context.Database.CanConnectAsync();
                if (!canConnect)
                {
                    logger.LogError("Cannot connect to the database. Please check your connection string and ensure PostgreSQL is running.");
                    throw new Exception("Cannot connect to the database. Please check your connection string and ensure PostgreSQL is running.");
                }

                logger.LogInformation("Database connection successful");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error checking database connection: {Message}", ex.Message);
                throw;
            }

            // Check if the database needs to be created
            logger.LogInformation("Checking if database needs to be created...");
            var pendingMigrations = await context.Database.GetPendingMigrationsAsync();
            var pendingMigrationsList = pendingMigrations.ToList();
            if (pendingMigrationsList.Any())
            {
                logger.LogInformation("Pending migrations: {0}", string.Join(", ", pendingMigrationsList));
            }
            else
            {
                logger.LogInformation("No pending migrations found");
            }

            // Apply migrations (handle gracefully if tables already exist)
            logger.LogInformation("Applying migrations...");
            try
            {
                await context.Database.MigrateAsync();
                logger.LogInformation("Migrations applied successfully");
            }
            catch (Exception migrationEx)
            {
                logger.LogWarning(migrationEx, "Migration failed, but continuing with seeding. This might be expected if database was created with EnsureCreated.");
            }

        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred while initializing the database: {Message}", ex.Message);
            if (ex.InnerException != null)
            {
                logger.LogError(ex.InnerException, "Inner exception: {Message}", ex.InnerException.Message);
            }
            // Don't throw - continue with seeding
            logger.LogWarning("Database initialization had issues, but continuing with seeding");
        }

        // Always attempt seeding regardless of migration status
        try
        {
            await SeedDataAsync(context, logger);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred while seeding the database: {Message}", ex.Message);
            // Don't throw - let the application continue
        }
    }

    private static async Task SeedDataAsync(InventoryDbContext context, ILogger logger)
    {
        try
        {
            logger.LogInformation("Starting inventory database seeding...");

            // Create a logger factory to get the correct logger type
            using var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
            var seederLogger = loggerFactory.CreateLogger<InventoryDbSeeder>();

            var seeder = new InventoryDbSeeder(context, seederLogger);
            await seeder.SeedAsync();
            logger.LogInformation("Inventory database seeding completed successfully.");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred while seeding the inventory database.");
            throw;
        }
    }
}
