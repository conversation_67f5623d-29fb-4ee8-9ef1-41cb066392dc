using System;
using System.ComponentModel.DataAnnotations;
using SubscriptionService.Domain.Enums;

namespace SubscriptionService.API.Models
{
    /// <summary>
    /// Request model for creating a new subscription plan
    /// </summary>
    public class CreateSubscriptionPlanRequest
    {
        /// <summary>
        /// Name of the subscription plan
        /// </summary>
        [Required(ErrorMessage = "Plan name is required")]
        [StringLength(100, MinimumLength = 3, ErrorMessage = "Plan name must be between 3 and 100 characters")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Description of the subscription plan
        /// </summary>
        [Required(ErrorMessage = "Plan description is required")]
        [StringLength(500, MinimumLength = 10, ErrorMessage = "Plan description must be between 10 and 500 characters")]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// Subscription tier (Trial, Basic, Silver, Gold, Custom)
        /// </summary>
        [Required(ErrorMessage = "Subscription tier is required")]
        public SubscriptionTier Tier { get; set; }

        /// <summary>
        /// Maximum number of branches allowed
        /// </summary>
        [Required(ErrorMessage = "Max branches is required")]
        [Range(1, 1000, ErrorMessage = "Max branches must be between 1 and 1000")]
        public int MaxBranches { get; set; }

        /// <summary>
        /// Maximum number of users allowed
        /// </summary>
        [Required(ErrorMessage = "Max users is required")]
        [Range(1, 10000, ErrorMessage = "Max users must be between 1 and 10000")]
        public int MaxUsers { get; set; }

        /// <summary>
        /// Base monthly price of the subscription plan
        /// </summary>
        [Required(ErrorMessage = "Base monthly price is required")]
        [Range(0, 999999.99, ErrorMessage = "Base monthly price must be between 0 and 999999.99")]
        public decimal BaseMonthlyPrice { get; set; }

        /// <summary>
        /// Default country code (ISO 3166-1 alpha-2)
        /// </summary>
        [StringLength(2, MinimumLength = 2, ErrorMessage = "Country code must be exactly 2 characters")]
        public string DefaultCountryCode { get; set; } = "US";

        /// <summary>
        /// Default currency code (ISO 4217)
        /// </summary>
        [StringLength(3, MinimumLength = 3, ErrorMessage = "Currency code must be exactly 3 characters")]
        public string DefaultCurrencyCode { get; set; } = "USD";

        /// <summary>
        /// Indicates if this is a trial plan
        /// </summary>
        public bool IsTrialPlan { get; set; } = false;

        /// <summary>
        /// Trial period in days (required if IsTrialPlan is true)
        /// </summary>
        [Range(1, 365, ErrorMessage = "Trial period must be between 1 and 365 days")]
        public int? TrialPeriodDays { get; set; }

        /// <summary>
        /// Advanced features configuration
        /// </summary>
        public CreateAdvancedFeaturesRequest? AdvancedFeatures { get; set; }

        /// <summary>
        /// Billing cycle discount configuration
        /// </summary>
        public CreateBillingCycleDiscountRequest? BillingCycleDiscount { get; set; }

        /// <summary>
        /// Currency-specific pricing configurations
        /// </summary>
        public List<CreateCurrencyPricingRequest>? CurrencyPricings { get; set; }

        /// <summary>
        /// Feature access configurations
        /// </summary>
        public List<CreateFeatureAccessRequest>? FeatureAccesses { get; set; }

        /// <summary>
        /// Indicates if this is a master plan (default: true)
        /// </summary>
        public bool IsMasterPlan { get; set; } = true;


    }

    /// <summary>
    /// Request model for advanced features configuration
    /// </summary>
    public class CreateAdvancedFeaturesRequest
    {
        /// <summary>
        /// Enable custom reports feature
        /// </summary>
        public bool CustomReports { get; set; } = false;

        /// <summary>
        /// Enable document export feature
        /// </summary>
        public bool DocumentExport { get; set; } = false;
    }

    /// <summary>
    /// Request model for billing cycle discount configuration
    /// </summary>
    public class CreateBillingCycleDiscountRequest
    {
        /// <summary>
        /// Discount percentage for quarterly billing (0-100)
        /// </summary>
        [Range(0, 100, ErrorMessage = "Quarterly discount percentage must be between 0 and 100")]
        public decimal QuarterlyDiscountPercentage { get; set; } = 5.0m;

        /// <summary>
        /// Discount percentage for yearly billing (0-100)
        /// </summary>
        [Range(0, 100, ErrorMessage = "Yearly discount percentage must be between 0 and 100")]
        public decimal YearlyDiscountPercentage { get; set; } = 10.0m;

        /// <summary>
        /// Indicates if discounts are active
        /// </summary>
        public bool IsActive { get; set; } = true;
    }

    /// <summary>
    /// Request model for currency-specific pricing
    /// </summary>
    public class CreateCurrencyPricingRequest
    {
        /// <summary>
        /// Country code (ISO 3166-1 alpha-2)
        /// </summary>
        [Required(ErrorMessage = "Country code is required")]
        [StringLength(2, MinimumLength = 2, ErrorMessage = "Country code must be exactly 2 characters")]
        public string CountryCode { get; set; } = string.Empty;

        /// <summary>
        /// Currency code (ISO 4217)
        /// </summary>
        [Required(ErrorMessage = "Currency code is required")]
        [StringLength(3, MinimumLength = 3, ErrorMessage = "Currency code must be exactly 3 characters")]
        public string CurrencyCode { get; set; } = string.Empty;

        /// <summary>
        /// Exchange rate from default currency
        /// </summary>
        [Required(ErrorMessage = "Exchange rate is required")]
        [Range(0.000001, 999999, ErrorMessage = "Exchange rate must be greater than 0")]
        public decimal ExchangeRate { get; set; }

        /// <summary>
        /// Localized monthly price in the target currency
        /// </summary>
        [Required(ErrorMessage = "Localized monthly price is required")]
        [Range(0, 999999.99, ErrorMessage = "Localized monthly price must be between 0 and 999999.99")]
        public decimal LocalizedMonthlyPrice { get; set; }
    }

    /// <summary>
    /// Request model for feature access configuration
    /// </summary>
    public class CreateFeatureAccessRequest
    {
        /// <summary>
        /// Name of the feature
        /// </summary>
        [Required(ErrorMessage = "Feature name is required")]
        [StringLength(100, MinimumLength = 3, ErrorMessage = "Feature name must be between 3 and 100 characters")]
        public string FeatureName { get; set; } = string.Empty;

        /// <summary>
        /// Indicates if the feature is enabled
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// Usage limit for the feature (optional)
        /// </summary>
        [Range(1, int.MaxValue, ErrorMessage = "Usage limit must be greater than 0")]
        public int? UsageLimit { get; set; }
    }
}
