-- Add New Roles and Permissions Migration Script
-- This script adds the missing branch roles and their permissions

-- Insert new roles if they don't exist
INSERT INTO "Roles" ("Id", "Name", "Description", "IsActive", "CreatedAt", "CreatedBy", "UpdatedAt", "UpdatedBy")
SELECT 
    gen_random_uuid(),
    'VendorAdmin',
    'Vendor administrator with vendor-level access',
    true,
    NOW(),
    'System',
    NOW(),
    'System'
WHERE NOT EXISTS (SELECT 1 FROM "Roles" WHERE "Name" = 'VendorAdmin');

INSERT INTO "Roles" ("Id", "Name", "Description", "IsActive", "CreatedAt", "CreatedBy", "UpdatedAt", "UpdatedBy")
SELECT 
    gen_random_uuid(),
    'BranchAdmin',
    'Branch administrator with branch-level access',
    true,
    NOW(),
    'System',
    NOW(),
    'System'
WHERE NOT EXISTS (SELECT 1 FROM "Roles" WHERE "Name" = 'BranchAdmin');

INSERT INTO "Roles" ("Id", "Name", "Description", "IsActive", "CreatedAt", "CreatedBy", "UpdatedAt", "UpdatedBy")
SELECT 
    gen_random_uuid(),
    'Service Advisor',
    'Service advisor role for customer interaction and service management',
    true,
    NOW(),
    'System',
    NOW(),
    'System'
WHERE NOT EXISTS (SELECT 1 FROM "Roles" WHERE "Name" = 'Service Advisor');

INSERT INTO "Roles" ("Id", "Name", "Description", "IsActive", "CreatedAt", "CreatedBy", "UpdatedAt", "UpdatedBy")
SELECT 
    gen_random_uuid(),
    'Technician',
    'Technician role for vehicle maintenance and repair',
    true,
    NOW(),
    'System',
    NOW(),
    'System'
WHERE NOT EXISTS (SELECT 1 FROM "Roles" WHERE "Name" = 'Technician');

INSERT INTO "Roles" ("Id", "Name", "Description", "IsActive", "CreatedAt", "CreatedBy", "UpdatedAt", "UpdatedBy")
SELECT 
    gen_random_uuid(),
    'Inventory Manager',
    'Inventory manager role for parts and supplies management',
    true,
    NOW(),
    'System',
    NOW(),
    'System'
WHERE NOT EXISTS (SELECT 1 FROM "Roles" WHERE "Name" = 'Inventory Manager');

INSERT INTO "Roles" ("Id", "Name", "Description", "IsActive", "CreatedAt", "CreatedBy", "UpdatedAt", "UpdatedBy")
SELECT 
    gen_random_uuid(),
    'Cashier',
    'Cashier role for payment processing and billing',
    true,
    NOW(),
    'System',
    NOW(),
    'System'
WHERE NOT EXISTS (SELECT 1 FROM "Roles" WHERE "Name" = 'Cashier');

-- Add new permissions for branch roles
INSERT INTO "Permissions" ("Id", "Name", "Description", "Resource", "Action", "IsActive", "CreatedAt", "CreatedBy", "UpdatedAt", "UpdatedBy")
SELECT 
    gen_random_uuid(),
    'Vehicles.Create',
    'Permission to create vehicles',
    'Vehicles',
    'Create',
    true,
    NOW(),
    'System',
    NOW(),
    'System'
WHERE NOT EXISTS (SELECT 1 FROM "Permissions" WHERE "Name" = 'Vehicles.Create');

INSERT INTO "Permissions" ("Id", "Name", "Description", "Resource", "Action", "IsActive", "CreatedAt", "CreatedBy", "UpdatedAt", "UpdatedBy")
SELECT 
    gen_random_uuid(),
    'Vehicles.Update',
    'Permission to update vehicles',
    'Vehicles',
    'Update',
    true,
    NOW(),
    'System',
    NOW(),
    'System'
WHERE NOT EXISTS (SELECT 1 FROM "Permissions" WHERE "Name" = 'Vehicles.Update');

INSERT INTO "Permissions" ("Id", "Name", "Description", "Resource", "Action", "IsActive", "CreatedAt", "CreatedBy", "UpdatedAt", "UpdatedBy")
SELECT 
    gen_random_uuid(),
    'Vehicles.Delete',
    'Permission to delete vehicles',
    'Vehicles',
    'Delete',
    true,
    NOW(),
    'System',
    NOW(),
    'System'
WHERE NOT EXISTS (SELECT 1 FROM "Permissions" WHERE "Name" = 'Vehicles.Delete');

INSERT INTO "Permissions" ("Id", "Name", "Description", "Resource", "Action", "IsActive", "CreatedAt", "CreatedBy", "UpdatedAt", "UpdatedBy")
SELECT 
    gen_random_uuid(),
    'Branches.Create',
    'Permission to create branches',
    'Branches',
    'Create',
    true,
    NOW(),
    'System',
    NOW(),
    'System'
WHERE NOT EXISTS (SELECT 1 FROM "Permissions" WHERE "Name" = 'Branches.Create');

INSERT INTO "Permissions" ("Id", "Name", "Description", "Resource", "Action", "IsActive", "CreatedAt", "CreatedBy", "UpdatedAt", "UpdatedBy")
SELECT 
    gen_random_uuid(),
    'Vendors.Create',
    'Permission to create vendors',
    'Vendors',
    'Create',
    true,
    NOW(),
    'System',
    NOW(),
    'System'
WHERE NOT EXISTS (SELECT 1 FROM "Permissions" WHERE "Name" = 'Vendors.Create');

-- Assign permissions to VendorAdmin role
INSERT INTO "RolePermissions" ("Id", "RoleId", "PermissionId", "IsActive", "CreatedAt", "CreatedBy", "UpdatedAt", "UpdatedBy")
SELECT 
    gen_random_uuid(),
    r."Id",
    p."Id",
    true,
    NOW(),
    'System',
    NOW(),
    'System'
FROM "Roles" r
CROSS JOIN "Permissions" p
WHERE r."Name" = 'VendorAdmin'
  AND p."Name" IN ('Vendors.Create', 'Branches.Create', 'Vehicles.Create', 'Vehicles.Update', 'Vehicles.View')
  AND NOT EXISTS (
    SELECT 1 FROM "RolePermissions" rp 
    WHERE rp."RoleId" = r."Id" AND rp."PermissionId" = p."Id"
  );

-- Assign permissions to BranchAdmin role
INSERT INTO "RolePermissions" ("Id", "RoleId", "PermissionId", "IsActive", "CreatedAt", "CreatedBy", "UpdatedAt", "UpdatedBy")
SELECT 
    gen_random_uuid(),
    r."Id",
    p."Id",
    true,
    NOW(),
    'System',
    NOW(),
    'System'
FROM "Roles" r
CROSS JOIN "Permissions" p
WHERE r."Name" = 'BranchAdmin'
  AND p."Name" IN ('Vehicles.Create', 'Vehicles.Update', 'Vehicles.View')
  AND NOT EXISTS (
    SELECT 1 FROM "RolePermissions" rp 
    WHERE rp."RoleId" = r."Id" AND rp."PermissionId" = p."Id"
  );

-- Assign permissions to Service Advisor role
INSERT INTO "RolePermissions" ("Id", "RoleId", "PermissionId", "IsActive", "CreatedAt", "CreatedBy", "UpdatedAt", "UpdatedBy")
SELECT 
    gen_random_uuid(),
    r."Id",
    p."Id",
    true,
    NOW(),
    'System',
    NOW(),
    'System'
FROM "Roles" r
CROSS JOIN "Permissions" p
WHERE r."Name" = 'Service Advisor'
  AND p."Name" IN ('Vehicles.View', 'Vehicles.Update')
  AND NOT EXISTS (
    SELECT 1 FROM "RolePermissions" rp 
    WHERE rp."RoleId" = r."Id" AND rp."PermissionId" = p."Id"
  );

-- Assign permissions to Technician role
INSERT INTO "RolePermissions" ("Id", "RoleId", "PermissionId", "IsActive", "CreatedAt", "CreatedBy", "UpdatedAt", "UpdatedBy")
SELECT 
    gen_random_uuid(),
    r."Id",
    p."Id",
    true,
    NOW(),
    'System',
    NOW(),
    'System'
FROM "Roles" r
CROSS JOIN "Permissions" p
WHERE r."Name" = 'Technician'
  AND p."Name" IN ('Vehicles.View', 'Vehicles.Update')
  AND NOT EXISTS (
    SELECT 1 FROM "RolePermissions" rp 
    WHERE rp."RoleId" = r."Id" AND rp."PermissionId" = p."Id"
  );

-- Assign permissions to Inventory Manager role
INSERT INTO "RolePermissions" ("Id", "RoleId", "PermissionId", "IsActive", "CreatedAt", "CreatedBy", "UpdatedAt", "UpdatedBy")
SELECT 
    gen_random_uuid(),
    r."Id",
    p."Id",
    true,
    NOW(),
    'System',
    NOW(),
    'System'
FROM "Roles" r
CROSS JOIN "Permissions" p
WHERE r."Name" = 'Inventory Manager'
  AND p."Name" IN ('Vehicles.View')
  AND NOT EXISTS (
    SELECT 1 FROM "RolePermissions" rp 
    WHERE rp."RoleId" = r."Id" AND rp."PermissionId" = p."Id"
  );

-- Assign permissions to Cashier role
INSERT INTO "RolePermissions" ("Id", "RoleId", "PermissionId", "IsActive", "CreatedAt", "CreatedBy", "UpdatedAt", "UpdatedBy")
SELECT 
    gen_random_uuid(),
    r."Id",
    p."Id",
    true,
    NOW(),
    'System',
    NOW(),
    'System'
FROM "Roles" r
CROSS JOIN "Permissions" p
WHERE r."Name" = 'Cashier'
  AND p."Name" IN ('Vehicles.View')
  AND NOT EXISTS (
    SELECT 1 FROM "RolePermissions" rp 
    WHERE rp."RoleId" = r."Id" AND rp."PermissionId" = p."Id"
  );

-- Display results
SELECT 'Roles added/updated:' as message;
SELECT "Name", "Description" FROM "Roles" WHERE "Name" IN ('VendorAdmin', 'BranchAdmin', 'Service Advisor', 'Technician', 'Inventory Manager', 'Cashier');

SELECT 'Permissions added/updated:' as message;
SELECT "Name", "Description" FROM "Permissions" WHERE "Name" IN ('Vehicles.Create', 'Vehicles.Update', 'Vehicles.Delete', 'Branches.Create', 'Vendors.Create');

SELECT 'Role-Permission assignments completed.' as message;
