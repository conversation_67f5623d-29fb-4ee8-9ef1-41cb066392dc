using System;
using System.Collections.Generic;
using System.Linq;
using IdentityService.Domain.Common;
using IdentityService.Domain.Enums;
using IdentityService.Domain.ValueObjects;

namespace IdentityService.Domain.Entities;

public class User : BaseEntity
{
    public string Email { get; private set; }
    public string PhoneNumber { get; private set; }
    public PasswordHash? PasswordHash { get; private set; }
    public bool EmailVerified { get; private set; }
    public bool PhoneNumberVerified { get; private set; }
    public bool RememberMe { get; private set; }
    public DateTime? LastLoginAt { get; private set; }
    public string RefreshToken { get; private set; }
    public DateTime? RefreshTokenExpiryTime { get; private set; }


    // Security stamp that changes when security-critical actions occur (password change, etc.)
    // This is used to invalidate tokens when security-critical changes are made
    public DateTime SecurityStamp { get; private set; }

    public List<UserRole> UserRoles { get; private set; }
    public List<UserPermission> UserPermissions { get; private set; }
    public List<PasswordResetToken> PasswordResetTokens { get; private set; }
    public List<UserSubscription> Subscriptions { get; private set; }
    public List<UserBranch> UserBranches { get; private set; }

    // Navigation properties for vendor and primary branch
    public Guid? VendorId { get; private set; }
    public Guid? PrimaryBranchId { get; private set; }
    public virtual Vendor Vendor { get; private set; }

    public User()
    {
        UserRoles = new List<UserRole>();
        UserPermissions = new List<UserPermission>();
        PasswordResetTokens = new List<PasswordResetToken>();
        Subscriptions = new List<UserSubscription>();
        UserBranches = new List<UserBranch>();
        RefreshToken = string.Empty; // Initialize with empty string to avoid null reference
        SecurityStamp = DateTime.UtcNow; // Initialize security stamp with current time
    }

    public static User Create(
        string email,
        string phoneNumber,
        string? passwordHash,
        string createdBy)
    {
        return new User
        {
            Email = email,
            PhoneNumber = phoneNumber,
            PasswordHash = PasswordHash.FromNullableString(passwordHash),
            CreatedBy = createdBy,
            RefreshToken = string.Empty
        };
    }

    public void UpdatePassword(string newPasswordHash, string updatedBy)
    {
        PasswordHash = PasswordHash.FromNullableString(newPasswordHash);
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        // Update security stamp to invalidate existing tokens
        UpdateSecurityStamp();
    }

    // Updates the security stamp to invalidate existing tokens
    public void UpdateSecurityStamp()
    {
        SecurityStamp = DateTime.UtcNow;
    }

    public void SetRememberMe(bool rememberMe)
    {
        RememberMe = rememberMe;
        UpdatedAt = DateTime.UtcNow;
    }

    public void UpdateLastLogin()
    {
        LastLoginAt = DateTime.UtcNow;
    }

    public void SetRefreshToken(string token, DateTime expiryTime)
    {
        RefreshToken = token;
        RefreshTokenExpiryTime = expiryTime;
    }

    public void VerifyEmail()
    {
        EmailVerified = true;
        UpdatedAt = DateTime.UtcNow;
    }

    public void VerifyPhoneNumber()
    {
        PhoneNumberVerified = true;
        UpdatedAt = DateTime.UtcNow;
    }

    public void UpdateEmail(string email, string updatedBy)
    {
        Email = email;
        EmailVerified = false;
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;
    }

    public void UpdatePhoneNumber(string phoneNumber, string updatedBy)
    {
        PhoneNumber = phoneNumber;
        PhoneNumberVerified = false;
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;
    }

    public void Delete(string updatedBy)
    {
        IsDeleted = true;
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;
    }

    public void Restore(string updatedBy)
    {
        IsDeleted = false;
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;
    }

    public void AddRole(Role role, string addedBy)
    {
        UserRoles.Add(UserRole.Create(Id, role.Id, addedBy, this, role));
    }

    public void RemoveRole(Role role)
    {
        UserRoles.RemoveAll(ur => ur.RoleId == role.Id);
    }

    public void AddSubscription(
        Guid subscriptionId,
        string planName,
        SubscriptionTier tier,
        DateTime startDate,
        DateTime endDate,
        DateTime? trialEndDate,
        SubscriptionStatus status,
        string addedBy)
    {
        var subscription = UserSubscription.Create(
            Id,
            this,
            subscriptionId,
            planName,
            tier,
            startDate,
            endDate,
            trialEndDate,
            status,
            addedBy);

        Subscriptions.Add(subscription);
    }

    public void UpdateSubscriptionStatus(Guid subscriptionId, SubscriptionStatus status, string updatedBy)
    {
        var subscription = Subscriptions.FirstOrDefault(s => s.SubscriptionId == subscriptionId);
        if (subscription != null)
        {
            subscription.UpdateStatus(status, updatedBy);
        }
    }

    public bool HasActiveSubscription()
    {
        return Subscriptions.Any(s => s.IsActive());
    }

    public SubscriptionTier GetHighestSubscriptionTier()
    {
        if (!HasActiveSubscription())
            return SubscriptionTier.Free;

        return Subscriptions
            .Where(s => s.IsActive())
            .OrderByDescending(s => s.Tier)
            .Select(s => s.Tier)
            .FirstOrDefault();
    }

    public bool HasSubscriptionFeature(string featureName)
    {
        return Subscriptions
            .Where(s => s.IsActive())
            .Any(s => s.HasFeature(featureName));
    }

    public void SetVendor(Guid vendorId, string updatedBy)
    {
        VendorId = vendorId;
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;
    }

    public void SetPrimaryBranch(Guid branchId, string updatedBy)
    {
        PrimaryBranchId = branchId;
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;
    }

    public void AddBranch(Branch branch, bool isPrimary, string addedBy)
    {
        var userBranch = UserBranch.Create(this, branch, isPrimary, addedBy);
        UserBranches.Add(userBranch);

        if (isPrimary)
        {
            PrimaryBranchId = branch.Id;
        }
    }

    public void RemoveBranch(Guid branchId)
    {
        UserBranches.RemoveAll(ub => ub.BranchId == branchId);

        if (PrimaryBranchId == branchId)
        {
            // If we removed the primary branch, set a new primary branch if available
            var firstBranch = UserBranches.FirstOrDefault();
            PrimaryBranchId = firstBranch?.BranchId;

            if (firstBranch != null)
            {
                firstBranch.SetAsPrimary("System");
            }
        }
    }

    public bool HasAccessToBranch(Guid branchId)
    {
        return UserBranches.Any(ub => ub.BranchId == branchId);
    }

    public bool IsVendorAdmin()
    {
        return UserRoles.Any(ur => ur.Role?.Name == "VendorAdmin");
    }

    public bool IsBranchAdmin()
    {
        return UserRoles.Any(ur => ur.Role?.Name == "BranchAdmin");
    }

    // User Permission Management Methods

    /// <summary>
    /// Adds a direct permission to the user
    /// </summary>
    public void AddPermission(Permission permission, PermissionType type, DateTime? expiresAt, string? reason, string addedBy)
    {
        // Remove existing permission if it exists
        RemovePermission(permission.Id);

        var userPermission = UserPermission.Create(this, permission, type, expiresAt, reason, addedBy);
        UserPermissions.Add(userPermission);
    }

    /// <summary>
    /// Removes a direct permission from the user
    /// </summary>
    public void RemovePermission(Guid permissionId)
    {
        UserPermissions.RemoveAll(up => up.PermissionId == permissionId);
    }

    /// <summary>
    /// Grants a permission to the user (convenience method)
    /// </summary>
    public void GrantPermission(Permission permission, DateTime? expiresAt, string? reason, string addedBy)
    {
        AddPermission(permission, PermissionType.Grant, expiresAt, reason, addedBy);
    }

    /// <summary>
    /// Denies a permission to the user (convenience method)
    /// </summary>
    public void DenyPermission(Permission permission, DateTime? expiresAt, string? reason, string addedBy)
    {
        AddPermission(permission, PermissionType.Deny, expiresAt, reason, addedBy);
    }

    /// <summary>
    /// Checks if user has a specific direct permission (not through roles)
    /// </summary>
    public bool HasDirectPermission(string permissionName)
    {
        return UserPermissions.Any(up =>
            up.Permission.Name == permissionName &&
            up.GrantsAccess());
    }

    /// <summary>
    /// Checks if user is explicitly denied a permission
    /// </summary>
    public bool IsPermissionDenied(string permissionName)
    {
        return UserPermissions.Any(up =>
            up.Permission.Name == permissionName &&
            up.DeniesAccess());
    }

    /// <summary>
    /// Gets all active user permissions
    /// </summary>
    public List<UserPermission> GetActivePermissions()
    {
        return UserPermissions.Where(up => up.IsCurrentlyActive()).ToList();
    }
}