using FluentValidation;
using InventoryManagementService.Domain.Interfaces;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using VMS.Contracts.Common.Authorization;

namespace InventoryManagementService.Application.Features.Parts.Commands;

public class DeactivatePartCommand : IRequest
{
    public Guid PartId { get; set; }
    public string UpdatedBy { get; set; } = string.Empty;
}

public class DeactivatePartCommandValidator : AbstractValidator<DeactivatePartCommand>
{
    public DeactivatePartCommandValidator()
    {
        RuleFor(x => x.PartId)
            .NotEmpty().WithMessage("Part ID is required");

        RuleFor(x => x.UpdatedBy)
            .NotEmpty().WithMessage("UpdatedBy is required");
    }
}

public class DeactivatePartCommandHandler : IRequestHandler<DeactivatePartCommand>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<DeactivatePartCommandHandler> _logger;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly IEventPublisher _eventPublisher;

    public DeactivatePartCommandHandler(
        IUnitOfWork unitOfWork,
        ILogger<DeactivatePartCommandHandler> logger,
        IHttpContextAccessor httpContextAccessor,
        IEventPublisher eventPublisher)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
        _httpContextAccessor = httpContextAccessor;
        _eventPublisher = eventPublisher;
    }

    public async Task Handle(DeactivatePartCommand request, CancellationToken cancellationToken)
    {
        // Check permissions - Admin roles bypass, others need explicit permission
        var user = _httpContextAccessor.HttpContext?.User;
        if (!PermissionHelper.IsAdmin(user))
        {
            PermissionHelper.ValidateAccess(user, "Parts.Update");
        }

        _logger.LogInformation("Deactivating part with ID: {PartId}", request.PartId);

        var part = await _unitOfWork.Parts.GetByIdAsync(request.PartId);

        if (part == null)
        {
            throw new KeyNotFoundException($"Part with ID {request.PartId} not found");
        }

        part.Deactivate();
        part.SetUpdatedBy(request.UpdatedBy);

        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("Part deactivated successfully with ID: {PartId}", part.Id);

        // Publish event
        await _eventPublisher.PublishPartDeactivatedEvent(part.Id, part.SKU);
    }
}
