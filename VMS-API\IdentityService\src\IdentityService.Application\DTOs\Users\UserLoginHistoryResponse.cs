using System;

namespace IdentityService.Application.DTOs.Users;

/// <summary>
/// Response DTO for user login history
/// </summary>
public class UserLoginHistoryResponse
{
    /// <summary>
    /// The ID of the login history entry
    /// </summary>
    public Guid Id { get; set; }
    
    /// <summary>
    /// The ID of the user who attempted to log in
    /// </summary>
    public Guid? UserId { get; set; }
    
    /// <summary>
    /// The email used in the login attempt
    /// </summary>
    public string Email { get; set; }
    
    /// <summary>
    /// The phone number used in the login attempt (if applicable)
    /// </summary>
    public string PhoneNumber { get; set; }
    
    /// <summary>
    /// Whether the login was successful
    /// </summary>
    public bool IsSuccessful { get; set; }
    
    /// <summary>
    /// The failure reason (if applicable)
    /// </summary>
    public string FailureReason { get; set; }
    
    /// <summary>
    /// The IP address of the client
    /// </summary>
    public string IpAddress { get; set; }
    
    /// <summary>
    /// The user agent of the client
    /// </summary>
    public string UserAgent { get; set; }
    
    /// <summary>
    /// The device information of the client
    /// </summary>
    public string DeviceInfo { get; set; }
    
    /// <summary>
    /// The location information of the client (if available)
    /// </summary>
    public string Location { get; set; }
    
    /// <summary>
    /// The login method used
    /// </summary>
    public string LoginMethod { get; set; }
    
    /// <summary>
    /// When the login attempt occurred
    /// </summary>
    public DateTime LoginTime { get; set; }
}
