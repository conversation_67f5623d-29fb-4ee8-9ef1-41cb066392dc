namespace IdentityService.Domain.Enums;

/// <summary>
/// Represents the status of a user session
/// </summary>
public enum SessionStatus
{
    /// <summary>
    /// The session is active
    /// </summary>
    Active = 0,
    
    /// <summary>
    /// The session has ended normally (user logged out)
    /// </summary>
    Ended = 1,
    
    /// <summary>
    /// The session has expired (timeout)
    /// </summary>
    Expired = 2,
    
    /// <summary>
    /// The session was terminated for security reasons
    /// </summary>
    Terminated = 3
}
