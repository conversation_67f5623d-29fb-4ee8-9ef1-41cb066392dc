using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace IdentityService.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class OptimizeSessionIndexes : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Add LastActiveAt index for session cleanup queries
            migrationBuilder.CreateIndex(
                name: "IX_UserSessions_LastActiveAt",
                table: "UserSessions",
                column: "LastActiveAt");

            // Add composite indexes for optimized session cleanup queries
            migrationBuilder.CreateIndex(
                name: "IX_UserSessions_Status_ExpiresAt",
                table: "UserSessions",
                columns: new[] { "Status", "ExpiresAt" });

            migrationBuilder.CreateIndex(
                name: "IX_UserSessions_Status_LastActiveAt_ExpiresAt",
                table: "UserSessions",
                columns: new[] { "Status", "LastActiveAt", "ExpiresAt" });

            migrationBuilder.CreateIndex(
                name: "IX_UserSessions_UserId_Status_ExpiresAt",
                table: "UserSessions",
                columns: new[] { "UserId", "Status", "ExpiresAt" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Drop the indexes in reverse order
            migrationBuilder.DropIndex(
                name: "IX_UserSessions_UserId_Status_ExpiresAt",
                table: "UserSessions");

            migrationBuilder.DropIndex(
                name: "IX_UserSessions_Status_LastActiveAt_ExpiresAt",
                table: "UserSessions");

            migrationBuilder.DropIndex(
                name: "IX_UserSessions_Status_ExpiresAt",
                table: "UserSessions");

            migrationBuilder.DropIndex(
                name: "IX_UserSessions_LastActiveAt",
                table: "UserSessions");
        }
    }
}
