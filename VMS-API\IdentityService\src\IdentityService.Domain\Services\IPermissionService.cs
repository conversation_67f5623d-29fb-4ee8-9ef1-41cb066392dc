using IdentityService.Domain.Entities;
using IdentityService.Domain.Enums;

namespace IdentityService.Domain.Services;

/// <summary>
/// Service for advanced permission resolution combining role-based and user-specific permissions
/// </summary>
public interface IPermissionService
{
    /// <summary>
    /// Resolves all effective permissions for a user (role-based + user-specific)
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <returns>List of effective permissions</returns>
    Task<List<Permission>> GetEffectivePermissionsAsync(Guid userId);

    /// <summary>
    /// Checks if a user has access to a specific permission
    /// Considers both role-based and user-specific permissions with proper precedence
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="permissionName">Permission name to check</param>
    /// <returns>True if user has access, false otherwise</returns>
    Task<bool> HasPermissionAsync(Guid userId, string permissionName);

    /// <summary>
    /// Gets all permissions a user has through roles only
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <returns>List of role-based permissions</returns>
    Task<List<Permission>> GetRoleBasedPermissionsAsync(Guid userId);

    /// <summary>
    /// Gets all direct user permissions (not through roles)
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <returns>List of user-specific permissions</returns>
    Task<List<UserPermission>> GetUserSpecificPermissionsAsync(Guid userId);

    /// <summary>
    /// Assigns a permission directly to a user
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="permissionId">Permission ID</param>
    /// <param name="type">Grant or Deny</param>
    /// <param name="expiresAt">Optional expiration date</param>
    /// <param name="reason">Reason for assignment</param>
    /// <param name="assignedBy">Who assigned the permission</param>
    Task AssignUserPermissionAsync(Guid userId, Guid permissionId, PermissionType type, DateTime? expiresAt, string? reason, string assignedBy);

    /// <summary>
    /// Removes a direct permission from a user
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="permissionId">Permission ID</param>
    Task RemoveUserPermissionAsync(Guid userId, Guid permissionId);

    /// <summary>
    /// Gets permission analysis for a user showing sources (role vs direct)
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <returns>Detailed permission analysis</returns>
    Task<PermissionAnalysis> GetPermissionAnalysisAsync(Guid userId);

    /// <summary>
    /// Bulk assigns multiple permissions to a user
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="permissionAssignments">List of permission assignments</param>
    Task BulkAssignUserPermissionsAsync(Guid userId, List<UserPermissionAssignment> permissionAssignments);

    /// <summary>
    /// Cleans up expired user permissions
    /// </summary>
    Task CleanupExpiredPermissionsAsync();
}

/// <summary>
/// Represents a permission assignment request
/// </summary>
public class UserPermissionAssignment
{
    public Guid PermissionId { get; set; }
    public PermissionType Type { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public string? Reason { get; set; }
}

/// <summary>
/// Detailed analysis of a user's permissions
/// </summary>
public class PermissionAnalysis
{
    public Guid UserId { get; set; }
    public List<PermissionSource> Permissions { get; set; } = new();
    public List<string> ConflictingPermissions { get; set; } = new();
    public List<string> ExpiredPermissions { get; set; } = new();
}

/// <summary>
/// Represents the source of a permission
/// </summary>
public class PermissionSource
{
    public string PermissionName { get; set; } = string.Empty;
    public string Source { get; set; } = string.Empty; // "Role: RoleName" or "Direct"
    public PermissionType? Type { get; set; } // Only for direct permissions
    public DateTime? ExpiresAt { get; set; }
    public bool IsActive { get; set; }
    public bool IsEffective { get; set; } // Final result after conflict resolution
}
