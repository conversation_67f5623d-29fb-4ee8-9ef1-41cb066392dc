using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System;

namespace BranchManagementService.Infrastructure.Persistence
{
    public static class MigrationManager
    {
        public static IHost MigrateDatabase(this IHost host)
        {
            using (var scope = host.Services.CreateScope())
            {
                using (var appContext = scope.ServiceProvider.GetRequiredService<BranchDbContext>())
                {
                    try
                    {
                        var logger = scope.ServiceProvider.GetRequiredService<ILogger<BranchDbContext>>();
                        logger.LogInformation("Applying migrations...");
                        
                        appContext.Database.Migrate();
                        
                        logger.LogInformation("Migrations applied successfully");
                        
                        // Seed data if needed
                        SeedData(appContext, logger);
                    }
                    catch (Exception ex)
                    {
                        var logger = scope.ServiceProvider.GetRequiredService<ILogger<BranchDbContext>>();
                        logger.LogError(ex, "An error occurred while applying migrations");
                        throw;
                    }
                }
            }

            return host;
        }

        private static void SeedData(BranchDbContext context, ILogger<BranchDbContext> logger)
        {
            logger.LogInformation("Checking if seed data is needed...");
            
            // Check if there are any branches in the database
            if (!context.Branches.Any())
            {
                logger.LogInformation("Seeding branch data...");
                
                // Add seed data
                var demoVendorId = Guid.Parse("51ee3ae6-7fba-4973-9086-05d791f41498"); // Demo vendor ID
                
                var branches = new List<Domain.Entities.Branch>
                {
                    Domain.Entities.Branch.Create(
                        demoVendorId,
                        "Main Branch",
                        "<EMAIL>",
                        "John Doe",
                        "9876543210",
                        "123 Main Street",
                        "Mumbai",
                        "Maharashtra",
                        "400001",
                        Domain.Enums.VehicleTypeSupport.Both,
                        "System"
                    ),
                    Domain.Entities.Branch.Create(
                        demoVendorId,
                        "North Branch",
                        "<EMAIL>",
                        "Jane Smith",
                        "8765432109",
                        "456 North Avenue",
                        "Delhi",
                        "Delhi",
                        "110001",
                        Domain.Enums.VehicleTypeSupport.Car,
                        "System"
                    ),
                    Domain.Entities.Branch.Create(
                        demoVendorId,
                        "South Branch",
                        "<EMAIL>",
                        "Robert Johnson",
                        "7654321098",
                        "789 South Road",
                        "Bangalore",
                        "Karnataka",
                        "560001",
                        Domain.Enums.VehicleTypeSupport.Bike,
                        "System"
                    )
                };
                
                context.Branches.AddRange(branches);
                context.SaveChanges();
                
                logger.LogInformation("Seed data added successfully");
            }
            else
            {
                logger.LogInformation("Database already contains branch data, skipping seed");
            }
        }
    }
}
