using System;
using System.Threading;
using System.Threading.Tasks;
using IdentityService.Domain.Common;
using IdentityService.Domain.Entities;
using IdentityService.Domain.Interfaces;
using IdentityService.Infrastructure.Persistence.Migrations;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Diagnostics;

namespace IdentityService.Infrastructure.Persistence;

public class ApplicationDbContext : DbContext
{
    private readonly ICurrentUserService _currentUserService;
    private readonly IDateTime _dateTime;

    public ApplicationDbContext(
        DbContextOptions<ApplicationDbContext> options,
        ICurrentUserService currentUserService,
        IDateTime dateTime) : base(options)
    {
        _currentUserService = currentUserService;
        _dateTime = dateTime;
    }

    public DbSet<User> Users { get; set; }
    public DbSet<Role> Roles { get; set; }
    public DbSet<Permission> Permissions { get; set; }
    public DbSet<UserRole> UserRoles { get; set; }
    public DbSet<RolePermission> RolePermissions { get; set; }
    public DbSet<PasswordResetToken> PasswordResetTokens { get; set; }
    public DbSet<AuditLog> AuditLogs { get; set; }
    public DbSet<Menu> Menus { get; set; }
    public DbSet<MenuPermission> MenuPermissions { get; set; }
    public DbSet<Branch> Branches { get; set; }
    public DbSet<UserBranch> UserBranches { get; set; }
    public DbSet<UserSubscription> UserSubscriptions { get; set; }
    public DbSet<SubscriptionFeature> SubscriptionFeatures { get; set; }
    public DbSet<UserSession> UserSessions { get; set; }
    public DbSet<UserLoginHistory> UserLoginHistories { get; set; }

    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        // Handle required properties before saving
        foreach (var entry in ChangeTracker.Entries<BaseEntity>())
        {
            switch (entry.State)
            {
                case EntityState.Added:
                    // Set CreatedBy if not already set
                    if (string.IsNullOrEmpty(entry.Entity.CreatedBy))
                    {
                        entry.Entity.SetCreatedBy(_currentUserService.UserId?.ToString() ?? "System");
                    }
                    break;
                case EntityState.Modified:
                    // Set UpdatedBy
                    entry.Entity.SetUpdatedBy(_currentUserService.UserId?.ToString() ?? "System");
                    break;
            }
        }

        try
        {
            return await base.SaveChangesAsync(cancellationToken);
        }
        catch (DbUpdateException ex)
        {
            // Log the error
            Console.WriteLine($"Database update error: {ex.Message}");

            // Check for null constraint violations
            if (ex.InnerException != null &&
                (ex.InnerException.Message.Contains("NOT NULL") ||
                 ex.InnerException.Message.Contains("null value")))
            {
                // Try to identify the entity and property causing the issue
                Console.WriteLine("Null constraint violation detected. Entity validation failed.");
            }

            throw; // Re-throw the exception to be handled by the global exception handler
        }
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.ApplyConfigurationsFromAssembly(typeof(ApplicationDbContext).Assembly);
        base.OnModelCreating(modelBuilder);
    }

    public async Task SeedDataAsync()
    {
        await SeedData.SeedAsync(this);
    }
}