# Debugging BranchManagementService in VS Code

This document provides instructions for debugging the BranchManagementService in Visual Studio Code.

## Prerequisites

1. Visual Studio Code installed
2. .NET 8.0 SDK installed
3. C# extension for VS Code installed

## Debugging Steps

1. Open the BranchManagementService folder in VS Code:
   ```
   code VMS-API\BranchManagementService
   ```

2. Open the Run and Debug view by clicking on the Run icon in the Activity Bar on the side of VS Code or by pressing `Ctrl+Shift+D`.

3. From the dropdown at the top of the Run and Debug view, select "BranchManagementService".

4. Click the green play button or press `F5` to start debugging.

5. VS Code will:
   - Build the project
   - Launch the BranchManagementService
   - Open the Swagger UI in your default browser

6. You can set breakpoints in your code by clicking in the gutter (the space to the left of the line numbers) of any .cs file.

7. When execution reaches a breakpoint, VS Code will pause execution and allow you to:
   - Inspect variables
   - Step through code
   - Evaluate expressions in the Debug Console

## Available Tasks

The following tasks are available in VS Code:

- **build**: Builds the solution
- **publish**: Publishes the solution
- **watch**: Runs the API with hot reload
- **ef-migrations-add**: Adds a new EF Core migration (prompts for name)
- **ef-database-update**: Updates the database to the latest migration

To run a task:
1. Press `Ctrl+Shift+P` to open the Command Palette
2. Type "Tasks: Run Task" and select it
3. Choose the task you want to run

## Troubleshooting

If you encounter issues:

1. **Port already in use**: Make sure no other service is running on port 5004
2. **Database connection issues**: Verify your connection string in appsettings.json
3. **Build errors**: Check the Problems panel for compilation errors

## Additional Resources

- [VS Code Debugging Documentation](https://code.visualstudio.com/docs/editor/debugging)
- [C# in VS Code](https://code.visualstudio.com/docs/languages/csharp)
- [Entity Framework Core Tools](https://docs.microsoft.com/en-us/ef/core/cli/dotnet)
