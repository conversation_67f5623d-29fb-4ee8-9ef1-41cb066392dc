using InventoryManagementService.Domain.Common;
using InventoryManagementService.Domain.Enums;

namespace InventoryManagementService.Domain.Entities;

public class InventoryAdjustment : BaseEntity
{
    public Guid VendorId { get; private set; }
    public Guid BranchId { get; private set; }
    public string AdjustmentNumber { get; private set; } = string.Empty;
    public AdjustmentReason Reason { get; private set; }
    public string ReasonDescription { get; private set; } = string.Empty;
    public DateTime AdjustmentDate { get; private set; }
    public string? Notes { get; private set; }
    public string? ApprovedBy { get; private set; }
    public DateTime? ApprovedDate { get; private set; }
    public bool IsApproved { get; private set; }

    // Navigation properties
    public List<InventoryAdjustmentItem> Items { get; private set; } = new();

    private InventoryAdjustment() { }

    public static InventoryAdjustment Create(
        Guid vendorId,
        Guid branchId,
        string adjustmentNumber,
        AdjustmentReason reason,
        string reasonDescription,
        string createdBy,
        string? notes = null)
    {
        return new InventoryAdjustment
        {
            VendorId = vendorId,
            BranchId = branchId,
            AdjustmentNumber = adjustmentNumber.Trim(),
            Reason = reason,
            ReasonDescription = reasonDescription.Trim(),
            AdjustmentDate = DateTime.UtcNow,
            Notes = notes?.Trim(),
            IsApproved = false,
            CreatedBy = createdBy
        };
    }

    public void AddItem(Guid partId, int previousQuantity, int newQuantity, string createdBy, string? notes = null)
    {
        if (IsApproved)
            throw new InvalidOperationException("Cannot add items to an approved adjustment");

        var item = InventoryAdjustmentItem.Create(Id, partId, previousQuantity, newQuantity, createdBy, notes);
        Items.Add(item);
    }

    public void RemoveItem(Guid itemId)
    {
        if (IsApproved)
            throw new InvalidOperationException("Cannot remove items from an approved adjustment");

        var item = Items.FirstOrDefault(i => i.Id == itemId);
        if (item != null)
        {
            Items.Remove(item);
        }
    }

    public void Approve(string approvedBy)
    {
        if (IsApproved)
            throw new InvalidOperationException("Adjustment is already approved");

        if (!Items.Any())
            throw new InvalidOperationException("Cannot approve adjustment without items");

        IsApproved = true;
        ApprovedBy = approvedBy;
        ApprovedDate = DateTime.UtcNow;
        SetUpdatedBy(approvedBy);
    }

    public void UpdateNotes(string notes, string updatedBy)
    {
        if (IsApproved)
            throw new InvalidOperationException("Cannot update notes for approved adjustment");

        Notes = notes?.Trim();
        SetUpdatedBy(updatedBy);
    }
}
