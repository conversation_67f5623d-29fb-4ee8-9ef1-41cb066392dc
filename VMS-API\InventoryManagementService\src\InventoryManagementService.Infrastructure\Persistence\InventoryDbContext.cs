using InventoryManagementService.Domain.Entities;
using Microsoft.EntityFrameworkCore;

namespace InventoryManagementService.Infrastructure.Persistence;

public class InventoryDbContext : DbContext
{
    public InventoryDbContext(DbContextOptions<InventoryDbContext> options) : base(options)
    {
    }

    public DbSet<Part> Parts { get; set; }
    public DbSet<PartCompatibility> PartCompatibilities { get; set; }
    public DbSet<InventoryItem> InventoryItems { get; set; }
    public DbSet<InventoryTransaction> InventoryTransactions { get; set; }
    public DbSet<StockTransfer> StockTransfers { get; set; }
    public DbSet<StockTransferItem> StockTransferItems { get; set; }
    public DbSet<InventoryAdjustment> InventoryAdjustments { get; set; }
    public DbSet<InventoryAdjustmentItem> InventoryAdjustmentItems { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Part configuration
        modelBuilder.Entity<Part>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.SKU).IsRequired().HasMaxLength(50);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(200);
            entity.Property(e => e.Description).IsRequired().HasMaxLength(1000);
            entity.Property(e => e.Manufacturer).IsRequired().HasMaxLength(100);
            entity.Property(e => e.CostPrice).HasColumnType("decimal(18,2)");
            entity.Property(e => e.RetailPrice).HasColumnType("decimal(18,2)");
            entity.Property(e => e.PartNumber).HasMaxLength(100);
            entity.Property(e => e.Barcode).HasMaxLength(100);
            entity.Property(e => e.ImageUrl).HasMaxLength(500);
            entity.Property(e => e.Notes).HasMaxLength(1000);
            entity.Property(e => e.CreatedBy).IsRequired().HasMaxLength(100);
            entity.Property(e => e.UpdatedBy).HasMaxLength(100);

            entity.HasIndex(e => e.SKU).IsUnique();
            entity.HasIndex(e => e.Name);
            entity.HasIndex(e => e.Manufacturer);
            entity.HasIndex(e => e.Category);
        });

        // PartCompatibility configuration
        modelBuilder.Entity<PartCompatibility>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Make).IsRequired().HasMaxLength(50);
            entity.Property(e => e.Model).IsRequired().HasMaxLength(50);
            entity.Property(e => e.EngineType).HasMaxLength(50);
            entity.Property(e => e.Variant).HasMaxLength(50);
            entity.Property(e => e.CreatedBy).IsRequired().HasMaxLength(100);
            entity.Property(e => e.UpdatedBy).HasMaxLength(100);

            entity.HasOne(e => e.Part)
                  .WithMany(p => p.Compatibilities)
                  .HasForeignKey(e => e.PartId)
                  .OnDelete(DeleteBehavior.Cascade);

            entity.HasIndex(e => new { e.PartId, e.Make, e.Model, e.YearFrom, e.YearTo });
        });

        // InventoryItem configuration
        modelBuilder.Entity<InventoryItem>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.StorageLocation).IsRequired().HasMaxLength(100);
            entity.Property(e => e.Bin).HasMaxLength(50);
            entity.Property(e => e.Shelf).HasMaxLength(50);
            entity.Property(e => e.CreatedBy).IsRequired().HasMaxLength(100);
            entity.Property(e => e.UpdatedBy).HasMaxLength(100);

            entity.HasOne(e => e.Part)
                  .WithMany(p => p.InventoryItems)
                  .HasForeignKey(e => e.PartId)
                  .OnDelete(DeleteBehavior.Restrict);

            entity.HasIndex(e => new { e.PartId, e.BranchId }).IsUnique();
            entity.HasIndex(e => e.BranchId);
            entity.HasIndex(e => e.VendorId);
        });

        // InventoryTransaction configuration
        modelBuilder.Entity<InventoryTransaction>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Reason).IsRequired().HasMaxLength(200);
            entity.Property(e => e.Reference).HasMaxLength(100);
            entity.Property(e => e.CreatedBy).IsRequired().HasMaxLength(100);

            entity.HasOne(e => e.InventoryItem)
                  .WithMany(i => i.Transactions)
                  .HasForeignKey(e => e.InventoryItemId)
                  .OnDelete(DeleteBehavior.Cascade);

            entity.HasIndex(e => e.InventoryItemId);
            entity.HasIndex(e => e.TransactionDate);
            entity.HasIndex(e => e.TransactionType);
        });

        // StockTransfer configuration
        modelBuilder.Entity<StockTransfer>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.TransferNumber).IsRequired().HasMaxLength(50);
            entity.Property(e => e.Notes).HasMaxLength(1000);
            entity.Property(e => e.ShippedBy).HasMaxLength(100);
            entity.Property(e => e.ReceivedBy).HasMaxLength(100);
            entity.Property(e => e.CreatedBy).IsRequired().HasMaxLength(100);
            entity.Property(e => e.UpdatedBy).HasMaxLength(100);

            entity.HasIndex(e => e.TransferNumber).IsUnique();
            entity.HasIndex(e => e.SourceBranchId);
            entity.HasIndex(e => e.DestinationBranchId);
            entity.HasIndex(e => e.Status);
            entity.HasIndex(e => e.VendorId);
        });

        // StockTransferItem configuration
        modelBuilder.Entity<StockTransferItem>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Notes).HasMaxLength(500);
            entity.Property(e => e.CreatedBy).IsRequired().HasMaxLength(100);
            entity.Property(e => e.UpdatedBy).HasMaxLength(100);

            entity.HasOne(e => e.StockTransfer)
                  .WithMany(st => st.Items)
                  .HasForeignKey(e => e.StockTransferId)
                  .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(e => e.Part)
                  .WithMany()
                  .HasForeignKey(e => e.PartId)
                  .OnDelete(DeleteBehavior.Restrict);

            entity.HasIndex(e => e.StockTransferId);
            entity.HasIndex(e => e.PartId);
        });

        // InventoryAdjustment configuration
        modelBuilder.Entity<InventoryAdjustment>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.AdjustmentNumber).IsRequired().HasMaxLength(50);
            entity.Property(e => e.ReasonDescription).IsRequired().HasMaxLength(200);
            entity.Property(e => e.Notes).HasMaxLength(1000);
            entity.Property(e => e.ApprovedBy).HasMaxLength(100);
            entity.Property(e => e.CreatedBy).IsRequired().HasMaxLength(100);
            entity.Property(e => e.UpdatedBy).HasMaxLength(100);

            entity.HasIndex(e => e.AdjustmentNumber).IsUnique();
            entity.HasIndex(e => e.BranchId);
            entity.HasIndex(e => e.VendorId);
            entity.HasIndex(e => e.Reason);
            entity.HasIndex(e => e.IsApproved);
        });

        // InventoryAdjustmentItem configuration
        modelBuilder.Entity<InventoryAdjustmentItem>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Notes).HasMaxLength(500);
            entity.Property(e => e.CreatedBy).IsRequired().HasMaxLength(100);
            entity.Property(e => e.UpdatedBy).HasMaxLength(100);

            entity.HasOne(e => e.InventoryAdjustment)
                  .WithMany(ia => ia.Items)
                  .HasForeignKey(e => e.InventoryAdjustmentId)
                  .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(e => e.Part)
                  .WithMany()
                  .HasForeignKey(e => e.PartId)
                  .OnDelete(DeleteBehavior.Restrict);

            entity.HasIndex(e => e.InventoryAdjustmentId);
            entity.HasIndex(e => e.PartId);
        });
    }
}
