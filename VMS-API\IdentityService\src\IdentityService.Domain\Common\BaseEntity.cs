using System;

namespace IdentityService.Domain.Common;

public abstract class BaseEntity
{
    public Guid Id { get;  set; }
    public DateTime CreatedAt { get;  set; }
    public DateTime? UpdatedAt { get;  set; }
    public string CreatedBy { get; set; } = "System"; // Default value to prevent null exceptions
    public string? UpdatedBy { get;  set; }
    public bool IsDeleted { get;  set; }

    // Concurrency token to handle optimistic concurrency
    // This is managed by the database, so we don't set a default value
    public byte[]? RowVersion { get; set; }

    public BaseEntity()
    {
        Id = Guid.NewGuid();
        CreatedAt = DateTime.UtcNow;
    }

    public void SetCreatedBy(string createdBy)
    {
        if (string.IsNullOrEmpty(CreatedBy))
        {
            CreatedBy = createdBy;
            CreatedAt = DateTime.UtcNow;
        }
    }

    public void SetUpdatedBy(string updatedBy)
    {
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;
    }

    public void MarkAsDeleted()
    {
        IsDeleted = true;
        SetUpdatedBy("System");
    }
}