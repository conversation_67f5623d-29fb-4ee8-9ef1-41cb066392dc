using System.Text;
using Microsoft.OpenApi.Models;
using IdentityService.Domain.Interfaces;
using IdentityService.Application.Interfaces;
using IdentityService.Application.Features.Auth.Commands;
using IdentityService.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Serilog;
using Serilog.Events;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using MediatR;
using AutoMapper;
using IdentityService.Infrastructure.Persistence.Repositories;
using System.Security.Claims;
using Microsoft.AspNetCore.Http;
using IdentityService.Infrastructure.Services;
using IdentityService.Infrastructure.Messaging;
using Microsoft.AspNetCore.Rewrite;
using IdentityService.Domain.Enums;
using IdentityService.Infrastructure.Authorization;
using Microsoft.AspNetCore.Authorization;
using FluentValidation;
using IdentityService.Application.Behaviors;
using IdentityService.API.Middleware;
using IdentityService.API.Filters;
using IdentityService.Application.Common;
using VMSContracts.ServiceClients;
using VMSContracts.Common.Authorization;
using IdentityService.Infrastructure.Persistence.Migrations;
using IdentityService.Infrastructure.Messaging.Consumers;
using MassTransit;

var builder = WebApplication.CreateBuilder(args);

// Configure Serilog
Log.Logger = new LoggerConfiguration()
    .MinimumLevel.Debug()
    .MinimumLevel.Override("Microsoft", LogEventLevel.Information)
    .Enrich.FromLogContext()
    .WriteTo.Console()
    .WriteTo.File("logs/identity-service-.txt", rollingInterval: RollingInterval.Day)
    .CreateLogger();

builder.Host.UseSerilog();

builder.Services.AddControllers();

// ✅ Fixed CORS Setup
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowFrontend", policy =>
    {
        var origins = builder.Configuration.GetSection("AllowedOrigins").Get<string[]>();

        if (origins != null && origins.Length > 0)
        {
            policy.WithOrigins(origins)
                  .AllowAnyHeader()
                  .AllowAnyMethod();
        }
        else
        {
            policy.AllowAnyOrigin()
                  .AllowAnyHeader()
                  .AllowAnyMethod();
        }
    });
});

// Database
builder.Services.AddDbContext<ApplicationDbContext>(options =>
    options.UseNpgsql(builder.Configuration.GetConnectionString("DefaultConnection")));

builder.Services.AddHttpContextAccessor();

// Authentication
builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuer = true,
        ValidateAudience = true,
        ValidateLifetime = true,
        ValidateIssuerSigningKey = true,
        ValidIssuer = builder.Configuration["Jwt:Issuer"],
        ValidAudience = builder.Configuration["Jwt:Audience"],
        IssuerSigningKey = new SymmetricSecurityKey(
            Encoding.UTF8.GetBytes(builder.Configuration["Jwt:Key"] ?? throw new InvalidOperationException("JWT Key is not configured"))),
        ClockSkew = TimeSpan.Zero,
    };

    options.Events = new JwtBearerEvents
    {
        OnAuthenticationFailed = context =>
        {
            var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<Program>>();
            logger.LogError("Authentication failed: {Exception}", context.Exception);
            return Task.CompletedTask;
        },
        OnTokenValidated = context =>
        {
            var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<Program>>();
            var claims = context.Principal?.Claims.Select(c => $"{c.Type}: {c.Value}").ToList();
            if (claims != null && claims.Any())
            {
                logger.LogInformation("Token validated successfully with claims: {Claims}", string.Join(", ", claims));
            }
            return Task.CompletedTask;
        },
        OnChallenge = context =>
        {
            var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<Program>>();
            logger.LogWarning("OnChallenge: {Error}", context.Error);
            return Task.CompletedTask;
        },
        OnMessageReceived = context =>
        {
            var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<Program>>();
            logger.LogInformation("Token received and is being processed");
            return Task.CompletedTask;
        }
    };
});

// DI
builder.Services.AddScoped<IApplicationBuilder, ApplicationBuilder>();
builder.Services.AddScoped<IUserRepository, UserRepository>();
builder.Services.AddScoped<ICurrentUserService, CurrentUserService>();
builder.Services.AddScoped<IUserIdAccessor, HttpContextUserIdAccessor>();
builder.Services.AddScoped<IDateTime, DateTimeService>();
builder.Services.AddScoped<IUnitOfWork, UnitOfWork>();
builder.Services.AddScoped<IPasswordHasher, PasswordHasher>();
builder.Services.AddScoped<IEmailService, EmailService>();
builder.Services.AddScoped<ITokenService, TokenService>();
builder.Services.AddScoped<IAuditLogService, AuditLogService>();
builder.Services.AddScoped<IPasswordComplexityService, PasswordComplexityService>();
builder.Services.AddScoped<ISessionManagementService, SessionManagementService>();
builder.Services.AddScoped<IdentityService.Domain.Services.IPermissionService, IdentityService.Infrastructure.Services.PermissionService>();
builder.Services.AddScoped<IdentityService.Domain.Services.IMenuService, IdentityService.Infrastructure.Services.MenuService>();

// Options
builder.Services.Configure<IdentityService.Domain.Services.PasswordComplexityOptions>(
    builder.Configuration.GetSection("PasswordComplexity"));
builder.Services.Configure<IdentityService.Domain.Services.SessionManagementOptions>(
    builder.Configuration.GetSection("SessionManagement"));

// ✅ Use shared VMS Authorization with all policies and handlers
builder.Services.AddVMSAuthorization("IdentityService");

// Additional subscription policies specific to IdentityService
builder.Services.AddAuthorization(options =>
{
    options.AddPolicy("Subscription.Basic", policy =>
        policy.RequireAuthenticatedUser().AddRequirements(new SubscriptionTierRequirement(SubscriptionTier.Basic)));
    // Add more subscription policies as needed...
});

// ✅ Register additional authorization handlers for subscription features
builder.Services.AddScoped<IAuthorizationHandler, SubscriptionTierHandler>();
builder.Services.AddScoped<IAuthorizationHandler, SubscriptionFeatureHandler>();

// Swagger
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo { Title = "Identity Service API", Version = "v1" });

    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
        Name = "Authorization",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.Http,
        Scheme = "bearer",
        BearerFormat = "JWT"
    });

    c.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            new string[] {}
        }
    });

    c.OperationFilter<SecurityRequirementsOperationFilter>();
});

// MediatR + Validation + Mapping
builder.Services.AddMediatR(typeof(LoginCommand).Assembly);
builder.Services.AddTransient(typeof(IPipelineBehavior<,>), typeof(UserIdPropagationBehavior<,>));
builder.Services.AddTransient(typeof(IPipelineBehavior<,>), typeof(ValidationBehavior<,>));
builder.Services.AddTransient<IValidator<LoginCommand>, IdentityService.Application.Validators.Auth.LoginCommandValidator>();
builder.Services.AddAutoMapper(typeof(Program).Assembly);
builder.Services.AddControllers(options =>
{
    options.Filters.Add<ValidationExceptionHandler>();
});

// Messaging + Clients + Hosted Services
// Modified MassTransit configuration to properly register VendorRegisteredConsumer
builder.Services.AddMassTransit(config =>
{
    // Register consumers
    config.AddConsumer<SubscriptionCreatedConsumer>();
    config.AddConsumer<SubscriptionStatusChangedConsumer>();
    config.AddConsumer<VendorRegisteredConsumer>();

    // Configure RabbitMQ
    config.UsingRabbitMq((context, cfg) =>
    {
        var rabbitMqConfig = builder.Configuration.GetSection("RabbitMQ");
        var host = rabbitMqConfig["HostName"] ?? "localhost";
        var username = rabbitMqConfig["UserName"] ?? "guest";
        var password = rabbitMqConfig["Password"] ?? "guest";
        var virtualHost = rabbitMqConfig["VirtualHost"] ?? "/";

        cfg.Host(host, virtualHost, h =>
        {
            h.Username(username);
            h.Password(password);
        });

        // Configure retry policy
        cfg.UseMessageRetry(r =>
        {
            r.Interval(3, TimeSpan.FromSeconds(5));
            r.Ignore<InvalidOperationException>(); // Ignore business logic exceptions
        });

        // Configure error handling
        cfg.UseMessageRetry(r =>
        {
            r.Exponential(
                retryLimit: 3,
                minInterval: TimeSpan.FromSeconds(1),
                maxInterval: TimeSpan.FromSeconds(10),
                intervalDelta: TimeSpan.FromSeconds(2));
        });

        // Explicitly configure the VendorRegistered endpoint
        cfg.ReceiveEndpoint("VendorRegistered", e =>
        {
            e.ConfigureConsumer<VendorRegisteredConsumer>(context);
        });

        // Configure other consumer endpoints
        cfg.ConfigureEndpoints(context);
    });
});

// Register the event publisher
builder.Services.AddScoped<IEventPublisher, EventPublisher>();

// Messaging + Clients + Hosted Services
// builder.Services.AddMassTransitWithRabbitMq(builder.Configuration);
builder.Services.AddTenantManagementServiceClient(builder.Configuration);
builder.Services.AddSubscriptionServiceClient(builder.Configuration);
// builder.Services.AddMassTransitConsumers(typeof(VendorRegisteredConsumer).Assembly);
builder.Services.AddHostedService<IdentityService.API.Services.SessionCleanupService>();

var app = builder.Build();

// Redirect root to Swagger
var option = new RewriteOptions();
option.AddRedirect("^$", "swagger");
app.UseRewriter(option);

app.UseHttpsRedirection();

// ✅ CORS BEFORE authentication
app.UseCors("AllowFrontend");

app.UseExceptionHandlingMiddleware();
app.UseAuthentication();
app.UseAuthorization();
app.UseSecurityStampValidation();
app.UseSessionValidation();
app.UseUserIdPropagation();

app.MapControllers();
app.UseSwagger();
app.UseSwaggerUI();

// Apply EF Core migrations and seed data
using (var scope = app.Services.CreateScope())
{
    var services = scope.ServiceProvider;
    try
    {
        var context = services.GetRequiredService<ApplicationDbContext>();
        var logger = services.GetRequiredService<ILogger<Program>>();

        // Apply migrations
        context.Database.Migrate();
        logger.LogInformation("Database migrations applied successfully.");

        // Get password hasher service
        var passwordHasher = services.GetRequiredService<IdentityService.Application.Interfaces.IPasswordHasher>();

        // Seed new roles and permissions
        await UpdateRolesSeeder.SeedNewRolesAndPermissions(context, passwordHasher);
        logger.LogInformation("Roles and permissions seeding completed successfully.");
    }
    catch (Exception ex)
    {
        var logger = services.GetRequiredService<ILogger<Program>>();
        logger.LogError(ex, "An error occurred while migrating the database or seeding data.");
    }
}

app.Run();
