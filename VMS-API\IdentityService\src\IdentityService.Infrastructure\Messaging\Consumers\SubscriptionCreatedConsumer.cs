using System;
using System.Threading.Tasks;
using IdentityService.Domain.Entities;
using IdentityService.Domain.Enums;
using IdentityService.Domain.Interfaces;
using MassTransit;
using Microsoft.Extensions.Logging;
using VMSContracts.Subscription.Events;

namespace IdentityService.Infrastructure.Messaging.Consumers;

/// <summary>
/// Consumer for SubscriptionCreated events
/// </summary>
public class SubscriptionCreatedConsumer : IConsumer<SubscriptionCreated>
{
    private readonly ILogger<SubscriptionCreatedConsumer> _logger;
    private readonly IUnitOfWork _unitOfWork;

    public SubscriptionCreatedConsumer(
        ILogger<SubscriptionCreatedConsumer> logger,
        IUnitOfWork unitOfWork)
    {
        _logger = logger;
        _unitOfWork = unitOfWork;
    }

    public async Task Consume(ConsumeContext<SubscriptionCreated> context)
    {
        var @event = context.Message;

        _logger.LogInformation(
            "Received SubscriptionCreated event for user {UserId}, subscription {SubscriptionId}, plan {PlanName}",
            @event.UserId, @event.SubscriptionId, @event.PlanName);

        try
        {
            // Get the user
            var user = await _unitOfWork.UserRepository.GetByIdAsync(@event.UserId);
            if (user == null)
            {
                _logger.LogWarning("User with ID {@UserId} not found", @event.UserId);
                return;
            }

            // Convert subscription tier
            var tier = ConvertSubscriptionTier(@event.Tier);

            // Convert subscription status (assuming new subscriptions are active or in trial)
            var status = @event.TrialEndDate.HasValue && @event.TrialEndDate.Value > DateTime.UtcNow
                ? SubscriptionStatus.Trial
                : SubscriptionStatus.Active;

            // Add subscription to user
            user.AddSubscription(
                @event.SubscriptionId,
                @event.PlanName,
                tier,
                @event.StartDate,
                @event.EndDate,
                @event.TrialEndDate,
                status,
                "System");

            // Save changes
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation(
                "Successfully added subscription {SubscriptionId} to user {UserId}",
                @event.SubscriptionId, @event.UserId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex,
                "Error processing SubscriptionCreated event for user {UserId}, subscription {SubscriptionId}",
                @event.UserId, @event.SubscriptionId);
        }
    }

    private IdentityService.Domain.Enums.SubscriptionTier ConvertSubscriptionTier(VMSContracts.Subscription.Enums.SubscriptionTier tier)
    {
        // Convert based on the integer value of the enum
        return (int)tier switch
        {
            0 => IdentityService.Domain.Enums.SubscriptionTier.Free,
            1 => IdentityService.Domain.Enums.SubscriptionTier.Basic,
            2 => IdentityService.Domain.Enums.SubscriptionTier.Standard,
            3 => IdentityService.Domain.Enums.SubscriptionTier.Premium,
            4 => IdentityService.Domain.Enums.SubscriptionTier.Enterprise,
            _ => IdentityService.Domain.Enums.SubscriptionTier.Free
        };
    }
}
