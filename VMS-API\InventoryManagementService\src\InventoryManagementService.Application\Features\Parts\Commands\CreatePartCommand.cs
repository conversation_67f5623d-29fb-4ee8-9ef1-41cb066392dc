using AutoMapper;
using FluentValidation;
using InventoryManagementService.Application.DTOs;
using InventoryManagementService.Domain.Entities;
using InventoryManagementService.Domain.Interfaces;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using VMS.Contracts.Common.Authorization;

namespace InventoryManagementService.Application.Features.Parts.Commands;

public class CreatePartCommand : IRequest<PartDto>
{
    public CreatePartDto Part { get; set; } = null!;
    public string CreatedBy { get; set; } = string.Empty;
}

public class CreatePartCommandValidator : AbstractValidator<CreatePartCommand>
{
    public CreatePartCommandValidator()
    {
        RuleFor(x => x.Part.SKU)
            .NotEmpty().WithMessage("SKU is required")
            .MaximumLength(50).WithMessage("SKU cannot exceed 50 characters");

        RuleFor(x => x.Part.Name)
            .NotEmpty().WithMessage("Name is required")
            .MaximumLength(200).WithMessage("Name cannot exceed 200 characters");

        RuleFor(x => x.Part.Description)
            .NotEmpty().WithMessage("Description is required")
            .MaximumLength(1000).WithMessage("Description cannot exceed 1000 characters");

        RuleFor(x => x.Part.Manufacturer)
            .NotEmpty().WithMessage("Manufacturer is required")
            .MaximumLength(100).WithMessage("Manufacturer cannot exceed 100 characters");

        RuleFor(x => x.Part.CostPrice)
            .GreaterThan(0).WithMessage("Cost price must be greater than 0");

        RuleFor(x => x.Part.RetailPrice)
            .GreaterThan(0).WithMessage("Retail price must be greater than 0");

        RuleFor(x => x.Part.Category)
            .IsInEnum().WithMessage("Invalid category");

        RuleFor(x => x.CreatedBy)
            .NotEmpty().WithMessage("CreatedBy is required");
    }
}

public class CreatePartCommandHandler : IRequestHandler<CreatePartCommand, PartDto>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly ILogger<CreatePartCommandHandler> _logger;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly IEventPublisher _eventPublisher;

    public CreatePartCommandHandler(
        IUnitOfWork unitOfWork,
        IMapper mapper,
        ILogger<CreatePartCommandHandler> logger,
        IHttpContextAccessor httpContextAccessor,
        IEventPublisher eventPublisher)
    {
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _logger = logger;
        _httpContextAccessor = httpContextAccessor;
        _eventPublisher = eventPublisher;
    }

    public async Task<PartDto> Handle(CreatePartCommand request, CancellationToken cancellationToken)
    {
        // Check permissions - Admin roles bypass, others need explicit permission
        var user = _httpContextAccessor.HttpContext?.User;
        if (!PermissionHelper.IsAdmin(user))
        {
            PermissionHelper.ValidateAccess(user, "Parts.Create");
        }

        _logger.LogInformation("Creating new part with SKU: {SKU}", request.Part.SKU);

        // Check if SKU already exists
        if (await _unitOfWork.Parts.SkuExistsAsync(request.Part.SKU))
        {
            throw new InvalidOperationException($"Part with SKU '{request.Part.SKU}' already exists");
        }

        var part = Part.Create(
            request.Part.SKU,
            request.Part.Name,
            request.Part.Description,
            request.Part.Manufacturer,
            request.Part.CostPrice,
            request.Part.RetailPrice,
            request.Part.Category,
            request.CreatedBy,
            request.Part.PartNumber,
            request.Part.Barcode,
            request.Part.WarrantyMonths,
            request.Part.ImageUrl,
            request.Part.Notes);

        await _unitOfWork.Parts.AddAsync(part);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("Part created successfully with ID: {PartId}", part.Id);

        // Publish event
        await _eventPublisher.PublishPartCreatedEvent(
            part.Id,
            part.SKU,
            part.Name,
            part.Manufacturer);

        return _mapper.Map<PartDto>(part);
    }
}
