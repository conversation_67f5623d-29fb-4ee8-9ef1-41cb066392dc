using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using IdentityService.Domain.Entities;
using IdentityService.Infrastructure.Persistence;

namespace IdentityService.Infrastructure.Persistence.Migrations
{
    /// <summary>
    /// Seeder class to add new roles and permissions
    /// </summary>
    public static class UpdateRolesSeeder
    {
        /// <summary>
        /// Seeds the complete VMS data including roles, permissions, and users
        /// </summary>
        /// <param name="context">The database context</param>
        /// <param name="passwordHasher">The password hasher service</param>
        public static async Task SeedNewRolesAndPermissions(ApplicationDbContext context, Application.Interfaces.IPasswordHasher? passwordHasher = null)
        {
            try
            {
                Console.WriteLine("🔄 Starting VMS Identity data seeding with correct dependency order...");
                Console.WriteLine($"🔍 Database: {context.Database.GetConnectionString()}");

                // Check current counts
                var existingRoles = await context.Roles.CountAsync();
                var existingPermissions = await context.Permissions.CountAsync();
                var existingUsers = await context.Users.CountAsync();
                var existingMenus = await context.Menus.CountAsync();

                Console.WriteLine($"📊 Current counts - Roles: {existingRoles}, Permissions: {existingPermissions}, Users: {existingUsers}, Menus: {existingMenus}");

                // PHASE 1: Independent entities (no foreign key dependencies)
                Console.WriteLine("\n🔄 PHASE 1: Seeding independent entities...");

                // Step 1: Add roles (independent)
                Console.WriteLine("🔄 Step 1: Seeding roles...");
                await SeedNewRoles(context);

                // Step 2: Add permissions (independent)
                Console.WriteLine("🔄 Step 2: Seeding permissions...");
                await SeedNewPermissions(context);

                // Step 3: Add menus (independent, but parent-child relationship)
                Console.WriteLine("🔄 Step 3: Seeding menus...");
                await SeedMenus(context);

                // Save Phase 1 changes
                Console.WriteLine("💾 Saving Phase 1 changes...");
                await context.SaveChangesAsync();

                // PHASE 2: Relationship entities (require foreign keys)
                Console.WriteLine("\n🔄 PHASE 2: Seeding relationship entities...");

                // Step 4: Assign permissions to roles
                Console.WriteLine("🔄 Step 4: Assigning permissions to roles...");
                await SeedRolePermissions(context);

                // Step 5: Assign permissions to menus
                Console.WriteLine("🔄 Step 5: Assigning permissions to menus...");
                await SeedMenuPermissions(context);

                // Save Phase 2 changes
                Console.WriteLine("💾 Saving Phase 2 changes...");
                await context.SaveChangesAsync();

                // PHASE 3: User entities and their relationships
                Console.WriteLine("\n🔄 PHASE 3: Seeding users and user relationships...");

                // Step 6: Create users (without vendor/branch assignments - will be added later)
                Console.WriteLine("🔄 Step 6: Creating users...");
                await SeedUsersForAllRoles(context, passwordHasher);

                // Final save
                Console.WriteLine("💾 Final save...");
                await context.SaveChangesAsync();

                // Final counts
                var finalRoles = await context.Roles.CountAsync();
                var finalPermissions = await context.Permissions.CountAsync();
                var finalUsers = await context.Users.CountAsync();
                var finalUserRoles = await context.UserRoles.CountAsync();
                var finalMenus = await context.Menus.CountAsync();

                Console.WriteLine("\n✅ Complete VMS Identity data seeding completed successfully!");
                Console.WriteLine("📊 Final Summary:");
                Console.WriteLine($"   • {finalRoles} Roles in database");
                Console.WriteLine($"   • {finalPermissions} Permissions in database");
                Console.WriteLine($"   • {finalMenus} Menus in database");
                Console.WriteLine($"   • {finalUsers} Users in database");
                Console.WriteLine($"   • {finalUserRoles} User-Role assignments");
                Console.WriteLine("   • Common password: Admin@123");
                Console.WriteLine("   • Note: Vendor/Branch assignments will be added when vendors register");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error during seeding: {ex.Message}");
                throw;
            }
        }



        private static async Task SeedMenus(ApplicationDbContext context)
        {
            if (await context.Menus.AnyAsync())
            {
                Console.WriteLine("⏭️ Menus already exist, skipping menu seeding");
                return;
            }

            Console.WriteLine("📋 Creating VMS menus...");

            // Create parent menus first
            var parentMenus = new List<Menu>
            {
                Menu.Create("dashboard", "Dashboard", "/dashboard", "dashboard", 1, null, "System"),
                Menu.Create("users", "User Management", "/users", "users", 2, null, "System"),
                Menu.Create("vehicles", "Vehicle Management", "/vehicles", "car", 3, null, "System"),
                Menu.Create("services", "Service Management", "/services", "tools", 4, null, "System"),
                Menu.Create("inventory", "Inventory", "/inventory", "package", 5, null, "System"),
                Menu.Create("reports", "Reports", "/reports", "chart-bar", 6, null, "System"),
                Menu.Create("settings", "Settings", "/settings", "settings", 7, null, "System")
            };

            await context.Menus.AddRangeAsync(parentMenus);
            await context.SaveChangesAsync();

            Console.WriteLine($"✅ Created {parentMenus.Count} parent menus");
        }

        private static async Task SeedMenuPermissions(ApplicationDbContext context)
        {
            if (await context.MenuPermissions.AnyAsync())
            {
                Console.WriteLine("⏭️ Menu permissions already exist, skipping menu permission seeding");
                return;
            }

            Console.WriteLine("🔗 Assigning permissions to menus...");

            var menus = await context.Menus.ToListAsync();
            var permissions = await context.Permissions.ToListAsync();

            var menuPermissions = new List<MenuPermission>();

            foreach (var menu in menus)
            {
                // Assign view permission to each menu
                var viewPermission = permissions.FirstOrDefault(p => p.Name.Contains("View"));
                if (viewPermission != null)
                {
                    menuPermissions.Add(MenuPermission.Create(menu, viewPermission, "System"));
                }
            }

            await context.MenuPermissions.AddRangeAsync(menuPermissions);
            await context.SaveChangesAsync();

            Console.WriteLine($"✅ Created {menuPermissions.Count} menu-permission assignments");
        }



        private static async Task SeedNewRoles(ApplicationDbContext context)
        {
            Console.WriteLine("👥 Seeding roles...");

            var newRoles = new List<(string Name, string Description)>
            {
                ("SuperAdmin", "Super administrator with full system access"),
                ("VendorAdmin", "Vendor administrator with vendor-level access"),
                ("BranchAdmin", "Branch administrator with branch-level access"),
                ("Service Advisor", "Service advisor role for customer interaction and service management"),
                ("Technician", "Technician role for vehicle maintenance and repair"),
                ("Inventory Manager", "Inventory manager role for parts and supplies management"),
                ("Cashier", "Cashier role for payment processing and billing")
            };

            foreach (var (name, description) in newRoles)
            {
                var existingRole = await context.Roles.FirstOrDefaultAsync(r => r.Name == name);
                if (existingRole == null)
                {
                    var role = Role.Create(name, description, "System");
                    await context.Roles.AddAsync(role);
                    Console.WriteLine($"➕ Added role: {name}");
                }
                else
                {
                    Console.WriteLine($"⏭️ Role already exists: {name}");
                }
            }
        }

        private static async Task SeedNewPermissions(ApplicationDbContext context)
        {
            Console.WriteLine("🔐 Seeding permissions...");

            var newPermissions = new List<(string Name, string Description, string Resource, string Action)>
            {
                // User Management Permissions
                ("Users.View", "Permission to view users", "Users", "View"),
                ("Users.Create", "Permission to create users", "Users", "Create"),
                ("Users.Update", "Permission to update users", "Users", "Update"),
                ("Users.Delete", "Permission to delete users", "Users", "Delete"),

                // Role Management Permissions
                ("Roles.View", "Permission to view roles", "Roles", "View"),
                ("Roles.Create", "Permission to create roles", "Roles", "Create"),
                ("Roles.Update", "Permission to update roles", "Roles", "Update"),
                ("Roles.Delete", "Permission to delete roles", "Roles", "Delete"),

                // Permission Management Permissions
                ("Permissions.View", "Permission to view permissions", "Permissions", "View"),
                ("Permissions.Create", "Permission to create permissions", "Permissions", "Create"),
                ("Permissions.Update", "Permission to update permissions", "Permissions", "Update"),
                ("Permissions.Delete", "Permission to delete permissions", "Permissions", "Delete"),

                // Vehicle Management Permissions
                ("Vehicles.View", "Permission to view vehicles", "Vehicles", "View"),
                ("Vehicles.Create", "Permission to create vehicles", "Vehicles", "Create"),
                ("Vehicles.Update", "Permission to update vehicles", "Vehicles", "Update"),
                ("Vehicles.Delete", "Permission to delete vehicles", "Vehicles", "Delete"),

                // Branch Management Permissions
                ("Branches.View", "Permission to view branches", "Branches", "View"),
                ("Branches.Create", "Permission to create branches", "Branches", "Create"),
                ("Branches.Update", "Permission to update branches", "Branches", "Update"),
                ("Branches.Delete", "Permission to delete branches", "Branches", "Delete"),

                // Vendor Management Permissions
                ("Vendors.View", "Permission to view vendors", "Vendors", "View"),
                ("Vendors.Create", "Permission to create vendors", "Vendors", "Create"),
                ("Vendors.Update", "Permission to update vendors", "Vendors", "Update"),
                ("Vendors.Delete", "Permission to delete vendors", "Vendors", "Delete"),

                // Service Management Permissions
                ("Service.View", "Permission to view service records", "Service", "View"),
                ("Service.Create", "Permission to create service records", "Service", "Create"),
                ("Service.Update", "Permission to update service records", "Service", "Update"),
                ("Service.Delete", "Permission to delete service records", "Service", "Delete"),

                // Customer Management Permissions
                ("Customer.View", "Permission to view customers", "Customer", "View"),
                ("Customer.Create", "Permission to create customers", "Customer", "Create"),
                ("Customer.Update", "Permission to update customers", "Customer", "Update"),
                ("Customer.Delete", "Permission to delete customers", "Customer", "Delete"),

                // Maintenance Permissions
                ("Maintenance.View", "Permission to view maintenance records", "Maintenance", "View"),
                ("Maintenance.Create", "Permission to create maintenance records", "Maintenance", "Create"),
                ("Maintenance.Update", "Permission to update maintenance records", "Maintenance", "Update"),
                ("Maintenance.Delete", "Permission to delete maintenance records", "Maintenance", "Delete"),

                // Inventory Permissions
                ("Inventory.View", "Permission to view inventory", "Inventory", "View"),
                ("Inventory.Create", "Permission to create inventory items", "Inventory", "Create"),
                ("Inventory.Update", "Permission to update inventory items", "Inventory", "Update"),
                ("Inventory.Delete", "Permission to delete inventory items", "Inventory", "Delete"),

                // Parts Permissions
                ("Parts.View", "Permission to view parts", "Parts", "View"),
                ("Parts.Create", "Permission to create parts", "Parts", "Create"),
                ("Parts.Update", "Permission to update parts", "Parts", "Update"),
                ("Parts.Delete", "Permission to delete parts", "Parts", "Delete"),

                // Stock Permissions
                ("Stock.View", "Permission to view stock", "Stock", "View"),
                ("Stock.Create", "Permission to create stock entries", "Stock", "Create"),
                ("Stock.Update", "Permission to update stock entries", "Stock", "Update"),
                ("Stock.Delete", "Permission to delete stock entries", "Stock", "Delete"),

                // Billing Permissions
                ("Billing.View", "Permission to view billing records", "Billing", "View"),
                ("Billing.Create", "Permission to create billing records", "Billing", "Create"),
                ("Billing.Update", "Permission to update billing records", "Billing", "Update"),
                ("Billing.Delete", "Permission to delete billing records", "Billing", "Delete"),

                // Payment Permissions
                ("Payment.View", "Permission to view payments", "Payment", "View"),
                ("Payment.Create", "Permission to create payments", "Payment", "Create"),
                ("Payment.Update", "Permission to update payments", "Payment", "Update"),
                ("Payment.Delete", "Permission to delete payments", "Payment", "Delete"),

                // Invoice Permissions
                ("Invoice.View", "Permission to view invoices", "Invoice", "View"),
                ("Invoice.Create", "Permission to create invoices", "Invoice", "Create"),
                ("Invoice.Update", "Permission to update invoices", "Invoice", "Update"),
                ("Invoice.Delete", "Permission to delete invoices", "Invoice", "Delete")
            };

            foreach (var (name, description, resource, action) in newPermissions)
            {
                var existingPermission = await context.Permissions.FirstOrDefaultAsync(p => p.Name == name);
                if (existingPermission == null)
                {
                    var permission = Permission.Create(name, description, resource, action, "System");
                    await context.Permissions.AddAsync(permission);
                    Console.WriteLine($"➕ Added permission: {name}");
                }
                else
                {
                    Console.WriteLine($"⏭️ Permission already exists: {name}");
                }
            }

            Console.WriteLine($"✅ Processed {newPermissions.Count} permissions");
        }

        private static async Task SeedRolePermissions(ApplicationDbContext context)
        {
            Console.WriteLine("🔗 Assigning permissions to roles...");

            // Define role-permission mappings
            var rolePermissionMappings = new Dictionary<string, List<string>>
            {
                ["SuperAdmin"] = new List<string>(), // SuperAdmin gets ALL permissions (will be handled separately)
                ["VendorAdmin"] = new List<string>
                {
                    "Vendors.View", "Vendors.Update", "Branches.View", "Branches.Create", "Branches.Update", "Branches.Delete",
                    "Vehicles.View", "Vehicles.Create", "Vehicles.Update", "Vehicles.Delete",
                    "Service.View", "Service.Create", "Service.Update", "Service.Delete",
                    "Customer.View", "Customer.Create", "Customer.Update", "Customer.Delete",
                    "Users.View", "Users.Create", "Users.Update"
                },
                ["BranchAdmin"] = new List<string>
                {
                    "Vehicles.View", "Vehicles.Create", "Vehicles.Update", "Vehicles.Delete",
                    "Service.View", "Service.Create", "Service.Update", "Service.Delete",
                    "Customer.View", "Customer.Create", "Customer.Update", "Customer.Delete",
                    "Maintenance.View", "Maintenance.Create", "Maintenance.Update", "Maintenance.Delete",
                    "Users.View", "Users.Create", "Users.Update"
                },
                ["Service Advisor"] = new List<string>
                {
                    "Vehicles.View", "Vehicles.Update",
                    "Service.View", "Service.Create", "Service.Update",
                    "Customer.View", "Customer.Create", "Customer.Update"
                },
                ["Technician"] = new List<string>
                {
                    "Vehicles.View", "Vehicles.Update",
                    "Service.View", "Service.Update",
                    "Maintenance.View", "Maintenance.Create", "Maintenance.Update"
                },
                ["Inventory Manager"] = new List<string>
                {
                    "Inventory.View", "Inventory.Create", "Inventory.Update", "Inventory.Delete",
                    "Parts.View", "Parts.Create", "Parts.Update", "Parts.Delete",
                    "Stock.View", "Stock.Create", "Stock.Update", "Stock.Delete"
                },
                ["Cashier"] = new List<string>
                {
                    "Billing.View", "Billing.Create", "Billing.Update",
                    "Payment.View", "Payment.Create", "Payment.Update",
                    "Invoice.View", "Invoice.Create", "Invoice.Update"
                }
            };

            // Handle SuperAdmin separately - assign ALL permissions
            var superAdminRole = await context.Roles.FirstOrDefaultAsync(r => r.Name == "SuperAdmin");
            if (superAdminRole != null)
            {
                var allPermissions = await context.Permissions.ToListAsync();
                foreach (var permission in allPermissions)
                {
                    var existingRolePermission = await context.RolePermissions
                        .FirstOrDefaultAsync(rp => rp.RoleId == superAdminRole.Id && rp.PermissionId == permission.Id);

                    if (existingRolePermission == null)
                    {
                        var rolePermission = RolePermission.Create(superAdminRole, permission, "System");
                        await context.RolePermissions.AddAsync(rolePermission);
                    }
                }
                Console.WriteLine($"🔗 Assigned ALL permissions to SuperAdmin role");
            }

            foreach (var (roleName, permissionNames) in rolePermissionMappings)
            {
                var role = await context.Roles.FirstOrDefaultAsync(r => r.Name == roleName);
                if (role == null)
                {
                    Console.WriteLine($"⚠️ Role not found: {roleName}");
                    continue;
                }

                foreach (var permissionName in permissionNames)
                {
                    var permission = await context.Permissions.FirstOrDefaultAsync(p => p.Name == permissionName);
                    if (permission == null)
                    {
                        Console.WriteLine($"⚠️ Permission not found: {permissionName}");
                        continue;
                    }

                    var existingRolePermission = await context.RolePermissions
                        .FirstOrDefaultAsync(rp => rp.RoleId == role.Id && rp.PermissionId == permission.Id);

                    if (existingRolePermission == null)
                    {
                        var rolePermission = RolePermission.Create(role, permission, "System");
                        await context.RolePermissions.AddAsync(rolePermission);
                        Console.WriteLine($"🔗 Assigned permission '{permissionName}' to role '{roleName}'");
                    }
                    else
                    {
                        Console.WriteLine($"⏭️ Permission '{permissionName}' already assigned to role '{roleName}'");
                    }
                }
            }
        }

        private static async Task SeedUsersForAllRoles(ApplicationDbContext context, Application.Interfaces.IPasswordHasher? passwordHasher)
        {
            Console.WriteLine("👤 Seeding users for all roles...");

            // Check if users already exist
            if (await context.Users.AnyAsync())
            {
                Console.WriteLine("⏭️ Users already exist, skipping user seeding");
                return;
            }

            // Create password hasher if not provided
            if (passwordHasher == null)
            {
                passwordHasher = new Infrastructure.Services.PasswordHasher();
            }

            // Hash the password "Admin@123" using the proper password hasher
            const string plainPassword = "Admin@123";
            var passwordHash = passwordHasher.HashPassword(plainPassword);

            Console.WriteLine($"🔐 Using password: {plainPassword}");
            Console.WriteLine($"🔐 Generated hash: {passwordHash[..20]}...");

            // Get all roles
            var roles = await context.Roles.ToListAsync();

            var allUsers = new List<User>();

            foreach (var role in roles)
            {
                Console.WriteLine($"Creating 10 users for role: {role.Name}");

                for (int i = 1; i <= 10; i++)
                {
                    var user = CreateUserForRole(role, i, passwordHash);
                    allUsers.Add(user);
                }
            }

            Console.WriteLine($"🔄 Adding {allUsers.Count} users to database...");

            // Add all users to context
            await context.Users.AddRangeAsync(allUsers);
            await context.SaveChangesAsync(); // Save users first to get IDs

            Console.WriteLine($"✅ Users saved to database. Now assigning roles...");

            // Reload users from database to get proper IDs
            var savedUsers = await context.Users.ToListAsync();
            Console.WriteLine($"🔍 Reloaded {savedUsers.Count} users from database");

            // Create UserRole entities directly
            var userRoles = new List<UserRole>();

            foreach (var role in roles)
            {
                var roleEmailPrefix = GetRoleEmailPrefix(role.Name);
                var roleUsers = savedUsers.Where(u => u.Email.Contains(roleEmailPrefix)).ToList();
                Console.WriteLine($"🔗 Creating UserRole entries for {role.Name} role - {roleUsers.Count} users");

                foreach (var user in roleUsers)
                {
                    Console.WriteLine($"   📝 Creating UserRole: User {user.Email} -> Role {role.Name}");
                    var userRole = UserRole.Create(user.Id, role.Id, "System", user, role);
                    userRoles.Add(userRole);
                }
            }

            Console.WriteLine($"🔄 Adding {userRoles.Count} UserRole entries to database...");
            await context.UserRoles.AddRangeAsync(userRoles);
            await context.SaveChangesAsync();

            Console.WriteLine($"✅ Created {allUsers.Count} users across {roles.Count} roles");

            // Verify the data was saved
            var finalUsers = await context.Users.CountAsync();
            var finalUserRoles = await context.UserRoles.CountAsync();
            Console.WriteLine($"🔍 Final Verification - Users in DB: {finalUsers}, UserRoles in DB: {finalUserRoles}");
        }

        private static User CreateUserForRole(Role role, int userNumber, string passwordHash)
        {
            var (email, phone) = GenerateUserDetails(role.Name, userNumber);

            var user = User.Create(email, phone, passwordHash, "System");

            // Set email and phone as verified
            user.VerifyEmail();
            user.VerifyPhoneNumber();

            return user;
        }

        private static (string email, string phone) GenerateUserDetails(string roleName, int userNumber)
        {
            string email, phone;

            switch (roleName)
            {
                case "SuperAdmin":
                    email = $"superadmin{userNumber}@vms.com";
                    phone = $"900000000{userNumber:D1}";
                    break;

                case "VendorAdmin":
                    email = $"vendoradmin{userNumber}@vms.com";
                    phone = $"90000001{userNumber:D1}";
                    break;

                case "BranchAdmin":
                    email = $"branchadmin{userNumber}@vms.com";
                    phone = $"90000002{userNumber:D1}";
                    break;

                case "Service Advisor":
                    email = $"serviceadvisor{userNumber}@vms.com";
                    phone = $"90000003{userNumber:D1}";
                    break;

                case "Technician":
                    email = $"technician{userNumber}@vms.com";
                    phone = $"90000004{userNumber:D1}";
                    break;

                case "Inventory Manager":
                    email = $"inventory{userNumber}@vms.com";
                    phone = $"90000005{userNumber:D1}";
                    break;

                case "Cashier":
                    email = $"cashier{userNumber}@vms.com";
                    phone = $"90000006{userNumber:D1}";
                    break;

                default:
                    email = $"user{userNumber}@vms.com";
                    phone = $"90000009{userNumber:D1}";
                    break;
            }

            return (email, phone);
        }

        private static string GetRoleEmailPrefix(string roleName)
        {
            return roleName.ToLower() switch
            {
                "superadmin" => "superadmin",
                "vendoradmin" => "vendoradmin",
                "branchadmin" => "branchadmin",
                "service advisor" => "serviceadvisor",
                "technician" => "technician",
                "inventory manager" => "inventory",
                "cashier" => "cashier",
                _ => "user"
            };
        }
    }
}
