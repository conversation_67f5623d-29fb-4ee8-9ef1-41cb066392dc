using FluentValidation;
using IdentityService.Application.Features.Permissions.Commands;

namespace IdentityService.Application.Validators.Permissions;

public class CreatePermissionCommandValidator : BaseValidator<CreatePermissionCommand>
{
    public CreatePermissionCommandValidator()
    {
        RuleFor(x => x.Request).NotNull().WithMessage("Request cannot be null.");
        
        When(x => x.Request != null, () =>
        {
            RuleFor(x => x.Request.Name)
                .Cascade(CascadeMode.Stop)
                .NotEmpty().WithMessage("Permission name is required.")
                .MaximumLength(100).WithMessage("Permission name cannot exceed 100 characters.");
                
            RuleFor(x => x.Request.Description)
                .Cascade(CascadeMode.Stop)
                .NotEmpty().WithMessage("Description is required.")
                .MaximumLength(500).WithMessage("Description cannot exceed 500 characters.");
                
            RuleFor(x => x.Request.Resource)
                .Cascade(CascadeMode.Stop)
                .NotEmpty().WithMessage("Resource is required.")
                .MaximumLength(100).WithMessage("Resource cannot exceed 100 characters.");
                
            RuleFor(x => x.Request.Action)
                .Cascade(CascadeMode.Stop)
                .NotEmpty().WithMessage("Action is required.")
                .MaximumLength(50).WithMessage("Action cannot exceed 50 characters.");
        });
    }
}
