using AutoMapper;
using InventoryManagementService.Application.DTOs;
using InventoryManagementService.Domain.Interfaces;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using VMS.Contracts.Common.Authorization;

namespace InventoryManagementService.Application.Features.InventoryAdjustments.Queries;

public class GetInventoryAdjustmentByIdQuery : IRequest<InventoryAdjustmentDto?>
{
    public Guid AdjustmentId { get; set; }
}

public class GetInventoryAdjustmentByIdQueryHandler : IRequestHandler<GetInventoryAdjustmentByIdQuery, InventoryAdjustmentDto?>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly ILogger<GetInventoryAdjustmentByIdQueryHandler> _logger;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public GetInventoryAdjustmentByIdQueryHandler(
        IUnitOfWork unitOfWork,
        IMapper mapper,
        ILogger<GetInventoryAdjustmentByIdQueryHandler> logger,
        IHttpContextAccessor httpContextAccessor)
    {
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _logger = logger;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<InventoryAdjustmentDto?> Handle(GetInventoryAdjustmentByIdQuery request, CancellationToken cancellationToken)
    {
        // Check permissions - Admin roles bypass, others need explicit permission
        var user = _httpContextAccessor.HttpContext?.User;
        if (!PermissionHelper.IsAdmin(user))
        {
            PermissionHelper.ValidateAccess(user, "Adjustments.View");
        }

        _logger.LogInformation("Getting inventory adjustment by ID: {AdjustmentId}", request.AdjustmentId);

        var adjustment = await _unitOfWork.InventoryAdjustments.GetByIdAsync(request.AdjustmentId);

        if (adjustment == null)
        {
            _logger.LogWarning("Inventory adjustment not found with ID: {AdjustmentId}", request.AdjustmentId);
            return null;
        }

        _logger.LogInformation("Inventory adjustment found with ID: {AdjustmentId}", adjustment.Id);
        return _mapper.Map<InventoryAdjustmentDto>(adjustment);
    }
}
