-- Migration: Add Advanced Subscription Features
-- Description: Adds new properties to SubscriptionPlan table for enhanced subscription configuration
-- Date: 2025-06-16
-- Author: VMS Development Team

-- Add new columns to SubscriptionPlans table
ALTER TABLE "SubscriptionPlans" 
ADD COLUMN "BillingCycle" integer NOT NULL DEFAULT 1,
ADD COLUMN "IsTrialPlan" boolean NOT NULL DEFAULT false,
ADD COLUMN "TrialPeriodDays" integer NULL;

-- Add AdvancedFeatures columns (owned entity)
ALTER TABLE "SubscriptionPlans"
ADD COLUMN "AdvancedFeatures_CustomReports" boolean NOT NULL DEFAULT false,
ADD COLUMN "AdvancedFeatures_DocumentExport" boolean NOT NULL DEFAULT false,
ADD COLUMN "AdvancedFeatures_CreatedAt" timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN "AdvancedFeatures_UpdatedAt" timestamp with time zone NULL;

-- Add comments for documentation
COMMENT ON COLUMN "SubscriptionPlans"."BillingCycle" IS 'Billing cycle: 1=Monthly, 2=Quarterly, 3=Yearly';
COMMENT ON COLUMN "SubscriptionPlans"."IsTrialPlan" IS 'Indicates if this is a trial plan';
COMMENT ON COLUMN "SubscriptionPlans"."TrialPeriodDays" IS 'Trial period in days (nullable, only applicable for trial plans)';
COMMENT ON COLUMN "SubscriptionPlans"."AdvancedFeatures_CustomReports" IS 'Indicates if custom reports feature is enabled';
COMMENT ON COLUMN "SubscriptionPlans"."AdvancedFeatures_DocumentExport" IS 'Indicates if document export feature is enabled';
COMMENT ON COLUMN "SubscriptionPlans"."AdvancedFeatures_CreatedAt" IS 'When advanced features configuration was created';
COMMENT ON COLUMN "SubscriptionPlans"."AdvancedFeatures_UpdatedAt" IS 'When advanced features configuration was last updated';

-- Update existing plans with default values
-- Set BillingCycle to Monthly (1) for all existing plans
UPDATE "SubscriptionPlans" SET "BillingCycle" = 1 WHERE "BillingCycle" IS NULL;

-- Set trial configuration for existing Trial tier plans
UPDATE "SubscriptionPlans" 
SET "IsTrialPlan" = true, "TrialPeriodDays" = 14 
WHERE "Tier" = 0; -- SubscriptionTier.Trial = 0

-- Enable advanced features for Gold tier plans
UPDATE "SubscriptionPlans" 
SET "AdvancedFeatures_CustomReports" = true, "AdvancedFeatures_DocumentExport" = true
WHERE "Tier" = 3; -- SubscriptionTier.Gold = 3

-- Enable custom reports for Silver tier plans
UPDATE "SubscriptionPlans" 
SET "AdvancedFeatures_CustomReports" = true
WHERE "Tier" = 2; -- SubscriptionTier.Silver = 2

-- Add check constraints for data validation
ALTER TABLE "SubscriptionPlans" 
ADD CONSTRAINT "CK_SubscriptionPlans_BillingCycle" 
CHECK ("BillingCycle" IN (1, 2, 3));

ALTER TABLE "SubscriptionPlans" 
ADD CONSTRAINT "CK_SubscriptionPlans_TrialPeriodDays" 
CHECK ("TrialPeriodDays" IS NULL OR ("TrialPeriodDays" > 0 AND "TrialPeriodDays" <= 365));

-- Add conditional constraint for trial plans
ALTER TABLE "SubscriptionPlans" 
ADD CONSTRAINT "CK_SubscriptionPlans_TrialConfiguration" 
CHECK (
    ("IsTrialPlan" = false AND "TrialPeriodDays" IS NULL) OR
    ("IsTrialPlan" = true AND "TrialPeriodDays" IS NOT NULL)
);

-- Create indexes for better query performance
CREATE INDEX "IX_SubscriptionPlans_BillingCycle" ON "SubscriptionPlans" ("BillingCycle");
CREATE INDEX "IX_SubscriptionPlans_IsTrialPlan" ON "SubscriptionPlans" ("IsTrialPlan");
CREATE INDEX "IX_SubscriptionPlans_AdvancedFeatures" ON "SubscriptionPlans" ("AdvancedFeatures_CustomReports", "AdvancedFeatures_DocumentExport");

-- Verify the migration
SELECT 
    "Id",
    "Name",
    "Tier",
    "BillingCycle",
    "IsTrialPlan",
    "TrialPeriodDays",
    "AdvancedFeatures_CustomReports",
    "AdvancedFeatures_DocumentExport"
FROM "SubscriptionPlans"
ORDER BY "Tier", "Name";

-- Migration completed successfully
-- Note: This migration is backward compatible and preserves existing data
-- Existing vendors will continue to work with their current subscription plans
