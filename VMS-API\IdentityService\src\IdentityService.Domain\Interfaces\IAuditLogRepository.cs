using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using IdentityService.Domain.Entities;

namespace IdentityService.Domain.Interfaces;

public interface IAuditLogRepository : IGenericRepository<AuditLog>
{
    Task<(IReadOnlyList<AuditLog> Items, int TotalCount)> GetFilteredAsync(
        string? action = null,
        string? entityName = null,
        string? entityId = null,
        Guid? userId = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        int pageNumber = 1,
        int pageSize = 10);
        
    Task<IReadOnlyList<AuditLog>> GetByUserIdAsync(Guid userId);
    Task<IReadOnlyList<AuditLog>> GetByEntityAsync(string entityName, string entityId);
    Task<byte[]> ExportToCsvAsync(IReadOnlyList<AuditLog> auditLogs);
    Task<byte[]> ExportToPdfAsync(IReadOnlyList<AuditLog> auditLogs);
}
