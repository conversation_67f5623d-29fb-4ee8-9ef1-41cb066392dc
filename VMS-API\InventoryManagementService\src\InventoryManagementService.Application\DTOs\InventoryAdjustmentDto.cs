using InventoryManagementService.Domain.Enums;

namespace InventoryManagementService.Application.DTOs;

public class InventoryAdjustmentDto
{
    public Guid Id { get; set; }
    public Guid VendorId { get; set; }
    public Guid BranchId { get; set; }
    public string AdjustmentNumber { get; set; } = string.Empty;
    public AdjustmentReason Reason { get; set; }
    public string ReasonDescription { get; set; } = string.Empty;
    public DateTime AdjustmentDate { get; set; }
    public string? Notes { get; set; }
    public string? ApprovedBy { get; set; }
    public DateTime? ApprovedDate { get; set; }
    public bool IsApproved { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public DateTime? UpdatedAt { get; set; }
    public string? UpdatedBy { get; set; }
    
    public List<InventoryAdjustmentItemDto> Items { get; set; } = new();
}

public class InventoryAdjustmentItemDto
{
    public Guid Id { get; set; }
    public Guid InventoryAdjustmentId { get; set; }
    public Guid PartId { get; set; }
    public int PreviousQuantity { get; set; }
    public int NewQuantity { get; set; }
    public int AdjustmentQuantity { get; set; }
    public string? Notes { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    
    public PartDto? Part { get; set; }
}

public class CreateInventoryAdjustmentDto
{
    public Guid VendorId { get; set; }
    public Guid BranchId { get; set; }
    public AdjustmentReason Reason { get; set; }
    public string ReasonDescription { get; set; } = string.Empty;
    public string? Notes { get; set; }
    public List<CreateAdjustmentItemDto> Items { get; set; } = new();
}

public class CreateAdjustmentItemDto
{
    public Guid PartId { get; set; }
    public int PreviousQuantity { get; set; }
    public int NewQuantity { get; set; }
    public string? Notes { get; set; }
}

public class AddAdjustmentItemDto
{
    public Guid PartId { get; set; }
    public int PreviousQuantity { get; set; }
    public int NewQuantity { get; set; }
    public string? Notes { get; set; }
}

public class UpdateAdjustmentNotesDto
{
    public string Notes { get; set; } = string.Empty;
}
