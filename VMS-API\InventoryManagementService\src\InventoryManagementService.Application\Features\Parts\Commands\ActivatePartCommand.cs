using FluentValidation;
using InventoryManagementService.Domain.Interfaces;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using VMS.Contracts.Common.Authorization;

namespace InventoryManagementService.Application.Features.Parts.Commands;

public class ActivatePartCommand : IRequest
{
    public Guid PartId { get; set; }
    public string UpdatedBy { get; set; } = string.Empty;
}

public class ActivatePartCommandValidator : AbstractValidator<ActivatePartCommand>
{
    public ActivatePartCommandValidator()
    {
        RuleFor(x => x.PartId)
            .NotEmpty().WithMessage("Part ID is required");

        RuleFor(x => x.UpdatedBy)
            .NotEmpty().WithMessage("UpdatedBy is required");
    }
}

public class ActivatePartCommandHandler : IRequestHandler<ActivatePartCommand>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<ActivatePartCommandHandler> _logger;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly IEventPublisher _eventPublisher;

    public ActivatePartCommandHandler(
        IUnitOfWork unitOfWork,
        ILogger<ActivatePartCommandHandler> logger,
        IHttpContextAccessor httpContextAccessor,
        IEventPublisher eventPublisher)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
        _httpContextAccessor = httpContextAccessor;
        _eventPublisher = eventPublisher;
    }

    public async Task Handle(ActivatePartCommand request, CancellationToken cancellationToken)
    {
        // Check permissions - Admin roles bypass, others need explicit permission
        var user = _httpContextAccessor.HttpContext?.User;
        if (!PermissionHelper.IsAdmin(user))
        {
            PermissionHelper.ValidateAccess(user, "Parts.Update");
        }

        _logger.LogInformation("Activating part with ID: {PartId}", request.PartId);

        var part = await _unitOfWork.Parts.GetByIdAsync(request.PartId);

        if (part == null)
        {
            throw new KeyNotFoundException($"Part with ID {request.PartId} not found");
        }

        part.Activate();
        part.SetUpdatedBy(request.UpdatedBy);

        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("Part activated successfully with ID: {PartId}", part.Id);

        // Publish event
        await _eventPublisher.PublishPartActivatedEvent(part.Id, part.SKU);
    }
}
