using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using IdentityService.Application.Common;
using IdentityService.Application.DTOs.Users;
using IdentityService.Domain.Interfaces;
using MediatR;

namespace IdentityService.Application.Features.Users.Queries;

public class GetUsersQuery : BaseRequest<List<UserResponse>>
{
    public bool IncludeInactive { get; set; } = false;
}

public class GetUsersQueryHandler : IRequestHandler<GetUsersQuery, List<UserResponse>>
{
    private readonly IUnitOfWork _unitOfWork;

    public GetUsersQueryHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
    }

    public async Task<List<UserResponse>> Handle(GetUsersQuery query, CancellationToken cancellationToken)
    {
        var users = await _unitOfWork.UserRepository.ListAllAsync();

        // Filter out inactive users if requested
        if (!query.IncludeInactive)
        {
            users = users.Where(u => !u.IsDeleted).ToList();
        }

        var responses = new List<UserResponse>();

        foreach (var user in users)
        {
            var roles = await _unitOfWork.UserRepository.GetUserRolesAsync(user.Id);

            responses.Add(new UserResponse
            {
                Id = user.Id,
                Email = user.Email,
                PhoneNumber = user.PhoneNumber,
                EmailVerified = user.EmailVerified,
                PhoneNumberVerified = user.PhoneNumberVerified,
                IsActive = !user.IsDeleted,
                LastLoginAt = user.LastLoginAt,
                CreatedAt = user.CreatedAt,
                CreatedBy = user.CreatedBy,
                UpdatedAt = user.UpdatedAt,
                UpdatedBy = user.UpdatedBy,
                Roles = new List<UserRoleResponse>()
            });
        }

        return responses;
    }
}
