using FluentValidation;
using InventoryManagementService.Domain.Interfaces;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using VMS.Contracts.Common.Authorization;

namespace InventoryManagementService.Application.Features.StockTransfers.Commands;

public class AddTransferItemCommand : IRequest
{
    public Guid TransferId { get; set; }
    public Guid PartId { get; set; }
    public int Quantity { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
}

public class AddTransferItemCommandValidator : AbstractValidator<AddTransferItemCommand>
{
    public AddTransferItemCommandValidator()
    {
        RuleFor(x => x.TransferId)
            .NotEmpty().WithMessage("Transfer ID is required");

        RuleFor(x => x.PartId)
            .NotEmpty().WithMessage("Part ID is required");

        RuleFor(x => x.Quantity)
            .GreaterThan(0).WithMessage("Quantity must be greater than 0");

        RuleFor(x => x.CreatedBy)
            .NotEmpty().WithMessage("CreatedBy is required");
    }
}

public class AddTransferItemCommandHandler : IRequestHandler<AddTransferItemCommand>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<AddTransferItemCommandHandler> _logger;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public AddTransferItemCommandHandler(
        IUnitOfWork unitOfWork,
        ILogger<AddTransferItemCommandHandler> logger,
        IHttpContextAccessor httpContextAccessor)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task Handle(AddTransferItemCommand request, CancellationToken cancellationToken)
    {
        // Check permissions - Admin roles bypass, others need explicit permission
        var user = _httpContextAccessor.HttpContext?.User;
        if (!PermissionHelper.IsAdmin(user))
        {
            PermissionHelper.ValidateAccess(user, "Transfers.Update");
        }

        _logger.LogInformation("Adding item to transfer {TransferId}: Part {PartId}, Quantity {Quantity}", 
            request.TransferId, request.PartId, request.Quantity);

        var transfer = await _unitOfWork.StockTransfers.GetByIdAsync(request.TransferId);

        if (transfer == null)
        {
            throw new KeyNotFoundException($"Stock transfer with ID {request.TransferId} not found");
        }

        // Check if part exists
        if (!await _unitOfWork.Parts.ExistsAsync(request.PartId))
        {
            throw new KeyNotFoundException($"Part with ID {request.PartId} not found");
        }

        // Check if there's sufficient stock at source branch
        var inventoryItem = await _unitOfWork.Inventory.GetByPartAndBranchAsync(request.PartId, transfer.SourceBranchId);
        if (inventoryItem == null)
        {
            throw new InvalidOperationException($"Part {request.PartId} is not available at source branch {transfer.SourceBranchId}");
        }

        if (inventoryItem.CurrentStock < request.Quantity)
        {
            throw new InvalidOperationException($"Insufficient stock. Available: {inventoryItem.CurrentStock}, Requested: {request.Quantity}");
        }

        transfer.AddItem(request.PartId, request.Quantity, request.CreatedBy);

        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("Item added successfully to transfer {TransferId}", transfer.Id);
    }
}
