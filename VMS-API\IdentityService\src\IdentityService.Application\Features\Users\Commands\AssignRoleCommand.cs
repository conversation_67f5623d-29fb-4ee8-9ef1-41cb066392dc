using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using IdentityService.Application.Common;
using IdentityService.Application.DTOs.Users;
using IdentityService.Application.Interfaces;
using IdentityService.Domain.Entities;
using IdentityService.Domain.Interfaces;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace IdentityService.Application.Features.Users.Commands;

public class AssignRoleCommand : BaseRequest<UserResponse>
{
    // This UserId is the target user's ID, not the current user's ID
    public new Guid UserId { get; set; }
    public AssignRoleRequest Request { get; set; }
}

public class AssignRoleCommandHandler : IRequestHandler<AssignRoleCommand, UserResponse>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICurrentUserService _currentUserService;
    private readonly IAuditLogService _auditLogService;

    public AssignRoleCommandHandler(
        IUnitOfWork unitOfWork,
        ICurrentUserService currentUserService,
        IAuditLogService auditLogService)
    {
        _unitOfWork = unitOfWork;
        _currentUserService = currentUserService;
        _auditLogService = auditLogService;
    }

    public async Task<UserResponse> Handle(AssignRoleCommand command, CancellationToken cancellationToken)
    {
        const int maxRetries = 5; // Increase max retries
        int retryCount = 0;

        while (true)
        {
            try
            {
                // Begin a transaction to ensure atomicity
                await _unitOfWork.BeginTransactionAsync();

                // Get user with explicit loading of UserRoles to ensure we have the complete entity
                var user = await _unitOfWork.UserRepository.GetByIdWithRolesAsync(command.UserId);
                if (user == null)
                    throw new InvalidOperationException($"User with ID {command.UserId} not found");

                var request = command.Request;
                var updatedBy = _currentUserService.UserId.ToString() ?? "System";

                // Get current roles for audit log
                var currentRoleNames = await _unitOfWork.UserRepository.GetUserRolesAsync(user.Id);
                var oldValues = System.Text.Json.JsonSerializer.Serialize(currentRoleNames);

                // Get all roles for reference
                var allRoles = await _unitOfWork.RoleRepository.GetAllAsync();

                // Get current role entities
                var currentRoles = new List<Role>();
                foreach (var roleName in currentRoleNames)
                {
                    var role = allRoles.FirstOrDefault(r => r.Name == roleName);
                    if (role != null)
                    {
                        currentRoles.Add(role);
                    }
                }

                // Get the new roles to be assigned
                var newRoles = new List<Role>();
                foreach (var roleId in request.RoleIds)
                {
                    var role = allRoles.FirstOrDefault(r => r.Id == roleId);
                    if (role != null)
                    {
                        newRoles.Add(role);
                    }
                }

                // Find roles to remove (roles that are in currentRoles but not in newRoles)
                var rolesToRemove = currentRoles.Where(cr => !newRoles.Any(nr => nr.Id == cr.Id)).ToList();

                // Find roles to add (roles that are in newRoles but not in currentRoles)
                var rolesToAdd = newRoles.Where(nr => !currentRoles.Any(cr => cr.Id == nr.Id)).ToList();

                // Only remove roles that are no longer needed
                foreach (var role in rolesToRemove)
                {
                    user.RemoveRole(role);
                }

                // Only add roles that are not already assigned
                foreach (var role in rolesToAdd)
                {
                    user.AddRole(role, updatedBy);
                }

                // Save changes
                await _unitOfWork.UserRepository.UpdateAsync(user);
                await _unitOfWork.SaveChangesAsync(cancellationToken);

                // Commit the transaction
                await _unitOfWork.CommitTransactionAsync();

                // Get updated roles for audit log and response
                var updatedRoleNames = await _unitOfWork.UserRepository.GetUserRolesAsync(user.Id);
                var newValues = System.Text.Json.JsonSerializer.Serialize(updatedRoleNames);

                // Create audit log
                await _auditLogService.CreateAuditLogAsync(
                    "AssignRoles",
                    "User",
                    user.Id.ToString(),
                    oldValues,
                    newValues,
                    "UserRoles",
                    _currentUserService.UserId ?? Guid.Empty);

                // Create response
                var response = new UserResponse
                {
                    Id = user.Id,
                    Email = user.Email,
                    PhoneNumber = user.PhoneNumber,
                    EmailVerified = user.EmailVerified,
                    PhoneNumberVerified = user.PhoneNumberVerified,
                    IsActive = !user.IsDeleted,
                    LastLoginAt = user.LastLoginAt,
                    CreatedAt = user.CreatedAt,
                    CreatedBy = user.CreatedBy,
                    UpdatedAt = user.UpdatedAt,
                    UpdatedBy = user.UpdatedBy,
                    Roles = new List<UserRoleResponse>()
                };

                return response;
            }
            catch (Exception ex) when (ex.GetType().Name == "DbUpdateConcurrencyException")
            {
                // Rollback the transaction
                await _unitOfWork.RollbackTransactionAsync();

                retryCount++;
                if (retryCount >= maxRetries)
                {
                    throw new InvalidOperationException("Failed to assign roles after multiple attempts due to concurrency conflicts. Please try again later.", ex);
                }

                // Exponential backoff: wait longer with each retry
                await Task.Delay(200 * (int)Math.Pow(2, retryCount), cancellationToken);
            }
            catch (InvalidOperationException ex) when (ex.Message.Contains("deleted by another user"))
            {
                // Rollback the transaction
                await _unitOfWork.RollbackTransactionAsync();

                retryCount++;
                if (retryCount >= maxRetries)
                {
                    throw new InvalidOperationException("Failed to assign roles after multiple attempts due to concurrency conflicts. Please try again later.", ex);
                }

                // Exponential backoff: wait longer with each retry
                await Task.Delay(200 * (int)Math.Pow(2, retryCount), cancellationToken);
            }
            catch (Exception)
            {
                // Rollback the transaction for any other exception
                await _unitOfWork.RollbackTransactionAsync();
                throw; // Rethrow the original exception
            }
        }
    }
}
