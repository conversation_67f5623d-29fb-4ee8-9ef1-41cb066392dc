using AutoMapper;
using BranchManagementService.Application.DTOs;
using BranchManagementService.Domain.Enums;
using BranchManagementService.Domain.Interfaces;
using MediatR;
using Microsoft.Extensions.Logging;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace BranchManagementService.Application.Features.Branches.Commands
{
    public class UpdateBranchCommand : IRequest<BranchDto>
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string ContactPerson { get; set; } = string.Empty;
        public string MobileNumber { get; set; } = string.Empty;
        public string Address { get; set; } = string.Empty;
        public string City { get; set; } = string.Empty;
        public string State { get; set; } = string.Empty;
        public string ZipCode { get; set; } = string.Empty;
        public VehicleTypeSupport VehicleTypeSupport { get; set; }
    }

    public class UpdateBranchCommandHandler : IRequestHandler<UpdateBranchCommand, BranchDto>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly ILogger<UpdateBranchCommandHandler> _logger;

        public UpdateBranchCommandHandler(
            IUnitOfWork unitOfWork,
            IMapper mapper,
            ILogger<UpdateBranchCommandHandler> logger)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task<BranchDto> Handle(UpdateBranchCommand request, CancellationToken cancellationToken)
        {
            try
            {
                var branch = await _unitOfWork.Branches.GetByIdAsync(request.Id);
                if (branch == null)
                {
                    throw new KeyNotFoundException($"Branch with ID {request.Id} not found");
                }

                branch.Update(
                    request.Name,
                    request.Email,
                    request.ContactPerson,
                    request.MobileNumber,
                    request.Address,
                    request.City,
                    request.State,
                    request.ZipCode,
                    request.VehicleTypeSupport,
                    "System");

                await _unitOfWork.Branches.UpdateAsync(branch);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("Branch {BranchId} updated", branch.Id);

                return _mapper.Map<BranchDto>(branch);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating branch {BranchId}", request.Id);
                throw;
            }
        }
    }
}
