2025-05-16 13:50:54.532 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'AuditLog'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-16 13:50:54.627 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'PasswordResetToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-16 13:50:54.628 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserBranch'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-16 13:50:54.628 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserRole'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-16 13:50:54.628 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserSession'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-16 13:50:54.628 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserSubscription'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-16 13:50:55.312 +05:30 [INF] Executed DbCommand (62ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-05-16 13:50:55.331 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-05-16 13:50:55.420 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-05-16 13:50:55.524 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-05-16 13:50:55.527 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-05-16 13:50:55.540 +05:30 [INF] No migrations were applied. The database is already up to date.
2025-05-16 13:50:55.578 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-16 13:50:55.750 +05:30 [INF] Configured endpoint SubscriptionCreated, Consumer: IdentityService.Infrastructure.Messaging.Consumers.SubscriptionCreatedConsumer
2025-05-16 13:50:55.757 +05:30 [INF] Configured endpoint SubscriptionStatusChanged, Consumer: IdentityService.Infrastructure.Messaging.Consumers.SubscriptionStatusChangedConsumer
2025-05-16 13:50:55.762 +05:30 [INF] Configured endpoint VendorRegistered, Consumer: IdentityService.Infrastructure.Messaging.Consumers.VendorRegisteredConsumer
2025-05-16 13:50:56.162 +05:30 [DBG] Starting bus instances: IBus
2025-05-16 13:50:56.172 +05:30 [DBG] Starting bus: "rabbitmq://localhost/"
2025-05-16 13:50:56.222 +05:30 [INF] Session cleanup service is starting
2025-05-16 13:50:56.223 +05:30 [DBG] Starting session cleanup
2025-05-16 13:50:56.313 +05:30 [DBG] Connect: guest@localhost:5672/
2025-05-16 13:50:56.464 +05:30 [DBG] Connected: guest@localhost:5672/ (address: amqp://localhost:5672, local: 58691)
2025-05-16 13:50:56.526 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/DESKTOPI7D4M6U_IdentityServiceAPI_bus_my1yyyfpkikqd8okbdq3ewwwre?temporary=true"
2025-05-16 13:50:56.554 +05:30 [DBG] Declare queue: name: SubscriptionCreated, durable, consumer-count: 2 message-count: 0
2025-05-16 13:50:56.554 +05:30 [DBG] Declare queue: name: VendorRegistered, durable, consumer-count: 1 message-count: 0
2025-05-16 13:50:56.554 +05:30 [DBG] Declare queue: name: SubscriptionStatusChanged, durable, consumer-count: 1 message-count: 0
2025-05-16 13:50:56.576 +05:30 [DBG] Declare exchange: name: VendorRegistered, type: fanout, durable
2025-05-16 13:50:56.590 +05:30 [DBG] Declare exchange: name: VMSContracts.Tenant.Events:VendorRegistered, type: fanout, durable
2025-05-16 13:50:56.593 +05:30 [DBG] Declare exchange: name: SubscriptionStatusChanged, type: fanout, durable
2025-05-16 13:50:56.594 +05:30 [DBG] Declare exchange: name: VMSContracts.Subscription.Events:SubscriptionStatusChanged, type: fanout, durable
2025-05-16 13:50:56.595 +05:30 [DBG] Declare exchange: name: SubscriptionCreated, type: fanout, durable
2025-05-16 13:50:56.596 +05:30 [DBG] Declare exchange: name: VMSContracts.Subscription.Events:SubscriptionCreated, type: fanout, durable
2025-05-16 13:50:56.605 +05:30 [DBG] Bind queue: source: SubscriptionStatusChanged, destination: SubscriptionStatusChanged
2025-05-16 13:50:56.605 +05:30 [DBG] Bind queue: source: SubscriptionCreated, destination: SubscriptionCreated
2025-05-16 13:50:56.605 +05:30 [DBG] Bind queue: source: VendorRegistered, destination: VendorRegistered
2025-05-16 13:50:56.636 +05:30 [DBG] Bind exchange: source: VMSContracts.Tenant.Events:VendorRegistered, destination: VendorRegistered
2025-05-16 13:50:56.636 +05:30 [DBG] Bind exchange: source: VMSContracts.Subscription.Events:SubscriptionCreated, destination: SubscriptionCreated
2025-05-16 13:50:56.638 +05:30 [DBG] Bind exchange: source: VMSContracts.Subscription.Events:SubscriptionStatusChanged, destination: SubscriptionStatusChanged
2025-05-16 13:50:56.695 +05:30 [DBG] Consumer Ok: "rabbitmq://localhost/VendorRegistered" - amq.ctag-U5ZjWx1oz79MNkHR23swyg
2025-05-16 13:50:56.696 +05:30 [DBG] Consumer Ok: "rabbitmq://localhost/SubscriptionCreated" - amq.ctag-cyjrN-xIXFo7-fn0HbDetA
2025-05-16 13:50:56.696 +05:30 [DBG] Consumer Ok: "rabbitmq://localhost/SubscriptionStatusChanged" - amq.ctag-iU9Boo8EbLm-MtwkkfzFMg
2025-05-16 13:50:56.697 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/VendorRegistered"
2025-05-16 13:50:56.697 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/SubscriptionCreated"
2025-05-16 13:50:56.697 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/SubscriptionStatusChanged"
2025-05-16 13:50:56.702 +05:30 [INF] Bus started: "rabbitmq://localhost/"
2025-05-16 13:50:57.043 +05:30 [INF] Executed DbCommand (22ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-16 13:50:57.098 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-16 13:50:57.138 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-16 13:50:57.139 +05:30 [DBG] Session cleanup completed
2025-05-16 13:50:57.720 +05:30 [ERR] Hosting failed to start
System.IO.IOException: Failed to bind to address http://127.0.0.1:5263: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: Only one usage of each socket address (protocol/network address/port) is normally permitted.
 ---> System.Net.Sockets.SocketException (10048): Only one usage of each socket address (protocol/network address/port) is normally permitted.
   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
2025-05-16 13:50:57.867 +05:30 [DBG] Stopping bus instances: IBus
2025-05-16 13:50:57.993 +05:30 [DBG] Stopping bus: "rabbitmq://localhost/"
2025-05-16 13:50:57.982 +05:30 [ERR] BackgroundService failed
System.Threading.Tasks.TaskCanceledException: A task was canceled.
   at IdentityService.API.Services.SessionCleanupService.ExecuteAsync(CancellationToken stoppingToken) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.API\Services\SessionCleanupService.cs:line 43
   at Microsoft.Extensions.Hosting.Internal.Host.TryExecuteBackgroundServiceAsync(BackgroundService backgroundService)
2025-05-16 13:50:58.001 +05:30 [FTL] The HostOptions.BackgroundServiceExceptionBehavior is configured to StopHost. A BackgroundService has thrown an unhandled exception, and the IHost instance is stopping. To avoid this behavior, configure this to Ignore; however the BackgroundService will not be restarted.
System.Threading.Tasks.TaskCanceledException: A task was canceled.
   at IdentityService.API.Services.SessionCleanupService.ExecuteAsync(CancellationToken stoppingToken) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.API\Services\SessionCleanupService.cs:line 43
   at Microsoft.Extensions.Hosting.Internal.Host.TryExecuteBackgroundServiceAsync(BackgroundService backgroundService)
2025-05-16 13:50:58.002 +05:30 [INF] Application is shutting down...
2025-05-16 13:50:58.005 +05:30 [DBG] Endpoint Stopping: "rabbitmq://localhost/SubscriptionCreated"
2025-05-16 13:50:58.015 +05:30 [DBG] Consumer Stopping: "rabbitmq://localhost/SubscriptionCreated" (Stop Receive Transport)
2025-05-16 13:50:58.022 +05:30 [DBG] Consumer Cancel Ok: "rabbitmq://localhost/SubscriptionCreated" - amq.ctag-cyjrN-xIXFo7-fn0HbDetA
2025-05-16 13:50:58.026 +05:30 [DBG] Endpoint Stopping: "rabbitmq://localhost/SubscriptionStatusChanged"
2025-05-16 13:50:58.027 +05:30 [DBG] Consumer Stopping: "rabbitmq://localhost/SubscriptionStatusChanged" (Stop Receive Transport)
2025-05-16 13:50:58.028 +05:30 [DBG] Endpoint Stopping: "rabbitmq://localhost/VendorRegistered"
2025-05-16 13:50:58.028 +05:30 [DBG] Consumer Stopping: "rabbitmq://localhost/VendorRegistered" (Stop Receive Transport)
2025-05-16 13:50:58.029 +05:30 [DBG] Consumer Cancel Ok: "rabbitmq://localhost/SubscriptionStatusChanged" - amq.ctag-iU9Boo8EbLm-MtwkkfzFMg
2025-05-16 13:50:58.035 +05:30 [DBG] Endpoint Completed: "rabbitmq://localhost/SubscriptionCreated"
2025-05-16 13:50:58.035 +05:30 [DBG] Endpoint Completed: "rabbitmq://localhost/SubscriptionStatusChanged"
2025-05-16 13:50:58.051 +05:30 [DBG] Consumer Completed: "rabbitmq://localhost/SubscriptionCreated": 0 received, 0 concurrent, amq.ctag-cyjrN-xIXFo7-fn0HbDetA
2025-05-16 13:50:58.051 +05:30 [DBG] Consumer Completed: "rabbitmq://localhost/SubscriptionStatusChanged": 0 received, 0 concurrent, amq.ctag-iU9Boo8EbLm-MtwkkfzFMg
2025-05-16 13:50:58.055 +05:30 [DBG] Consumer Cancel Ok: "rabbitmq://localhost/VendorRegistered" - amq.ctag-U5ZjWx1oz79MNkHR23swyg
2025-05-16 13:50:58.056 +05:30 [DBG] Endpoint Completed: "rabbitmq://localhost/VendorRegistered"
2025-05-16 13:50:58.056 +05:30 [DBG] Consumer Completed: "rabbitmq://localhost/VendorRegistered": 0 received, 0 concurrent, amq.ctag-U5ZjWx1oz79MNkHR23swyg
2025-05-16 13:50:58.070 +05:30 [DBG] Endpoint Stopping: "rabbitmq://localhost/DESKTOPI7D4M6U_IdentityServiceAPI_bus_my1yyyfpkikqd8okbdq3ewwwre?temporary=true"
2025-05-16 13:50:58.160 +05:30 [DBG] Endpoint Completed: "rabbitmq://localhost/DESKTOPI7D4M6U_IdentityServiceAPI_bus_my1yyyfpkikqd8okbdq3ewwwre?temporary=true"
2025-05-16 13:50:58.164 +05:30 [DBG] Disconnect: guest@localhost:5672/
2025-05-16 13:50:58.170 +05:30 [DBG] Disconnected: guest@localhost:5672/
2025-05-16 13:50:58.175 +05:30 [INF] Bus stopped: "rabbitmq://localhost/"
2025-05-16 15:35:33.903 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'AuditLog'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-16 15:35:33.968 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'PasswordResetToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-16 15:35:33.971 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserBranch'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-16 15:35:33.973 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserRole'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-16 15:35:33.979 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserSession'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-16 15:35:33.981 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserSubscription'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-16 15:35:34.680 +05:30 [INF] Executed DbCommand (85ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-05-16 15:35:34.708 +05:30 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-05-16 15:35:35.132 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-05-16 15:35:35.365 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-05-16 15:35:35.383 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-05-16 15:35:35.427 +05:30 [INF] No migrations were applied. The database is already up to date.
2025-05-16 15:35:35.496 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-16 15:35:35.721 +05:30 [INF] Configured endpoint SubscriptionCreated, Consumer: IdentityService.Infrastructure.Messaging.Consumers.SubscriptionCreatedConsumer
2025-05-16 15:35:35.726 +05:30 [INF] Configured endpoint SubscriptionStatusChanged, Consumer: IdentityService.Infrastructure.Messaging.Consumers.SubscriptionStatusChangedConsumer
2025-05-16 15:35:35.730 +05:30 [INF] Configured endpoint VendorRegistered, Consumer: IdentityService.Infrastructure.Messaging.Consumers.VendorRegisteredConsumer
2025-05-16 15:35:35.933 +05:30 [DBG] Starting bus instances: IBus
2025-05-16 15:35:35.940 +05:30 [DBG] Starting bus: "rabbitmq://localhost/"
2025-05-16 15:35:35.963 +05:30 [INF] Session cleanup service is starting
2025-05-16 15:35:35.966 +05:30 [DBG] Starting session cleanup
2025-05-16 15:35:36.006 +05:30 [DBG] Connect: guest@localhost:5672/
2025-05-16 15:35:36.109 +05:30 [DBG] Connected: guest@localhost:5672/ (address: amqp://localhost:5672, local: 59705)
2025-05-16 15:35:36.153 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/DESKTOPI7D4M6U_IdentityServiceAPI_bus_konoyyfpkikqdsd1bdq3eajufc?temporary=true"
2025-05-16 15:35:36.190 +05:30 [DBG] Declare queue: name: SubscriptionCreated, durable, consumer-count: 0 message-count: 0
2025-05-16 15:35:36.190 +05:30 [DBG] Declare queue: name: VendorRegistered, durable, consumer-count: 0 message-count: 0
2025-05-16 15:35:36.190 +05:30 [DBG] Declare queue: name: SubscriptionStatusChanged, durable, consumer-count: 0 message-count: 0
2025-05-16 15:35:36.228 +05:30 [DBG] Declare exchange: name: VendorRegistered, type: fanout, durable
2025-05-16 15:35:36.228 +05:30 [DBG] Declare exchange: name: SubscriptionStatusChanged, type: fanout, durable
2025-05-16 15:35:36.232 +05:30 [DBG] Declare exchange: name: SubscriptionCreated, type: fanout, durable
2025-05-16 15:35:36.236 +05:30 [DBG] Declare exchange: name: VMSContracts.Tenant.Events:VendorRegistered, type: fanout, durable
2025-05-16 15:35:36.236 +05:30 [DBG] Declare exchange: name: VMSContracts.Subscription.Events:SubscriptionStatusChanged, type: fanout, durable
2025-05-16 15:35:36.255 +05:30 [DBG] Declare exchange: name: VMSContracts.Subscription.Events:SubscriptionCreated, type: fanout, durable
2025-05-16 15:35:36.264 +05:30 [DBG] Bind queue: source: VendorRegistered, destination: VendorRegistered
2025-05-16 15:35:36.264 +05:30 [DBG] Bind queue: source: SubscriptionStatusChanged, destination: SubscriptionStatusChanged
2025-05-16 15:35:36.265 +05:30 [DBG] Bind queue: source: SubscriptionCreated, destination: SubscriptionCreated
2025-05-16 15:35:36.293 +05:30 [DBG] Bind exchange: source: VMSContracts.Subscription.Events:SubscriptionCreated, destination: SubscriptionCreated
2025-05-16 15:35:36.293 +05:30 [DBG] Bind exchange: source: VMSContracts.Tenant.Events:VendorRegistered, destination: VendorRegistered
2025-05-16 15:35:36.293 +05:30 [DBG] Bind exchange: source: VMSContracts.Subscription.Events:SubscriptionStatusChanged, destination: SubscriptionStatusChanged
2025-05-16 15:35:36.351 +05:30 [DBG] Consumer Ok: "rabbitmq://localhost/VendorRegistered" - amq.ctag-Ex7kZDC9hpyr5eY0pNA8dA
2025-05-16 15:35:36.351 +05:30 [DBG] Consumer Ok: "rabbitmq://localhost/SubscriptionCreated" - amq.ctag-g2LieCm28YIAYmDM04hjcQ
2025-05-16 15:35:36.351 +05:30 [DBG] Consumer Ok: "rabbitmq://localhost/SubscriptionStatusChanged" - amq.ctag-nC0touSdrbogCvDzTemSQw
2025-05-16 15:35:36.355 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/VendorRegistered"
2025-05-16 15:35:36.355 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/SubscriptionCreated"
2025-05-16 15:35:36.358 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/SubscriptionStatusChanged"
2025-05-16 15:35:36.365 +05:30 [INF] Bus started: "rabbitmq://localhost/"
2025-05-16 15:35:36.537 +05:30 [INF] Executed DbCommand (20ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-16 15:35:36.590 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-16 15:35:36.633 +05:30 [INF] Now listening on: http://localhost:5263
2025-05-16 15:35:36.637 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-05-16 15:35:36.638 +05:30 [INF] Hosting environment: Development
2025-05-16 15:35:36.640 +05:30 [INF] Content root path: E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.API
2025-05-16 15:35:36.845 +05:30 [INF] Expired session "958023d4-a56d-4ab5-87c6-26d79ca407c8" for user "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 15:35:36.857 +05:30 [WRN] HttpContext is null
2025-05-16 15:35:36.944 +05:30 [INF] Executed DbCommand (7ms) [Parameters=[@p17='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?', @p2='?', @p3='?', @p4='?' (DbType = DateTime), @p5='?' (DbType = DateTime), @p6='?', @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime), @p9='?', @p18='?' (DbType = Binary), @p10='?' (DbType = DateTime), @p11='?' (DbType = Int32), @p12='?', @p13='?' (DbType = DateTime), @p14='?', @p15='?', @p16='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
UPDATE "UserSessions" SET "CreatedAt" = @p0, "CreatedBy" = @p1, "DeviceInfo" = @p2, "EndReason" = @p3, "EndedAt" = @p4, "ExpiresAt" = @p5, "IpAddress" = @p6, "IsDeleted" = @p7, "LastActiveAt" = @p8, "RefreshToken" = @p9, "StartedAt" = @p10, "Status" = @p11, "Token" = @p12, "UpdatedAt" = @p13, "UpdatedBy" = @p14, "UserAgent" = @p15, "UserId" = @p16
WHERE "Id" = @p17 AND "RowVersion" IS NULL
RETURNING "RowVersion";
2025-05-16 15:35:36.968 +05:30 [INF] Expired 1 idle or expired sessions
2025-05-16 15:35:36.970 +05:30 [DBG] Session cleanup completed
2025-05-16 15:36:50.038 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/ - null null
2025-05-16 15:36:50.105 +05:30 [INF] Request was redirected to /swagger
2025-05-16 15:36:50.144 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/ - 302 0 null 104.6376ms
2025-05-16 15:36:50.162 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/swagger/index.html - null null
2025-05-16 15:36:50.281 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/swagger/index.html - 200 null text/html;charset=utf-8 118.4636ms
2025-05-16 15:36:50.404 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/swagger/v1/swagger.json - null null
2025-05-16 15:36:50.615 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 210.871ms
2025-05-16 15:37:02.707 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5263/api/Users/<USER>/roles - application/json 65
2025-05-16 15:37:02.766 +05:30 [WRN] Failed to determine the https port for redirect.
2025-05-16 15:37:04.126 +05:30 [INF] Token received and is being processed
2025-05-16 15:37:04.210 +05:30 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/16/2025 08:44:47', Current time (UTC): '05/16/2025 10:07:04'.
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-05-16 15:37:04.237 +05:30 [ERR] Authentication failed: Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/16/2025 08:44:47', Current time (UTC): '05/16/2025 10:07:04'.
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-05-16 15:37:04.243 +05:30 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '05/16/2025 08:44:47', Current time (UTC): '05/16/2025 10:07:04'.
2025-05-16 15:37:04.338 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__token_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Token" = @__token_0
LIMIT 1
2025-05-16 15:37:04.345 +05:30 [WRN] Session "958023d4-a56d-4ab5-87c6-26d79ca407c8" is not active
2025-05-16 15:37:04.347 +05:30 [WRN] Invalid session for token eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.RRsW-RMhCK7ssdWks6742YRDYF9Qsq0TCfd47J1j-TQ
2025-05-16 15:37:04.362 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5263/api/Users/<USER>/roles - 401 null application/json; charset=utf-8 1655.508ms
2025-05-16 15:37:38.701 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5263/api/Auth/login - application/json 143
2025-05-16 15:37:38.707 +05:30 [INF] Token received and is being processed
2025-05-16 15:37:38.718 +05:30 [WRN] User is null or not authenticated
2025-05-16 15:37:38.719 +05:30 [WRN] No user ID found in token
2025-05-16 15:37:38.724 +05:30 [INF] Executing endpoint 'IdentityService.API.Controllers.AuthController.Login (IdentityService.API)'
2025-05-16 15:37:38.752 +05:30 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[IdentityService.Application.DTOs.Auth.LoginResponse]] Login(IdentityService.Application.DTOs.Auth.LoginRequest) on controller IdentityService.API.Controllers.AuthController (IdentityService.API).
2025-05-16 15:37:38.828 +05:30 [WRN] User ID not found in HttpContext.Items
2025-05-16 15:37:38.831 +05:30 [INF] Setting UserId "00000000-0000-0000-0000-000000000000" on request of type LoginCommand
2025-05-16 15:37:38.833 +05:30 [WRN] User ID not found in HttpContext.Items
2025-05-16 15:37:38.839 +05:30 [INF] Validating request of type LoginCommand
2025-05-16 15:37:38.849 +05:30 [INF] Request content: {
  "Request": {
    "Email": "<EMAIL>",
    "Password": "Admin@123",
    "PhoneNumber": "8088020878",
    "Otp": "1234",
    "RememberMe": true,
    "IsEmailLogin": true,
    "IsPhoneLogin": true
  },
  "UserId": "00000000-0000-0000-0000-000000000000"
}
2025-05-16 15:37:38.851 +05:30 [INF] Request UserId property value: "00000000-0000-0000-0000-000000000000"
2025-05-16 15:37:38.852 +05:30 [WRN] UserId is empty GUID (00000000-0000-0000-0000-000000000000)
2025-05-16 15:37:38.874 +05:30 [INF] Validation passed for request of type LoginCommand
2025-05-16 15:37:38.964 +05:30 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-05-16 15:37:38.984 +05:30 [INF] Executed DbCommand (11ms) [Parameters=[@__email_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash", t0."Id", t0."CreatedAt", t0."CreatedBy", t0."IsDeleted", t0."RoleId", t0."RowVersion", t0."UpdatedAt", t0."UpdatedBy", t0."UserId", t0."Id0", t0."CreatedAt0", t0."CreatedBy0", t0."Description", t0."IsDeleted0", t0."Name", t0."RowVersion0", t0."UpdatedAt0", t0."UpdatedBy0", t0."Id1", t0."CreatedAt1", t0."CreatedBy1", t0."IsDeleted1", t0."PermissionId", t0."RoleId0", t0."RowVersion1", t0."UpdatedAt1", t0."UpdatedBy1", t0."Id00", t0."Action", t0."CreatedAt00", t0."CreatedBy00", t0."Description0", t0."IsDeleted00", t0."Name0", t0."Resource", t0."RowVersion00", t0."UpdatedAt00", t0."UpdatedBy00"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."Email", u."EmailVerified", u."IsDeleted", u."LastLoginAt", u."PhoneNumber", u."PhoneNumberVerified", u."PrimaryBranchId", u."RefreshToken", u."RefreshTokenExpiryTime", u."RememberMe", u."RowVersion", u."SecurityStamp", u."UpdatedAt", u."UpdatedBy", u."VendorId", u."PasswordHash"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__email_0
    LIMIT 1
) AS t
LEFT JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."IsDeleted", u0."RoleId", u0."RowVersion", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", r."Id" AS "Id0", r."CreatedAt" AS "CreatedAt0", r."CreatedBy" AS "CreatedBy0", r."Description", r."IsDeleted" AS "IsDeleted0", r."Name", r."RowVersion" AS "RowVersion0", r."UpdatedAt" AS "UpdatedAt0", r."UpdatedBy" AS "UpdatedBy0", t1."Id" AS "Id1", t1."CreatedAt" AS "CreatedAt1", t1."CreatedBy" AS "CreatedBy1", t1."IsDeleted" AS "IsDeleted1", t1."PermissionId", t1."RoleId" AS "RoleId0", t1."RowVersion" AS "RowVersion1", t1."UpdatedAt" AS "UpdatedAt1", t1."UpdatedBy" AS "UpdatedBy1", t1."Id0" AS "Id00", t1."Action", t1."CreatedAt0" AS "CreatedAt00", t1."CreatedBy0" AS "CreatedBy00", t1."Description" AS "Description0", t1."IsDeleted0" AS "IsDeleted00", t1."Name" AS "Name0", t1."Resource", t1."RowVersion0" AS "RowVersion00", t1."UpdatedAt0" AS "UpdatedAt00", t1."UpdatedBy0" AS "UpdatedBy00"
    FROM "UserRoles" AS u0
    INNER JOIN "Roles" AS r ON u0."RoleId" = r."Id"
    LEFT JOIN (
        SELECT r0."Id", r0."CreatedAt", r0."CreatedBy", r0."IsDeleted", r0."PermissionId", r0."RoleId", r0."RowVersion", r0."UpdatedAt", r0."UpdatedBy", p."Id" AS "Id0", p."Action", p."CreatedAt" AS "CreatedAt0", p."CreatedBy" AS "CreatedBy0", p."Description", p."IsDeleted" AS "IsDeleted0", p."Name", p."Resource", p."RowVersion" AS "RowVersion0", p."UpdatedAt" AS "UpdatedAt0", p."UpdatedBy" AS "UpdatedBy0"
        FROM "RolePermissions" AS r0
        INNER JOIN "Permissions" AS p ON r0."PermissionId" = p."Id"
    ) AS t1 ON r."Id" = t1."RoleId"
) AS t0 ON t."Id" = t0."UserId"
ORDER BY t."Id", t0."Id", t0."Id0", t0."Id1"
2025-05-16 15:37:39.183 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT count(*)::int
FROM "UserSessions" AS u
WHERE u."UserId" = @__userId_0 AND u."Status" = 0 AND u."ExpiresAt" > now()
2025-05-16 15:37:39.188 +05:30 [WRN] User is null or not authenticated
2025-05-16 15:37:39.204 +05:30 [WRN] User is null or not authenticated
2025-05-16 15:37:39.205 +05:30 [WRN] User is null or not authenticated
2025-05-16 15:37:39.242 +05:30 [INF] Executed DbCommand (5ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = DateTime), @p2='?', @p3='?', @p4='?', @p5='?', @p6='?', @p7='?' (DbType = Boolean), @p8='?' (DbType = Boolean), @p9='?', @p10='?' (DbType = Int32), @p11='?', @p12='?' (DbType = Binary), @p13='?' (DbType = DateTime), @p14='?', @p15='?', @p16='?' (DbType = Guid), @p17='?' (DbType = Guid), @p18='?' (DbType = DateTime), @p19='?', @p20='?', @p21='?', @p22='?' (DbType = DateTime), @p23='?' (DbType = DateTime), @p24='?', @p25='?' (DbType = Boolean), @p26='?' (DbType = DateTime), @p27='?', @p28='?' (DbType = DateTime), @p29='?' (DbType = Int32), @p30='?', @p31='?' (DbType = DateTime), @p32='?', @p33='?', @p34='?' (DbType = Guid), @p40='?' (DbType = Guid), @p35='?' (DbType = DateTime), @p36='?', @p37='?' (DbType = DateTime), @p41='?' (DbType = Binary), @p38='?' (DbType = DateTime), @p39='?'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "UserLoginHistories" ("Id", "CreatedAt", "CreatedBy", "DeviceInfo", "Email", "FailureReason", "IpAddress", "IsDeleted", "IsSuccessful", "Location", "LoginMethod", "PhoneNumber", "RowVersion", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16);
INSERT INTO "UserSessions" ("Id", "CreatedAt", "CreatedBy", "DeviceInfo", "EndReason", "EndedAt", "ExpiresAt", "IpAddress", "IsDeleted", "LastActiveAt", "RefreshToken", "StartedAt", "Status", "Token", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34)
RETURNING "RowVersion";
UPDATE "Users" SET "LastLoginAt" = @p35, "RefreshToken" = @p36, "RefreshTokenExpiryTime" = @p37, "UpdatedAt" = @p38, "UpdatedBy" = @p39
WHERE "Id" = @p40 AND "RowVersion" = @p41
RETURNING "RowVersion";
2025-05-16 15:37:39.270 +05:30 [INF] Created new session "ac54b93e-4d26-4a45-bb66-1be13c0b56e8" for user "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 15:37:39.284 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Name"
FROM "UserRoles" AS u
INNER JOIN "Roles" AS r ON u."RoleId" = r."Id"
WHERE u."UserId" = @__userId_0
2025-05-16 15:37:39.295 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."Description", r."IsDeleted", r."Name", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "UserRoles" AS u
INNER JOIN "Roles" AS r ON u."RoleId" = r."Id"
WHERE u."UserId" = @__userId_0
2025-05-16 15:37:39.308 +05:30 [INF] Executing OkObjectResult, writing value of type 'IdentityService.Application.DTOs.Auth.LoginResponse'.
2025-05-16 15:37:39.328 +05:30 [INF] Executed action IdentityService.API.Controllers.AuthController.Login (IdentityService.API) in 567.8585ms
2025-05-16 15:37:39.333 +05:30 [INF] Executed endpoint 'IdentityService.API.Controllers.AuthController.Login (IdentityService.API)'
2025-05-16 15:37:39.335 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5263/api/Auth/login - 200 null application/json; charset=utf-8 634.0785ms
2025-05-16 15:40:36.973 +05:30 [DBG] Starting session cleanup
2025-05-16 15:40:37.060 +05:30 [INF] Executed DbCommand (7ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-16 15:40:37.067 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-16 15:40:37.071 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-16 15:40:37.072 +05:30 [DBG] Session cleanup completed
2025-05-16 15:42:35.811 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5263/api/Users/<USER>/roles - application/json 65
2025-05-16 15:42:35.845 +05:30 [INF] Token received and is being processed
2025-05-16 15:42:35.869 +05:30 [INF] Token validated successfully with claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 15:42:35.872 +05:30 [INF] NameIdentifier claim found: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e
2025-05-16 15:42:35.909 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__get_Item_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."Email", u."EmailVerified", u."IsDeleted", u."LastLoginAt", u."PhoneNumber", u."PhoneNumberVerified", u."PrimaryBranchId", u."RefreshToken", u."RefreshTokenExpiryTime", u."RememberMe", u."RowVersion", u."SecurityStamp", u."UpdatedAt", u."UpdatedBy", u."VendorId", u."PasswordHash"
FROM "Users" AS u
WHERE NOT (u."IsDeleted") AND u."Id" = @__get_Item_0
LIMIT 1
2025-05-16 15:42:35.915 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__token_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Token" = @__token_0
LIMIT 1
2025-05-16 15:42:35.924 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 15:42:35.926 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 15:42:35.931 +05:30 [INF] Executed DbCommand (4ms) [Parameters=[@p17='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?', @p2='?', @p3='?', @p4='?' (DbType = DateTime), @p5='?' (DbType = DateTime), @p6='?', @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime), @p9='?', @p18='?' (DbType = Binary), @p10='?' (DbType = DateTime), @p11='?' (DbType = Int32), @p12='?', @p13='?' (DbType = DateTime), @p14='?', @p15='?', @p16='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
UPDATE "UserSessions" SET "CreatedAt" = @p0, "CreatedBy" = @p1, "DeviceInfo" = @p2, "EndReason" = @p3, "EndedAt" = @p4, "ExpiresAt" = @p5, "IpAddress" = @p6, "IsDeleted" = @p7, "LastActiveAt" = @p8, "RefreshToken" = @p9, "StartedAt" = @p10, "Status" = @p11, "Token" = @p12, "UpdatedAt" = @p13, "UpdatedBy" = @p14, "UserAgent" = @p15, "UserId" = @p16
WHERE "Id" = @p17 AND "RowVersion" IS NULL
RETURNING "RowVersion";
2025-05-16 15:42:35.941 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__token_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Token" = @__token_0
LIMIT 1
2025-05-16 15:42:35.947 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 15:42:35.950 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 15:42:35.953 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@p17='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?', @p2='?', @p3='?', @p4='?' (DbType = DateTime), @p5='?' (DbType = DateTime), @p6='?', @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime), @p9='?', @p18='?' (DbType = Binary), @p10='?' (DbType = DateTime), @p11='?' (DbType = Int32), @p12='?', @p13='?' (DbType = DateTime), @p14='?', @p15='?', @p16='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
UPDATE "UserSessions" SET "CreatedAt" = @p0, "CreatedBy" = @p1, "DeviceInfo" = @p2, "EndReason" = @p3, "EndedAt" = @p4, "ExpiresAt" = @p5, "IpAddress" = @p6, "IsDeleted" = @p7, "LastActiveAt" = @p8, "RefreshToken" = @p9, "StartedAt" = @p10, "Status" = @p11, "Token" = @p12, "UpdatedAt" = @p13, "UpdatedBy" = @p14, "UserAgent" = @p15, "UserId" = @p16
WHERE "Id" = @p17 AND "RowVersion" IS NULL
RETURNING "RowVersion";
2025-05-16 15:42:35.959 +05:30 [DBG] Updated last active time for session "ac54b93e-4d26-4a45-bb66-1be13c0b56e8"
2025-05-16 15:42:35.960 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 15:42:35.975 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 15:42:36.012 +05:30 [INF] User ID from token: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 15:42:36.019 +05:30 [INF] Executing endpoint 'IdentityService.API.Controllers.UsersController.AssignRoles (IdentityService.API)'
2025-05-16 15:42:36.046 +05:30 [INF] Route matched with {action = "AssignRoles", controller = "Users"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[IdentityService.Application.DTOs.Users.UserResponse]] AssignRoles(System.Guid, IdentityService.Application.DTOs.Users.AssignRoleRequest) on controller IdentityService.API.Controllers.UsersController (IdentityService.API).
2025-05-16 15:42:36.081 +05:30 [INF] Retrieved user ID from HttpContext.Items: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 15:42:36.096 +05:30 [INF] Setting UserId "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e" on request of type AssignRoleCommand
2025-05-16 15:42:36.106 +05:30 [INF] Retrieved user ID from HttpContext.Items: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 15:42:36.110 +05:30 [INF] Validating request of type AssignRoleCommand
2025-05-16 15:42:36.138 +05:30 [INF] Request content: {
  "UserId": "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e",
  "Request": {
    "RoleIds": [
      "0247e353-d1fb-4118-b630-fecff038de1e"
    ]
  }
}
2025-05-16 15:42:36.145 +05:30 [INF] Request UserId property value: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 15:42:36.147 +05:30 [INF] Validation passed for request of type AssignRoleCommand
2025-05-16 15:42:36.214 +05:30 [INF] Executed DbCommand (6ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash", t0."Id", t0."CreatedAt", t0."CreatedBy", t0."IsDeleted", t0."RoleId", t0."RowVersion", t0."UpdatedAt", t0."UpdatedBy", t0."UserId", t0."Id0", t0."CreatedAt0", t0."CreatedBy0", t0."Description", t0."IsDeleted0", t0."Name", t0."RowVersion0", t0."UpdatedAt0", t0."UpdatedBy0"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."Email", u."EmailVerified", u."IsDeleted", u."LastLoginAt", u."PhoneNumber", u."PhoneNumberVerified", u."PrimaryBranchId", u."RefreshToken", u."RefreshTokenExpiryTime", u."RememberMe", u."RowVersion", u."SecurityStamp", u."UpdatedAt", u."UpdatedBy", u."VendorId", u."PasswordHash"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Id" = @__userId_0
    LIMIT 1
) AS t
LEFT JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."IsDeleted", u0."RoleId", u0."RowVersion", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", r."Id" AS "Id0", r."CreatedAt" AS "CreatedAt0", r."CreatedBy" AS "CreatedBy0", r."Description", r."IsDeleted" AS "IsDeleted0", r."Name", r."RowVersion" AS "RowVersion0", r."UpdatedAt" AS "UpdatedAt0", r."UpdatedBy" AS "UpdatedBy0"
    FROM "UserRoles" AS u0
    INNER JOIN "Roles" AS r ON u0."RoleId" = r."Id"
) AS t0 ON t."Id" = t0."UserId"
ORDER BY t."Id", t0."Id"
2025-05-16 15:42:36.319 +05:30 [INF] Executed DbCommand (25ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash", t0."Id", t0."CreatedAt", t0."CreatedBy", t0."IsDeleted", t0."RoleId", t0."RowVersion", t0."UpdatedAt", t0."UpdatedBy", t0."UserId", t0."Id0", t0."CreatedAt0", t0."CreatedBy0", t0."Description", t0."IsDeleted0", t0."Name", t0."RowVersion0", t0."UpdatedAt0", t0."UpdatedBy0"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."Email", u."EmailVerified", u."IsDeleted", u."LastLoginAt", u."PhoneNumber", u."PhoneNumberVerified", u."PrimaryBranchId", u."RefreshToken", u."RefreshTokenExpiryTime", u."RememberMe", u."RowVersion", u."SecurityStamp", u."UpdatedAt", u."UpdatedBy", u."VendorId", u."PasswordHash"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Id" = @__userId_0
    LIMIT 1
) AS t
LEFT JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."IsDeleted", u0."RoleId", u0."RowVersion", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", r."Id" AS "Id0", r."CreatedAt" AS "CreatedAt0", r."CreatedBy" AS "CreatedBy0", r."Description", r."IsDeleted" AS "IsDeleted0", r."Name", r."RowVersion" AS "RowVersion0", r."UpdatedAt" AS "UpdatedAt0", r."UpdatedBy" AS "UpdatedBy0"
    FROM "UserRoles" AS u0
    INNER JOIN "Roles" AS r ON u0."RoleId" = r."Id"
) AS t0 ON t."Id" = t0."UserId"
ORDER BY t."Id", t0."Id"
2025-05-16 15:42:36.354 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 15:42:36.363 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 15:42:36.373 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Name"
FROM "UserRoles" AS u
INNER JOIN "Roles" AS r ON u."RoleId" = r."Id"
WHERE u."UserId" = @__userId_0
2025-05-16 15:42:36.407 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."Description", r."IsDeleted", r."Name", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "Roles" AS r
2025-05-16 15:42:36.626 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 15:42:36.627 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 15:42:36.628 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 15:42:36.630 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 15:42:36.646 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@p8='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?', @p2='?' (DbType = Boolean), @p3='?' (DbType = Guid), @p4='?' (DbType = Binary), @p5='?' (DbType = DateTime), @p6='?', @p7='?' (DbType = Guid), @p25='?' (DbType = Guid), @p9='?' (DbType = DateTime), @p10='?', @p11='?', @p12='?' (DbType = Boolean), @p13='?' (DbType = Boolean), @p14='?' (DbType = DateTime), @p15='?', @p16='?' (DbType = Boolean), @p17='?' (DbType = Guid), @p18='?', @p19='?' (DbType = DateTime), @p20='?' (DbType = Boolean), @p26='?' (DbType = Binary), @p21='?' (DbType = DateTime), @p22='?' (DbType = DateTime), @p23='?', @p24='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
UPDATE "UserRoles" SET "CreatedAt" = @p0, "CreatedBy" = @p1, "IsDeleted" = @p2, "RoleId" = @p3, "RowVersion" = @p4, "UpdatedAt" = @p5, "UpdatedBy" = @p6, "UserId" = @p7
WHERE "Id" = @p8;
UPDATE "Users" SET "CreatedAt" = @p9, "CreatedBy" = @p10, "Email" = @p11, "EmailVerified" = @p12, "IsDeleted" = @p13, "LastLoginAt" = @p14, "PhoneNumber" = @p15, "PhoneNumberVerified" = @p16, "PrimaryBranchId" = @p17, "RefreshToken" = @p18, "RefreshTokenExpiryTime" = @p19, "RememberMe" = @p20, "SecurityStamp" = @p21, "UpdatedAt" = @p22, "UpdatedBy" = @p23, "VendorId" = @p24
WHERE "Id" = @p25 AND "RowVersion" = @p26
RETURNING "RowVersion";
2025-05-16 15:42:36.693 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__get_Item_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."IsDeleted", u."RoleId", u."RowVersion", u."UpdatedAt", u."UpdatedBy", u."UserId"
FROM "UserRoles" AS u
WHERE u."Id" = @__get_Item_0
LIMIT 1
2025-05-16 15:42:37.107 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash", t0."Id", t0."CreatedAt", t0."CreatedBy", t0."IsDeleted", t0."RoleId", t0."RowVersion", t0."UpdatedAt", t0."UpdatedBy", t0."UserId", t0."Id0", t0."CreatedAt0", t0."CreatedBy0", t0."Description", t0."IsDeleted0", t0."Name", t0."RowVersion0", t0."UpdatedAt0", t0."UpdatedBy0"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."Email", u."EmailVerified", u."IsDeleted", u."LastLoginAt", u."PhoneNumber", u."PhoneNumberVerified", u."PrimaryBranchId", u."RefreshToken", u."RefreshTokenExpiryTime", u."RememberMe", u."RowVersion", u."SecurityStamp", u."UpdatedAt", u."UpdatedBy", u."VendorId", u."PasswordHash"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Id" = @__userId_0
    LIMIT 1
) AS t
LEFT JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."IsDeleted", u0."RoleId", u0."RowVersion", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", r."Id" AS "Id0", r."CreatedAt" AS "CreatedAt0", r."CreatedBy" AS "CreatedBy0", r."Description", r."IsDeleted" AS "IsDeleted0", r."Name", r."RowVersion" AS "RowVersion0", r."UpdatedAt" AS "UpdatedAt0", r."UpdatedBy" AS "UpdatedBy0"
    FROM "UserRoles" AS u0
    INNER JOIN "Roles" AS r ON u0."RoleId" = r."Id"
) AS t0 ON t."Id" = t0."UserId"
ORDER BY t."Id", t0."Id"
2025-05-16 15:42:37.116 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash", t0."Id", t0."CreatedAt", t0."CreatedBy", t0."IsDeleted", t0."RoleId", t0."RowVersion", t0."UpdatedAt", t0."UpdatedBy", t0."UserId", t0."Id0", t0."CreatedAt0", t0."CreatedBy0", t0."Description", t0."IsDeleted0", t0."Name", t0."RowVersion0", t0."UpdatedAt0", t0."UpdatedBy0"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."Email", u."EmailVerified", u."IsDeleted", u."LastLoginAt", u."PhoneNumber", u."PhoneNumberVerified", u."PrimaryBranchId", u."RefreshToken", u."RefreshTokenExpiryTime", u."RememberMe", u."RowVersion", u."SecurityStamp", u."UpdatedAt", u."UpdatedBy", u."VendorId", u."PasswordHash"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Id" = @__userId_0
    LIMIT 1
) AS t
LEFT JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."IsDeleted", u0."RoleId", u0."RowVersion", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", r."Id" AS "Id0", r."CreatedAt" AS "CreatedAt0", r."CreatedBy" AS "CreatedBy0", r."Description", r."IsDeleted" AS "IsDeleted0", r."Name", r."RowVersion" AS "RowVersion0", r."UpdatedAt" AS "UpdatedAt0", r."UpdatedBy" AS "UpdatedBy0"
    FROM "UserRoles" AS u0
    INNER JOIN "Roles" AS r ON u0."RoleId" = r."Id"
) AS t0 ON t."Id" = t0."UserId"
ORDER BY t."Id", t0."Id"
2025-05-16 15:42:37.128 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 15:42:37.133 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 15:42:37.136 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Name"
FROM "UserRoles" AS u
INNER JOIN "Roles" AS r ON u."RoleId" = r."Id"
WHERE u."UserId" = @__userId_0
2025-05-16 15:42:37.144 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."Description", r."IsDeleted", r."Name", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "Roles" AS r
2025-05-16 15:42:37.150 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 15:42:37.158 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 15:42:37.160 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 15:42:37.161 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 15:42:37.163 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 15:42:37.167 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 15:42:37.177 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@p8='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?', @p2='?' (DbType = Boolean), @p3='?' (DbType = Guid), @p4='?' (DbType = Binary), @p5='?' (DbType = DateTime), @p6='?', @p7='?' (DbType = Guid), @p17='?' (DbType = Guid), @p9='?' (DbType = DateTime), @p10='?', @p11='?' (DbType = Boolean), @p12='?' (DbType = Guid), @p13='?' (DbType = Binary), @p14='?' (DbType = DateTime), @p15='?', @p16='?' (DbType = Guid), @p34='?' (DbType = Guid), @p18='?' (DbType = DateTime), @p19='?', @p20='?', @p21='?' (DbType = Boolean), @p22='?' (DbType = Boolean), @p23='?' (DbType = DateTime), @p24='?', @p25='?' (DbType = Boolean), @p26='?' (DbType = Guid), @p27='?', @p28='?' (DbType = DateTime), @p29='?' (DbType = Boolean), @p35='?' (DbType = Binary), @p30='?' (DbType = DateTime), @p31='?' (DbType = DateTime), @p32='?', @p33='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
UPDATE "UserRoles" SET "CreatedAt" = @p0, "CreatedBy" = @p1, "IsDeleted" = @p2, "RoleId" = @p3, "RowVersion" = @p4, "UpdatedAt" = @p5, "UpdatedBy" = @p6, "UserId" = @p7
WHERE "Id" = @p8;
UPDATE "UserRoles" SET "CreatedAt" = @p9, "CreatedBy" = @p10, "IsDeleted" = @p11, "RoleId" = @p12, "RowVersion" = @p13, "UpdatedAt" = @p14, "UpdatedBy" = @p15, "UserId" = @p16
WHERE "Id" = @p17;
UPDATE "Users" SET "CreatedAt" = @p18, "CreatedBy" = @p19, "Email" = @p20, "EmailVerified" = @p21, "IsDeleted" = @p22, "LastLoginAt" = @p23, "PhoneNumber" = @p24, "PhoneNumberVerified" = @p25, "PrimaryBranchId" = @p26, "RefreshToken" = @p27, "RefreshTokenExpiryTime" = @p28, "RememberMe" = @p29, "SecurityStamp" = @p30, "UpdatedAt" = @p31, "UpdatedBy" = @p32, "VendorId" = @p33
WHERE "Id" = @p34 AND "RowVersion" = @p35
RETURNING "RowVersion";
2025-05-16 15:42:37.186 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__get_Item_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."IsDeleted", u."RoleId", u."RowVersion", u."UpdatedAt", u."UpdatedBy", u."UserId"
FROM "UserRoles" AS u
WHERE u."Id" = @__get_Item_0
LIMIT 1
2025-05-16 15:42:38.002 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash", t0."Id", t0."CreatedAt", t0."CreatedBy", t0."IsDeleted", t0."RoleId", t0."RowVersion", t0."UpdatedAt", t0."UpdatedBy", t0."UserId", t0."Id0", t0."CreatedAt0", t0."CreatedBy0", t0."Description", t0."IsDeleted0", t0."Name", t0."RowVersion0", t0."UpdatedAt0", t0."UpdatedBy0"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."Email", u."EmailVerified", u."IsDeleted", u."LastLoginAt", u."PhoneNumber", u."PhoneNumberVerified", u."PrimaryBranchId", u."RefreshToken", u."RefreshTokenExpiryTime", u."RememberMe", u."RowVersion", u."SecurityStamp", u."UpdatedAt", u."UpdatedBy", u."VendorId", u."PasswordHash"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Id" = @__userId_0
    LIMIT 1
) AS t
LEFT JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."IsDeleted", u0."RoleId", u0."RowVersion", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", r."Id" AS "Id0", r."CreatedAt" AS "CreatedAt0", r."CreatedBy" AS "CreatedBy0", r."Description", r."IsDeleted" AS "IsDeleted0", r."Name", r."RowVersion" AS "RowVersion0", r."UpdatedAt" AS "UpdatedAt0", r."UpdatedBy" AS "UpdatedBy0"
    FROM "UserRoles" AS u0
    INNER JOIN "Roles" AS r ON u0."RoleId" = r."Id"
) AS t0 ON t."Id" = t0."UserId"
ORDER BY t."Id", t0."Id"
2025-05-16 15:42:38.038 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash", t0."Id", t0."CreatedAt", t0."CreatedBy", t0."IsDeleted", t0."RoleId", t0."RowVersion", t0."UpdatedAt", t0."UpdatedBy", t0."UserId", t0."Id0", t0."CreatedAt0", t0."CreatedBy0", t0."Description", t0."IsDeleted0", t0."Name", t0."RowVersion0", t0."UpdatedAt0", t0."UpdatedBy0"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."Email", u."EmailVerified", u."IsDeleted", u."LastLoginAt", u."PhoneNumber", u."PhoneNumberVerified", u."PrimaryBranchId", u."RefreshToken", u."RefreshTokenExpiryTime", u."RememberMe", u."RowVersion", u."SecurityStamp", u."UpdatedAt", u."UpdatedBy", u."VendorId", u."PasswordHash"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Id" = @__userId_0
    LIMIT 1
) AS t
LEFT JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."IsDeleted", u0."RoleId", u0."RowVersion", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", r."Id" AS "Id0", r."CreatedAt" AS "CreatedAt0", r."CreatedBy" AS "CreatedBy0", r."Description", r."IsDeleted" AS "IsDeleted0", r."Name", r."RowVersion" AS "RowVersion0", r."UpdatedAt" AS "UpdatedAt0", r."UpdatedBy" AS "UpdatedBy0"
    FROM "UserRoles" AS u0
    INNER JOIN "Roles" AS r ON u0."RoleId" = r."Id"
) AS t0 ON t."Id" = t0."UserId"
ORDER BY t."Id", t0."Id"
2025-05-16 15:42:38.054 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 15:42:38.060 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 15:42:38.063 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Name"
FROM "UserRoles" AS u
INNER JOIN "Roles" AS r ON u."RoleId" = r."Id"
WHERE u."UserId" = @__userId_0
2025-05-16 15:42:38.069 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."Description", r."IsDeleted", r."Name", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "Roles" AS r
2025-05-16 15:42:38.075 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 15:42:38.078 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 15:42:38.080 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 15:42:38.085 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 15:42:38.086 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 15:42:38.090 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 15:42:38.093 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 15:42:38.094 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 15:42:38.101 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@p8='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?', @p2='?' (DbType = Boolean), @p3='?' (DbType = Guid), @p4='?' (DbType = Binary), @p5='?' (DbType = DateTime), @p6='?', @p7='?' (DbType = Guid), @p17='?' (DbType = Guid), @p9='?' (DbType = DateTime), @p10='?', @p11='?' (DbType = Boolean), @p12='?' (DbType = Guid), @p13='?' (DbType = Binary), @p14='?' (DbType = DateTime), @p15='?', @p16='?' (DbType = Guid), @p26='?' (DbType = Guid), @p18='?' (DbType = DateTime), @p19='?', @p20='?' (DbType = Boolean), @p21='?' (DbType = Guid), @p22='?' (DbType = Binary), @p23='?' (DbType = DateTime), @p24='?', @p25='?' (DbType = Guid), @p43='?' (DbType = Guid), @p27='?' (DbType = DateTime), @p28='?', @p29='?', @p30='?' (DbType = Boolean), @p31='?' (DbType = Boolean), @p32='?' (DbType = DateTime), @p33='?', @p34='?' (DbType = Boolean), @p35='?' (DbType = Guid), @p36='?', @p37='?' (DbType = DateTime), @p38='?' (DbType = Boolean), @p44='?' (DbType = Binary), @p39='?' (DbType = DateTime), @p40='?' (DbType = DateTime), @p41='?', @p42='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
UPDATE "UserRoles" SET "CreatedAt" = @p0, "CreatedBy" = @p1, "IsDeleted" = @p2, "RoleId" = @p3, "RowVersion" = @p4, "UpdatedAt" = @p5, "UpdatedBy" = @p6, "UserId" = @p7
WHERE "Id" = @p8;
UPDATE "UserRoles" SET "CreatedAt" = @p9, "CreatedBy" = @p10, "IsDeleted" = @p11, "RoleId" = @p12, "RowVersion" = @p13, "UpdatedAt" = @p14, "UpdatedBy" = @p15, "UserId" = @p16
WHERE "Id" = @p17;
UPDATE "UserRoles" SET "CreatedAt" = @p18, "CreatedBy" = @p19, "IsDeleted" = @p20, "RoleId" = @p21, "RowVersion" = @p22, "UpdatedAt" = @p23, "UpdatedBy" = @p24, "UserId" = @p25
WHERE "Id" = @p26;
UPDATE "Users" SET "CreatedAt" = @p27, "CreatedBy" = @p28, "Email" = @p29, "EmailVerified" = @p30, "IsDeleted" = @p31, "LastLoginAt" = @p32, "PhoneNumber" = @p33, "PhoneNumberVerified" = @p34, "PrimaryBranchId" = @p35, "RefreshToken" = @p36, "RefreshTokenExpiryTime" = @p37, "RememberMe" = @p38, "SecurityStamp" = @p39, "UpdatedAt" = @p40, "UpdatedBy" = @p41, "VendorId" = @p42
WHERE "Id" = @p43 AND "RowVersion" = @p44
RETURNING "RowVersion";
2025-05-16 15:42:38.118 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__get_Item_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."IsDeleted", u."RoleId", u."RowVersion", u."UpdatedAt", u."UpdatedBy", u."UserId"
FROM "UserRoles" AS u
WHERE u."Id" = @__get_Item_0
LIMIT 1
2025-05-16 15:42:39.738 +05:30 [INF] Executed DbCommand (6ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash", t0."Id", t0."CreatedAt", t0."CreatedBy", t0."IsDeleted", t0."RoleId", t0."RowVersion", t0."UpdatedAt", t0."UpdatedBy", t0."UserId", t0."Id0", t0."CreatedAt0", t0."CreatedBy0", t0."Description", t0."IsDeleted0", t0."Name", t0."RowVersion0", t0."UpdatedAt0", t0."UpdatedBy0"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."Email", u."EmailVerified", u."IsDeleted", u."LastLoginAt", u."PhoneNumber", u."PhoneNumberVerified", u."PrimaryBranchId", u."RefreshToken", u."RefreshTokenExpiryTime", u."RememberMe", u."RowVersion", u."SecurityStamp", u."UpdatedAt", u."UpdatedBy", u."VendorId", u."PasswordHash"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Id" = @__userId_0
    LIMIT 1
) AS t
LEFT JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."IsDeleted", u0."RoleId", u0."RowVersion", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", r."Id" AS "Id0", r."CreatedAt" AS "CreatedAt0", r."CreatedBy" AS "CreatedBy0", r."Description", r."IsDeleted" AS "IsDeleted0", r."Name", r."RowVersion" AS "RowVersion0", r."UpdatedAt" AS "UpdatedAt0", r."UpdatedBy" AS "UpdatedBy0"
    FROM "UserRoles" AS u0
    INNER JOIN "Roles" AS r ON u0."RoleId" = r."Id"
) AS t0 ON t."Id" = t0."UserId"
ORDER BY t."Id", t0."Id"
2025-05-16 15:42:39.779 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash", t0."Id", t0."CreatedAt", t0."CreatedBy", t0."IsDeleted", t0."RoleId", t0."RowVersion", t0."UpdatedAt", t0."UpdatedBy", t0."UserId", t0."Id0", t0."CreatedAt0", t0."CreatedBy0", t0."Description", t0."IsDeleted0", t0."Name", t0."RowVersion0", t0."UpdatedAt0", t0."UpdatedBy0"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."Email", u."EmailVerified", u."IsDeleted", u."LastLoginAt", u."PhoneNumber", u."PhoneNumberVerified", u."PrimaryBranchId", u."RefreshToken", u."RefreshTokenExpiryTime", u."RememberMe", u."RowVersion", u."SecurityStamp", u."UpdatedAt", u."UpdatedBy", u."VendorId", u."PasswordHash"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Id" = @__userId_0
    LIMIT 1
) AS t
LEFT JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."IsDeleted", u0."RoleId", u0."RowVersion", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", r."Id" AS "Id0", r."CreatedAt" AS "CreatedAt0", r."CreatedBy" AS "CreatedBy0", r."Description", r."IsDeleted" AS "IsDeleted0", r."Name", r."RowVersion" AS "RowVersion0", r."UpdatedAt" AS "UpdatedAt0", r."UpdatedBy" AS "UpdatedBy0"
    FROM "UserRoles" AS u0
    INNER JOIN "Roles" AS r ON u0."RoleId" = r."Id"
) AS t0 ON t."Id" = t0."UserId"
ORDER BY t."Id", t0."Id"
2025-05-16 15:42:39.793 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 15:42:39.796 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 15:42:39.803 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Name"
FROM "UserRoles" AS u
INNER JOIN "Roles" AS r ON u."RoleId" = r."Id"
WHERE u."UserId" = @__userId_0
2025-05-16 15:42:39.808 +05:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."Description", r."IsDeleted", r."Name", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "Roles" AS r
2025-05-16 15:42:39.812 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 15:42:39.815 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 15:42:39.819 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 15:42:39.821 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 15:42:39.822 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 15:42:39.824 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 15:42:39.825 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 15:42:39.827 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 15:42:39.828 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 15:42:39.830 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 15:42:39.837 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@p8='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?', @p2='?' (DbType = Boolean), @p3='?' (DbType = Guid), @p4='?' (DbType = Binary), @p5='?' (DbType = DateTime), @p6='?', @p7='?' (DbType = Guid), @p17='?' (DbType = Guid), @p9='?' (DbType = DateTime), @p10='?', @p11='?' (DbType = Boolean), @p12='?' (DbType = Guid), @p13='?' (DbType = Binary), @p14='?' (DbType = DateTime), @p15='?', @p16='?' (DbType = Guid), @p26='?' (DbType = Guid), @p18='?' (DbType = DateTime), @p19='?', @p20='?' (DbType = Boolean), @p21='?' (DbType = Guid), @p22='?' (DbType = Binary), @p23='?' (DbType = DateTime), @p24='?', @p25='?' (DbType = Guid), @p35='?' (DbType = Guid), @p27='?' (DbType = DateTime), @p28='?', @p29='?' (DbType = Boolean), @p30='?' (DbType = Guid), @p31='?' (DbType = Binary), @p32='?' (DbType = DateTime), @p33='?', @p34='?' (DbType = Guid), @p52='?' (DbType = Guid), @p36='?' (DbType = DateTime), @p37='?', @p38='?', @p39='?' (DbType = Boolean), @p40='?' (DbType = Boolean), @p41='?' (DbType = DateTime), @p42='?', @p43='?' (DbType = Boolean), @p44='?' (DbType = Guid), @p45='?', @p46='?' (DbType = DateTime), @p47='?' (DbType = Boolean), @p53='?' (DbType = Binary), @p48='?' (DbType = DateTime), @p49='?' (DbType = DateTime), @p50='?', @p51='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
UPDATE "UserRoles" SET "CreatedAt" = @p0, "CreatedBy" = @p1, "IsDeleted" = @p2, "RoleId" = @p3, "RowVersion" = @p4, "UpdatedAt" = @p5, "UpdatedBy" = @p6, "UserId" = @p7
WHERE "Id" = @p8;
UPDATE "UserRoles" SET "CreatedAt" = @p9, "CreatedBy" = @p10, "IsDeleted" = @p11, "RoleId" = @p12, "RowVersion" = @p13, "UpdatedAt" = @p14, "UpdatedBy" = @p15, "UserId" = @p16
WHERE "Id" = @p17;
UPDATE "UserRoles" SET "CreatedAt" = @p18, "CreatedBy" = @p19, "IsDeleted" = @p20, "RoleId" = @p21, "RowVersion" = @p22, "UpdatedAt" = @p23, "UpdatedBy" = @p24, "UserId" = @p25
WHERE "Id" = @p26;
UPDATE "UserRoles" SET "CreatedAt" = @p27, "CreatedBy" = @p28, "IsDeleted" = @p29, "RoleId" = @p30, "RowVersion" = @p31, "UpdatedAt" = @p32, "UpdatedBy" = @p33, "UserId" = @p34
WHERE "Id" = @p35;
UPDATE "Users" SET "CreatedAt" = @p36, "CreatedBy" = @p37, "Email" = @p38, "EmailVerified" = @p39, "IsDeleted" = @p40, "LastLoginAt" = @p41, "PhoneNumber" = @p42, "PhoneNumberVerified" = @p43, "PrimaryBranchId" = @p44, "RefreshToken" = @p45, "RefreshTokenExpiryTime" = @p46, "RememberMe" = @p47, "SecurityStamp" = @p48, "UpdatedAt" = @p49, "UpdatedBy" = @p50, "VendorId" = @p51
WHERE "Id" = @p52 AND "RowVersion" = @p53
RETURNING "RowVersion";
2025-05-16 15:42:39.845 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__get_Item_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."IsDeleted", u."RoleId", u."RowVersion", u."UpdatedAt", u."UpdatedBy", u."UserId"
FROM "UserRoles" AS u
WHERE u."Id" = @__get_Item_0
LIMIT 1
2025-05-16 15:42:43.073 +05:30 [INF] Executed DbCommand (6ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash", t0."Id", t0."CreatedAt", t0."CreatedBy", t0."IsDeleted", t0."RoleId", t0."RowVersion", t0."UpdatedAt", t0."UpdatedBy", t0."UserId", t0."Id0", t0."CreatedAt0", t0."CreatedBy0", t0."Description", t0."IsDeleted0", t0."Name", t0."RowVersion0", t0."UpdatedAt0", t0."UpdatedBy0"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."Email", u."EmailVerified", u."IsDeleted", u."LastLoginAt", u."PhoneNumber", u."PhoneNumberVerified", u."PrimaryBranchId", u."RefreshToken", u."RefreshTokenExpiryTime", u."RememberMe", u."RowVersion", u."SecurityStamp", u."UpdatedAt", u."UpdatedBy", u."VendorId", u."PasswordHash"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Id" = @__userId_0
    LIMIT 1
) AS t
LEFT JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."IsDeleted", u0."RoleId", u0."RowVersion", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", r."Id" AS "Id0", r."CreatedAt" AS "CreatedAt0", r."CreatedBy" AS "CreatedBy0", r."Description", r."IsDeleted" AS "IsDeleted0", r."Name", r."RowVersion" AS "RowVersion0", r."UpdatedAt" AS "UpdatedAt0", r."UpdatedBy" AS "UpdatedBy0"
    FROM "UserRoles" AS u0
    INNER JOIN "Roles" AS r ON u0."RoleId" = r."Id"
) AS t0 ON t."Id" = t0."UserId"
ORDER BY t."Id", t0."Id"
2025-05-16 15:42:43.105 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash", t0."Id", t0."CreatedAt", t0."CreatedBy", t0."IsDeleted", t0."RoleId", t0."RowVersion", t0."UpdatedAt", t0."UpdatedBy", t0."UserId", t0."Id0", t0."CreatedAt0", t0."CreatedBy0", t0."Description", t0."IsDeleted0", t0."Name", t0."RowVersion0", t0."UpdatedAt0", t0."UpdatedBy0"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."Email", u."EmailVerified", u."IsDeleted", u."LastLoginAt", u."PhoneNumber", u."PhoneNumberVerified", u."PrimaryBranchId", u."RefreshToken", u."RefreshTokenExpiryTime", u."RememberMe", u."RowVersion", u."SecurityStamp", u."UpdatedAt", u."UpdatedBy", u."VendorId", u."PasswordHash"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Id" = @__userId_0
    LIMIT 1
) AS t
LEFT JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."IsDeleted", u0."RoleId", u0."RowVersion", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", r."Id" AS "Id0", r."CreatedAt" AS "CreatedAt0", r."CreatedBy" AS "CreatedBy0", r."Description", r."IsDeleted" AS "IsDeleted0", r."Name", r."RowVersion" AS "RowVersion0", r."UpdatedAt" AS "UpdatedAt0", r."UpdatedBy" AS "UpdatedBy0"
    FROM "UserRoles" AS u0
    INNER JOIN "Roles" AS r ON u0."RoleId" = r."Id"
) AS t0 ON t."Id" = t0."UserId"
ORDER BY t."Id", t0."Id"
2025-05-16 15:42:43.118 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 15:42:43.119 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 15:42:43.123 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Name"
FROM "UserRoles" AS u
INNER JOIN "Roles" AS r ON u."RoleId" = r."Id"
WHERE u."UserId" = @__userId_0
2025-05-16 15:42:43.128 +05:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."Description", r."IsDeleted", r."Name", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "Roles" AS r
2025-05-16 15:42:43.133 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 15:42:43.135 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 15:42:43.136 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 15:42:43.139 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 15:42:43.141 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 15:42:43.144 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 15:42:43.148 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 15:42:43.152 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 15:42:43.155 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 15:42:43.156 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 15:42:43.157 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 15:42:43.159 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 15:42:43.162 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@p8='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?', @p2='?' (DbType = Boolean), @p3='?' (DbType = Guid), @p4='?' (DbType = Binary), @p5='?' (DbType = DateTime), @p6='?', @p7='?' (DbType = Guid), @p17='?' (DbType = Guid), @p9='?' (DbType = DateTime), @p10='?', @p11='?' (DbType = Boolean), @p12='?' (DbType = Guid), @p13='?' (DbType = Binary), @p14='?' (DbType = DateTime), @p15='?', @p16='?' (DbType = Guid), @p26='?' (DbType = Guid), @p18='?' (DbType = DateTime), @p19='?', @p20='?' (DbType = Boolean), @p21='?' (DbType = Guid), @p22='?' (DbType = Binary), @p23='?' (DbType = DateTime), @p24='?', @p25='?' (DbType = Guid), @p35='?' (DbType = Guid), @p27='?' (DbType = DateTime), @p28='?', @p29='?' (DbType = Boolean), @p30='?' (DbType = Guid), @p31='?' (DbType = Binary), @p32='?' (DbType = DateTime), @p33='?', @p34='?' (DbType = Guid), @p44='?' (DbType = Guid), @p36='?' (DbType = DateTime), @p37='?', @p38='?' (DbType = Boolean), @p39='?' (DbType = Guid), @p40='?' (DbType = Binary), @p41='?' (DbType = DateTime), @p42='?', @p43='?' (DbType = Guid), @p61='?' (DbType = Guid), @p45='?' (DbType = DateTime), @p46='?', @p47='?', @p48='?' (DbType = Boolean), @p49='?' (DbType = Boolean), @p50='?' (DbType = DateTime), @p51='?', @p52='?' (DbType = Boolean), @p53='?' (DbType = Guid), @p54='?', @p55='?' (DbType = DateTime), @p56='?' (DbType = Boolean), @p62='?' (DbType = Binary), @p57='?' (DbType = DateTime), @p58='?' (DbType = DateTime), @p59='?', @p60='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
UPDATE "UserRoles" SET "CreatedAt" = @p0, "CreatedBy" = @p1, "IsDeleted" = @p2, "RoleId" = @p3, "RowVersion" = @p4, "UpdatedAt" = @p5, "UpdatedBy" = @p6, "UserId" = @p7
WHERE "Id" = @p8;
UPDATE "UserRoles" SET "CreatedAt" = @p9, "CreatedBy" = @p10, "IsDeleted" = @p11, "RoleId" = @p12, "RowVersion" = @p13, "UpdatedAt" = @p14, "UpdatedBy" = @p15, "UserId" = @p16
WHERE "Id" = @p17;
UPDATE "UserRoles" SET "CreatedAt" = @p18, "CreatedBy" = @p19, "IsDeleted" = @p20, "RoleId" = @p21, "RowVersion" = @p22, "UpdatedAt" = @p23, "UpdatedBy" = @p24, "UserId" = @p25
WHERE "Id" = @p26;
UPDATE "UserRoles" SET "CreatedAt" = @p27, "CreatedBy" = @p28, "IsDeleted" = @p29, "RoleId" = @p30, "RowVersion" = @p31, "UpdatedAt" = @p32, "UpdatedBy" = @p33, "UserId" = @p34
WHERE "Id" = @p35;
UPDATE "UserRoles" SET "CreatedAt" = @p36, "CreatedBy" = @p37, "IsDeleted" = @p38, "RoleId" = @p39, "RowVersion" = @p40, "UpdatedAt" = @p41, "UpdatedBy" = @p42, "UserId" = @p43
WHERE "Id" = @p44;
UPDATE "Users" SET "CreatedAt" = @p45, "CreatedBy" = @p46, "Email" = @p47, "EmailVerified" = @p48, "IsDeleted" = @p49, "LastLoginAt" = @p50, "PhoneNumber" = @p51, "PhoneNumberVerified" = @p52, "PrimaryBranchId" = @p53, "RefreshToken" = @p54, "RefreshTokenExpiryTime" = @p55, "RememberMe" = @p56, "SecurityStamp" = @p57, "UpdatedAt" = @p58, "UpdatedBy" = @p59, "VendorId" = @p60
WHERE "Id" = @p61 AND "RowVersion" = @p62
RETURNING "RowVersion";
2025-05-16 15:42:43.175 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__get_Item_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."IsDeleted", u."RoleId", u."RowVersion", u."UpdatedAt", u."UpdatedBy", u."UserId"
FROM "UserRoles" AS u
WHERE u."Id" = @__get_Item_0
LIMIT 1
2025-05-16 15:42:43.191 +05:30 [WRN] Invalid operation during role assignment
System.InvalidOperationException: Failed to assign roles after multiple attempts due to concurrency conflicts. Please try again later.
 ---> System.InvalidOperationException: The record you attempted to edit was deleted by another user. Entity: UserRole
   at IdentityService.Infrastructure.Persistence.UnitOfWork.SaveChangesAsync(CancellationToken cancellationToken) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.Infrastructure\Persistence\UnitOfWork.cs:line 66
   at IdentityService.Application.Features.Users.Commands.AssignRoleCommandHandler.Handle(AssignRoleCommand command, CancellationToken cancellationToken) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.Application\Features\Users\Commands\AssignRoleCommand.cs:line 108
   --- End of inner exception stack trace ---
   at IdentityService.Application.Features.Users.Commands.AssignRoleCommandHandler.Handle(AssignRoleCommand command, CancellationToken cancellationToken) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.Application\Features\Users\Commands\AssignRoleCommand.cs:line 168
   at IdentityService.Application.Behaviors.ValidationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.Application\Behaviors\ValidationBehavior.cs:line 95
   at IdentityService.Application.Behaviors.UserIdPropagationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.Application\Behaviors\UserIdPropagationBehavior.cs:line 39
   at MediatR.Pipeline.RequestExceptionProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestExceptionProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestExceptionActionProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestExceptionActionProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestPostProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestPreProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at IdentityService.API.Controllers.UsersController.AssignRoles(Guid id, AssignRoleRequest request) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.API\Controllers\UsersController.cs:line 170
2025-05-16 15:42:43.242 +05:30 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-05-16 15:42:43.245 +05:30 [INF] Executed action IdentityService.API.Controllers.UsersController.AssignRoles (IdentityService.API) in 7184.6204ms
2025-05-16 15:42:43.247 +05:30 [INF] Executed endpoint 'IdentityService.API.Controllers.UsersController.AssignRoles (IdentityService.API)'
2025-05-16 15:42:43.249 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5263/api/Users/<USER>/roles - 400 null application/json; charset=utf-8 7437.3447ms
2025-05-16 15:45:37.076 +05:30 [DBG] Starting session cleanup
2025-05-16 15:45:37.092 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-16 15:45:37.105 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-16 15:45:37.114 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-16 15:45:37.117 +05:30 [DBG] Session cleanup completed
2025-05-16 15:50:37.124 +05:30 [DBG] Starting session cleanup
2025-05-16 15:50:37.340 +05:30 [INF] Executed DbCommand (14ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-16 15:50:37.345 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-16 15:50:37.350 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-16 15:50:37.352 +05:30 [DBG] Session cleanup completed
2025-05-16 15:55:37.366 +05:30 [DBG] Starting session cleanup
2025-05-16 15:55:37.380 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-16 15:55:37.386 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-16 15:55:37.391 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-16 15:55:37.393 +05:30 [DBG] Session cleanup completed
2025-05-16 16:00:37.399 +05:30 [DBG] Starting session cleanup
2025-05-16 16:00:37.589 +05:30 [INF] Executed DbCommand (6ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-16 16:00:37.594 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-16 16:00:37.600 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-16 16:00:37.601 +05:30 [DBG] Session cleanup completed
2025-05-16 16:05:37.610 +05:30 [DBG] Starting session cleanup
2025-05-16 16:05:37.623 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-16 16:05:37.629 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-16 16:05:37.634 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-16 16:05:37.637 +05:30 [DBG] Session cleanup completed
2025-05-16 16:07:28.636 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5263/api/Users/<USER>/roles - application/json 65
2025-05-16 16:07:28.695 +05:30 [INF] Token received and is being processed
2025-05-16 16:07:28.696 +05:30 [INF] Token validated successfully with claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 16:07:28.703 +05:30 [INF] NameIdentifier claim found: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e
2025-05-16 16:07:28.794 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__get_Item_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."Email", u."EmailVerified", u."IsDeleted", u."LastLoginAt", u."PhoneNumber", u."PhoneNumberVerified", u."PrimaryBranchId", u."RefreshToken", u."RefreshTokenExpiryTime", u."RememberMe", u."RowVersion", u."SecurityStamp", u."UpdatedAt", u."UpdatedBy", u."VendorId", u."PasswordHash"
FROM "Users" AS u
WHERE NOT (u."IsDeleted") AND u."Id" = @__get_Item_0
LIMIT 1
2025-05-16 16:07:28.805 +05:30 [INF] Executed DbCommand (6ms) [Parameters=[@__token_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Token" = @__token_0
LIMIT 1
2025-05-16 16:07:28.810 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 16:07:28.811 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:07:28.817 +05:30 [INF] Executed DbCommand (4ms) [Parameters=[@p17='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?', @p2='?', @p3='?', @p4='?' (DbType = DateTime), @p5='?' (DbType = DateTime), @p6='?', @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime), @p9='?', @p18='?' (DbType = Binary), @p10='?' (DbType = DateTime), @p11='?' (DbType = Int32), @p12='?', @p13='?' (DbType = DateTime), @p14='?', @p15='?', @p16='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
UPDATE "UserSessions" SET "CreatedAt" = @p0, "CreatedBy" = @p1, "DeviceInfo" = @p2, "EndReason" = @p3, "EndedAt" = @p4, "ExpiresAt" = @p5, "IpAddress" = @p6, "IsDeleted" = @p7, "LastActiveAt" = @p8, "RefreshToken" = @p9, "StartedAt" = @p10, "Status" = @p11, "Token" = @p12, "UpdatedAt" = @p13, "UpdatedBy" = @p14, "UserAgent" = @p15, "UserId" = @p16
WHERE "Id" = @p17 AND "RowVersion" IS NULL
RETURNING "RowVersion";
2025-05-16 16:07:28.823 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__token_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Token" = @__token_0
LIMIT 1
2025-05-16 16:07:28.829 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 16:07:28.833 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:07:28.837 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@p17='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?', @p2='?', @p3='?', @p4='?' (DbType = DateTime), @p5='?' (DbType = DateTime), @p6='?', @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime), @p9='?', @p18='?' (DbType = Binary), @p10='?' (DbType = DateTime), @p11='?' (DbType = Int32), @p12='?', @p13='?' (DbType = DateTime), @p14='?', @p15='?', @p16='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
UPDATE "UserSessions" SET "CreatedAt" = @p0, "CreatedBy" = @p1, "DeviceInfo" = @p2, "EndReason" = @p3, "EndedAt" = @p4, "ExpiresAt" = @p5, "IpAddress" = @p6, "IsDeleted" = @p7, "LastActiveAt" = @p8, "RefreshToken" = @p9, "StartedAt" = @p10, "Status" = @p11, "Token" = @p12, "UpdatedAt" = @p13, "UpdatedBy" = @p14, "UserAgent" = @p15, "UserId" = @p16
WHERE "Id" = @p17 AND "RowVersion" IS NULL
RETURNING "RowVersion";
2025-05-16 16:07:28.841 +05:30 [DBG] Updated last active time for session "ac54b93e-4d26-4a45-bb66-1be13c0b56e8"
2025-05-16 16:07:28.842 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 16:07:28.845 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:07:28.846 +05:30 [INF] User ID from token: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:07:28.848 +05:30 [INF] Executing endpoint 'IdentityService.API.Controllers.UsersController.AssignRoles (IdentityService.API)'
2025-05-16 16:07:28.851 +05:30 [INF] Route matched with {action = "AssignRoles", controller = "Users"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[IdentityService.Application.DTOs.Users.UserResponse]] AssignRoles(System.Guid, IdentityService.Application.DTOs.Users.AssignRoleRequest) on controller IdentityService.API.Controllers.UsersController (IdentityService.API).
2025-05-16 16:07:28.856 +05:30 [INF] Retrieved user ID from HttpContext.Items: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:07:28.857 +05:30 [INF] Setting UserId "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e" on request of type AssignRoleCommand
2025-05-16 16:07:28.859 +05:30 [INF] Retrieved user ID from HttpContext.Items: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:07:28.859 +05:30 [INF] Validating request of type AssignRoleCommand
2025-05-16 16:07:28.860 +05:30 [INF] Request content: {
  "UserId": "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e",
  "Request": {
    "RoleIds": [
      "0247e353-d1fb-4118-b630-fecff038de1e"
    ]
  }
}
2025-05-16 16:07:28.861 +05:30 [INF] Request UserId property value: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:07:28.862 +05:30 [INF] Validation passed for request of type AssignRoleCommand
2025-05-16 16:07:28.876 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash", t0."Id", t0."CreatedAt", t0."CreatedBy", t0."IsDeleted", t0."RoleId", t0."RowVersion", t0."UpdatedAt", t0."UpdatedBy", t0."UserId", t0."Id0", t0."CreatedAt0", t0."CreatedBy0", t0."Description", t0."IsDeleted0", t0."Name", t0."RowVersion0", t0."UpdatedAt0", t0."UpdatedBy0"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."Email", u."EmailVerified", u."IsDeleted", u."LastLoginAt", u."PhoneNumber", u."PhoneNumberVerified", u."PrimaryBranchId", u."RefreshToken", u."RefreshTokenExpiryTime", u."RememberMe", u."RowVersion", u."SecurityStamp", u."UpdatedAt", u."UpdatedBy", u."VendorId", u."PasswordHash"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Id" = @__userId_0
    LIMIT 1
) AS t
LEFT JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."IsDeleted", u0."RoleId", u0."RowVersion", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", r."Id" AS "Id0", r."CreatedAt" AS "CreatedAt0", r."CreatedBy" AS "CreatedBy0", r."Description", r."IsDeleted" AS "IsDeleted0", r."Name", r."RowVersion" AS "RowVersion0", r."UpdatedAt" AS "UpdatedAt0", r."UpdatedBy" AS "UpdatedBy0"
    FROM "UserRoles" AS u0
    INNER JOIN "Roles" AS r ON u0."RoleId" = r."Id"
) AS t0 ON t."Id" = t0."UserId"
ORDER BY t."Id", t0."Id"
2025-05-16 16:07:28.883 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash", t0."Id", t0."CreatedAt", t0."CreatedBy", t0."IsDeleted", t0."RoleId", t0."RowVersion", t0."UpdatedAt", t0."UpdatedBy", t0."UserId", t0."Id0", t0."CreatedAt0", t0."CreatedBy0", t0."Description", t0."IsDeleted0", t0."Name", t0."RowVersion0", t0."UpdatedAt0", t0."UpdatedBy0"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."Email", u."EmailVerified", u."IsDeleted", u."LastLoginAt", u."PhoneNumber", u."PhoneNumberVerified", u."PrimaryBranchId", u."RefreshToken", u."RefreshTokenExpiryTime", u."RememberMe", u."RowVersion", u."SecurityStamp", u."UpdatedAt", u."UpdatedBy", u."VendorId", u."PasswordHash"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Id" = @__userId_0
    LIMIT 1
) AS t
LEFT JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."IsDeleted", u0."RoleId", u0."RowVersion", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", r."Id" AS "Id0", r."CreatedAt" AS "CreatedAt0", r."CreatedBy" AS "CreatedBy0", r."Description", r."IsDeleted" AS "IsDeleted0", r."Name", r."RowVersion" AS "RowVersion0", r."UpdatedAt" AS "UpdatedAt0", r."UpdatedBy" AS "UpdatedBy0"
    FROM "UserRoles" AS u0
    INNER JOIN "Roles" AS r ON u0."RoleId" = r."Id"
) AS t0 ON t."Id" = t0."UserId"
ORDER BY t."Id", t0."Id"
2025-05-16 16:07:28.888 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 16:07:28.890 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:07:28.892 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Name"
FROM "UserRoles" AS u
INNER JOIN "Roles" AS r ON u."RoleId" = r."Id"
WHERE u."UserId" = @__userId_0
2025-05-16 16:07:28.894 +05:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."Description", r."IsDeleted", r."Name", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "Roles" AS r
2025-05-16 16:07:28.898 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 16:07:28.901 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:07:28.902 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 16:07:28.903 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:07:28.906 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@p8='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?', @p2='?' (DbType = Boolean), @p3='?' (DbType = Guid), @p4='?' (DbType = Binary), @p5='?' (DbType = DateTime), @p6='?', @p7='?' (DbType = Guid), @p25='?' (DbType = Guid), @p9='?' (DbType = DateTime), @p10='?', @p11='?', @p12='?' (DbType = Boolean), @p13='?' (DbType = Boolean), @p14='?' (DbType = DateTime), @p15='?', @p16='?' (DbType = Boolean), @p17='?' (DbType = Guid), @p18='?', @p19='?' (DbType = DateTime), @p20='?' (DbType = Boolean), @p26='?' (DbType = Binary), @p21='?' (DbType = DateTime), @p22='?' (DbType = DateTime), @p23='?', @p24='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
UPDATE "UserRoles" SET "CreatedAt" = @p0, "CreatedBy" = @p1, "IsDeleted" = @p2, "RoleId" = @p3, "RowVersion" = @p4, "UpdatedAt" = @p5, "UpdatedBy" = @p6, "UserId" = @p7
WHERE "Id" = @p8;
UPDATE "Users" SET "CreatedAt" = @p9, "CreatedBy" = @p10, "Email" = @p11, "EmailVerified" = @p12, "IsDeleted" = @p13, "LastLoginAt" = @p14, "PhoneNumber" = @p15, "PhoneNumberVerified" = @p16, "PrimaryBranchId" = @p17, "RefreshToken" = @p18, "RefreshTokenExpiryTime" = @p19, "RememberMe" = @p20, "SecurityStamp" = @p21, "UpdatedAt" = @p22, "UpdatedBy" = @p23, "VendorId" = @p24
WHERE "Id" = @p25 AND "RowVersion" = @p26
RETURNING "RowVersion";
2025-05-16 16:07:28.911 +05:30 [INF] Executed DbCommand (0ms) [Parameters=[@__get_Item_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."IsDeleted", u."RoleId", u."RowVersion", u."UpdatedAt", u."UpdatedBy", u."UserId"
FROM "UserRoles" AS u
WHERE u."Id" = @__get_Item_0
LIMIT 1
2025-05-16 16:07:29.336 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash", t0."Id", t0."CreatedAt", t0."CreatedBy", t0."IsDeleted", t0."RoleId", t0."RowVersion", t0."UpdatedAt", t0."UpdatedBy", t0."UserId", t0."Id0", t0."CreatedAt0", t0."CreatedBy0", t0."Description", t0."IsDeleted0", t0."Name", t0."RowVersion0", t0."UpdatedAt0", t0."UpdatedBy0"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."Email", u."EmailVerified", u."IsDeleted", u."LastLoginAt", u."PhoneNumber", u."PhoneNumberVerified", u."PrimaryBranchId", u."RefreshToken", u."RefreshTokenExpiryTime", u."RememberMe", u."RowVersion", u."SecurityStamp", u."UpdatedAt", u."UpdatedBy", u."VendorId", u."PasswordHash"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Id" = @__userId_0
    LIMIT 1
) AS t
LEFT JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."IsDeleted", u0."RoleId", u0."RowVersion", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", r."Id" AS "Id0", r."CreatedAt" AS "CreatedAt0", r."CreatedBy" AS "CreatedBy0", r."Description", r."IsDeleted" AS "IsDeleted0", r."Name", r."RowVersion" AS "RowVersion0", r."UpdatedAt" AS "UpdatedAt0", r."UpdatedBy" AS "UpdatedBy0"
    FROM "UserRoles" AS u0
    INNER JOIN "Roles" AS r ON u0."RoleId" = r."Id"
) AS t0 ON t."Id" = t0."UserId"
ORDER BY t."Id", t0."Id"
2025-05-16 16:07:29.350 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash", t0."Id", t0."CreatedAt", t0."CreatedBy", t0."IsDeleted", t0."RoleId", t0."RowVersion", t0."UpdatedAt", t0."UpdatedBy", t0."UserId", t0."Id0", t0."CreatedAt0", t0."CreatedBy0", t0."Description", t0."IsDeleted0", t0."Name", t0."RowVersion0", t0."UpdatedAt0", t0."UpdatedBy0"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."Email", u."EmailVerified", u."IsDeleted", u."LastLoginAt", u."PhoneNumber", u."PhoneNumberVerified", u."PrimaryBranchId", u."RefreshToken", u."RefreshTokenExpiryTime", u."RememberMe", u."RowVersion", u."SecurityStamp", u."UpdatedAt", u."UpdatedBy", u."VendorId", u."PasswordHash"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Id" = @__userId_0
    LIMIT 1
) AS t
LEFT JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."IsDeleted", u0."RoleId", u0."RowVersion", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", r."Id" AS "Id0", r."CreatedAt" AS "CreatedAt0", r."CreatedBy" AS "CreatedBy0", r."Description", r."IsDeleted" AS "IsDeleted0", r."Name", r."RowVersion" AS "RowVersion0", r."UpdatedAt" AS "UpdatedAt0", r."UpdatedBy" AS "UpdatedBy0"
    FROM "UserRoles" AS u0
    INNER JOIN "Roles" AS r ON u0."RoleId" = r."Id"
) AS t0 ON t."Id" = t0."UserId"
ORDER BY t."Id", t0."Id"
2025-05-16 16:07:29.358 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 16:07:29.360 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:07:29.363 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Name"
FROM "UserRoles" AS u
INNER JOIN "Roles" AS r ON u."RoleId" = r."Id"
WHERE u."UserId" = @__userId_0
2025-05-16 16:07:29.371 +05:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."Description", r."IsDeleted", r."Name", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "Roles" AS r
2025-05-16 16:07:29.396 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 16:07:29.405 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:07:29.407 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 16:07:29.410 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:07:29.412 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 16:07:29.416 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:07:29.422 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@p8='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?', @p2='?' (DbType = Boolean), @p3='?' (DbType = Guid), @p4='?' (DbType = Binary), @p5='?' (DbType = DateTime), @p6='?', @p7='?' (DbType = Guid), @p17='?' (DbType = Guid), @p9='?' (DbType = DateTime), @p10='?', @p11='?' (DbType = Boolean), @p12='?' (DbType = Guid), @p13='?' (DbType = Binary), @p14='?' (DbType = DateTime), @p15='?', @p16='?' (DbType = Guid), @p34='?' (DbType = Guid), @p18='?' (DbType = DateTime), @p19='?', @p20='?', @p21='?' (DbType = Boolean), @p22='?' (DbType = Boolean), @p23='?' (DbType = DateTime), @p24='?', @p25='?' (DbType = Boolean), @p26='?' (DbType = Guid), @p27='?', @p28='?' (DbType = DateTime), @p29='?' (DbType = Boolean), @p35='?' (DbType = Binary), @p30='?' (DbType = DateTime), @p31='?' (DbType = DateTime), @p32='?', @p33='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
UPDATE "UserRoles" SET "CreatedAt" = @p0, "CreatedBy" = @p1, "IsDeleted" = @p2, "RoleId" = @p3, "RowVersion" = @p4, "UpdatedAt" = @p5, "UpdatedBy" = @p6, "UserId" = @p7
WHERE "Id" = @p8;
UPDATE "UserRoles" SET "CreatedAt" = @p9, "CreatedBy" = @p10, "IsDeleted" = @p11, "RoleId" = @p12, "RowVersion" = @p13, "UpdatedAt" = @p14, "UpdatedBy" = @p15, "UserId" = @p16
WHERE "Id" = @p17;
UPDATE "Users" SET "CreatedAt" = @p18, "CreatedBy" = @p19, "Email" = @p20, "EmailVerified" = @p21, "IsDeleted" = @p22, "LastLoginAt" = @p23, "PhoneNumber" = @p24, "PhoneNumberVerified" = @p25, "PrimaryBranchId" = @p26, "RefreshToken" = @p27, "RefreshTokenExpiryTime" = @p28, "RememberMe" = @p29, "SecurityStamp" = @p30, "UpdatedAt" = @p31, "UpdatedBy" = @p32, "VendorId" = @p33
WHERE "Id" = @p34 AND "RowVersion" = @p35
RETURNING "RowVersion";
2025-05-16 16:07:29.431 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__get_Item_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."IsDeleted", u."RoleId", u."RowVersion", u."UpdatedAt", u."UpdatedBy", u."UserId"
FROM "UserRoles" AS u
WHERE u."Id" = @__get_Item_0
LIMIT 1
2025-05-16 16:07:30.252 +05:30 [INF] Executed DbCommand (9ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash", t0."Id", t0."CreatedAt", t0."CreatedBy", t0."IsDeleted", t0."RoleId", t0."RowVersion", t0."UpdatedAt", t0."UpdatedBy", t0."UserId", t0."Id0", t0."CreatedAt0", t0."CreatedBy0", t0."Description", t0."IsDeleted0", t0."Name", t0."RowVersion0", t0."UpdatedAt0", t0."UpdatedBy0"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."Email", u."EmailVerified", u."IsDeleted", u."LastLoginAt", u."PhoneNumber", u."PhoneNumberVerified", u."PrimaryBranchId", u."RefreshToken", u."RefreshTokenExpiryTime", u."RememberMe", u."RowVersion", u."SecurityStamp", u."UpdatedAt", u."UpdatedBy", u."VendorId", u."PasswordHash"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Id" = @__userId_0
    LIMIT 1
) AS t
LEFT JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."IsDeleted", u0."RoleId", u0."RowVersion", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", r."Id" AS "Id0", r."CreatedAt" AS "CreatedAt0", r."CreatedBy" AS "CreatedBy0", r."Description", r."IsDeleted" AS "IsDeleted0", r."Name", r."RowVersion" AS "RowVersion0", r."UpdatedAt" AS "UpdatedAt0", r."UpdatedBy" AS "UpdatedBy0"
    FROM "UserRoles" AS u0
    INNER JOIN "Roles" AS r ON u0."RoleId" = r."Id"
) AS t0 ON t."Id" = t0."UserId"
ORDER BY t."Id", t0."Id"
2025-05-16 16:07:30.305 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash", t0."Id", t0."CreatedAt", t0."CreatedBy", t0."IsDeleted", t0."RoleId", t0."RowVersion", t0."UpdatedAt", t0."UpdatedBy", t0."UserId", t0."Id0", t0."CreatedAt0", t0."CreatedBy0", t0."Description", t0."IsDeleted0", t0."Name", t0."RowVersion0", t0."UpdatedAt0", t0."UpdatedBy0"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."Email", u."EmailVerified", u."IsDeleted", u."LastLoginAt", u."PhoneNumber", u."PhoneNumberVerified", u."PrimaryBranchId", u."RefreshToken", u."RefreshTokenExpiryTime", u."RememberMe", u."RowVersion", u."SecurityStamp", u."UpdatedAt", u."UpdatedBy", u."VendorId", u."PasswordHash"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Id" = @__userId_0
    LIMIT 1
) AS t
LEFT JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."IsDeleted", u0."RoleId", u0."RowVersion", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", r."Id" AS "Id0", r."CreatedAt" AS "CreatedAt0", r."CreatedBy" AS "CreatedBy0", r."Description", r."IsDeleted" AS "IsDeleted0", r."Name", r."RowVersion" AS "RowVersion0", r."UpdatedAt" AS "UpdatedAt0", r."UpdatedBy" AS "UpdatedBy0"
    FROM "UserRoles" AS u0
    INNER JOIN "Roles" AS r ON u0."RoleId" = r."Id"
) AS t0 ON t."Id" = t0."UserId"
ORDER BY t."Id", t0."Id"
2025-05-16 16:07:30.315 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 16:07:30.319 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:07:30.322 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Name"
FROM "UserRoles" AS u
INNER JOIN "Roles" AS r ON u."RoleId" = r."Id"
WHERE u."UserId" = @__userId_0
2025-05-16 16:07:30.325 +05:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."Description", r."IsDeleted", r."Name", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "Roles" AS r
2025-05-16 16:07:30.327 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 16:07:30.329 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:07:30.331 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 16:07:30.334 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:07:30.337 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 16:07:30.338 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:07:30.339 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 16:07:30.340 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:07:30.343 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@p8='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?', @p2='?' (DbType = Boolean), @p3='?' (DbType = Guid), @p4='?' (DbType = Binary), @p5='?' (DbType = DateTime), @p6='?', @p7='?' (DbType = Guid), @p17='?' (DbType = Guid), @p9='?' (DbType = DateTime), @p10='?', @p11='?' (DbType = Boolean), @p12='?' (DbType = Guid), @p13='?' (DbType = Binary), @p14='?' (DbType = DateTime), @p15='?', @p16='?' (DbType = Guid), @p26='?' (DbType = Guid), @p18='?' (DbType = DateTime), @p19='?', @p20='?' (DbType = Boolean), @p21='?' (DbType = Guid), @p22='?' (DbType = Binary), @p23='?' (DbType = DateTime), @p24='?', @p25='?' (DbType = Guid), @p43='?' (DbType = Guid), @p27='?' (DbType = DateTime), @p28='?', @p29='?', @p30='?' (DbType = Boolean), @p31='?' (DbType = Boolean), @p32='?' (DbType = DateTime), @p33='?', @p34='?' (DbType = Boolean), @p35='?' (DbType = Guid), @p36='?', @p37='?' (DbType = DateTime), @p38='?' (DbType = Boolean), @p44='?' (DbType = Binary), @p39='?' (DbType = DateTime), @p40='?' (DbType = DateTime), @p41='?', @p42='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
UPDATE "UserRoles" SET "CreatedAt" = @p0, "CreatedBy" = @p1, "IsDeleted" = @p2, "RoleId" = @p3, "RowVersion" = @p4, "UpdatedAt" = @p5, "UpdatedBy" = @p6, "UserId" = @p7
WHERE "Id" = @p8;
UPDATE "UserRoles" SET "CreatedAt" = @p9, "CreatedBy" = @p10, "IsDeleted" = @p11, "RoleId" = @p12, "RowVersion" = @p13, "UpdatedAt" = @p14, "UpdatedBy" = @p15, "UserId" = @p16
WHERE "Id" = @p17;
UPDATE "UserRoles" SET "CreatedAt" = @p18, "CreatedBy" = @p19, "IsDeleted" = @p20, "RoleId" = @p21, "RowVersion" = @p22, "UpdatedAt" = @p23, "UpdatedBy" = @p24, "UserId" = @p25
WHERE "Id" = @p26;
UPDATE "Users" SET "CreatedAt" = @p27, "CreatedBy" = @p28, "Email" = @p29, "EmailVerified" = @p30, "IsDeleted" = @p31, "LastLoginAt" = @p32, "PhoneNumber" = @p33, "PhoneNumberVerified" = @p34, "PrimaryBranchId" = @p35, "RefreshToken" = @p36, "RefreshTokenExpiryTime" = @p37, "RememberMe" = @p38, "SecurityStamp" = @p39, "UpdatedAt" = @p40, "UpdatedBy" = @p41, "VendorId" = @p42
WHERE "Id" = @p43 AND "RowVersion" = @p44
RETURNING "RowVersion";
2025-05-16 16:07:30.353 +05:30 [INF] Executed DbCommand (0ms) [Parameters=[@__get_Item_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."IsDeleted", u."RoleId", u."RowVersion", u."UpdatedAt", u."UpdatedBy", u."UserId"
FROM "UserRoles" AS u
WHERE u."Id" = @__get_Item_0
LIMIT 1
2025-05-16 16:07:31.975 +05:30 [INF] Executed DbCommand (7ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash", t0."Id", t0."CreatedAt", t0."CreatedBy", t0."IsDeleted", t0."RoleId", t0."RowVersion", t0."UpdatedAt", t0."UpdatedBy", t0."UserId", t0."Id0", t0."CreatedAt0", t0."CreatedBy0", t0."Description", t0."IsDeleted0", t0."Name", t0."RowVersion0", t0."UpdatedAt0", t0."UpdatedBy0"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."Email", u."EmailVerified", u."IsDeleted", u."LastLoginAt", u."PhoneNumber", u."PhoneNumberVerified", u."PrimaryBranchId", u."RefreshToken", u."RefreshTokenExpiryTime", u."RememberMe", u."RowVersion", u."SecurityStamp", u."UpdatedAt", u."UpdatedBy", u."VendorId", u."PasswordHash"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Id" = @__userId_0
    LIMIT 1
) AS t
LEFT JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."IsDeleted", u0."RoleId", u0."RowVersion", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", r."Id" AS "Id0", r."CreatedAt" AS "CreatedAt0", r."CreatedBy" AS "CreatedBy0", r."Description", r."IsDeleted" AS "IsDeleted0", r."Name", r."RowVersion" AS "RowVersion0", r."UpdatedAt" AS "UpdatedAt0", r."UpdatedBy" AS "UpdatedBy0"
    FROM "UserRoles" AS u0
    INNER JOIN "Roles" AS r ON u0."RoleId" = r."Id"
) AS t0 ON t."Id" = t0."UserId"
ORDER BY t."Id", t0."Id"
2025-05-16 16:07:32.017 +05:30 [INF] Executed DbCommand (5ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash", t0."Id", t0."CreatedAt", t0."CreatedBy", t0."IsDeleted", t0."RoleId", t0."RowVersion", t0."UpdatedAt", t0."UpdatedBy", t0."UserId", t0."Id0", t0."CreatedAt0", t0."CreatedBy0", t0."Description", t0."IsDeleted0", t0."Name", t0."RowVersion0", t0."UpdatedAt0", t0."UpdatedBy0"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."Email", u."EmailVerified", u."IsDeleted", u."LastLoginAt", u."PhoneNumber", u."PhoneNumberVerified", u."PrimaryBranchId", u."RefreshToken", u."RefreshTokenExpiryTime", u."RememberMe", u."RowVersion", u."SecurityStamp", u."UpdatedAt", u."UpdatedBy", u."VendorId", u."PasswordHash"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Id" = @__userId_0
    LIMIT 1
) AS t
LEFT JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."IsDeleted", u0."RoleId", u0."RowVersion", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", r."Id" AS "Id0", r."CreatedAt" AS "CreatedAt0", r."CreatedBy" AS "CreatedBy0", r."Description", r."IsDeleted" AS "IsDeleted0", r."Name", r."RowVersion" AS "RowVersion0", r."UpdatedAt" AS "UpdatedAt0", r."UpdatedBy" AS "UpdatedBy0"
    FROM "UserRoles" AS u0
    INNER JOIN "Roles" AS r ON u0."RoleId" = r."Id"
) AS t0 ON t."Id" = t0."UserId"
ORDER BY t."Id", t0."Id"
2025-05-16 16:07:32.025 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 16:07:32.027 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:07:32.029 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Name"
FROM "UserRoles" AS u
INNER JOIN "Roles" AS r ON u."RoleId" = r."Id"
WHERE u."UserId" = @__userId_0
2025-05-16 16:07:32.036 +05:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."Description", r."IsDeleted", r."Name", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "Roles" AS r
2025-05-16 16:07:32.039 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 16:07:32.041 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:07:32.043 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 16:07:32.045 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:07:32.046 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 16:07:32.051 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:07:32.053 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 16:07:32.055 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:07:32.056 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 16:07:32.058 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:07:32.061 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@p8='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?', @p2='?' (DbType = Boolean), @p3='?' (DbType = Guid), @p4='?' (DbType = Binary), @p5='?' (DbType = DateTime), @p6='?', @p7='?' (DbType = Guid), @p17='?' (DbType = Guid), @p9='?' (DbType = DateTime), @p10='?', @p11='?' (DbType = Boolean), @p12='?' (DbType = Guid), @p13='?' (DbType = Binary), @p14='?' (DbType = DateTime), @p15='?', @p16='?' (DbType = Guid), @p26='?' (DbType = Guid), @p18='?' (DbType = DateTime), @p19='?', @p20='?' (DbType = Boolean), @p21='?' (DbType = Guid), @p22='?' (DbType = Binary), @p23='?' (DbType = DateTime), @p24='?', @p25='?' (DbType = Guid), @p35='?' (DbType = Guid), @p27='?' (DbType = DateTime), @p28='?', @p29='?' (DbType = Boolean), @p30='?' (DbType = Guid), @p31='?' (DbType = Binary), @p32='?' (DbType = DateTime), @p33='?', @p34='?' (DbType = Guid), @p52='?' (DbType = Guid), @p36='?' (DbType = DateTime), @p37='?', @p38='?', @p39='?' (DbType = Boolean), @p40='?' (DbType = Boolean), @p41='?' (DbType = DateTime), @p42='?', @p43='?' (DbType = Boolean), @p44='?' (DbType = Guid), @p45='?', @p46='?' (DbType = DateTime), @p47='?' (DbType = Boolean), @p53='?' (DbType = Binary), @p48='?' (DbType = DateTime), @p49='?' (DbType = DateTime), @p50='?', @p51='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
UPDATE "UserRoles" SET "CreatedAt" = @p0, "CreatedBy" = @p1, "IsDeleted" = @p2, "RoleId" = @p3, "RowVersion" = @p4, "UpdatedAt" = @p5, "UpdatedBy" = @p6, "UserId" = @p7
WHERE "Id" = @p8;
UPDATE "UserRoles" SET "CreatedAt" = @p9, "CreatedBy" = @p10, "IsDeleted" = @p11, "RoleId" = @p12, "RowVersion" = @p13, "UpdatedAt" = @p14, "UpdatedBy" = @p15, "UserId" = @p16
WHERE "Id" = @p17;
UPDATE "UserRoles" SET "CreatedAt" = @p18, "CreatedBy" = @p19, "IsDeleted" = @p20, "RoleId" = @p21, "RowVersion" = @p22, "UpdatedAt" = @p23, "UpdatedBy" = @p24, "UserId" = @p25
WHERE "Id" = @p26;
UPDATE "UserRoles" SET "CreatedAt" = @p27, "CreatedBy" = @p28, "IsDeleted" = @p29, "RoleId" = @p30, "RowVersion" = @p31, "UpdatedAt" = @p32, "UpdatedBy" = @p33, "UserId" = @p34
WHERE "Id" = @p35;
UPDATE "Users" SET "CreatedAt" = @p36, "CreatedBy" = @p37, "Email" = @p38, "EmailVerified" = @p39, "IsDeleted" = @p40, "LastLoginAt" = @p41, "PhoneNumber" = @p42, "PhoneNumberVerified" = @p43, "PrimaryBranchId" = @p44, "RefreshToken" = @p45, "RefreshTokenExpiryTime" = @p46, "RememberMe" = @p47, "SecurityStamp" = @p48, "UpdatedAt" = @p49, "UpdatedBy" = @p50, "VendorId" = @p51
WHERE "Id" = @p52 AND "RowVersion" = @p53
RETURNING "RowVersion";
2025-05-16 16:07:32.073 +05:30 [INF] Executed DbCommand (0ms) [Parameters=[@__get_Item_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."IsDeleted", u."RoleId", u."RowVersion", u."UpdatedAt", u."UpdatedBy", u."UserId"
FROM "UserRoles" AS u
WHERE u."Id" = @__get_Item_0
LIMIT 1
2025-05-16 16:07:35.288 +05:30 [INF] Executed DbCommand (5ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash", t0."Id", t0."CreatedAt", t0."CreatedBy", t0."IsDeleted", t0."RoleId", t0."RowVersion", t0."UpdatedAt", t0."UpdatedBy", t0."UserId", t0."Id0", t0."CreatedAt0", t0."CreatedBy0", t0."Description", t0."IsDeleted0", t0."Name", t0."RowVersion0", t0."UpdatedAt0", t0."UpdatedBy0"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."Email", u."EmailVerified", u."IsDeleted", u."LastLoginAt", u."PhoneNumber", u."PhoneNumberVerified", u."PrimaryBranchId", u."RefreshToken", u."RefreshTokenExpiryTime", u."RememberMe", u."RowVersion", u."SecurityStamp", u."UpdatedAt", u."UpdatedBy", u."VendorId", u."PasswordHash"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Id" = @__userId_0
    LIMIT 1
) AS t
LEFT JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."IsDeleted", u0."RoleId", u0."RowVersion", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", r."Id" AS "Id0", r."CreatedAt" AS "CreatedAt0", r."CreatedBy" AS "CreatedBy0", r."Description", r."IsDeleted" AS "IsDeleted0", r."Name", r."RowVersion" AS "RowVersion0", r."UpdatedAt" AS "UpdatedAt0", r."UpdatedBy" AS "UpdatedBy0"
    FROM "UserRoles" AS u0
    INNER JOIN "Roles" AS r ON u0."RoleId" = r."Id"
) AS t0 ON t."Id" = t0."UserId"
ORDER BY t."Id", t0."Id"
2025-05-16 16:07:35.340 +05:30 [INF] Executed DbCommand (6ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash", t0."Id", t0."CreatedAt", t0."CreatedBy", t0."IsDeleted", t0."RoleId", t0."RowVersion", t0."UpdatedAt", t0."UpdatedBy", t0."UserId", t0."Id0", t0."CreatedAt0", t0."CreatedBy0", t0."Description", t0."IsDeleted0", t0."Name", t0."RowVersion0", t0."UpdatedAt0", t0."UpdatedBy0"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."Email", u."EmailVerified", u."IsDeleted", u."LastLoginAt", u."PhoneNumber", u."PhoneNumberVerified", u."PrimaryBranchId", u."RefreshToken", u."RefreshTokenExpiryTime", u."RememberMe", u."RowVersion", u."SecurityStamp", u."UpdatedAt", u."UpdatedBy", u."VendorId", u."PasswordHash"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Id" = @__userId_0
    LIMIT 1
) AS t
LEFT JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."IsDeleted", u0."RoleId", u0."RowVersion", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", r."Id" AS "Id0", r."CreatedAt" AS "CreatedAt0", r."CreatedBy" AS "CreatedBy0", r."Description", r."IsDeleted" AS "IsDeleted0", r."Name", r."RowVersion" AS "RowVersion0", r."UpdatedAt" AS "UpdatedAt0", r."UpdatedBy" AS "UpdatedBy0"
    FROM "UserRoles" AS u0
    INNER JOIN "Roles" AS r ON u0."RoleId" = r."Id"
) AS t0 ON t."Id" = t0."UserId"
ORDER BY t."Id", t0."Id"
2025-05-16 16:07:35.354 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 16:07:35.357 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:07:35.360 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Name"
FROM "UserRoles" AS u
INNER JOIN "Roles" AS r ON u."RoleId" = r."Id"
WHERE u."UserId" = @__userId_0
2025-05-16 16:07:35.364 +05:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."Description", r."IsDeleted", r."Name", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "Roles" AS r
2025-05-16 16:07:35.368 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 16:07:35.370 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:07:35.371 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 16:07:35.373 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:07:35.374 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 16:07:35.376 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:07:35.377 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 16:07:35.378 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:07:35.381 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 16:07:35.382 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:07:35.384 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 16:07:35.386 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:07:35.389 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@p8='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?', @p2='?' (DbType = Boolean), @p3='?' (DbType = Guid), @p4='?' (DbType = Binary), @p5='?' (DbType = DateTime), @p6='?', @p7='?' (DbType = Guid), @p17='?' (DbType = Guid), @p9='?' (DbType = DateTime), @p10='?', @p11='?' (DbType = Boolean), @p12='?' (DbType = Guid), @p13='?' (DbType = Binary), @p14='?' (DbType = DateTime), @p15='?', @p16='?' (DbType = Guid), @p26='?' (DbType = Guid), @p18='?' (DbType = DateTime), @p19='?', @p20='?' (DbType = Boolean), @p21='?' (DbType = Guid), @p22='?' (DbType = Binary), @p23='?' (DbType = DateTime), @p24='?', @p25='?' (DbType = Guid), @p35='?' (DbType = Guid), @p27='?' (DbType = DateTime), @p28='?', @p29='?' (DbType = Boolean), @p30='?' (DbType = Guid), @p31='?' (DbType = Binary), @p32='?' (DbType = DateTime), @p33='?', @p34='?' (DbType = Guid), @p44='?' (DbType = Guid), @p36='?' (DbType = DateTime), @p37='?', @p38='?' (DbType = Boolean), @p39='?' (DbType = Guid), @p40='?' (DbType = Binary), @p41='?' (DbType = DateTime), @p42='?', @p43='?' (DbType = Guid), @p61='?' (DbType = Guid), @p45='?' (DbType = DateTime), @p46='?', @p47='?', @p48='?' (DbType = Boolean), @p49='?' (DbType = Boolean), @p50='?' (DbType = DateTime), @p51='?', @p52='?' (DbType = Boolean), @p53='?' (DbType = Guid), @p54='?', @p55='?' (DbType = DateTime), @p56='?' (DbType = Boolean), @p62='?' (DbType = Binary), @p57='?' (DbType = DateTime), @p58='?' (DbType = DateTime), @p59='?', @p60='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
UPDATE "UserRoles" SET "CreatedAt" = @p0, "CreatedBy" = @p1, "IsDeleted" = @p2, "RoleId" = @p3, "RowVersion" = @p4, "UpdatedAt" = @p5, "UpdatedBy" = @p6, "UserId" = @p7
WHERE "Id" = @p8;
UPDATE "UserRoles" SET "CreatedAt" = @p9, "CreatedBy" = @p10, "IsDeleted" = @p11, "RoleId" = @p12, "RowVersion" = @p13, "UpdatedAt" = @p14, "UpdatedBy" = @p15, "UserId" = @p16
WHERE "Id" = @p17;
UPDATE "UserRoles" SET "CreatedAt" = @p18, "CreatedBy" = @p19, "IsDeleted" = @p20, "RoleId" = @p21, "RowVersion" = @p22, "UpdatedAt" = @p23, "UpdatedBy" = @p24, "UserId" = @p25
WHERE "Id" = @p26;
UPDATE "UserRoles" SET "CreatedAt" = @p27, "CreatedBy" = @p28, "IsDeleted" = @p29, "RoleId" = @p30, "RowVersion" = @p31, "UpdatedAt" = @p32, "UpdatedBy" = @p33, "UserId" = @p34
WHERE "Id" = @p35;
UPDATE "UserRoles" SET "CreatedAt" = @p36, "CreatedBy" = @p37, "IsDeleted" = @p38, "RoleId" = @p39, "RowVersion" = @p40, "UpdatedAt" = @p41, "UpdatedBy" = @p42, "UserId" = @p43
WHERE "Id" = @p44;
UPDATE "Users" SET "CreatedAt" = @p45, "CreatedBy" = @p46, "Email" = @p47, "EmailVerified" = @p48, "IsDeleted" = @p49, "LastLoginAt" = @p50, "PhoneNumber" = @p51, "PhoneNumberVerified" = @p52, "PrimaryBranchId" = @p53, "RefreshToken" = @p54, "RefreshTokenExpiryTime" = @p55, "RememberMe" = @p56, "SecurityStamp" = @p57, "UpdatedAt" = @p58, "UpdatedBy" = @p59, "VendorId" = @p60
WHERE "Id" = @p61 AND "RowVersion" = @p62
RETURNING "RowVersion";
2025-05-16 16:07:35.398 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__get_Item_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."IsDeleted", u."RoleId", u."RowVersion", u."UpdatedAt", u."UpdatedBy", u."UserId"
FROM "UserRoles" AS u
WHERE u."Id" = @__get_Item_0
LIMIT 1
2025-05-16 16:07:35.404 +05:30 [WRN] Invalid operation during role assignment
System.InvalidOperationException: Failed to assign roles after multiple attempts due to concurrency conflicts. Please try again later.
 ---> System.InvalidOperationException: The record you attempted to edit was deleted by another user. Entity: UserRole
   at IdentityService.Infrastructure.Persistence.UnitOfWork.SaveChangesAsync(CancellationToken cancellationToken) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.Infrastructure\Persistence\UnitOfWork.cs:line 66
   at IdentityService.Application.Features.Users.Commands.AssignRoleCommandHandler.Handle(AssignRoleCommand command, CancellationToken cancellationToken) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.Application\Features\Users\Commands\AssignRoleCommand.cs:line 108
   --- End of inner exception stack trace ---
   at IdentityService.Application.Features.Users.Commands.AssignRoleCommandHandler.Handle(AssignRoleCommand command, CancellationToken cancellationToken) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.Application\Features\Users\Commands\AssignRoleCommand.cs:line 168
   at IdentityService.Application.Behaviors.ValidationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.Application\Behaviors\ValidationBehavior.cs:line 95
   at IdentityService.Application.Behaviors.UserIdPropagationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.Application\Behaviors\UserIdPropagationBehavior.cs:line 39
   at MediatR.Pipeline.RequestExceptionProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestExceptionProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestExceptionActionProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestExceptionActionProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestPostProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestPreProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at IdentityService.API.Controllers.UsersController.AssignRoles(Guid id, AssignRoleRequest request) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.API\Controllers\UsersController.cs:line 170
2025-05-16 16:07:35.411 +05:30 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-05-16 16:07:35.415 +05:30 [INF] Executed action IdentityService.API.Controllers.UsersController.AssignRoles (IdentityService.API) in 6560.1729ms
2025-05-16 16:07:35.418 +05:30 [INF] Executed endpoint 'IdentityService.API.Controllers.UsersController.AssignRoles (IdentityService.API)'
2025-05-16 16:07:35.419 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5263/api/Users/<USER>/roles - 400 null application/json; charset=utf-8 6782.7432ms
2025-05-16 16:09:04.571 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/api/users - null null
2025-05-16 16:09:04.574 +05:30 [INF] Token received and is being processed
2025-05-16 16:09:04.585 +05:30 [INF] Authorization failed. These requirements were not met:
ClaimsAuthorizationRequirement:Claim.Type=permission and Claim.Value is one of the following values: (Users.View)
2025-05-16 16:09:04.590 +05:30 [WRN] OnChallenge: null
2025-05-16 16:09:04.591 +05:30 [INF] AuthenticationScheme: Bearer was challenged.
2025-05-16 16:09:04.597 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/api/users - 401 0 null 26.083ms
2025-05-16 16:09:16.800 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5263/api/auth/login - application/json 70
2025-05-16 16:09:16.805 +05:30 [INF] Token received and is being processed
2025-05-16 16:09:16.808 +05:30 [WRN] User is null or not authenticated
2025-05-16 16:09:16.810 +05:30 [WRN] No user ID found in token
2025-05-16 16:09:16.813 +05:30 [INF] Executing endpoint 'IdentityService.API.Controllers.AuthController.Login (IdentityService.API)'
2025-05-16 16:09:16.816 +05:30 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[IdentityService.Application.DTOs.Auth.LoginResponse]] Login(IdentityService.Application.DTOs.Auth.LoginRequest) on controller IdentityService.API.Controllers.AuthController (IdentityService.API).
2025-05-16 16:09:16.820 +05:30 [WRN] User ID not found in HttpContext.Items
2025-05-16 16:09:16.821 +05:30 [INF] Setting UserId "00000000-0000-0000-0000-000000000000" on request of type LoginCommand
2025-05-16 16:09:16.822 +05:30 [WRN] User ID not found in HttpContext.Items
2025-05-16 16:09:16.823 +05:30 [INF] Validating request of type LoginCommand
2025-05-16 16:09:16.825 +05:30 [INF] Request content: {
  "Request": {
    "Email": "<EMAIL>",
    "Password": "Admin123!",
    "PhoneNumber": null,
    "Otp": null,
    "RememberMe": false,
    "IsEmailLogin": true,
    "IsPhoneLogin": false
  },
  "UserId": "00000000-0000-0000-0000-000000000000"
}
2025-05-16 16:09:16.830 +05:30 [INF] Request UserId property value: "00000000-0000-0000-0000-000000000000"
2025-05-16 16:09:16.830 +05:30 [WRN] UserId is empty GUID (00000000-0000-0000-0000-000000000000)
2025-05-16 16:09:16.831 +05:30 [INF] Validation passed for request of type LoginCommand
2025-05-16 16:09:16.837 +05:30 [INF] Executed DbCommand (4ms) [Parameters=[@__email_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash", t0."Id", t0."CreatedAt", t0."CreatedBy", t0."IsDeleted", t0."RoleId", t0."RowVersion", t0."UpdatedAt", t0."UpdatedBy", t0."UserId", t0."Id0", t0."CreatedAt0", t0."CreatedBy0", t0."Description", t0."IsDeleted0", t0."Name", t0."RowVersion0", t0."UpdatedAt0", t0."UpdatedBy0", t0."Id1", t0."CreatedAt1", t0."CreatedBy1", t0."IsDeleted1", t0."PermissionId", t0."RoleId0", t0."RowVersion1", t0."UpdatedAt1", t0."UpdatedBy1", t0."Id00", t0."Action", t0."CreatedAt00", t0."CreatedBy00", t0."Description0", t0."IsDeleted00", t0."Name0", t0."Resource", t0."RowVersion00", t0."UpdatedAt00", t0."UpdatedBy00"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."Email", u."EmailVerified", u."IsDeleted", u."LastLoginAt", u."PhoneNumber", u."PhoneNumberVerified", u."PrimaryBranchId", u."RefreshToken", u."RefreshTokenExpiryTime", u."RememberMe", u."RowVersion", u."SecurityStamp", u."UpdatedAt", u."UpdatedBy", u."VendorId", u."PasswordHash"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__email_0
    LIMIT 1
) AS t
LEFT JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."IsDeleted", u0."RoleId", u0."RowVersion", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", r."Id" AS "Id0", r."CreatedAt" AS "CreatedAt0", r."CreatedBy" AS "CreatedBy0", r."Description", r."IsDeleted" AS "IsDeleted0", r."Name", r."RowVersion" AS "RowVersion0", r."UpdatedAt" AS "UpdatedAt0", r."UpdatedBy" AS "UpdatedBy0", t1."Id" AS "Id1", t1."CreatedAt" AS "CreatedAt1", t1."CreatedBy" AS "CreatedBy1", t1."IsDeleted" AS "IsDeleted1", t1."PermissionId", t1."RoleId" AS "RoleId0", t1."RowVersion" AS "RowVersion1", t1."UpdatedAt" AS "UpdatedAt1", t1."UpdatedBy" AS "UpdatedBy1", t1."Id0" AS "Id00", t1."Action", t1."CreatedAt0" AS "CreatedAt00", t1."CreatedBy0" AS "CreatedBy00", t1."Description" AS "Description0", t1."IsDeleted0" AS "IsDeleted00", t1."Name" AS "Name0", t1."Resource", t1."RowVersion0" AS "RowVersion00", t1."UpdatedAt0" AS "UpdatedAt00", t1."UpdatedBy0" AS "UpdatedBy00"
    FROM "UserRoles" AS u0
    INNER JOIN "Roles" AS r ON u0."RoleId" = r."Id"
    LEFT JOIN (
        SELECT r0."Id", r0."CreatedAt", r0."CreatedBy", r0."IsDeleted", r0."PermissionId", r0."RoleId", r0."RowVersion", r0."UpdatedAt", r0."UpdatedBy", p."Id" AS "Id0", p."Action", p."CreatedAt" AS "CreatedAt0", p."CreatedBy" AS "CreatedBy0", p."Description", p."IsDeleted" AS "IsDeleted0", p."Name", p."Resource", p."RowVersion" AS "RowVersion0", p."UpdatedAt" AS "UpdatedAt0", p."UpdatedBy" AS "UpdatedBy0"
        FROM "RolePermissions" AS r0
        INNER JOIN "Permissions" AS p ON r0."PermissionId" = p."Id"
    ) AS t1 ON r."Id" = t1."RoleId"
) AS t0 ON t."Id" = t0."UserId"
ORDER BY t."Id", t0."Id", t0."Id0", t0."Id1"
2025-05-16 16:09:16.848 +05:30 [WRN] Login failed: Invalid email or password
System.UnauthorizedAccessException: Invalid email or password
   at IdentityService.Application.Features.Auth.Commands.LoginCommandHandler.Handle(LoginCommand request, CancellationToken cancellationToken) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.Application\Features\Auth\Commands\LoginCommand.cs:line 67
2025-05-16 16:09:16.855 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = DateTime), @p2='?', @p3='?', @p4='?', @p5='?', @p6='?', @p7='?' (DbType = Boolean), @p8='?' (DbType = Boolean), @p9='?', @p10='?' (DbType = Int32), @p11='?', @p12='?' (DbType = Binary), @p13='?' (DbType = DateTime), @p14='?', @p15='?', @p16='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "UserLoginHistories" ("Id", "CreatedAt", "CreatedBy", "DeviceInfo", "Email", "FailureReason", "IpAddress", "IsDeleted", "IsSuccessful", "Location", "LoginMethod", "PhoneNumber", "RowVersion", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16);
2025-05-16 16:09:16.861 +05:30 [WRN] Login failed for user
System.UnauthorizedAccessException: Invalid email or password
   at IdentityService.Application.Features.Auth.Commands.LoginCommandHandler.Handle(LoginCommand request, CancellationToken cancellationToken) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.Application\Features\Auth\Commands\LoginCommand.cs:line 67
   at IdentityService.Application.Features.Auth.Commands.LoginCommandHandler.Handle(LoginCommand request, CancellationToken cancellationToken) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.Application\Features\Auth\Commands\LoginCommand.cs:line 216
   at IdentityService.Application.Behaviors.ValidationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.Application\Behaviors\ValidationBehavior.cs:line 95
   at IdentityService.Application.Behaviors.UserIdPropagationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.Application\Behaviors\UserIdPropagationBehavior.cs:line 39
   at MediatR.Pipeline.RequestExceptionProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestExceptionProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestExceptionActionProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestExceptionActionProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestPostProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestPreProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at IdentityService.API.Controllers.AuthController.Login(LoginRequest request) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.API\Controllers\AuthController.cs:line 31
2025-05-16 16:09:16.867 +05:30 [INF] Executing UnauthorizedObjectResult, writing value of type '<>f__AnonymousType0`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-05-16 16:09:16.868 +05:30 [INF] Executed action IdentityService.API.Controllers.AuthController.Login (IdentityService.API) in 50.1204ms
2025-05-16 16:09:16.870 +05:30 [INF] Executed endpoint 'IdentityService.API.Controllers.AuthController.Login (IdentityService.API)'
2025-05-16 16:09:16.871 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5263/api/auth/login - 401 null application/json; charset=utf-8 70.6973ms
2025-05-16 16:09:43.923 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5263/api/users - application/json 152
2025-05-16 16:09:43.928 +05:30 [INF] Token received and is being processed
2025-05-16 16:09:43.931 +05:30 [WRN] User is null or not authenticated
2025-05-16 16:09:43.933 +05:30 [WRN] No user ID found in token
2025-05-16 16:09:43.934 +05:30 [INF] Executing endpoint 'IdentityService.API.Controllers.UsersController.CreateUser (IdentityService.API)'
2025-05-16 16:09:43.949 +05:30 [INF] Route matched with {action = "CreateUser", controller = "Users"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[IdentityService.Application.DTOs.Users.UserResponse]] CreateUser(IdentityService.Application.DTOs.Users.CreateUserRequest) on controller IdentityService.API.Controllers.UsersController (IdentityService.API).
2025-05-16 16:09:43.965 +05:30 [WRN] User ID not found in HttpContext.Items
2025-05-16 16:09:43.966 +05:30 [INF] Setting UserId "00000000-0000-0000-0000-000000000000" on request of type CreateUserCommand
2025-05-16 16:09:43.970 +05:30 [WRN] User ID not found in HttpContext.Items
2025-05-16 16:09:43.973 +05:30 [INF] Validating request of type CreateUserCommand
2025-05-16 16:09:43.977 +05:30 [INF] Request content: {
  "Request": {
    "Email": "<EMAIL>",
    "PhoneNumber": "1234567890",
    "Password": "Password123!",
    "ConfirmPassword": "Password123!",
    "RoleIds": null,
    "IsActive": true
  },
  "UserId": "00000000-0000-0000-0000-000000000000"
}
2025-05-16 16:09:43.979 +05:30 [INF] Request UserId property value: "00000000-0000-0000-0000-000000000000"
2025-05-16 16:09:43.980 +05:30 [WRN] UserId is empty GUID (00000000-0000-0000-0000-000000000000)
2025-05-16 16:09:43.981 +05:30 [INF] Validation passed for request of type CreateUserCommand
2025-05-16 16:09:44.003 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__email_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__email_0)
2025-05-16 16:09:44.013 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__phoneNumber_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."PhoneNumber" = @__phoneNumber_0)
2025-05-16 16:09:44.016 +05:30 [WRN] Invalid operation during user creation
System.InvalidOperationException: Phone number 1234567890 is already in use
   at IdentityService.Application.Features.Users.Commands.CreateUserCommandHandler.Handle(CreateUserCommand command, CancellationToken cancellationToken) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.Application\Features\Users\Commands\CreateUserCommand.cs:line 46
   at IdentityService.Application.Behaviors.ValidationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.Application\Behaviors\ValidationBehavior.cs:line 95
   at IdentityService.Application.Behaviors.UserIdPropagationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.Application\Behaviors\UserIdPropagationBehavior.cs:line 39
   at MediatR.Pipeline.RequestExceptionProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestExceptionProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestExceptionActionProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestExceptionActionProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestPostProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestPreProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at IdentityService.API.Controllers.UsersController.CreateUser(CreateUserRequest request) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.API\Controllers\UsersController.cs:line 74
2025-05-16 16:09:44.025 +05:30 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-05-16 16:09:44.027 +05:30 [INF] Executed action IdentityService.API.Controllers.UsersController.CreateUser (IdentityService.API) in 70.6483ms
2025-05-16 16:09:44.028 +05:30 [INF] Executed endpoint 'IdentityService.API.Controllers.UsersController.CreateUser (IdentityService.API)'
2025-05-16 16:09:44.029 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5263/api/users - 400 null application/json; charset=utf-8 106.2516ms
2025-05-16 16:10:37.645 +05:30 [DBG] Starting session cleanup
2025-05-16 16:10:37.665 +05:30 [INF] Executed DbCommand (8ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-16 16:10:37.685 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-16 16:10:37.691 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-16 16:10:37.693 +05:30 [DBG] Session cleanup completed
2025-05-16 16:10:57.320 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5263/api/users/00000000-0000-0000-0000-000000000001/roles - application/json 102
2025-05-16 16:10:57.461 +05:30 [INF] Token received and is being processed
2025-05-16 16:10:57.463 +05:30 [WRN] User is null or not authenticated
2025-05-16 16:10:57.465 +05:30 [WRN] No user ID found in token
2025-05-16 16:10:57.466 +05:30 [INF] Executing endpoint 'IdentityService.API.Controllers.UsersController.AssignRoles (IdentityService.API)'
2025-05-16 16:10:57.467 +05:30 [INF] Route matched with {action = "AssignRoles", controller = "Users"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[IdentityService.Application.DTOs.Users.UserResponse]] AssignRoles(System.Guid, IdentityService.Application.DTOs.Users.AssignRoleRequest) on controller IdentityService.API.Controllers.UsersController (IdentityService.API).
2025-05-16 16:10:57.496 +05:30 [WRN] User ID not found in HttpContext.Items
2025-05-16 16:10:57.510 +05:30 [INF] Setting UserId "00000000-0000-0000-0000-000000000000" on request of type AssignRoleCommand
2025-05-16 16:10:57.512 +05:30 [WRN] User ID not found in HttpContext.Items
2025-05-16 16:10:57.513 +05:30 [INF] Validating request of type AssignRoleCommand
2025-05-16 16:10:57.515 +05:30 [INF] Request content: {
  "UserId": "00000000-0000-0000-0000-000000000001",
  "Request": {
    "RoleIds": [
      "0247e353-d1fb-4118-b630-fecff038de1e"
    ]
  }
}
2025-05-16 16:10:57.517 +05:30 [INF] Request UserId property value: "00000000-0000-0000-0000-000000000001"
2025-05-16 16:10:57.518 +05:30 [INF] Validation passed for request of type AssignRoleCommand
2025-05-16 16:10:57.528 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash", t0."Id", t0."CreatedAt", t0."CreatedBy", t0."IsDeleted", t0."RoleId", t0."RowVersion", t0."UpdatedAt", t0."UpdatedBy", t0."UserId", t0."Id0", t0."CreatedAt0", t0."CreatedBy0", t0."Description", t0."IsDeleted0", t0."Name", t0."RowVersion0", t0."UpdatedAt0", t0."UpdatedBy0"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."Email", u."EmailVerified", u."IsDeleted", u."LastLoginAt", u."PhoneNumber", u."PhoneNumberVerified", u."PrimaryBranchId", u."RefreshToken", u."RefreshTokenExpiryTime", u."RememberMe", u."RowVersion", u."SecurityStamp", u."UpdatedAt", u."UpdatedBy", u."VendorId", u."PasswordHash"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Id" = @__userId_0
    LIMIT 1
) AS t
LEFT JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."IsDeleted", u0."RoleId", u0."RowVersion", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", r."Id" AS "Id0", r."CreatedAt" AS "CreatedAt0", r."CreatedBy" AS "CreatedBy0", r."Description", r."IsDeleted" AS "IsDeleted0", r."Name", r."RowVersion" AS "RowVersion0", r."UpdatedAt" AS "UpdatedAt0", r."UpdatedBy" AS "UpdatedBy0"
    FROM "UserRoles" AS u0
    INNER JOIN "Roles" AS r ON u0."RoleId" = r."Id"
) AS t0 ON t."Id" = t0."UserId"
ORDER BY t."Id", t0."Id"
2025-05-16 16:10:57.533 +05:30 [WRN] Invalid operation during role assignment
System.InvalidOperationException: User with ID 00000000-0000-0000-0000-000000000001 not found
   at IdentityService.Application.Features.Users.Commands.AssignRoleCommandHandler.Handle(AssignRoleCommand command, CancellationToken cancellationToken) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.Application\Features\Users\Commands\AssignRoleCommand.cs:line 54
   at IdentityService.Application.Features.Users.Commands.AssignRoleCommandHandler.Handle(AssignRoleCommand command, CancellationToken cancellationToken) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.Application\Features\Users\Commands\AssignRoleCommand.cs:line 178
   at IdentityService.Application.Behaviors.ValidationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.Application\Behaviors\ValidationBehavior.cs:line 95
   at IdentityService.Application.Behaviors.UserIdPropagationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.Application\Behaviors\UserIdPropagationBehavior.cs:line 39
   at MediatR.Pipeline.RequestExceptionProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestExceptionProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestExceptionActionProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestExceptionActionProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestPostProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestPreProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at IdentityService.API.Controllers.UsersController.AssignRoles(Guid id, AssignRoleRequest request) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.API\Controllers\UsersController.cs:line 170
2025-05-16 16:10:57.546 +05:30 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-05-16 16:10:57.548 +05:30 [INF] Executed action IdentityService.API.Controllers.UsersController.AssignRoles (IdentityService.API) in 70.575ms
2025-05-16 16:10:57.549 +05:30 [INF] Executed endpoint 'IdentityService.API.Controllers.UsersController.AssignRoles (IdentityService.API)'
2025-05-16 16:10:57.551 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5263/api/users/00000000-0000-0000-0000-000000000001/roles - 400 null application/json; charset=utf-8 230.806ms
2025-05-16 16:16:15.618 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'AuditLog'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-16 16:16:15.670 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'PasswordResetToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-16 16:16:15.671 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserBranch'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-16 16:16:15.671 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserRole'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-16 16:16:15.672 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserSession'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-16 16:16:15.673 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserSubscription'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-16 16:16:16.627 +05:30 [INF] Executed DbCommand (73ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-05-16 16:16:16.652 +05:30 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-05-16 16:16:16.760 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-05-16 16:16:16.860 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-05-16 16:16:16.861 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-05-16 16:16:16.886 +05:30 [INF] No migrations were applied. The database is already up to date.
2025-05-16 16:16:16.945 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-16 16:16:17.162 +05:30 [INF] Configured endpoint SubscriptionCreated, Consumer: IdentityService.Infrastructure.Messaging.Consumers.SubscriptionCreatedConsumer
2025-05-16 16:16:17.165 +05:30 [INF] Configured endpoint SubscriptionStatusChanged, Consumer: IdentityService.Infrastructure.Messaging.Consumers.SubscriptionStatusChangedConsumer
2025-05-16 16:16:17.167 +05:30 [INF] Configured endpoint VendorRegistered, Consumer: IdentityService.Infrastructure.Messaging.Consumers.VendorRegisteredConsumer
2025-05-16 16:16:17.354 +05:30 [DBG] Starting bus instances: IBus
2025-05-16 16:16:17.358 +05:30 [DBG] Starting bus: "rabbitmq://localhost/"
2025-05-16 16:16:17.388 +05:30 [INF] Session cleanup service is starting
2025-05-16 16:16:17.389 +05:30 [DBG] Starting session cleanup
2025-05-16 16:16:17.432 +05:30 [DBG] Connect: guest@localhost:5672/
2025-05-16 16:16:17.556 +05:30 [DBG] Connected: guest@localhost:5672/ (address: amqp://localhost:5672, local: 60466)
2025-05-16 16:16:17.607 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/DESKTOPI7D4M6U_IdentityServiceAPI_bus_1oeyyyfpkikqd39bbdq3e3zn8r?temporary=true"
2025-05-16 16:16:17.642 +05:30 [DBG] Declare queue: name: VendorRegistered, durable, consumer-count: 0 message-count: 0
2025-05-16 16:16:17.642 +05:30 [DBG] Declare queue: name: SubscriptionCreated, durable, consumer-count: 0 message-count: 0
2025-05-16 16:16:17.642 +05:30 [DBG] Declare queue: name: SubscriptionStatusChanged, durable, consumer-count: 0 message-count: 0
2025-05-16 16:16:17.659 +05:30 [DBG] Declare exchange: name: SubscriptionStatusChanged, type: fanout, durable
2025-05-16 16:16:17.659 +05:30 [DBG] Declare exchange: name: VendorRegistered, type: fanout, durable
2025-05-16 16:16:17.659 +05:30 [DBG] Declare exchange: name: SubscriptionCreated, type: fanout, durable
2025-05-16 16:16:17.665 +05:30 [DBG] Declare exchange: name: VMSContracts.Tenant.Events:VendorRegistered, type: fanout, durable
2025-05-16 16:16:17.665 +05:30 [DBG] Declare exchange: name: VMSContracts.Subscription.Events:SubscriptionCreated, type: fanout, durable
2025-05-16 16:16:17.665 +05:30 [DBG] Declare exchange: name: VMSContracts.Subscription.Events:SubscriptionStatusChanged, type: fanout, durable
2025-05-16 16:16:17.672 +05:30 [DBG] Bind queue: source: SubscriptionStatusChanged, destination: SubscriptionStatusChanged
2025-05-16 16:16:17.672 +05:30 [DBG] Bind queue: source: SubscriptionCreated, destination: SubscriptionCreated
2025-05-16 16:16:17.672 +05:30 [DBG] Bind queue: source: VendorRegistered, destination: VendorRegistered
2025-05-16 16:16:17.694 +05:30 [DBG] Bind exchange: source: VMSContracts.Tenant.Events:VendorRegistered, destination: VendorRegistered
2025-05-16 16:16:17.694 +05:30 [DBG] Bind exchange: source: VMSContracts.Subscription.Events:SubscriptionCreated, destination: SubscriptionCreated
2025-05-16 16:16:17.694 +05:30 [DBG] Bind exchange: source: VMSContracts.Subscription.Events:SubscriptionStatusChanged, destination: SubscriptionStatusChanged
2025-05-16 16:16:17.737 +05:30 [DBG] Consumer Ok: "rabbitmq://localhost/VendorRegistered" - amq.ctag-puGEoufGy9JA6GkIR0MNoA
2025-05-16 16:16:17.737 +05:30 [DBG] Consumer Ok: "rabbitmq://localhost/SubscriptionCreated" - amq.ctag-Bi-AQ-lBwkXafJuLoocYKA
2025-05-16 16:16:17.737 +05:30 [DBG] Consumer Ok: "rabbitmq://localhost/SubscriptionStatusChanged" - amq.ctag-sPl0OH9onn16ZFYsx_Vu3w
2025-05-16 16:16:17.739 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/SubscriptionCreated"
2025-05-16 16:16:17.740 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/SubscriptionStatusChanged"
2025-05-16 16:16:17.739 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/VendorRegistered"
2025-05-16 16:16:17.744 +05:30 [INF] Bus started: "rabbitmq://localhost/"
2025-05-16 16:16:17.978 +05:30 [INF] Executed DbCommand (26ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-16 16:16:18.026 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-16 16:16:18.066 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-16 16:16:18.067 +05:30 [DBG] Session cleanup completed
2025-05-16 16:16:18.081 +05:30 [INF] Now listening on: http://localhost:5263
2025-05-16 16:16:18.081 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-05-16 16:16:18.082 +05:30 [INF] Hosting environment: Development
2025-05-16 16:16:18.082 +05:30 [INF] Content root path: E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.API
2025-05-16 16:16:18.477 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/swagger/index.html - null null
2025-05-16 16:16:18.645 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/swagger/index.html - 200 null text/html;charset=utf-8 171.4317ms
2025-05-16 16:16:18.790 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/swagger/v1/swagger.json - null null
2025-05-16 16:16:19.018 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 227.7179ms
2025-05-16 16:16:59.816 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5263/api/Users/<USER>/roles - application/json 65
2025-05-16 16:16:59.876 +05:30 [WRN] Failed to determine the https port for redirect.
2025-05-16 16:17:01.457 +05:30 [INF] Token received and is being processed
2025-05-16 16:17:01.558 +05:30 [WRN] User is null or not authenticated
2025-05-16 16:17:01.559 +05:30 [WRN] No user ID found in token
2025-05-16 16:17:01.561 +05:30 [INF] Executing endpoint 'IdentityService.API.Controllers.UsersController.AssignRoles (IdentityService.API)'
2025-05-16 16:17:01.581 +05:30 [INF] Route matched with {action = "AssignRoles", controller = "Users"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[IdentityService.Application.DTOs.Users.UserResponse]] AssignRoles(System.Guid, IdentityService.Application.DTOs.Users.AssignRoleRequest) on controller IdentityService.API.Controllers.UsersController (IdentityService.API).
2025-05-16 16:17:09.234 +05:30 [WRN] User ID not found in HttpContext.Items
2025-05-16 16:17:09.235 +05:30 [INF] Setting UserId "00000000-0000-0000-0000-000000000000" on request of type AssignRoleCommand
2025-05-16 16:17:09.235 +05:30 [WRN] User ID not found in HttpContext.Items
2025-05-16 16:17:09.240 +05:30 [INF] Validating request of type AssignRoleCommand
2025-05-16 16:17:09.250 +05:30 [INF] Request content: {
  "UserId": "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e",
  "Request": {
    "RoleIds": [
      "0247e353-d1fb-4118-b630-fecff038de1e"
    ]
  }
}
2025-05-16 16:17:09.250 +05:30 [INF] Request UserId property value: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:17:09.251 +05:30 [INF] Validation passed for request of type AssignRoleCommand
2025-05-16 16:17:09.289 +05:30 [INF] Executed DbCommand (4ms) [Parameters=[@__get_Item_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."Email", u."EmailVerified", u."IsDeleted", u."LastLoginAt", u."PhoneNumber", u."PhoneNumberVerified", u."PrimaryBranchId", u."RefreshToken", u."RefreshTokenExpiryTime", u."RememberMe", u."RowVersion", u."SecurityStamp", u."UpdatedAt", u."UpdatedBy", u."VendorId", u."PasswordHash"
FROM "Users" AS u
WHERE NOT (u."IsDeleted") AND u."Id" = @__get_Item_0
LIMIT 1
2025-05-16 16:17:09.481 +05:30 [WRN] User is null or not authenticated
2025-05-16 16:17:09.493 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Name"
FROM "UserRoles" AS u
INNER JOIN "Roles" AS r ON u."RoleId" = r."Id"
WHERE u."UserId" = @__userId_0
2025-05-16 16:17:09.514 +05:30 [INF] Executed DbCommand (4ms) [Parameters=[@p0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
DELETE FROM "UserRoles" WHERE "UserId" = @p0
2025-05-16 16:17:09.524 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__get_Item_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."Description", r."IsDeleted", r."Name", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "Roles" AS r
WHERE r."Id" = @__get_Item_0
LIMIT 1
2025-05-16 16:17:09.806 +05:30 [WRN] User is null or not authenticated
2025-05-16 16:17:09.806 +05:30 [WRN] User is null or not authenticated
2025-05-16 16:17:09.942 +05:30 [INF] Executed DbCommand (8ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = DateTime), @p2='?', @p3='?' (DbType = Boolean), @p4='?' (DbType = Guid), @p5='?' (DbType = Binary), @p6='?' (DbType = DateTime), @p7='?', @p8='?' (DbType = Guid), @p25='?' (DbType = Guid), @p9='?' (DbType = DateTime), @p10='?', @p11='?', @p12='?' (DbType = Boolean), @p13='?' (DbType = Boolean), @p14='?' (DbType = DateTime), @p15='?', @p16='?' (DbType = Boolean), @p17='?' (DbType = Guid), @p18='?', @p19='?' (DbType = DateTime), @p20='?' (DbType = Boolean), @p26='?' (DbType = Binary), @p21='?' (DbType = DateTime), @p22='?' (DbType = DateTime), @p23='?', @p24='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "UserRoles" ("Id", "CreatedAt", "CreatedBy", "IsDeleted", "RoleId", "RowVersion", "UpdatedAt", "UpdatedBy", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8);
UPDATE "Users" SET "CreatedAt" = @p9, "CreatedBy" = @p10, "Email" = @p11, "EmailVerified" = @p12, "IsDeleted" = @p13, "LastLoginAt" = @p14, "PhoneNumber" = @p15, "PhoneNumberVerified" = @p16, "PrimaryBranchId" = @p17, "RefreshToken" = @p18, "RefreshTokenExpiryTime" = @p19, "RememberMe" = @p20, "SecurityStamp" = @p21, "UpdatedAt" = @p22, "UpdatedBy" = @p23, "VendorId" = @p24
WHERE "Id" = @p25 AND "RowVersion" = @p26
RETURNING "RowVersion";
2025-05-16 16:17:09.993 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Name"
FROM "UserRoles" AS u
INNER JOIN "Roles" AS r ON u."RoleId" = r."Id"
WHERE u."UserId" = @__userId_0
2025-05-16 16:17:09.994 +05:30 [WRN] User is null or not authenticated
2025-05-16 16:17:10.001 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__get_Item_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."Email", u."EmailVerified", u."IsDeleted", u."LastLoginAt", u."PhoneNumber", u."PhoneNumberVerified", u."PrimaryBranchId", u."RefreshToken", u."RefreshTokenExpiryTime", u."RememberMe", u."RowVersion", u."SecurityStamp", u."UpdatedAt", u."UpdatedBy", u."VendorId", u."PasswordHash"
FROM "Users" AS u
WHERE NOT (u."IsDeleted") AND u."Id" = @__get_Item_0
LIMIT 1
2025-05-16 16:17:43.229 +05:30 [WRN] Invalid operation during role assignment
System.InvalidOperationException: Failed to assign roles. Please try again later.
 ---> System.ArgumentException: User with ID 00000000-0000-0000-0000-000000000000 not found
   at IdentityService.Infrastructure.Services.AuditLogService.CreateAuditLogAsync(String action, String entityName, String entityId, String oldValues, String newValues, String affectedColumns, Guid userId) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.Infrastructure\Services\AuditLogService.cs:line 38
   at IdentityService.Application.Features.Users.Commands.AssignRoleCommandHandler.Handle(AssignRoleCommand command, CancellationToken cancellationToken) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.Application\Features\Users\Commands\AssignRoleCommand.cs:line 90
   at IdentityService.Application.Features.Users.Commands.AssignRoleCommandHandler.Handle(AssignRoleCommand command, CancellationToken cancellationToken) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.Application\Features\Users\Commands\AssignRoleCommand.cs:line 122
   --- End of inner exception stack trace ---
   at IdentityService.Application.Features.Users.Commands.AssignRoleCommandHandler.Handle(AssignRoleCommand command, CancellationToken cancellationToken) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.Application\Features\Users\Commands\AssignRoleCommand.cs:line 131
   at IdentityService.Application.Behaviors.ValidationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.Application\Behaviors\ValidationBehavior.cs:line 95
   at IdentityService.Application.Behaviors.UserIdPropagationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.Application\Behaviors\UserIdPropagationBehavior.cs:line 39
   at MediatR.Pipeline.RequestExceptionProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestExceptionProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestExceptionActionProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestExceptionActionProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestPostProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestPreProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at IdentityService.API.Controllers.UsersController.AssignRoles(Guid id, AssignRoleRequest request) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.API\Controllers\UsersController.cs:line 170
2025-05-16 16:17:43.264 +05:30 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-05-16 16:17:43.275 +05:30 [INF] Executed action IdentityService.API.Controllers.UsersController.AssignRoles (IdentityService.API) in 41690.3882ms
2025-05-16 16:17:43.275 +05:30 [INF] Executed endpoint 'IdentityService.API.Controllers.UsersController.AssignRoles (IdentityService.API)'
2025-05-16 16:17:43.280 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5263/api/Users/<USER>/roles - 400 null application/json; charset=utf-8 43463.1035ms
2025-05-16 16:18:01.005 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5263/api/Auth/login - application/json 143
2025-05-16 16:18:01.034 +05:30 [INF] Token received and is being processed
2025-05-16 16:18:01.166 +05:30 [INF] Token validated successfully with claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 16:18:01.166 +05:30 [INF] NameIdentifier claim found: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e
2025-05-16 16:18:01.190 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__get_Item_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."Email", u."EmailVerified", u."IsDeleted", u."LastLoginAt", u."PhoneNumber", u."PhoneNumberVerified", u."PrimaryBranchId", u."RefreshToken", u."RefreshTokenExpiryTime", u."RememberMe", u."RowVersion", u."SecurityStamp", u."UpdatedAt", u."UpdatedBy", u."VendorId", u."PasswordHash"
FROM "Users" AS u
WHERE NOT (u."IsDeleted") AND u."Id" = @__get_Item_0
LIMIT 1
2025-05-16 16:18:01.201 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 16:18:01.201 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:18:01.201 +05:30 [INF] User ID from token: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:18:01.201 +05:30 [INF] Executing endpoint 'IdentityService.API.Controllers.AuthController.Login (IdentityService.API)'
2025-05-16 16:18:01.206 +05:30 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[IdentityService.Application.DTOs.Auth.LoginResponse]] Login(IdentityService.Application.DTOs.Auth.LoginRequest) on controller IdentityService.API.Controllers.AuthController (IdentityService.API).
2025-05-16 16:18:01.291 +05:30 [INF] Retrieved user ID from HttpContext.Items: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:18:01.292 +05:30 [INF] Setting UserId "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e" on request of type LoginCommand
2025-05-16 16:18:01.292 +05:30 [INF] Retrieved user ID from HttpContext.Items: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:18:01.297 +05:30 [INF] Validating request of type LoginCommand
2025-05-16 16:18:01.301 +05:30 [INF] Request content: {
  "Request": {
    "Email": "<EMAIL>",
    "Password": "Admin@123",
    "PhoneNumber": "8088020878",
    "Otp": "1234",
    "RememberMe": true,
    "IsEmailLogin": true,
    "IsPhoneLogin": true
  },
  "UserId": "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
}
2025-05-16 16:18:01.301 +05:30 [INF] Request UserId property value: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:18:01.344 +05:30 [INF] Validation passed for request of type LoginCommand
2025-05-16 16:18:01.571 +05:30 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-05-16 16:18:01.586 +05:30 [INF] Executed DbCommand (8ms) [Parameters=[@__email_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash", t0."Id", t0."CreatedAt", t0."CreatedBy", t0."IsDeleted", t0."RoleId", t0."RowVersion", t0."UpdatedAt", t0."UpdatedBy", t0."UserId", t0."Id0", t0."CreatedAt0", t0."CreatedBy0", t0."Description", t0."IsDeleted0", t0."Name", t0."RowVersion0", t0."UpdatedAt0", t0."UpdatedBy0", t0."Id1", t0."CreatedAt1", t0."CreatedBy1", t0."IsDeleted1", t0."PermissionId", t0."RoleId0", t0."RowVersion1", t0."UpdatedAt1", t0."UpdatedBy1", t0."Id00", t0."Action", t0."CreatedAt00", t0."CreatedBy00", t0."Description0", t0."IsDeleted00", t0."Name0", t0."Resource", t0."RowVersion00", t0."UpdatedAt00", t0."UpdatedBy00"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."Email", u."EmailVerified", u."IsDeleted", u."LastLoginAt", u."PhoneNumber", u."PhoneNumberVerified", u."PrimaryBranchId", u."RefreshToken", u."RefreshTokenExpiryTime", u."RememberMe", u."RowVersion", u."SecurityStamp", u."UpdatedAt", u."UpdatedBy", u."VendorId", u."PasswordHash"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__email_0
    LIMIT 1
) AS t
LEFT JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."IsDeleted", u0."RoleId", u0."RowVersion", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", r."Id" AS "Id0", r."CreatedAt" AS "CreatedAt0", r."CreatedBy" AS "CreatedBy0", r."Description", r."IsDeleted" AS "IsDeleted0", r."Name", r."RowVersion" AS "RowVersion0", r."UpdatedAt" AS "UpdatedAt0", r."UpdatedBy" AS "UpdatedBy0", t1."Id" AS "Id1", t1."CreatedAt" AS "CreatedAt1", t1."CreatedBy" AS "CreatedBy1", t1."IsDeleted" AS "IsDeleted1", t1."PermissionId", t1."RoleId" AS "RoleId0", t1."RowVersion" AS "RowVersion1", t1."UpdatedAt" AS "UpdatedAt1", t1."UpdatedBy" AS "UpdatedBy1", t1."Id0" AS "Id00", t1."Action", t1."CreatedAt0" AS "CreatedAt00", t1."CreatedBy0" AS "CreatedBy00", t1."Description" AS "Description0", t1."IsDeleted0" AS "IsDeleted00", t1."Name" AS "Name0", t1."Resource", t1."RowVersion0" AS "RowVersion00", t1."UpdatedAt0" AS "UpdatedAt00", t1."UpdatedBy0" AS "UpdatedBy00"
    FROM "UserRoles" AS u0
    INNER JOIN "Roles" AS r ON u0."RoleId" = r."Id"
    LEFT JOIN (
        SELECT r0."Id", r0."CreatedAt", r0."CreatedBy", r0."IsDeleted", r0."PermissionId", r0."RoleId", r0."RowVersion", r0."UpdatedAt", r0."UpdatedBy", p."Id" AS "Id0", p."Action", p."CreatedAt" AS "CreatedAt0", p."CreatedBy" AS "CreatedBy0", p."Description", p."IsDeleted" AS "IsDeleted0", p."Name", p."Resource", p."RowVersion" AS "RowVersion0", p."UpdatedAt" AS "UpdatedAt0", p."UpdatedBy" AS "UpdatedBy0"
        FROM "RolePermissions" AS r0
        INNER JOIN "Permissions" AS p ON r0."PermissionId" = p."Id"
    ) AS t1 ON r."Id" = t1."RoleId"
) AS t0 ON t."Id" = t0."UserId"
ORDER BY t."Id", t0."Id", t0."Id0", t0."Id1"
2025-05-16 16:18:01.824 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT count(*)::int
FROM "UserSessions" AS u
WHERE u."UserId" = @__userId_0 AND u."Status" = 0 AND u."ExpiresAt" > now()
2025-05-16 16:18:01.833 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 16:18:01.833 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:18:01.886 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: 928108a7-2e9e-4418-b325-56360fea16ef, security_stamp: 638820282925527280, permission: test.permission, nbf: 1747390059, exp: 1747393659, iss: identity-service, aud: vms-api
2025-05-16 16:18:01.886 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:18:01.899 +05:30 [INF] Executed DbCommand (5ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = DateTime), @p2='?', @p3='?', @p4='?', @p5='?', @p6='?', @p7='?' (DbType = Boolean), @p8='?' (DbType = Boolean), @p9='?', @p10='?' (DbType = Int32), @p11='?', @p12='?' (DbType = Binary), @p13='?' (DbType = DateTime), @p14='?', @p15='?', @p16='?' (DbType = Guid), @p17='?' (DbType = Guid), @p18='?' (DbType = DateTime), @p19='?', @p20='?', @p21='?', @p22='?' (DbType = DateTime), @p23='?' (DbType = DateTime), @p24='?', @p25='?' (DbType = Boolean), @p26='?' (DbType = DateTime), @p27='?', @p28='?' (DbType = DateTime), @p29='?' (DbType = Int32), @p30='?', @p31='?' (DbType = DateTime), @p32='?', @p33='?', @p34='?' (DbType = Guid), @p40='?' (DbType = Guid), @p35='?' (DbType = DateTime), @p36='?', @p37='?' (DbType = DateTime), @p41='?' (DbType = Binary), @p38='?' (DbType = DateTime), @p39='?'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "UserLoginHistories" ("Id", "CreatedAt", "CreatedBy", "DeviceInfo", "Email", "FailureReason", "IpAddress", "IsDeleted", "IsSuccessful", "Location", "LoginMethod", "PhoneNumber", "RowVersion", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16);
INSERT INTO "UserSessions" ("Id", "CreatedAt", "CreatedBy", "DeviceInfo", "EndReason", "EndedAt", "ExpiresAt", "IpAddress", "IsDeleted", "LastActiveAt", "RefreshToken", "StartedAt", "Status", "Token", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34)
RETURNING "RowVersion";
UPDATE "Users" SET "LastLoginAt" = @p35, "RefreshToken" = @p36, "RefreshTokenExpiryTime" = @p37, "UpdatedAt" = @p38, "UpdatedBy" = @p39
WHERE "Id" = @p40 AND "RowVersion" = @p41
RETURNING "RowVersion";
2025-05-16 16:18:01.907 +05:30 [INF] Created new session "3a38e051-b212-4ec8-82d3-c3fe07b4ca2a" for user "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:18:01.913 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Name"
FROM "UserRoles" AS u
INNER JOIN "Roles" AS r ON u."RoleId" = r."Id"
WHERE u."UserId" = @__userId_0
2025-05-16 16:18:01.923 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."Description", r."IsDeleted", r."Name", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "UserRoles" AS u
INNER JOIN "Roles" AS r ON u."RoleId" = r."Id"
WHERE u."UserId" = @__userId_0
2025-05-16 16:18:01.935 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__role_Id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "RolePermissions" AS r
INNER JOIN "Permissions" AS p ON r."PermissionId" = p."Id"
WHERE r."RoleId" = @__role_Id_0
2025-05-16 16:18:01.939 +05:30 [INF] Executing OkObjectResult, writing value of type 'IdentityService.Application.DTOs.Auth.LoginResponse'.
2025-05-16 16:18:01.956 +05:30 [INF] Executed action IdentityService.API.Controllers.AuthController.Login (IdentityService.API) in 749.5579ms
2025-05-16 16:18:01.956 +05:30 [INF] Executed endpoint 'IdentityService.API.Controllers.AuthController.Login (IdentityService.API)'
2025-05-16 16:18:01.957 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5263/api/Auth/login - 200 null application/json; charset=utf-8 952.0286ms
2025-05-16 16:18:22.554 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5263/api/Users/<USER>/roles - application/json 65
2025-05-16 16:18:22.561 +05:30 [INF] Token received and is being processed
2025-05-16 16:18:22.681 +05:30 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignature(JsonWebToken jwtToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignatureAndIssuerSecurityKey(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-05-16 16:18:22.683 +05:30 [ERR] Authentication failed: Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignature(JsonWebToken jwtToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignatureAndIssuerSecurityKey(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-05-16 16:18:22.683 +05:30 [INF] Bearer was not authenticated. Failure message: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
2025-05-16 16:18:22.704 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__token_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Token" = @__token_0
LIMIT 1
2025-05-16 16:18:22.717 +05:30 [WRN] Session with token eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.mHsOmvO53W_bBjPSppTkV082ycGAz3CAemwZ1z7yMPk",   "refreshToken": "fe8b0e34-ae9c-4fa6-b734-affc3e25ab19 not found
2025-05-16 16:18:22.718 +05:30 [WRN] Invalid session for token eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.mHsOmvO53W_bBjPSppTkV082ycGAz3CAemwZ1z7yMPk",   "refreshToken": "fe8b0e34-ae9c-4fa6-b734-affc3e25ab19
2025-05-16 16:18:22.720 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5263/api/Users/<USER>/roles - 401 null application/json; charset=utf-8 166.0691ms
2025-05-16 16:18:36.507 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5263/api/Users/<USER>/roles - application/json 65
2025-05-16 16:18:36.507 +05:30 [INF] Token received and is being processed
2025-05-16 16:18:36.577 +05:30 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignature(JsonWebToken jwtToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignatureAndIssuerSecurityKey(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-05-16 16:18:36.577 +05:30 [ERR] Authentication failed: Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignature(JsonWebToken jwtToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignatureAndIssuerSecurityKey(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-05-16 16:18:36.578 +05:30 [INF] Bearer was not authenticated. Failure message: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
2025-05-16 16:18:36.580 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__token_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Token" = @__token_0
LIMIT 1
2025-05-16 16:18:36.581 +05:30 [WRN] Session with token eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.mHsOmvO53W_bBjPSppTkV082ycGAz3CAemwZ1z7yMPk",   "refreshToken": "fe8b0e34-ae9c-4fa6-b734-affc3e25ab19 not found
2025-05-16 16:18:36.582 +05:30 [WRN] Invalid session for token eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.mHsOmvO53W_bBjPSppTkV082ycGAz3CAemwZ1z7yMPk",   "refreshToken": "fe8b0e34-ae9c-4fa6-b734-affc3e25ab19
2025-05-16 16:18:36.587 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5263/api/Users/<USER>/roles - 401 null application/json; charset=utf-8 79.7981ms
2025-05-16 16:18:55.856 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5263/api/Auth/login - application/json 143
2025-05-16 16:18:55.858 +05:30 [INF] Token received and is being processed
2025-05-16 16:18:55.927 +05:30 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignature(JsonWebToken jwtToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignatureAndIssuerSecurityKey(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-05-16 16:18:55.928 +05:30 [ERR] Authentication failed: Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignature(JsonWebToken jwtToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignatureAndIssuerSecurityKey(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-05-16 16:18:55.928 +05:30 [INF] Bearer was not authenticated. Failure message: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
2025-05-16 16:18:55.928 +05:30 [WRN] User is null or not authenticated
2025-05-16 16:18:55.928 +05:30 [WRN] No user ID found in token
2025-05-16 16:18:55.929 +05:30 [INF] Executing endpoint 'IdentityService.API.Controllers.AuthController.Login (IdentityService.API)'
2025-05-16 16:18:55.929 +05:30 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[IdentityService.Application.DTOs.Auth.LoginResponse]] Login(IdentityService.Application.DTOs.Auth.LoginRequest) on controller IdentityService.API.Controllers.AuthController (IdentityService.API).
2025-05-16 16:18:55.934 +05:30 [WRN] User ID not found in HttpContext.Items
2025-05-16 16:18:55.934 +05:30 [INF] Setting UserId "00000000-0000-0000-0000-000000000000" on request of type LoginCommand
2025-05-16 16:18:55.934 +05:30 [WRN] User ID not found in HttpContext.Items
2025-05-16 16:18:55.934 +05:30 [INF] Validating request of type LoginCommand
2025-05-16 16:18:55.934 +05:30 [INF] Request content: {
  "Request": {
    "Email": "<EMAIL>",
    "Password": "Admin@123",
    "PhoneNumber": "8088020878",
    "Otp": "1234",
    "RememberMe": true,
    "IsEmailLogin": true,
    "IsPhoneLogin": true
  },
  "UserId": "00000000-0000-0000-0000-000000000000"
}
2025-05-16 16:18:55.935 +05:30 [INF] Request UserId property value: "00000000-0000-0000-0000-000000000000"
2025-05-16 16:18:55.935 +05:30 [WRN] UserId is empty GUID (00000000-0000-0000-0000-000000000000)
2025-05-16 16:18:55.936 +05:30 [INF] Validation passed for request of type LoginCommand
2025-05-16 16:18:55.941 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__email_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash", t0."Id", t0."CreatedAt", t0."CreatedBy", t0."IsDeleted", t0."RoleId", t0."RowVersion", t0."UpdatedAt", t0."UpdatedBy", t0."UserId", t0."Id0", t0."CreatedAt0", t0."CreatedBy0", t0."Description", t0."IsDeleted0", t0."Name", t0."RowVersion0", t0."UpdatedAt0", t0."UpdatedBy0", t0."Id1", t0."CreatedAt1", t0."CreatedBy1", t0."IsDeleted1", t0."PermissionId", t0."RoleId0", t0."RowVersion1", t0."UpdatedAt1", t0."UpdatedBy1", t0."Id00", t0."Action", t0."CreatedAt00", t0."CreatedBy00", t0."Description0", t0."IsDeleted00", t0."Name0", t0."Resource", t0."RowVersion00", t0."UpdatedAt00", t0."UpdatedBy00"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."Email", u."EmailVerified", u."IsDeleted", u."LastLoginAt", u."PhoneNumber", u."PhoneNumberVerified", u."PrimaryBranchId", u."RefreshToken", u."RefreshTokenExpiryTime", u."RememberMe", u."RowVersion", u."SecurityStamp", u."UpdatedAt", u."UpdatedBy", u."VendorId", u."PasswordHash"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__email_0
    LIMIT 1
) AS t
LEFT JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."IsDeleted", u0."RoleId", u0."RowVersion", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", r."Id" AS "Id0", r."CreatedAt" AS "CreatedAt0", r."CreatedBy" AS "CreatedBy0", r."Description", r."IsDeleted" AS "IsDeleted0", r."Name", r."RowVersion" AS "RowVersion0", r."UpdatedAt" AS "UpdatedAt0", r."UpdatedBy" AS "UpdatedBy0", t1."Id" AS "Id1", t1."CreatedAt" AS "CreatedAt1", t1."CreatedBy" AS "CreatedBy1", t1."IsDeleted" AS "IsDeleted1", t1."PermissionId", t1."RoleId" AS "RoleId0", t1."RowVersion" AS "RowVersion1", t1."UpdatedAt" AS "UpdatedAt1", t1."UpdatedBy" AS "UpdatedBy1", t1."Id0" AS "Id00", t1."Action", t1."CreatedAt0" AS "CreatedAt00", t1."CreatedBy0" AS "CreatedBy00", t1."Description" AS "Description0", t1."IsDeleted0" AS "IsDeleted00", t1."Name" AS "Name0", t1."Resource", t1."RowVersion0" AS "RowVersion00", t1."UpdatedAt0" AS "UpdatedAt00", t1."UpdatedBy0" AS "UpdatedBy00"
    FROM "UserRoles" AS u0
    INNER JOIN "Roles" AS r ON u0."RoleId" = r."Id"
    LEFT JOIN (
        SELECT r0."Id", r0."CreatedAt", r0."CreatedBy", r0."IsDeleted", r0."PermissionId", r0."RoleId", r0."RowVersion", r0."UpdatedAt", r0."UpdatedBy", p."Id" AS "Id0", p."Action", p."CreatedAt" AS "CreatedAt0", p."CreatedBy" AS "CreatedBy0", p."Description", p."IsDeleted" AS "IsDeleted0", p."Name", p."Resource", p."RowVersion" AS "RowVersion0", p."UpdatedAt" AS "UpdatedAt0", p."UpdatedBy" AS "UpdatedBy0"
        FROM "RolePermissions" AS r0
        INNER JOIN "Permissions" AS p ON r0."PermissionId" = p."Id"
    ) AS t1 ON r."Id" = t1."RoleId"
) AS t0 ON t."Id" = t0."UserId"
ORDER BY t."Id", t0."Id", t0."Id0", t0."Id1"
2025-05-16 16:18:56.033 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT count(*)::int
FROM "UserSessions" AS u
WHERE u."UserId" = @__userId_0 AND u."Status" = 0 AND u."ExpiresAt" > now()
2025-05-16 16:18:56.034 +05:30 [WRN] User is null or not authenticated
2025-05-16 16:18:56.035 +05:30 [WRN] User is null or not authenticated
2025-05-16 16:18:56.035 +05:30 [WRN] User is null or not authenticated
2025-05-16 16:18:56.038 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = DateTime), @p2='?', @p3='?', @p4='?', @p5='?', @p6='?', @p7='?' (DbType = Boolean), @p8='?' (DbType = Boolean), @p9='?', @p10='?' (DbType = Int32), @p11='?', @p12='?' (DbType = Binary), @p13='?' (DbType = DateTime), @p14='?', @p15='?', @p16='?' (DbType = Guid), @p17='?' (DbType = Guid), @p18='?' (DbType = DateTime), @p19='?', @p20='?', @p21='?', @p22='?' (DbType = DateTime), @p23='?' (DbType = DateTime), @p24='?', @p25='?' (DbType = Boolean), @p26='?' (DbType = DateTime), @p27='?', @p28='?' (DbType = DateTime), @p29='?' (DbType = Int32), @p30='?', @p31='?' (DbType = DateTime), @p32='?', @p33='?', @p34='?' (DbType = Guid), @p40='?' (DbType = Guid), @p35='?' (DbType = DateTime), @p36='?', @p37='?' (DbType = DateTime), @p41='?' (DbType = Binary), @p38='?' (DbType = DateTime), @p39='?'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "UserLoginHistories" ("Id", "CreatedAt", "CreatedBy", "DeviceInfo", "Email", "FailureReason", "IpAddress", "IsDeleted", "IsSuccessful", "Location", "LoginMethod", "PhoneNumber", "RowVersion", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16);
INSERT INTO "UserSessions" ("Id", "CreatedAt", "CreatedBy", "DeviceInfo", "EndReason", "EndedAt", "ExpiresAt", "IpAddress", "IsDeleted", "LastActiveAt", "RefreshToken", "StartedAt", "Status", "Token", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34)
RETURNING "RowVersion";
UPDATE "Users" SET "LastLoginAt" = @p35, "RefreshToken" = @p36, "RefreshTokenExpiryTime" = @p37, "UpdatedAt" = @p38, "UpdatedBy" = @p39
WHERE "Id" = @p40 AND "RowVersion" = @p41
RETURNING "RowVersion";
2025-05-16 16:18:56.039 +05:30 [INF] Created new session "83863f57-**************-7454547a2322" for user "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:18:56.041 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Name"
FROM "UserRoles" AS u
INNER JOIN "Roles" AS r ON u."RoleId" = r."Id"
WHERE u."UserId" = @__userId_0
2025-05-16 16:18:56.043 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."Description", r."IsDeleted", r."Name", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "UserRoles" AS u
INNER JOIN "Roles" AS r ON u."RoleId" = r."Id"
WHERE u."UserId" = @__userId_0
2025-05-16 16:18:56.045 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__role_Id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "RolePermissions" AS r
INNER JOIN "Permissions" AS p ON r."PermissionId" = p."Id"
WHERE r."RoleId" = @__role_Id_0
2025-05-16 16:18:56.046 +05:30 [INF] Executing OkObjectResult, writing value of type 'IdentityService.Application.DTOs.Auth.LoginResponse'.
2025-05-16 16:18:56.046 +05:30 [INF] Executed action IdentityService.API.Controllers.AuthController.Login (IdentityService.API) in 117.4088ms
2025-05-16 16:18:56.047 +05:30 [INF] Executed endpoint 'IdentityService.API.Controllers.AuthController.Login (IdentityService.API)'
2025-05-16 16:18:56.047 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5263/api/Auth/login - 200 null application/json; charset=utf-8 190.7781ms
2025-05-16 16:19:16.639 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5263/api/Users/<USER>/roles - application/json 65
2025-05-16 16:19:16.639 +05:30 [INF] Token received and is being processed
2025-05-16 16:19:16.781 +05:30 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignature(JsonWebToken jwtToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignatureAndIssuerSecurityKey(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-05-16 16:19:16.781 +05:30 [ERR] Authentication failed: Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignature(JsonWebToken jwtToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignatureAndIssuerSecurityKey(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-05-16 16:19:16.781 +05:30 [INF] Bearer was not authenticated. Failure message: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
2025-05-16 16:19:16.791 +05:30 [INF] Executed DbCommand (8ms) [Parameters=[@__token_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Token" = @__token_0
LIMIT 1
2025-05-16 16:19:16.791 +05:30 [WRN] Session with token eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.H3zbS8UNOFhh5k3SyZ1jjfsdjFoZmg09P5uKbtIiufw",   "refreshToken": "123a6ea6-61b8-49f7-a3d9-a970af235cb2 not found
2025-05-16 16:19:16.791 +05:30 [WRN] Invalid session for token eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.H3zbS8UNOFhh5k3SyZ1jjfsdjFoZmg09P5uKbtIiufw",   "refreshToken": "123a6ea6-61b8-49f7-a3d9-a970af235cb2
2025-05-16 16:19:16.791 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5263/api/Users/<USER>/roles - 401 null application/json; charset=utf-8 152.3522ms
2025-05-16 16:19:36.544 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5263/api/Auth/logout?logoutAllSessions=false - null 0
2025-05-16 16:19:36.544 +05:30 [INF] Token received and is being processed
2025-05-16 16:19:36.630 +05:30 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignature(JsonWebToken jwtToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignatureAndIssuerSecurityKey(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-05-16 16:19:36.630 +05:30 [ERR] Authentication failed: Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignature(JsonWebToken jwtToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignatureAndIssuerSecurityKey(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-05-16 16:19:36.630 +05:30 [INF] Bearer was not authenticated. Failure message: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
2025-05-16 16:19:36.639 +05:30 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-05-16 16:19:36.645 +05:30 [WRN] OnChallenge: invalid_token
2025-05-16 16:19:36.646 +05:30 [INF] AuthenticationScheme: Bearer was challenged.
2025-05-16 16:19:36.648 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5263/api/Auth/logout?logoutAllSessions=false - 401 0 null 104.5588ms
2025-05-16 16:20:22.025 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'AuditLog'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-16 16:20:22.069 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'PasswordResetToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-16 16:20:22.069 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserBranch'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-16 16:20:22.069 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserRole'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-16 16:20:22.069 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserSession'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-16 16:20:22.070 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserSubscription'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-16 16:20:22.783 +05:30 [INF] Executed DbCommand (71ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-05-16 16:20:22.801 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-05-16 16:20:22.890 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-05-16 16:20:22.969 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-05-16 16:20:22.970 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-05-16 16:20:22.983 +05:30 [INF] No migrations were applied. The database is already up to date.
2025-05-16 16:20:23.021 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-16 16:20:23.195 +05:30 [INF] Configured endpoint SubscriptionCreated, Consumer: IdentityService.Infrastructure.Messaging.Consumers.SubscriptionCreatedConsumer
2025-05-16 16:20:23.198 +05:30 [INF] Configured endpoint SubscriptionStatusChanged, Consumer: IdentityService.Infrastructure.Messaging.Consumers.SubscriptionStatusChangedConsumer
2025-05-16 16:20:23.200 +05:30 [INF] Configured endpoint VendorRegistered, Consumer: IdentityService.Infrastructure.Messaging.Consumers.VendorRegisteredConsumer
2025-05-16 16:20:23.353 +05:30 [DBG] Starting bus instances: IBus
2025-05-16 16:20:23.357 +05:30 [DBG] Starting bus: "rabbitmq://localhost/"
2025-05-16 16:20:23.390 +05:30 [INF] Session cleanup service is starting
2025-05-16 16:20:23.392 +05:30 [DBG] Starting session cleanup
2025-05-16 16:20:23.429 +05:30 [DBG] Connect: guest@localhost:5672/
2025-05-16 16:20:23.535 +05:30 [DBG] Connected: guest@localhost:5672/ (address: amqp://localhost:5672, local: 60540)
2025-05-16 16:20:23.574 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/DESKTOPI7D4M6U_IdentityServiceAPI_bus_dynyyyfpkikqn9q6bdq3e35irc?temporary=true"
2025-05-16 16:20:23.609 +05:30 [DBG] Declare queue: name: VendorRegistered, durable, consumer-count: 0 message-count: 0
2025-05-16 16:20:23.609 +05:30 [DBG] Declare queue: name: SubscriptionCreated, durable, consumer-count: 0 message-count: 0
2025-05-16 16:20:23.609 +05:30 [DBG] Declare queue: name: SubscriptionStatusChanged, durable, consumer-count: 0 message-count: 0
2025-05-16 16:20:23.618 +05:30 [DBG] Declare exchange: name: SubscriptionCreated, type: fanout, durable
2025-05-16 16:20:23.618 +05:30 [DBG] Declare exchange: name: SubscriptionStatusChanged, type: fanout, durable
2025-05-16 16:20:23.618 +05:30 [DBG] Declare exchange: name: VendorRegistered, type: fanout, durable
2025-05-16 16:20:23.625 +05:30 [DBG] Declare exchange: name: VMSContracts.Subscription.Events:SubscriptionStatusChanged, type: fanout, durable
2025-05-16 16:20:23.625 +05:30 [DBG] Declare exchange: name: VMSContracts.Tenant.Events:VendorRegistered, type: fanout, durable
2025-05-16 16:20:23.625 +05:30 [DBG] Declare exchange: name: VMSContracts.Subscription.Events:SubscriptionCreated, type: fanout, durable
2025-05-16 16:20:23.668 +05:30 [DBG] Bind queue: source: SubscriptionStatusChanged, destination: SubscriptionStatusChanged
2025-05-16 16:20:23.668 +05:30 [DBG] Bind queue: source: SubscriptionCreated, destination: SubscriptionCreated
2025-05-16 16:20:23.668 +05:30 [DBG] Bind queue: source: VendorRegistered, destination: VendorRegistered
2025-05-16 16:20:23.682 +05:30 [DBG] Bind exchange: source: VMSContracts.Subscription.Events:SubscriptionCreated, destination: SubscriptionCreated
2025-05-16 16:20:23.682 +05:30 [DBG] Bind exchange: source: VMSContracts.Subscription.Events:SubscriptionStatusChanged, destination: SubscriptionStatusChanged
2025-05-16 16:20:23.682 +05:30 [DBG] Bind exchange: source: VMSContracts.Tenant.Events:VendorRegistered, destination: VendorRegistered
2025-05-16 16:20:23.725 +05:30 [DBG] Consumer Ok: "rabbitmq://localhost/SubscriptionCreated" - amq.ctag-hxihkN2qsagnUGvWZd7C7w
2025-05-16 16:20:23.725 +05:30 [DBG] Consumer Ok: "rabbitmq://localhost/SubscriptionStatusChanged" - amq.ctag-EHLZzb9leAC2T52ck8V-cA
2025-05-16 16:20:23.725 +05:30 [DBG] Consumer Ok: "rabbitmq://localhost/VendorRegistered" - amq.ctag-wcG2JHjup6RiitQZKMLmjw
2025-05-16 16:20:23.727 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/VendorRegistered"
2025-05-16 16:20:23.727 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/SubscriptionStatusChanged"
2025-05-16 16:20:23.727 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/SubscriptionCreated"
2025-05-16 16:20:23.732 +05:30 [INF] Bus started: "rabbitmq://localhost/"
2025-05-16 16:20:23.941 +05:30 [INF] Executed DbCommand (22ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-16 16:20:23.999 +05:30 [INF] Executed DbCommand (4ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-16 16:20:24.036 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-16 16:20:24.037 +05:30 [DBG] Session cleanup completed
2025-05-16 16:20:24.050 +05:30 [INF] Now listening on: http://localhost:5263
2025-05-16 16:20:24.051 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-05-16 16:20:24.051 +05:30 [INF] Hosting environment: Development
2025-05-16 16:20:24.051 +05:30 [INF] Content root path: E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.API
2025-05-16 16:20:24.353 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/swagger/index.html - null null
2025-05-16 16:20:24.531 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/swagger/index.html - 200 null text/html;charset=utf-8 181.4059ms
2025-05-16 16:20:24.654 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/swagger/v1/swagger.json - null null
2025-05-16 16:20:24.874 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 219.3302ms
2025-05-16 16:20:59.826 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5263/api/Auth/login - application/json 143
2025-05-16 16:20:59.841 +05:30 [WRN] Failed to determine the https port for redirect.
2025-05-16 16:20:59.933 +05:30 [INF] Token received and is being processed
2025-05-16 16:21:00.047 +05:30 [WRN] User is null or not authenticated
2025-05-16 16:21:00.048 +05:30 [WRN] No user ID found in token
2025-05-16 16:21:00.050 +05:30 [INF] Executing endpoint 'IdentityService.API.Controllers.AuthController.Login (IdentityService.API)'
2025-05-16 16:21:00.074 +05:30 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[IdentityService.Application.DTOs.Auth.LoginResponse]] Login(IdentityService.Application.DTOs.Auth.LoginRequest) on controller IdentityService.API.Controllers.AuthController (IdentityService.API).
2025-05-16 16:21:00.176 +05:30 [WRN] User ID not found in HttpContext.Items
2025-05-16 16:21:00.177 +05:30 [INF] Setting UserId "00000000-0000-0000-0000-000000000000" on request of type LoginCommand
2025-05-16 16:21:00.178 +05:30 [WRN] User ID not found in HttpContext.Items
2025-05-16 16:21:00.183 +05:30 [INF] Validating request of type LoginCommand
2025-05-16 16:21:00.195 +05:30 [INF] Request content: {
  "Request": {
    "Email": "<EMAIL>",
    "Password": "Admin@123",
    "PhoneNumber": "8088020878",
    "Otp": "1234",
    "RememberMe": true,
    "IsEmailLogin": true,
    "IsPhoneLogin": true
  },
  "UserId": "00000000-0000-0000-0000-000000000000"
}
2025-05-16 16:21:00.196 +05:30 [INF] Request UserId property value: "00000000-0000-0000-0000-000000000000"
2025-05-16 16:21:00.196 +05:30 [WRN] UserId is empty GUID (00000000-0000-0000-0000-000000000000)
2025-05-16 16:21:00.216 +05:30 [INF] Validation passed for request of type LoginCommand
2025-05-16 16:21:00.311 +05:30 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-05-16 16:21:00.332 +05:30 [INF] Executed DbCommand (12ms) [Parameters=[@__email_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash", t0."Id", t0."CreatedAt", t0."CreatedBy", t0."IsDeleted", t0."RoleId", t0."RowVersion", t0."UpdatedAt", t0."UpdatedBy", t0."UserId", t0."Id0", t0."CreatedAt0", t0."CreatedBy0", t0."Description", t0."IsDeleted0", t0."Name", t0."RowVersion0", t0."UpdatedAt0", t0."UpdatedBy0", t0."Id1", t0."CreatedAt1", t0."CreatedBy1", t0."IsDeleted1", t0."PermissionId", t0."RoleId0", t0."RowVersion1", t0."UpdatedAt1", t0."UpdatedBy1", t0."Id00", t0."Action", t0."CreatedAt00", t0."CreatedBy00", t0."Description0", t0."IsDeleted00", t0."Name0", t0."Resource", t0."RowVersion00", t0."UpdatedAt00", t0."UpdatedBy00"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."Email", u."EmailVerified", u."IsDeleted", u."LastLoginAt", u."PhoneNumber", u."PhoneNumberVerified", u."PrimaryBranchId", u."RefreshToken", u."RefreshTokenExpiryTime", u."RememberMe", u."RowVersion", u."SecurityStamp", u."UpdatedAt", u."UpdatedBy", u."VendorId", u."PasswordHash"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__email_0
    LIMIT 1
) AS t
LEFT JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."IsDeleted", u0."RoleId", u0."RowVersion", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", r."Id" AS "Id0", r."CreatedAt" AS "CreatedAt0", r."CreatedBy" AS "CreatedBy0", r."Description", r."IsDeleted" AS "IsDeleted0", r."Name", r."RowVersion" AS "RowVersion0", r."UpdatedAt" AS "UpdatedAt0", r."UpdatedBy" AS "UpdatedBy0", t1."Id" AS "Id1", t1."CreatedAt" AS "CreatedAt1", t1."CreatedBy" AS "CreatedBy1", t1."IsDeleted" AS "IsDeleted1", t1."PermissionId", t1."RoleId" AS "RoleId0", t1."RowVersion" AS "RowVersion1", t1."UpdatedAt" AS "UpdatedAt1", t1."UpdatedBy" AS "UpdatedBy1", t1."Id0" AS "Id00", t1."Action", t1."CreatedAt0" AS "CreatedAt00", t1."CreatedBy0" AS "CreatedBy00", t1."Description" AS "Description0", t1."IsDeleted0" AS "IsDeleted00", t1."Name" AS "Name0", t1."Resource", t1."RowVersion0" AS "RowVersion00", t1."UpdatedAt0" AS "UpdatedAt00", t1."UpdatedBy0" AS "UpdatedBy00"
    FROM "UserRoles" AS u0
    INNER JOIN "Roles" AS r ON u0."RoleId" = r."Id"
    LEFT JOIN (
        SELECT r0."Id", r0."CreatedAt", r0."CreatedBy", r0."IsDeleted", r0."PermissionId", r0."RoleId", r0."RowVersion", r0."UpdatedAt", r0."UpdatedBy", p."Id" AS "Id0", p."Action", p."CreatedAt" AS "CreatedAt0", p."CreatedBy" AS "CreatedBy0", p."Description", p."IsDeleted" AS "IsDeleted0", p."Name", p."Resource", p."RowVersion" AS "RowVersion0", p."UpdatedAt" AS "UpdatedAt0", p."UpdatedBy" AS "UpdatedBy0"
        FROM "RolePermissions" AS r0
        INNER JOIN "Permissions" AS p ON r0."PermissionId" = p."Id"
    ) AS t1 ON r."Id" = t1."RoleId"
) AS t0 ON t."Id" = t0."UserId"
ORDER BY t."Id", t0."Id", t0."Id0", t0."Id1"
2025-05-16 16:21:00.885 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT count(*)::int
FROM "UserSessions" AS u
WHERE u."UserId" = @__userId_0 AND u."Status" = 0 AND u."ExpiresAt" > now()
2025-05-16 16:21:00.894 +05:30 [WRN] User is null or not authenticated
2025-05-16 16:21:01.045 +05:30 [WRN] User is null or not authenticated
2025-05-16 16:21:01.045 +05:30 [WRN] User is null or not authenticated
2025-05-16 16:21:01.212 +05:30 [INF] Executed DbCommand (7ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = DateTime), @p2='?', @p3='?', @p4='?', @p5='?', @p6='?', @p7='?' (DbType = Boolean), @p8='?' (DbType = Boolean), @p9='?', @p10='?' (DbType = Int32), @p11='?', @p12='?' (DbType = Binary), @p13='?' (DbType = DateTime), @p14='?', @p15='?', @p16='?' (DbType = Guid), @p17='?' (DbType = Guid), @p18='?' (DbType = DateTime), @p19='?', @p20='?', @p21='?', @p22='?' (DbType = DateTime), @p23='?' (DbType = DateTime), @p24='?', @p25='?' (DbType = Boolean), @p26='?' (DbType = DateTime), @p27='?', @p28='?' (DbType = DateTime), @p29='?' (DbType = Int32), @p30='?', @p31='?' (DbType = DateTime), @p32='?', @p33='?', @p34='?' (DbType = Guid), @p39='?' (DbType = Guid), @p35='?' (DbType = DateTime), @p36='?', @p37='?' (DbType = DateTime), @p40='?' (DbType = Binary), @p38='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "UserLoginHistories" ("Id", "CreatedAt", "CreatedBy", "DeviceInfo", "Email", "FailureReason", "IpAddress", "IsDeleted", "IsSuccessful", "Location", "LoginMethod", "PhoneNumber", "RowVersion", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16);
INSERT INTO "UserSessions" ("Id", "CreatedAt", "CreatedBy", "DeviceInfo", "EndReason", "EndedAt", "ExpiresAt", "IpAddress", "IsDeleted", "LastActiveAt", "RefreshToken", "StartedAt", "Status", "Token", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34)
RETURNING "RowVersion";
UPDATE "Users" SET "LastLoginAt" = @p35, "RefreshToken" = @p36, "RefreshTokenExpiryTime" = @p37, "UpdatedAt" = @p38
WHERE "Id" = @p39 AND "RowVersion" = @p40
RETURNING "RowVersion";
2025-05-16 16:21:01.247 +05:30 [INF] Created new session "0ca94f3a-f3ba-41bb-bf71-16253f3ae9f1" for user "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:21:01.258 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Name"
FROM "UserRoles" AS u
INNER JOIN "Roles" AS r ON u."RoleId" = r."Id"
WHERE u."UserId" = @__userId_0
2025-05-16 16:21:01.269 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."Description", r."IsDeleted", r."Name", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "UserRoles" AS u
INNER JOIN "Roles" AS r ON u."RoleId" = r."Id"
WHERE u."UserId" = @__userId_0
2025-05-16 16:21:01.283 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__role_Id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "RolePermissions" AS r
INNER JOIN "Permissions" AS p ON r."PermissionId" = p."Id"
WHERE r."RoleId" = @__role_Id_0
2025-05-16 16:21:01.298 +05:30 [INF] Executing OkObjectResult, writing value of type 'IdentityService.Application.DTOs.Auth.LoginResponse'.
2025-05-16 16:21:01.320 +05:30 [INF] Executed action IdentityService.API.Controllers.AuthController.Login (IdentityService.API) in 1241.7835ms
2025-05-16 16:21:01.322 +05:30 [INF] Executed endpoint 'IdentityService.API.Controllers.AuthController.Login (IdentityService.API)'
2025-05-16 16:21:01.328 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5263/api/Auth/login - 200 null application/json; charset=utf-8 1501.4464ms
2025-05-16 16:24:41.235 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5263/api/Users/<USER>/roles - application/json 65
2025-05-16 16:24:41.251 +05:30 [INF] Token received and is being processed
2025-05-16 16:24:41.417 +05:30 [INF] Token validated successfully with claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: ff90e7bd-53ca-4970-adc5-ee14dc83dedf, security_stamp: 638820282925527280, http://schemas.microsoft.com/ws/2008/06/identity/claims/role: BranchManager, permission: test.permission, nbf: 1747392660, exp: 1747396260, iss: identity-service, aud: vms-api
2025-05-16 16:24:41.418 +05:30 [INF] NameIdentifier claim found: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e
2025-05-16 16:24:41.581 +05:30 [INF] Executed DbCommand (11ms) [Parameters=[@__get_Item_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."Email", u."EmailVerified", u."IsDeleted", u."LastLoginAt", u."PhoneNumber", u."PhoneNumberVerified", u."PrimaryBranchId", u."RefreshToken", u."RefreshTokenExpiryTime", u."RememberMe", u."RowVersion", u."SecurityStamp", u."UpdatedAt", u."UpdatedBy", u."VendorId", u."PasswordHash"
FROM "Users" AS u
WHERE NOT (u."IsDeleted") AND u."Id" = @__get_Item_0
LIMIT 1
2025-05-16 16:24:41.612 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__token_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Token" = @__token_0
LIMIT 1
2025-05-16 16:24:41.620 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: ff90e7bd-53ca-4970-adc5-ee14dc83dedf, security_stamp: 638820282925527280, http://schemas.microsoft.com/ws/2008/06/identity/claims/role: BranchManager, permission: test.permission, nbf: 1747392660, exp: 1747396260, iss: identity-service, aud: vms-api
2025-05-16 16:24:41.621 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:24:41.629 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@p17='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?', @p2='?', @p3='?', @p4='?' (DbType = DateTime), @p5='?' (DbType = DateTime), @p6='?', @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime), @p9='?', @p18='?' (DbType = Binary), @p10='?' (DbType = DateTime), @p11='?' (DbType = Int32), @p12='?', @p13='?' (DbType = DateTime), @p14='?', @p15='?', @p16='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
UPDATE "UserSessions" SET "CreatedAt" = @p0, "CreatedBy" = @p1, "DeviceInfo" = @p2, "EndReason" = @p3, "EndedAt" = @p4, "ExpiresAt" = @p5, "IpAddress" = @p6, "IsDeleted" = @p7, "LastActiveAt" = @p8, "RefreshToken" = @p9, "StartedAt" = @p10, "Status" = @p11, "Token" = @p12, "UpdatedAt" = @p13, "UpdatedBy" = @p14, "UserAgent" = @p15, "UserId" = @p16
WHERE "Id" = @p17 AND "RowVersion" IS NULL
RETURNING "RowVersion";
2025-05-16 16:24:41.636 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__token_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Token" = @__token_0
LIMIT 1
2025-05-16 16:24:41.639 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: ff90e7bd-53ca-4970-adc5-ee14dc83dedf, security_stamp: 638820282925527280, http://schemas.microsoft.com/ws/2008/06/identity/claims/role: BranchManager, permission: test.permission, nbf: 1747392660, exp: 1747396260, iss: identity-service, aud: vms-api
2025-05-16 16:24:41.641 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:24:41.647 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@p17='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?', @p2='?', @p3='?', @p4='?' (DbType = DateTime), @p5='?' (DbType = DateTime), @p6='?', @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime), @p9='?', @p18='?' (DbType = Binary), @p10='?' (DbType = DateTime), @p11='?' (DbType = Int32), @p12='?', @p13='?' (DbType = DateTime), @p14='?', @p15='?', @p16='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
UPDATE "UserSessions" SET "CreatedAt" = @p0, "CreatedBy" = @p1, "DeviceInfo" = @p2, "EndReason" = @p3, "EndedAt" = @p4, "ExpiresAt" = @p5, "IpAddress" = @p6, "IsDeleted" = @p7, "LastActiveAt" = @p8, "RefreshToken" = @p9, "StartedAt" = @p10, "Status" = @p11, "Token" = @p12, "UpdatedAt" = @p13, "UpdatedBy" = @p14, "UserAgent" = @p15, "UserId" = @p16
WHERE "Id" = @p17 AND "RowVersion" IS NULL
RETURNING "RowVersion";
2025-05-16 16:24:41.648 +05:30 [DBG] Updated last active time for session "0ca94f3a-f3ba-41bb-bf71-16253f3ae9f1"
2025-05-16 16:24:41.648 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: ff90e7bd-53ca-4970-adc5-ee14dc83dedf, security_stamp: 638820282925527280, http://schemas.microsoft.com/ws/2008/06/identity/claims/role: BranchManager, permission: test.permission, nbf: 1747392660, exp: 1747396260, iss: identity-service, aud: vms-api
2025-05-16 16:24:41.649 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:24:41.649 +05:30 [INF] User ID from token: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:24:41.650 +05:30 [INF] Executing endpoint 'IdentityService.API.Controllers.UsersController.AssignRoles (IdentityService.API)'
2025-05-16 16:24:41.664 +05:30 [INF] Route matched with {action = "AssignRoles", controller = "Users"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[IdentityService.Application.DTOs.Users.UserResponse]] AssignRoles(System.Guid, IdentityService.Application.DTOs.Users.AssignRoleRequest) on controller IdentityService.API.Controllers.UsersController (IdentityService.API).
2025-05-16 16:24:48.099 +05:30 [INF] Retrieved user ID from HttpContext.Items: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:24:48.100 +05:30 [INF] Setting UserId "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e" on request of type AssignRoleCommand
2025-05-16 16:24:48.100 +05:30 [INF] Retrieved user ID from HttpContext.Items: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:24:48.101 +05:30 [INF] Validating request of type AssignRoleCommand
2025-05-16 16:24:48.105 +05:30 [INF] Request content: {
  "UserId": "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e",
  "Request": {
    "RoleIds": [
      "0247e353-d1fb-4118-b630-fecff038de1e"
    ]
  }
}
2025-05-16 16:24:48.106 +05:30 [INF] Request UserId property value: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:24:48.106 +05:30 [INF] Validation passed for request of type AssignRoleCommand
2025-05-16 16:24:48.114 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: ff90e7bd-53ca-4970-adc5-ee14dc83dedf, security_stamp: 638820282925527280, http://schemas.microsoft.com/ws/2008/06/identity/claims/role: BranchManager, permission: test.permission, nbf: 1747392660, exp: 1747396260, iss: identity-service, aud: vms-api
2025-05-16 16:24:48.114 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:24:48.116 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Name"
FROM "UserRoles" AS u
INNER JOIN "Roles" AS r ON u."RoleId" = r."Id"
WHERE u."UserId" = @__userId_0
2025-05-16 16:24:48.153 +05:30 [INF] Executed DbCommand (4ms) [Parameters=[@p0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
DELETE FROM "UserRoles" WHERE "UserId" = @p0
2025-05-16 16:24:48.163 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__get_Item_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."Description", r."IsDeleted", r."Name", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "Roles" AS r
WHERE r."Id" = @__get_Item_0
LIMIT 1
2025-05-16 16:24:48.167 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: ff90e7bd-53ca-4970-adc5-ee14dc83dedf, security_stamp: 638820282925527280, http://schemas.microsoft.com/ws/2008/06/identity/claims/role: BranchManager, permission: test.permission, nbf: 1747392660, exp: 1747396260, iss: identity-service, aud: vms-api
2025-05-16 16:24:48.167 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:24:48.182 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = DateTime), @p2='?', @p3='?' (DbType = Boolean), @p4='?' (DbType = Guid), @p5='?' (DbType = Binary), @p6='?' (DbType = DateTime), @p7='?', @p8='?' (DbType = Guid), @p25='?' (DbType = Guid), @p9='?' (DbType = DateTime), @p10='?', @p11='?', @p12='?' (DbType = Boolean), @p13='?' (DbType = Boolean), @p14='?' (DbType = DateTime), @p15='?', @p16='?' (DbType = Boolean), @p17='?' (DbType = Guid), @p18='?', @p19='?' (DbType = DateTime), @p20='?' (DbType = Boolean), @p26='?' (DbType = Binary), @p21='?' (DbType = DateTime), @p22='?' (DbType = DateTime), @p23='?', @p24='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "UserRoles" ("Id", "CreatedAt", "CreatedBy", "IsDeleted", "RoleId", "RowVersion", "UpdatedAt", "UpdatedBy", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8);
UPDATE "Users" SET "CreatedAt" = @p9, "CreatedBy" = @p10, "Email" = @p11, "EmailVerified" = @p12, "IsDeleted" = @p13, "LastLoginAt" = @p14, "PhoneNumber" = @p15, "PhoneNumberVerified" = @p16, "PrimaryBranchId" = @p17, "RefreshToken" = @p18, "RefreshTokenExpiryTime" = @p19, "RememberMe" = @p20, "SecurityStamp" = @p21, "UpdatedAt" = @p22, "UpdatedBy" = @p23, "VendorId" = @p24
WHERE "Id" = @p25 AND "RowVersion" = @p26
RETURNING "RowVersion";
2025-05-16 16:24:48.193 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Name"
FROM "UserRoles" AS u
INNER JOIN "Roles" AS r ON u."RoleId" = r."Id"
WHERE u."UserId" = @__userId_0
2025-05-16 16:24:48.193 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: ff90e7bd-53ca-4970-adc5-ee14dc83dedf, security_stamp: 638820282925527280, http://schemas.microsoft.com/ws/2008/06/identity/claims/role: BranchManager, permission: test.permission, nbf: 1747392660, exp: 1747396260, iss: identity-service, aud: vms-api
2025-05-16 16:24:48.194 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:24:48.196 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e, jti: ff90e7bd-53ca-4970-adc5-ee14dc83dedf, security_stamp: 638820282925527280, http://schemas.microsoft.com/ws/2008/06/identity/claims/role: BranchManager, permission: test.permission, nbf: 1747392660, exp: 1747396260, iss: identity-service, aud: vms-api
2025-05-16 16:24:48.196 +05:30 [INF] Successfully extracted user ID: "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:24:48.332 +05:30 [INF] Executed DbCommand (53ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?' (DbType = DateTime), @p4='?', @p5='?', @p6='?', @p7='?', @p8='?' (DbType = Boolean), @p9='?', @p10='?', @p11='?' (DbType = Binary), @p12='?' (DbType = DateTime), @p13='?', @p14='?', @p15='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "AuditLogs" ("Id", "Action", "AffectedColumns", "CreatedAt", "CreatedBy", "EntityId", "EntityName", "IpAddress", "IsDeleted", "NewValues", "OldValues", "RowVersion", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
2025-05-16 16:24:53.465 +05:30 [INF] Executing OkObjectResult, writing value of type 'IdentityService.Application.DTOs.Users.UserResponse'.
2025-05-16 16:24:53.474 +05:30 [INF] Executed action IdentityService.API.Controllers.UsersController.AssignRoles (IdentityService.API) in 11810.4832ms
2025-05-16 16:24:53.474 +05:30 [INF] Executed endpoint 'IdentityService.API.Controllers.UsersController.AssignRoles (IdentityService.API)'
2025-05-16 16:24:53.475 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5263/api/Users/<USER>/roles - 200 null application/json; charset=utf-8 12240.573ms
2025-05-16 16:25:24.044 +05:30 [DBG] Starting session cleanup
2025-05-16 16:25:24.070 +05:30 [INF] Executed DbCommand (10ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-16 16:25:24.080 +05:30 [INF] Executed DbCommand (6ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-16 16:25:24.087 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-16 16:25:24.087 +05:30 [DBG] Session cleanup completed
2025-05-16 16:25:44.224 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5263/api/users - application/json; charset=utf-8 null
2025-05-16 16:25:44.231 +05:30 [INF] Token received and is being processed
2025-05-16 16:25:44.233 +05:30 [WRN] User is null or not authenticated
2025-05-16 16:25:44.233 +05:30 [WRN] No user ID found in token
2025-05-16 16:25:44.233 +05:30 [INF] Executing endpoint 'IdentityService.API.Controllers.UsersController.CreateUser (IdentityService.API)'
2025-05-16 16:25:44.238 +05:30 [INF] Route matched with {action = "CreateUser", controller = "Users"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[IdentityService.Application.DTOs.Users.UserResponse]] CreateUser(IdentityService.Application.DTOs.Users.CreateUserRequest) on controller IdentityService.API.Controllers.UsersController (IdentityService.API).
2025-05-16 16:25:44.256 +05:30 [WRN] User ID not found in HttpContext.Items
2025-05-16 16:25:44.257 +05:30 [INF] Setting UserId "00000000-0000-0000-0000-000000000000" on request of type CreateUserCommand
2025-05-16 16:25:44.257 +05:30 [WRN] User ID not found in HttpContext.Items
2025-05-16 16:25:44.258 +05:30 [INF] Validating request of type CreateUserCommand
2025-05-16 16:25:44.261 +05:30 [INF] Request content: {
  "Request": {
    "Email": "<EMAIL>",
    "PhoneNumber": "2118527952",
    "Password": "Test@123456",
    "ConfirmPassword": "Test@123456",
    "RoleIds": null,
    "IsActive": true
  },
  "UserId": "00000000-0000-0000-0000-000000000000"
}
2025-05-16 16:25:44.261 +05:30 [INF] Request UserId property value: "00000000-0000-0000-0000-000000000000"
2025-05-16 16:25:44.261 +05:30 [WRN] UserId is empty GUID (00000000-0000-0000-0000-000000000000)
2025-05-16 16:25:44.261 +05:30 [INF] Validation passed for request of type CreateUserCommand
2025-05-16 16:25:44.454 +05:30 [INF] Executed DbCommand (17ms) [Parameters=[@__email_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__email_0)
2025-05-16 16:25:44.466 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__phoneNumber_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."PhoneNumber" = @__phoneNumber_0)
2025-05-16 16:25:44.665 +05:30 [WRN] User is null or not authenticated
2025-05-16 16:25:44.677 +05:30 [WRN] User is null or not authenticated
2025-05-16 16:25:44.686 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = DateTime), @p2='?', @p3='?', @p4='?' (DbType = DateTime), @p5='?', @p6='?' (DbType = Guid), @p7='?', @p8='?' (DbType = DateTime), @p9='?' (DbType = DateTime), @p10='?' (DbType = DateTime), @p11='?', @p12='?' (DbType = Guid), @p13='?'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Users" ("Id", "CreatedAt", "CreatedBy", "Email", "LastLoginAt", "PhoneNumber", "PrimaryBranchId", "RefreshToken", "RefreshTokenExpiryTime", "SecurityStamp", "UpdatedAt", "UpdatedBy", "VendorId", "PasswordHash")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13)
RETURNING "EmailVerified", "IsDeleted", "PhoneNumberVerified", "RememberMe", "RowVersion";
2025-05-16 16:25:44.701 +05:30 [INF] Executed DbCommand (7ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Name"
FROM "UserRoles" AS u
INNER JOIN "Roles" AS r ON u."RoleId" = r."Id"
WHERE u."UserId" = @__userId_0
2025-05-16 16:25:44.705 +05:30 [INF] Executing CreatedAtActionResult, writing value of type 'IdentityService.Application.DTOs.Users.UserResponse'.
2025-05-16 16:25:44.746 +05:30 [INF] Executed action IdentityService.API.Controllers.UsersController.CreateUser (IdentityService.API) in 508.2999ms
2025-05-16 16:25:44.746 +05:30 [INF] Executed endpoint 'IdentityService.API.Controllers.UsersController.CreateUser (IdentityService.API)'
2025-05-16 16:25:44.747 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5263/api/users - 201 null application/json; charset=utf-8 522.4485ms
2025-05-16 16:25:44.903 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5263/api/auth/login - application/json; charset=utf-8 null
2025-05-16 16:25:44.906 +05:30 [INF] Token received and is being processed
2025-05-16 16:25:44.907 +05:30 [WRN] User is null or not authenticated
2025-05-16 16:25:44.907 +05:30 [WRN] No user ID found in token
2025-05-16 16:25:44.907 +05:30 [INF] Executing endpoint 'IdentityService.API.Controllers.AuthController.Login (IdentityService.API)'
2025-05-16 16:25:44.907 +05:30 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[IdentityService.Application.DTOs.Auth.LoginResponse]] Login(IdentityService.Application.DTOs.Auth.LoginRequest) on controller IdentityService.API.Controllers.AuthController (IdentityService.API).
2025-05-16 16:25:44.915 +05:30 [WRN] User ID not found in HttpContext.Items
2025-05-16 16:25:44.915 +05:30 [INF] Setting UserId "00000000-0000-0000-0000-000000000000" on request of type LoginCommand
2025-05-16 16:25:44.915 +05:30 [WRN] User ID not found in HttpContext.Items
2025-05-16 16:25:44.915 +05:30 [INF] Validating request of type LoginCommand
2025-05-16 16:25:44.915 +05:30 [INF] Request content: {
  "Request": {
    "Email": "<EMAIL>",
    "Password": "Test@123456",
    "PhoneNumber": null,
    "Otp": null,
    "RememberMe": false,
    "IsEmailLogin": true,
    "IsPhoneLogin": false
  },
  "UserId": "00000000-0000-0000-0000-000000000000"
}
2025-05-16 16:25:44.915 +05:30 [INF] Request UserId property value: "00000000-0000-0000-0000-000000000000"
2025-05-16 16:25:44.916 +05:30 [WRN] UserId is empty GUID (00000000-0000-0000-0000-000000000000)
2025-05-16 16:25:44.917 +05:30 [INF] Validation passed for request of type LoginCommand
2025-05-16 16:25:44.931 +05:30 [INF] Executed DbCommand (11ms) [Parameters=[@__email_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash", t0."Id", t0."CreatedAt", t0."CreatedBy", t0."IsDeleted", t0."RoleId", t0."RowVersion", t0."UpdatedAt", t0."UpdatedBy", t0."UserId", t0."Id0", t0."CreatedAt0", t0."CreatedBy0", t0."Description", t0."IsDeleted0", t0."Name", t0."RowVersion0", t0."UpdatedAt0", t0."UpdatedBy0", t0."Id1", t0."CreatedAt1", t0."CreatedBy1", t0."IsDeleted1", t0."PermissionId", t0."RoleId0", t0."RowVersion1", t0."UpdatedAt1", t0."UpdatedBy1", t0."Id00", t0."Action", t0."CreatedAt00", t0."CreatedBy00", t0."Description0", t0."IsDeleted00", t0."Name0", t0."Resource", t0."RowVersion00", t0."UpdatedAt00", t0."UpdatedBy00"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."Email", u."EmailVerified", u."IsDeleted", u."LastLoginAt", u."PhoneNumber", u."PhoneNumberVerified", u."PrimaryBranchId", u."RefreshToken", u."RefreshTokenExpiryTime", u."RememberMe", u."RowVersion", u."SecurityStamp", u."UpdatedAt", u."UpdatedBy", u."VendorId", u."PasswordHash"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__email_0
    LIMIT 1
) AS t
LEFT JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."IsDeleted", u0."RoleId", u0."RowVersion", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", r."Id" AS "Id0", r."CreatedAt" AS "CreatedAt0", r."CreatedBy" AS "CreatedBy0", r."Description", r."IsDeleted" AS "IsDeleted0", r."Name", r."RowVersion" AS "RowVersion0", r."UpdatedAt" AS "UpdatedAt0", r."UpdatedBy" AS "UpdatedBy0", t1."Id" AS "Id1", t1."CreatedAt" AS "CreatedAt1", t1."CreatedBy" AS "CreatedBy1", t1."IsDeleted" AS "IsDeleted1", t1."PermissionId", t1."RoleId" AS "RoleId0", t1."RowVersion" AS "RowVersion1", t1."UpdatedAt" AS "UpdatedAt1", t1."UpdatedBy" AS "UpdatedBy1", t1."Id0" AS "Id00", t1."Action", t1."CreatedAt0" AS "CreatedAt00", t1."CreatedBy0" AS "CreatedBy00", t1."Description" AS "Description0", t1."IsDeleted0" AS "IsDeleted00", t1."Name" AS "Name0", t1."Resource", t1."RowVersion0" AS "RowVersion00", t1."UpdatedAt0" AS "UpdatedAt00", t1."UpdatedBy0" AS "UpdatedBy00"
    FROM "UserRoles" AS u0
    INNER JOIN "Roles" AS r ON u0."RoleId" = r."Id"
    LEFT JOIN (
        SELECT r0."Id", r0."CreatedAt", r0."CreatedBy", r0."IsDeleted", r0."PermissionId", r0."RoleId", r0."RowVersion", r0."UpdatedAt", r0."UpdatedBy", p."Id" AS "Id0", p."Action", p."CreatedAt" AS "CreatedAt0", p."CreatedBy" AS "CreatedBy0", p."Description", p."IsDeleted" AS "IsDeleted0", p."Name", p."Resource", p."RowVersion" AS "RowVersion0", p."UpdatedAt" AS "UpdatedAt0", p."UpdatedBy" AS "UpdatedBy0"
        FROM "RolePermissions" AS r0
        INNER JOIN "Permissions" AS p ON r0."PermissionId" = p."Id"
    ) AS t1 ON r."Id" = t1."RoleId"
) AS t0 ON t."Id" = t0."UserId"
ORDER BY t."Id", t0."Id", t0."Id0", t0."Id1"
2025-05-16 16:25:45.091 +05:30 [INF] Executed DbCommand (5ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT count(*)::int
FROM "UserSessions" AS u
WHERE u."UserId" = @__userId_0 AND u."Status" = 0 AND u."ExpiresAt" > now()
2025-05-16 16:25:45.092 +05:30 [WRN] User is null or not authenticated
2025-05-16 16:25:45.093 +05:30 [WRN] User is null or not authenticated
2025-05-16 16:25:45.094 +05:30 [WRN] User is null or not authenticated
2025-05-16 16:25:45.101 +05:30 [INF] Executed DbCommand (4ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = DateTime), @p2='?', @p3='?', @p4='?', @p5='?', @p6='?', @p7='?' (DbType = Boolean), @p8='?' (DbType = Boolean), @p9='?', @p10='?' (DbType = Int32), @p11='?', @p12='?' (DbType = Binary), @p13='?' (DbType = DateTime), @p14='?', @p15='?', @p16='?' (DbType = Guid), @p17='?' (DbType = Guid), @p18='?' (DbType = DateTime), @p19='?', @p20='?', @p21='?', @p22='?' (DbType = DateTime), @p23='?' (DbType = DateTime), @p24='?', @p25='?' (DbType = Boolean), @p26='?' (DbType = DateTime), @p27='?', @p28='?' (DbType = DateTime), @p29='?' (DbType = Int32), @p30='?', @p31='?' (DbType = DateTime), @p32='?', @p33='?', @p34='?' (DbType = Guid), @p40='?' (DbType = Guid), @p35='?' (DbType = DateTime), @p36='?', @p37='?' (DbType = DateTime), @p41='?' (DbType = Binary), @p38='?' (DbType = DateTime), @p39='?'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "UserLoginHistories" ("Id", "CreatedAt", "CreatedBy", "DeviceInfo", "Email", "FailureReason", "IpAddress", "IsDeleted", "IsSuccessful", "Location", "LoginMethod", "PhoneNumber", "RowVersion", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16);
INSERT INTO "UserSessions" ("Id", "CreatedAt", "CreatedBy", "DeviceInfo", "EndReason", "EndedAt", "ExpiresAt", "IpAddress", "IsDeleted", "LastActiveAt", "RefreshToken", "StartedAt", "Status", "Token", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34)
RETURNING "RowVersion";
UPDATE "Users" SET "LastLoginAt" = @p35, "RefreshToken" = @p36, "RefreshTokenExpiryTime" = @p37, "UpdatedAt" = @p38, "UpdatedBy" = @p39
WHERE "Id" = @p40 AND "RowVersion" = @p41
RETURNING "RowVersion";
2025-05-16 16:25:45.106 +05:30 [INF] Created new session "3b2aac8f-871b-4368-ae8a-0a1dde59ea8a" for user "fecf1012-4d02-425a-a9b2-62375c16c5ae"
2025-05-16 16:25:45.109 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Name"
FROM "UserRoles" AS u
INNER JOIN "Roles" AS r ON u."RoleId" = r."Id"
WHERE u."UserId" = @__userId_0
2025-05-16 16:25:45.111 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."Description", r."IsDeleted", r."Name", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "UserRoles" AS u
INNER JOIN "Roles" AS r ON u."RoleId" = r."Id"
WHERE u."UserId" = @__userId_0
2025-05-16 16:25:45.113 +05:30 [INF] Executing OkObjectResult, writing value of type 'IdentityService.Application.DTOs.Auth.LoginResponse'.
2025-05-16 16:25:45.113 +05:30 [INF] Executed action IdentityService.API.Controllers.AuthController.Login (IdentityService.API) in 205.7938ms
2025-05-16 16:25:45.114 +05:30 [INF] Executed endpoint 'IdentityService.API.Controllers.AuthController.Login (IdentityService.API)'
2025-05-16 16:25:45.114 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5263/api/auth/login - 200 null application/json; charset=utf-8 211.0355ms
2025-05-16 16:25:45.132 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5263/api/roles - application/json; charset=utf-8 null
2025-05-16 16:25:45.132 +05:30 [INF] Token received and is being processed
2025-05-16 16:25:45.135 +05:30 [INF] Token validated successfully with claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: fecf1012-4d02-425a-a9b2-62375c16c5ae, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: fecf1012-4d02-425a-a9b2-62375c16c5ae, jti: 49ab571d-82f4-4e38-aee6-8836fa2d8354, security_stamp: 638829897446663110, permission: test.permission, nbf: 1747392945, exp: 1747396545, iss: identity-service, aud: vms-api
2025-05-16 16:25:45.135 +05:30 [INF] NameIdentifier claim found: fecf1012-4d02-425a-a9b2-62375c16c5ae
2025-05-16 16:25:45.145 +05:30 [INF] Executed DbCommand (4ms) [Parameters=[@__get_Item_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."Email", u."EmailVerified", u."IsDeleted", u."LastLoginAt", u."PhoneNumber", u."PhoneNumberVerified", u."PrimaryBranchId", u."RefreshToken", u."RefreshTokenExpiryTime", u."RememberMe", u."RowVersion", u."SecurityStamp", u."UpdatedAt", u."UpdatedBy", u."VendorId", u."PasswordHash"
FROM "Users" AS u
WHERE NOT (u."IsDeleted") AND u."Id" = @__get_Item_0
LIMIT 1
2025-05-16 16:25:45.152 +05:30 [INF] Executed DbCommand (5ms) [Parameters=[@__token_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Token" = @__token_0
LIMIT 1
2025-05-16 16:25:45.153 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: fecf1012-4d02-425a-a9b2-62375c16c5ae, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: fecf1012-4d02-425a-a9b2-62375c16c5ae, jti: 49ab571d-82f4-4e38-aee6-8836fa2d8354, security_stamp: 638829897446663110, permission: test.permission, nbf: 1747392945, exp: 1747396545, iss: identity-service, aud: vms-api
2025-05-16 16:25:45.153 +05:30 [INF] Successfully extracted user ID: "fecf1012-4d02-425a-a9b2-62375c16c5ae"
2025-05-16 16:25:45.158 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@p17='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?', @p2='?', @p3='?', @p4='?' (DbType = DateTime), @p5='?' (DbType = DateTime), @p6='?', @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime), @p9='?', @p18='?' (DbType = Binary), @p10='?' (DbType = DateTime), @p11='?' (DbType = Int32), @p12='?', @p13='?' (DbType = DateTime), @p14='?', @p15='?', @p16='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
UPDATE "UserSessions" SET "CreatedAt" = @p0, "CreatedBy" = @p1, "DeviceInfo" = @p2, "EndReason" = @p3, "EndedAt" = @p4, "ExpiresAt" = @p5, "IpAddress" = @p6, "IsDeleted" = @p7, "LastActiveAt" = @p8, "RefreshToken" = @p9, "StartedAt" = @p10, "Status" = @p11, "Token" = @p12, "UpdatedAt" = @p13, "UpdatedBy" = @p14, "UserAgent" = @p15, "UserId" = @p16
WHERE "Id" = @p17 AND "RowVersion" IS NULL
RETURNING "RowVersion";
2025-05-16 16:25:45.182 +05:30 [INF] Executed DbCommand (16ms) [Parameters=[@__token_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Token" = @__token_0
LIMIT 1
2025-05-16 16:25:45.183 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: fecf1012-4d02-425a-a9b2-62375c16c5ae, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: fecf1012-4d02-425a-a9b2-62375c16c5ae, jti: 49ab571d-82f4-4e38-aee6-8836fa2d8354, security_stamp: 638829897446663110, permission: test.permission, nbf: 1747392945, exp: 1747396545, iss: identity-service, aud: vms-api
2025-05-16 16:25:45.184 +05:30 [INF] Successfully extracted user ID: "fecf1012-4d02-425a-a9b2-62375c16c5ae"
2025-05-16 16:25:45.187 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@p17='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?', @p2='?', @p3='?', @p4='?' (DbType = DateTime), @p5='?' (DbType = DateTime), @p6='?', @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime), @p9='?', @p18='?' (DbType = Binary), @p10='?' (DbType = DateTime), @p11='?' (DbType = Int32), @p12='?', @p13='?' (DbType = DateTime), @p14='?', @p15='?', @p16='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
UPDATE "UserSessions" SET "CreatedAt" = @p0, "CreatedBy" = @p1, "DeviceInfo" = @p2, "EndReason" = @p3, "EndedAt" = @p4, "ExpiresAt" = @p5, "IpAddress" = @p6, "IsDeleted" = @p7, "LastActiveAt" = @p8, "RefreshToken" = @p9, "StartedAt" = @p10, "Status" = @p11, "Token" = @p12, "UpdatedAt" = @p13, "UpdatedBy" = @p14, "UserAgent" = @p15, "UserId" = @p16
WHERE "Id" = @p17 AND "RowVersion" IS NULL
RETURNING "RowVersion";
2025-05-16 16:25:45.187 +05:30 [DBG] Updated last active time for session "3b2aac8f-871b-4368-ae8a-0a1dde59ea8a"
2025-05-16 16:25:45.187 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: fecf1012-4d02-425a-a9b2-62375c16c5ae, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: fecf1012-4d02-425a-a9b2-62375c16c5ae, jti: 49ab571d-82f4-4e38-aee6-8836fa2d8354, security_stamp: 638829897446663110, permission: test.permission, nbf: 1747392945, exp: 1747396545, iss: identity-service, aud: vms-api
2025-05-16 16:25:45.188 +05:30 [INF] Successfully extracted user ID: "fecf1012-4d02-425a-a9b2-62375c16c5ae"
2025-05-16 16:25:45.188 +05:30 [INF] User ID from token: "fecf1012-4d02-425a-a9b2-62375c16c5ae"
2025-05-16 16:25:45.188 +05:30 [INF] Executing endpoint 'IdentityService.API.Controllers.RolesController.CreateRole (IdentityService.API)'
2025-05-16 16:25:45.195 +05:30 [INF] Route matched with {action = "CreateRole", controller = "Roles"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[IdentityService.Application.DTOs.Roles.RoleResponse]] CreateRole(IdentityService.Application.DTOs.Roles.CreateRoleRequest) on controller IdentityService.API.Controllers.RolesController (IdentityService.API).
2025-05-16 16:25:45.205 +05:30 [INF] Retrieved user ID from HttpContext.Items: "fecf1012-4d02-425a-a9b2-62375c16c5ae"
2025-05-16 16:25:45.205 +05:30 [INF] Setting UserId "fecf1012-4d02-425a-a9b2-62375c16c5ae" on request of type CreateRoleCommand
2025-05-16 16:25:45.206 +05:30 [INF] Retrieved user ID from HttpContext.Items: "fecf1012-4d02-425a-a9b2-62375c16c5ae"
2025-05-16 16:25:45.206 +05:30 [INF] Validating request of type CreateRoleCommand
2025-05-16 16:25:45.213 +05:30 [INF] Request content: {
  "Request": {
    "Name": "Admin",
    "Description": "Administrator role with full access",
    "PermissionIds": null
  },
  "UserId": "fecf1012-4d02-425a-a9b2-62375c16c5ae"
}
2025-05-16 16:25:45.213 +05:30 [INF] Request UserId property value: "fecf1012-4d02-425a-a9b2-62375c16c5ae"
2025-05-16 16:25:45.215 +05:30 [INF] Validation passed for request of type CreateRoleCommand
2025-05-16 16:25:45.219 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: fecf1012-4d02-425a-a9b2-62375c16c5ae, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: fecf1012-4d02-425a-a9b2-62375c16c5ae, jti: 49ab571d-82f4-4e38-aee6-8836fa2d8354, security_stamp: 638829897446663110, permission: test.permission, nbf: 1747392945, exp: 1747396545, iss: identity-service, aud: vms-api
2025-05-16 16:25:45.219 +05:30 [INF] Successfully extracted user ID: "fecf1012-4d02-425a-a9b2-62375c16c5ae"
2025-05-16 16:25:45.223 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = DateTime), @p2='?', @p3='?', @p4='?' (DbType = Boolean), @p5='?', @p6='?' (DbType = Binary), @p7='?' (DbType = DateTime), @p8='?'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Roles" ("Id", "CreatedAt", "CreatedBy", "Description", "IsDeleted", "Name", "RowVersion", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8);
2025-05-16 16:25:45.229 +05:30 [INF] Executing CreatedAtActionResult, writing value of type 'IdentityService.Application.DTOs.Roles.RoleResponse'.
2025-05-16 16:25:45.279 +05:30 [INF] Executed action IdentityService.API.Controllers.RolesController.CreateRole (IdentityService.API) in 83.6002ms
2025-05-16 16:25:45.279 +05:30 [INF] Executed endpoint 'IdentityService.API.Controllers.RolesController.CreateRole (IdentityService.API)'
2025-05-16 16:25:45.279 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5263/api/roles - 201 null application/json; charset=utf-8 147.2512ms
2025-05-16 16:25:45.286 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/api/roles - null null
2025-05-16 16:25:45.286 +05:30 [INF] Token received and is being processed
2025-05-16 16:25:45.287 +05:30 [INF] Token validated successfully with claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: fecf1012-4d02-425a-a9b2-62375c16c5ae, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: fecf1012-4d02-425a-a9b2-62375c16c5ae, jti: 49ab571d-82f4-4e38-aee6-8836fa2d8354, security_stamp: 638829897446663110, permission: test.permission, nbf: 1747392945, exp: 1747396545, iss: identity-service, aud: vms-api
2025-05-16 16:25:45.287 +05:30 [INF] NameIdentifier claim found: fecf1012-4d02-425a-a9b2-62375c16c5ae
2025-05-16 16:25:45.302 +05:30 [INF] Authorization failed. These requirements were not met:
ClaimsAuthorizationRequirement:Claim.Type=permission and Claim.Value is one of the following values: (Roles.View)
2025-05-16 16:25:45.306 +05:30 [INF] AuthenticationScheme: Bearer was forbidden.
2025-05-16 16:25:45.316 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/api/roles - 403 0 null 30.3358ms
2025-05-16 16:29:42.250 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5263/api/users - application/json; charset=utf-8 null
2025-05-16 16:29:42.250 +05:30 [INF] Token received and is being processed
2025-05-16 16:29:42.251 +05:30 [WRN] User is null or not authenticated
2025-05-16 16:29:42.251 +05:30 [WRN] No user ID found in token
2025-05-16 16:29:42.252 +05:30 [INF] Executing endpoint 'IdentityService.API.Controllers.UsersController.CreateUser (IdentityService.API)'
2025-05-16 16:29:42.252 +05:30 [INF] Route matched with {action = "CreateUser", controller = "Users"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[IdentityService.Application.DTOs.Users.UserResponse]] CreateUser(IdentityService.Application.DTOs.Users.CreateUserRequest) on controller IdentityService.API.Controllers.UsersController (IdentityService.API).
2025-05-16 16:29:42.255 +05:30 [WRN] User ID not found in HttpContext.Items
2025-05-16 16:29:42.255 +05:30 [INF] Setting UserId "00000000-0000-0000-0000-000000000000" on request of type CreateUserCommand
2025-05-16 16:29:42.255 +05:30 [WRN] User ID not found in HttpContext.Items
2025-05-16 16:29:42.255 +05:30 [INF] Validating request of type CreateUserCommand
2025-05-16 16:29:42.255 +05:30 [INF] Request content: {
  "Request": {
    "Email": "<EMAIL>",
    "PhoneNumber": "1511911857",
    "Password": "Test@123456",
    "ConfirmPassword": "Test@123456",
    "RoleIds": null,
    "IsActive": true
  },
  "UserId": "00000000-0000-0000-0000-000000000000"
}
2025-05-16 16:29:42.255 +05:30 [INF] Request UserId property value: "00000000-0000-0000-0000-000000000000"
2025-05-16 16:29:42.256 +05:30 [WRN] UserId is empty GUID (00000000-0000-0000-0000-000000000000)
2025-05-16 16:29:42.256 +05:30 [INF] Validation passed for request of type CreateUserCommand
2025-05-16 16:29:42.267 +05:30 [INF] Executed DbCommand (6ms) [Parameters=[@__email_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__email_0)
2025-05-16 16:29:42.272 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__phoneNumber_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."PhoneNumber" = @__phoneNumber_0)
2025-05-16 16:29:42.392 +05:30 [WRN] User is null or not authenticated
2025-05-16 16:29:42.393 +05:30 [WRN] User is null or not authenticated
2025-05-16 16:29:42.396 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = DateTime), @p2='?', @p3='?', @p4='?' (DbType = DateTime), @p5='?', @p6='?' (DbType = Guid), @p7='?', @p8='?' (DbType = DateTime), @p9='?' (DbType = DateTime), @p10='?' (DbType = DateTime), @p11='?', @p12='?' (DbType = Guid), @p13='?'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Users" ("Id", "CreatedAt", "CreatedBy", "Email", "LastLoginAt", "PhoneNumber", "PrimaryBranchId", "RefreshToken", "RefreshTokenExpiryTime", "SecurityStamp", "UpdatedAt", "UpdatedBy", "VendorId", "PasswordHash")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13)
RETURNING "EmailVerified", "IsDeleted", "PhoneNumberVerified", "RememberMe", "RowVersion";
2025-05-16 16:29:42.399 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Name"
FROM "UserRoles" AS u
INNER JOIN "Roles" AS r ON u."RoleId" = r."Id"
WHERE u."UserId" = @__userId_0
2025-05-16 16:29:42.400 +05:30 [INF] Executing CreatedAtActionResult, writing value of type 'IdentityService.Application.DTOs.Users.UserResponse'.
2025-05-16 16:29:42.401 +05:30 [INF] Executed action IdentityService.API.Controllers.UsersController.CreateUser (IdentityService.API) in 149.2599ms
2025-05-16 16:29:42.401 +05:30 [INF] Executed endpoint 'IdentityService.API.Controllers.UsersController.CreateUser (IdentityService.API)'
2025-05-16 16:29:42.402 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5263/api/users - 201 null application/json; charset=utf-8 152.0066ms
2025-05-16 16:29:42.505 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5263/api/auth/login - application/json; charset=utf-8 null
2025-05-16 16:29:42.506 +05:30 [INF] Token received and is being processed
2025-05-16 16:29:42.508 +05:30 [WRN] User is null or not authenticated
2025-05-16 16:29:42.508 +05:30 [WRN] No user ID found in token
2025-05-16 16:29:42.509 +05:30 [INF] Executing endpoint 'IdentityService.API.Controllers.AuthController.Login (IdentityService.API)'
2025-05-16 16:29:42.509 +05:30 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[IdentityService.Application.DTOs.Auth.LoginResponse]] Login(IdentityService.Application.DTOs.Auth.LoginRequest) on controller IdentityService.API.Controllers.AuthController (IdentityService.API).
2025-05-16 16:29:42.514 +05:30 [WRN] User ID not found in HttpContext.Items
2025-05-16 16:29:42.514 +05:30 [INF] Setting UserId "00000000-0000-0000-0000-000000000000" on request of type LoginCommand
2025-05-16 16:29:42.514 +05:30 [WRN] User ID not found in HttpContext.Items
2025-05-16 16:29:42.514 +05:30 [INF] Validating request of type LoginCommand
2025-05-16 16:29:42.514 +05:30 [INF] Request content: {
  "Request": {
    "Email": "<EMAIL>",
    "Password": "Test@123456",
    "PhoneNumber": null,
    "Otp": null,
    "RememberMe": false,
    "IsEmailLogin": true,
    "IsPhoneLogin": false
  },
  "UserId": "00000000-0000-0000-0000-000000000000"
}
2025-05-16 16:29:42.514 +05:30 [INF] Request UserId property value: "00000000-0000-0000-0000-000000000000"
2025-05-16 16:29:42.514 +05:30 [WRN] UserId is empty GUID (00000000-0000-0000-0000-000000000000)
2025-05-16 16:29:42.514 +05:30 [INF] Validation passed for request of type LoginCommand
2025-05-16 16:29:42.522 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__email_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash", t0."Id", t0."CreatedAt", t0."CreatedBy", t0."IsDeleted", t0."RoleId", t0."RowVersion", t0."UpdatedAt", t0."UpdatedBy", t0."UserId", t0."Id0", t0."CreatedAt0", t0."CreatedBy0", t0."Description", t0."IsDeleted0", t0."Name", t0."RowVersion0", t0."UpdatedAt0", t0."UpdatedBy0", t0."Id1", t0."CreatedAt1", t0."CreatedBy1", t0."IsDeleted1", t0."PermissionId", t0."RoleId0", t0."RowVersion1", t0."UpdatedAt1", t0."UpdatedBy1", t0."Id00", t0."Action", t0."CreatedAt00", t0."CreatedBy00", t0."Description0", t0."IsDeleted00", t0."Name0", t0."Resource", t0."RowVersion00", t0."UpdatedAt00", t0."UpdatedBy00"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."Email", u."EmailVerified", u."IsDeleted", u."LastLoginAt", u."PhoneNumber", u."PhoneNumberVerified", u."PrimaryBranchId", u."RefreshToken", u."RefreshTokenExpiryTime", u."RememberMe", u."RowVersion", u."SecurityStamp", u."UpdatedAt", u."UpdatedBy", u."VendorId", u."PasswordHash"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__email_0
    LIMIT 1
) AS t
LEFT JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."IsDeleted", u0."RoleId", u0."RowVersion", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", r."Id" AS "Id0", r."CreatedAt" AS "CreatedAt0", r."CreatedBy" AS "CreatedBy0", r."Description", r."IsDeleted" AS "IsDeleted0", r."Name", r."RowVersion" AS "RowVersion0", r."UpdatedAt" AS "UpdatedAt0", r."UpdatedBy" AS "UpdatedBy0", t1."Id" AS "Id1", t1."CreatedAt" AS "CreatedAt1", t1."CreatedBy" AS "CreatedBy1", t1."IsDeleted" AS "IsDeleted1", t1."PermissionId", t1."RoleId" AS "RoleId0", t1."RowVersion" AS "RowVersion1", t1."UpdatedAt" AS "UpdatedAt1", t1."UpdatedBy" AS "UpdatedBy1", t1."Id0" AS "Id00", t1."Action", t1."CreatedAt0" AS "CreatedAt00", t1."CreatedBy0" AS "CreatedBy00", t1."Description" AS "Description0", t1."IsDeleted0" AS "IsDeleted00", t1."Name" AS "Name0", t1."Resource", t1."RowVersion0" AS "RowVersion00", t1."UpdatedAt0" AS "UpdatedAt00", t1."UpdatedBy0" AS "UpdatedBy00"
    FROM "UserRoles" AS u0
    INNER JOIN "Roles" AS r ON u0."RoleId" = r."Id"
    LEFT JOIN (
        SELECT r0."Id", r0."CreatedAt", r0."CreatedBy", r0."IsDeleted", r0."PermissionId", r0."RoleId", r0."RowVersion", r0."UpdatedAt", r0."UpdatedBy", p."Id" AS "Id0", p."Action", p."CreatedAt" AS "CreatedAt0", p."CreatedBy" AS "CreatedBy0", p."Description", p."IsDeleted" AS "IsDeleted0", p."Name", p."Resource", p."RowVersion" AS "RowVersion0", p."UpdatedAt" AS "UpdatedAt0", p."UpdatedBy" AS "UpdatedBy0"
        FROM "RolePermissions" AS r0
        INNER JOIN "Permissions" AS p ON r0."PermissionId" = p."Id"
    ) AS t1 ON r."Id" = t1."RoleId"
) AS t0 ON t."Id" = t0."UserId"
ORDER BY t."Id", t0."Id", t0."Id0", t0."Id1"
2025-05-16 16:29:42.626 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT count(*)::int
FROM "UserSessions" AS u
WHERE u."UserId" = @__userId_0 AND u."Status" = 0 AND u."ExpiresAt" > now()
2025-05-16 16:29:42.627 +05:30 [WRN] User is null or not authenticated
2025-05-16 16:29:42.627 +05:30 [WRN] User is null or not authenticated
2025-05-16 16:29:42.628 +05:30 [WRN] User is null or not authenticated
2025-05-16 16:29:42.632 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = DateTime), @p2='?', @p3='?', @p4='?', @p5='?', @p6='?', @p7='?' (DbType = Boolean), @p8='?' (DbType = Boolean), @p9='?', @p10='?' (DbType = Int32), @p11='?', @p12='?' (DbType = Binary), @p13='?' (DbType = DateTime), @p14='?', @p15='?', @p16='?' (DbType = Guid), @p17='?' (DbType = Guid), @p18='?' (DbType = DateTime), @p19='?', @p20='?', @p21='?', @p22='?' (DbType = DateTime), @p23='?' (DbType = DateTime), @p24='?', @p25='?' (DbType = Boolean), @p26='?' (DbType = DateTime), @p27='?', @p28='?' (DbType = DateTime), @p29='?' (DbType = Int32), @p30='?', @p31='?' (DbType = DateTime), @p32='?', @p33='?', @p34='?' (DbType = Guid), @p40='?' (DbType = Guid), @p35='?' (DbType = DateTime), @p36='?', @p37='?' (DbType = DateTime), @p41='?' (DbType = Binary), @p38='?' (DbType = DateTime), @p39='?'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "UserLoginHistories" ("Id", "CreatedAt", "CreatedBy", "DeviceInfo", "Email", "FailureReason", "IpAddress", "IsDeleted", "IsSuccessful", "Location", "LoginMethod", "PhoneNumber", "RowVersion", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16);
INSERT INTO "UserSessions" ("Id", "CreatedAt", "CreatedBy", "DeviceInfo", "EndReason", "EndedAt", "ExpiresAt", "IpAddress", "IsDeleted", "LastActiveAt", "RefreshToken", "StartedAt", "Status", "Token", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34)
RETURNING "RowVersion";
UPDATE "Users" SET "LastLoginAt" = @p35, "RefreshToken" = @p36, "RefreshTokenExpiryTime" = @p37, "UpdatedAt" = @p38, "UpdatedBy" = @p39
WHERE "Id" = @p40 AND "RowVersion" = @p41
RETURNING "RowVersion";
2025-05-16 16:29:42.633 +05:30 [INF] Created new session "68706a0d-86ba-4775-8310-9ab0dd83ff1c" for user "d8cb6df7-6f21-4e43-8da0-8a5b3ee866c8"
2025-05-16 16:29:42.635 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Name"
FROM "UserRoles" AS u
INNER JOIN "Roles" AS r ON u."RoleId" = r."Id"
WHERE u."UserId" = @__userId_0
2025-05-16 16:29:42.641 +05:30 [INF] Executed DbCommand (4ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."Description", r."IsDeleted", r."Name", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "UserRoles" AS u
INNER JOIN "Roles" AS r ON u."RoleId" = r."Id"
WHERE u."UserId" = @__userId_0
2025-05-16 16:29:42.644 +05:30 [INF] Executing OkObjectResult, writing value of type 'IdentityService.Application.DTOs.Auth.LoginResponse'.
2025-05-16 16:29:42.644 +05:30 [INF] Executed action IdentityService.API.Controllers.AuthController.Login (IdentityService.API) in 135.0094ms
2025-05-16 16:29:42.644 +05:30 [INF] Executed endpoint 'IdentityService.API.Controllers.AuthController.Login (IdentityService.API)'
2025-05-16 16:29:42.645 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5263/api/auth/login - 200 null application/json; charset=utf-8 139.2486ms
2025-05-16 16:29:42.654 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5263/api/roles - application/json; charset=utf-8 null
2025-05-16 16:29:42.654 +05:30 [INF] Token received and is being processed
2025-05-16 16:29:42.654 +05:30 [INF] Token validated successfully with claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: d8cb6df7-6f21-4e43-8da0-8a5b3ee866c8, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: d8cb6df7-6f21-4e43-8da0-8a5b3ee866c8, jti: 3f0e352f-8dc8-40fc-8208-1ee8deead9a0, security_stamp: 638829899823922980, permission: test.permission, nbf: 1747393182, exp: 1747396782, iss: identity-service, aud: vms-api
2025-05-16 16:29:42.654 +05:30 [INF] NameIdentifier claim found: d8cb6df7-6f21-4e43-8da0-8a5b3ee866c8
2025-05-16 16:29:42.657 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__get_Item_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."Email", u."EmailVerified", u."IsDeleted", u."LastLoginAt", u."PhoneNumber", u."PhoneNumberVerified", u."PrimaryBranchId", u."RefreshToken", u."RefreshTokenExpiryTime", u."RememberMe", u."RowVersion", u."SecurityStamp", u."UpdatedAt", u."UpdatedBy", u."VendorId", u."PasswordHash"
FROM "Users" AS u
WHERE NOT (u."IsDeleted") AND u."Id" = @__get_Item_0
LIMIT 1
2025-05-16 16:29:42.659 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__token_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Token" = @__token_0
LIMIT 1
2025-05-16 16:29:42.660 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: d8cb6df7-6f21-4e43-8da0-8a5b3ee866c8, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: d8cb6df7-6f21-4e43-8da0-8a5b3ee866c8, jti: 3f0e352f-8dc8-40fc-8208-1ee8deead9a0, security_stamp: 638829899823922980, permission: test.permission, nbf: 1747393182, exp: 1747396782, iss: identity-service, aud: vms-api
2025-05-16 16:29:42.660 +05:30 [INF] Successfully extracted user ID: "d8cb6df7-6f21-4e43-8da0-8a5b3ee866c8"
2025-05-16 16:29:42.663 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@p17='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?', @p2='?', @p3='?', @p4='?' (DbType = DateTime), @p5='?' (DbType = DateTime), @p6='?', @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime), @p9='?', @p18='?' (DbType = Binary), @p10='?' (DbType = DateTime), @p11='?' (DbType = Int32), @p12='?', @p13='?' (DbType = DateTime), @p14='?', @p15='?', @p16='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
UPDATE "UserSessions" SET "CreatedAt" = @p0, "CreatedBy" = @p1, "DeviceInfo" = @p2, "EndReason" = @p3, "EndedAt" = @p4, "ExpiresAt" = @p5, "IpAddress" = @p6, "IsDeleted" = @p7, "LastActiveAt" = @p8, "RefreshToken" = @p9, "StartedAt" = @p10, "Status" = @p11, "Token" = @p12, "UpdatedAt" = @p13, "UpdatedBy" = @p14, "UserAgent" = @p15, "UserId" = @p16
WHERE "Id" = @p17 AND "RowVersion" IS NULL
RETURNING "RowVersion";
2025-05-16 16:29:42.665 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__token_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Token" = @__token_0
LIMIT 1
2025-05-16 16:29:42.666 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: d8cb6df7-6f21-4e43-8da0-8a5b3ee866c8, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: d8cb6df7-6f21-4e43-8da0-8a5b3ee866c8, jti: 3f0e352f-8dc8-40fc-8208-1ee8deead9a0, security_stamp: 638829899823922980, permission: test.permission, nbf: 1747393182, exp: 1747396782, iss: identity-service, aud: vms-api
2025-05-16 16:29:42.666 +05:30 [INF] Successfully extracted user ID: "d8cb6df7-6f21-4e43-8da0-8a5b3ee866c8"
2025-05-16 16:29:42.672 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@p17='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?', @p2='?', @p3='?', @p4='?' (DbType = DateTime), @p5='?' (DbType = DateTime), @p6='?', @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime), @p9='?', @p18='?' (DbType = Binary), @p10='?' (DbType = DateTime), @p11='?' (DbType = Int32), @p12='?', @p13='?' (DbType = DateTime), @p14='?', @p15='?', @p16='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
UPDATE "UserSessions" SET "CreatedAt" = @p0, "CreatedBy" = @p1, "DeviceInfo" = @p2, "EndReason" = @p3, "EndedAt" = @p4, "ExpiresAt" = @p5, "IpAddress" = @p6, "IsDeleted" = @p7, "LastActiveAt" = @p8, "RefreshToken" = @p9, "StartedAt" = @p10, "Status" = @p11, "Token" = @p12, "UpdatedAt" = @p13, "UpdatedBy" = @p14, "UserAgent" = @p15, "UserId" = @p16
WHERE "Id" = @p17 AND "RowVersion" IS NULL
RETURNING "RowVersion";
2025-05-16 16:29:42.673 +05:30 [DBG] Updated last active time for session "68706a0d-86ba-4775-8310-9ab0dd83ff1c"
2025-05-16 16:29:42.673 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: d8cb6df7-6f21-4e43-8da0-8a5b3ee866c8, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: d8cb6df7-6f21-4e43-8da0-8a5b3ee866c8, jti: 3f0e352f-8dc8-40fc-8208-1ee8deead9a0, security_stamp: 638829899823922980, permission: test.permission, nbf: 1747393182, exp: 1747396782, iss: identity-service, aud: vms-api
2025-05-16 16:29:42.674 +05:30 [INF] Successfully extracted user ID: "d8cb6df7-6f21-4e43-8da0-8a5b3ee866c8"
2025-05-16 16:29:42.674 +05:30 [INF] User ID from token: "d8cb6df7-6f21-4e43-8da0-8a5b3ee866c8"
2025-05-16 16:29:42.675 +05:30 [INF] Executing endpoint 'IdentityService.API.Controllers.RolesController.CreateRole (IdentityService.API)'
2025-05-16 16:29:42.675 +05:30 [INF] Route matched with {action = "CreateRole", controller = "Roles"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[IdentityService.Application.DTOs.Roles.RoleResponse]] CreateRole(IdentityService.Application.DTOs.Roles.CreateRoleRequest) on controller IdentityService.API.Controllers.RolesController (IdentityService.API).
2025-05-16 16:29:42.678 +05:30 [INF] Retrieved user ID from HttpContext.Items: "d8cb6df7-6f21-4e43-8da0-8a5b3ee866c8"
2025-05-16 16:29:42.678 +05:30 [INF] Setting UserId "d8cb6df7-6f21-4e43-8da0-8a5b3ee866c8" on request of type CreateRoleCommand
2025-05-16 16:29:42.678 +05:30 [INF] Retrieved user ID from HttpContext.Items: "d8cb6df7-6f21-4e43-8da0-8a5b3ee866c8"
2025-05-16 16:29:42.678 +05:30 [INF] Validating request of type CreateRoleCommand
2025-05-16 16:29:42.678 +05:30 [INF] Request content: {
  "Request": {
    "Name": "Admin",
    "Description": "Administrator role with full access",
    "PermissionIds": null
  },
  "UserId": "d8cb6df7-6f21-4e43-8da0-8a5b3ee866c8"
}
2025-05-16 16:29:42.678 +05:30 [INF] Request UserId property value: "d8cb6df7-6f21-4e43-8da0-8a5b3ee866c8"
2025-05-16 16:29:42.678 +05:30 [INF] Validation passed for request of type CreateRoleCommand
2025-05-16 16:29:42.679 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: d8cb6df7-6f21-4e43-8da0-8a5b3ee866c8, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: d8cb6df7-6f21-4e43-8da0-8a5b3ee866c8, jti: 3f0e352f-8dc8-40fc-8208-1ee8deead9a0, security_stamp: 638829899823922980, permission: test.permission, nbf: 1747393182, exp: 1747396782, iss: identity-service, aud: vms-api
2025-05-16 16:29:42.680 +05:30 [INF] Successfully extracted user ID: "d8cb6df7-6f21-4e43-8da0-8a5b3ee866c8"
2025-05-16 16:29:42.682 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = DateTime), @p2='?', @p3='?', @p4='?' (DbType = Boolean), @p5='?', @p6='?' (DbType = Binary), @p7='?' (DbType = DateTime), @p8='?'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Roles" ("Id", "CreatedAt", "CreatedBy", "Description", "IsDeleted", "Name", "RowVersion", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8);
2025-05-16 16:29:42.685 +05:30 [INF] Executing CreatedAtActionResult, writing value of type 'IdentityService.Application.DTOs.Roles.RoleResponse'.
2025-05-16 16:29:42.685 +05:30 [INF] Executed action IdentityService.API.Controllers.RolesController.CreateRole (IdentityService.API) in 9.7844ms
2025-05-16 16:29:42.685 +05:30 [INF] Executed endpoint 'IdentityService.API.Controllers.RolesController.CreateRole (IdentityService.API)'
2025-05-16 16:29:42.686 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5263/api/roles - 201 null application/json; charset=utf-8 32.0935ms
2025-05-16 16:29:42.692 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/api/roles - null null
2025-05-16 16:29:42.693 +05:30 [INF] Token received and is being processed
2025-05-16 16:29:42.693 +05:30 [INF] Token validated successfully with claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: d8cb6df7-6f21-4e43-8da0-8a5b3ee866c8, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: d8cb6df7-6f21-4e43-8da0-8a5b3ee866c8, jti: 3f0e352f-8dc8-40fc-8208-1ee8deead9a0, security_stamp: 638829899823922980, permission: test.permission, nbf: 1747393182, exp: 1747396782, iss: identity-service, aud: vms-api
2025-05-16 16:29:42.693 +05:30 [INF] NameIdentifier claim found: d8cb6df7-6f21-4e43-8da0-8a5b3ee866c8
2025-05-16 16:29:42.696 +05:30 [INF] Authorization failed. These requirements were not met:
ClaimsAuthorizationRequirement:Claim.Type=permission and Claim.Value is one of the following values: (Roles.View)
2025-05-16 16:29:42.697 +05:30 [INF] AuthenticationScheme: Bearer was forbidden.
2025-05-16 16:29:42.697 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/api/roles - 403 0 null 4.5357ms
2025-05-16 16:30:24.083 +05:30 [DBG] Starting session cleanup
2025-05-16 16:30:24.088 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-16 16:30:24.092 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-16 16:30:24.092 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-16 16:30:24.092 +05:30 [DBG] Session cleanup completed
2025-05-16 16:35:24.096 +05:30 [DBG] Starting session cleanup
2025-05-16 16:35:24.240 +05:30 [INF] Executed DbCommand (10ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-16 16:35:24.251 +05:30 [INF] Executed DbCommand (10ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-16 16:35:24.258 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-16 16:35:24.258 +05:30 [DBG] Session cleanup completed
2025-05-16 16:36:30.674 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5263/api/users - application/json; charset=utf-8 null
2025-05-16 16:36:30.676 +05:30 [INF] Token received and is being processed
2025-05-16 16:36:30.682 +05:30 [WRN] User is null or not authenticated
2025-05-16 16:36:30.682 +05:30 [WRN] No user ID found in token
2025-05-16 16:36:30.682 +05:30 [INF] Executing endpoint 'IdentityService.API.Controllers.UsersController.CreateUser (IdentityService.API)'
2025-05-16 16:36:30.682 +05:30 [INF] Route matched with {action = "CreateUser", controller = "Users"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[IdentityService.Application.DTOs.Users.UserResponse]] CreateUser(IdentityService.Application.DTOs.Users.CreateUserRequest) on controller IdentityService.API.Controllers.UsersController (IdentityService.API).
2025-05-16 16:36:30.684 +05:30 [WRN] User ID not found in HttpContext.Items
2025-05-16 16:36:30.684 +05:30 [INF] Setting UserId "00000000-0000-0000-0000-000000000000" on request of type CreateUserCommand
2025-05-16 16:36:30.684 +05:30 [WRN] User ID not found in HttpContext.Items
2025-05-16 16:36:30.685 +05:30 [INF] Validating request of type CreateUserCommand
2025-05-16 16:36:30.688 +05:30 [INF] Request content: {
  "Request": {
    "Email": "<EMAIL>",
    "PhoneNumber": "7978871344",
    "Password": "Test@123456",
    "ConfirmPassword": "Test@123456",
    "RoleIds": null,
    "IsActive": true
  },
  "UserId": "00000000-0000-0000-0000-000000000000"
}
2025-05-16 16:36:30.688 +05:30 [INF] Request UserId property value: "00000000-0000-0000-0000-000000000000"
2025-05-16 16:36:30.688 +05:30 [WRN] UserId is empty GUID (00000000-0000-0000-0000-000000000000)
2025-05-16 16:36:30.688 +05:30 [INF] Validation passed for request of type CreateUserCommand
2025-05-16 16:36:30.692 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__email_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__email_0)
2025-05-16 16:36:30.729 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__phoneNumber_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."PhoneNumber" = @__phoneNumber_0)
2025-05-16 16:36:30.889 +05:30 [WRN] User is null or not authenticated
2025-05-16 16:36:30.890 +05:30 [WRN] User is null or not authenticated
2025-05-16 16:36:30.929 +05:30 [INF] Executed DbCommand (38ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = DateTime), @p2='?', @p3='?', @p4='?' (DbType = DateTime), @p5='?', @p6='?' (DbType = Guid), @p7='?', @p8='?' (DbType = DateTime), @p9='?' (DbType = DateTime), @p10='?' (DbType = DateTime), @p11='?', @p12='?' (DbType = Guid), @p13='?'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Users" ("Id", "CreatedAt", "CreatedBy", "Email", "LastLoginAt", "PhoneNumber", "PrimaryBranchId", "RefreshToken", "RefreshTokenExpiryTime", "SecurityStamp", "UpdatedAt", "UpdatedBy", "VendorId", "PasswordHash")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13)
RETURNING "EmailVerified", "IsDeleted", "PhoneNumberVerified", "RememberMe", "RowVersion";
2025-05-16 16:36:30.988 +05:30 [INF] Executed DbCommand (57ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Name"
FROM "UserRoles" AS u
INNER JOIN "Roles" AS r ON u."RoleId" = r."Id"
WHERE u."UserId" = @__userId_0
2025-05-16 16:36:30.989 +05:30 [INF] Executing CreatedAtActionResult, writing value of type 'IdentityService.Application.DTOs.Users.UserResponse'.
2025-05-16 16:36:30.989 +05:30 [INF] Executed action IdentityService.API.Controllers.UsersController.CreateUser (IdentityService.API) in 306.7431ms
2025-05-16 16:36:30.989 +05:30 [INF] Executed endpoint 'IdentityService.API.Controllers.UsersController.CreateUser (IdentityService.API)'
2025-05-16 16:36:30.990 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5263/api/users - 201 null application/json; charset=utf-8 315.6847ms
2025-05-16 16:36:31.227 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5263/api/auth/login - application/json; charset=utf-8 null
2025-05-16 16:36:31.228 +05:30 [INF] Token received and is being processed
2025-05-16 16:36:31.228 +05:30 [WRN] User is null or not authenticated
2025-05-16 16:36:31.229 +05:30 [WRN] No user ID found in token
2025-05-16 16:36:31.229 +05:30 [INF] Executing endpoint 'IdentityService.API.Controllers.AuthController.Login (IdentityService.API)'
2025-05-16 16:36:31.229 +05:30 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[IdentityService.Application.DTOs.Auth.LoginResponse]] Login(IdentityService.Application.DTOs.Auth.LoginRequest) on controller IdentityService.API.Controllers.AuthController (IdentityService.API).
2025-05-16 16:36:31.234 +05:30 [WRN] User ID not found in HttpContext.Items
2025-05-16 16:36:31.234 +05:30 [INF] Setting UserId "00000000-0000-0000-0000-000000000000" on request of type LoginCommand
2025-05-16 16:36:31.235 +05:30 [WRN] User ID not found in HttpContext.Items
2025-05-16 16:36:31.236 +05:30 [INF] Validating request of type LoginCommand
2025-05-16 16:36:31.241 +05:30 [INF] Request content: {
  "Request": {
    "Email": "<EMAIL>",
    "Password": "Test@123456",
    "PhoneNumber": null,
    "Otp": null,
    "RememberMe": false,
    "IsEmailLogin": true,
    "IsPhoneLogin": false
  },
  "UserId": "00000000-0000-0000-0000-000000000000"
}
2025-05-16 16:36:31.241 +05:30 [INF] Request UserId property value: "00000000-0000-0000-0000-000000000000"
2025-05-16 16:36:31.241 +05:30 [WRN] UserId is empty GUID (00000000-0000-0000-0000-000000000000)
2025-05-16 16:36:31.241 +05:30 [INF] Validation passed for request of type LoginCommand
2025-05-16 16:36:31.406 +05:30 [INF] Executed DbCommand (164ms) [Parameters=[@__email_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash", t0."Id", t0."CreatedAt", t0."CreatedBy", t0."IsDeleted", t0."RoleId", t0."RowVersion", t0."UpdatedAt", t0."UpdatedBy", t0."UserId", t0."Id0", t0."CreatedAt0", t0."CreatedBy0", t0."Description", t0."IsDeleted0", t0."Name", t0."RowVersion0", t0."UpdatedAt0", t0."UpdatedBy0", t0."Id1", t0."CreatedAt1", t0."CreatedBy1", t0."IsDeleted1", t0."PermissionId", t0."RoleId0", t0."RowVersion1", t0."UpdatedAt1", t0."UpdatedBy1", t0."Id00", t0."Action", t0."CreatedAt00", t0."CreatedBy00", t0."Description0", t0."IsDeleted00", t0."Name0", t0."Resource", t0."RowVersion00", t0."UpdatedAt00", t0."UpdatedBy00"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."Email", u."EmailVerified", u."IsDeleted", u."LastLoginAt", u."PhoneNumber", u."PhoneNumberVerified", u."PrimaryBranchId", u."RefreshToken", u."RefreshTokenExpiryTime", u."RememberMe", u."RowVersion", u."SecurityStamp", u."UpdatedAt", u."UpdatedBy", u."VendorId", u."PasswordHash"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__email_0
    LIMIT 1
) AS t
LEFT JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."IsDeleted", u0."RoleId", u0."RowVersion", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", r."Id" AS "Id0", r."CreatedAt" AS "CreatedAt0", r."CreatedBy" AS "CreatedBy0", r."Description", r."IsDeleted" AS "IsDeleted0", r."Name", r."RowVersion" AS "RowVersion0", r."UpdatedAt" AS "UpdatedAt0", r."UpdatedBy" AS "UpdatedBy0", t1."Id" AS "Id1", t1."CreatedAt" AS "CreatedAt1", t1."CreatedBy" AS "CreatedBy1", t1."IsDeleted" AS "IsDeleted1", t1."PermissionId", t1."RoleId" AS "RoleId0", t1."RowVersion" AS "RowVersion1", t1."UpdatedAt" AS "UpdatedAt1", t1."UpdatedBy" AS "UpdatedBy1", t1."Id0" AS "Id00", t1."Action", t1."CreatedAt0" AS "CreatedAt00", t1."CreatedBy0" AS "CreatedBy00", t1."Description" AS "Description0", t1."IsDeleted0" AS "IsDeleted00", t1."Name" AS "Name0", t1."Resource", t1."RowVersion0" AS "RowVersion00", t1."UpdatedAt0" AS "UpdatedAt00", t1."UpdatedBy0" AS "UpdatedBy00"
    FROM "UserRoles" AS u0
    INNER JOIN "Roles" AS r ON u0."RoleId" = r."Id"
    LEFT JOIN (
        SELECT r0."Id", r0."CreatedAt", r0."CreatedBy", r0."IsDeleted", r0."PermissionId", r0."RoleId", r0."RowVersion", r0."UpdatedAt", r0."UpdatedBy", p."Id" AS "Id0", p."Action", p."CreatedAt" AS "CreatedAt0", p."CreatedBy" AS "CreatedBy0", p."Description", p."IsDeleted" AS "IsDeleted0", p."Name", p."Resource", p."RowVersion" AS "RowVersion0", p."UpdatedAt" AS "UpdatedAt0", p."UpdatedBy" AS "UpdatedBy0"
        FROM "RolePermissions" AS r0
        INNER JOIN "Permissions" AS p ON r0."PermissionId" = p."Id"
    ) AS t1 ON r."Id" = t1."RoleId"
) AS t0 ON t."Id" = t0."UserId"
ORDER BY t."Id", t0."Id", t0."Id0", t0."Id1"
2025-05-16 16:36:31.585 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT count(*)::int
FROM "UserSessions" AS u
WHERE u."UserId" = @__userId_0 AND u."Status" = 0 AND u."ExpiresAt" > now()
2025-05-16 16:36:31.585 +05:30 [WRN] User is null or not authenticated
2025-05-16 16:36:31.586 +05:30 [WRN] User is null or not authenticated
2025-05-16 16:36:31.587 +05:30 [WRN] User is null or not authenticated
2025-05-16 16:36:31.604 +05:30 [INF] Executed DbCommand (16ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = DateTime), @p2='?', @p3='?', @p4='?', @p5='?', @p6='?', @p7='?' (DbType = Boolean), @p8='?' (DbType = Boolean), @p9='?', @p10='?' (DbType = Int32), @p11='?', @p12='?' (DbType = Binary), @p13='?' (DbType = DateTime), @p14='?', @p15='?', @p16='?' (DbType = Guid), @p17='?' (DbType = Guid), @p18='?' (DbType = DateTime), @p19='?', @p20='?', @p21='?', @p22='?' (DbType = DateTime), @p23='?' (DbType = DateTime), @p24='?', @p25='?' (DbType = Boolean), @p26='?' (DbType = DateTime), @p27='?', @p28='?' (DbType = DateTime), @p29='?' (DbType = Int32), @p30='?', @p31='?' (DbType = DateTime), @p32='?', @p33='?', @p34='?' (DbType = Guid), @p40='?' (DbType = Guid), @p35='?' (DbType = DateTime), @p36='?', @p37='?' (DbType = DateTime), @p41='?' (DbType = Binary), @p38='?' (DbType = DateTime), @p39='?'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "UserLoginHistories" ("Id", "CreatedAt", "CreatedBy", "DeviceInfo", "Email", "FailureReason", "IpAddress", "IsDeleted", "IsSuccessful", "Location", "LoginMethod", "PhoneNumber", "RowVersion", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16);
INSERT INTO "UserSessions" ("Id", "CreatedAt", "CreatedBy", "DeviceInfo", "EndReason", "EndedAt", "ExpiresAt", "IpAddress", "IsDeleted", "LastActiveAt", "RefreshToken", "StartedAt", "Status", "Token", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34)
RETURNING "RowVersion";
UPDATE "Users" SET "LastLoginAt" = @p35, "RefreshToken" = @p36, "RefreshTokenExpiryTime" = @p37, "UpdatedAt" = @p38, "UpdatedBy" = @p39
WHERE "Id" = @p40 AND "RowVersion" = @p41
RETURNING "RowVersion";
2025-05-16 16:36:31.610 +05:30 [INF] Created new session "03218d62-0da9-42ed-8648-42ebfb654ac7" for user "849d8d7d-8c16-4125-be2d-e40d6eaa9e7a"
2025-05-16 16:36:31.612 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Name"
FROM "UserRoles" AS u
INNER JOIN "Roles" AS r ON u."RoleId" = r."Id"
WHERE u."UserId" = @__userId_0
2025-05-16 16:36:31.613 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."Description", r."IsDeleted", r."Name", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "UserRoles" AS u
INNER JOIN "Roles" AS r ON u."RoleId" = r."Id"
WHERE u."UserId" = @__userId_0
2025-05-16 16:36:31.614 +05:30 [INF] Executing OkObjectResult, writing value of type 'IdentityService.Application.DTOs.Auth.LoginResponse'.
2025-05-16 16:36:31.614 +05:30 [INF] Executed action IdentityService.API.Controllers.AuthController.Login (IdentityService.API) in 384.6825ms
2025-05-16 16:36:31.614 +05:30 [INF] Executed endpoint 'IdentityService.API.Controllers.AuthController.Login (IdentityService.API)'
2025-05-16 16:36:31.614 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5263/api/auth/login - 200 null application/json; charset=utf-8 386.8499ms
2025-05-16 16:36:31.639 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5263/api/roles - application/json; charset=utf-8 null
2025-05-16 16:36:31.639 +05:30 [INF] Token received and is being processed
2025-05-16 16:36:31.639 +05:30 [INF] Token validated successfully with claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 849d8d7d-8c16-4125-be2d-e40d6eaa9e7a, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 849d8d7d-8c16-4125-be2d-e40d6eaa9e7a, jti: cdc38043-5322-492a-8eb3-e37d77ff852b, security_stamp: 638829903908897420, permission: test.permission, nbf: 1747393591, exp: 1747397191, iss: identity-service, aud: vms-api
2025-05-16 16:36:31.639 +05:30 [INF] NameIdentifier claim found: 849d8d7d-8c16-4125-be2d-e40d6eaa9e7a
2025-05-16 16:36:31.650 +05:30 [INF] Executed DbCommand (9ms) [Parameters=[@__get_Item_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."Email", u."EmailVerified", u."IsDeleted", u."LastLoginAt", u."PhoneNumber", u."PhoneNumberVerified", u."PrimaryBranchId", u."RefreshToken", u."RefreshTokenExpiryTime", u."RememberMe", u."RowVersion", u."SecurityStamp", u."UpdatedAt", u."UpdatedBy", u."VendorId", u."PasswordHash"
FROM "Users" AS u
WHERE NOT (u."IsDeleted") AND u."Id" = @__get_Item_0
LIMIT 1
2025-05-16 16:36:31.657 +05:30 [INF] Executed DbCommand (5ms) [Parameters=[@__token_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Token" = @__token_0
LIMIT 1
2025-05-16 16:36:31.657 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 849d8d7d-8c16-4125-be2d-e40d6eaa9e7a, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 849d8d7d-8c16-4125-be2d-e40d6eaa9e7a, jti: cdc38043-5322-492a-8eb3-e37d77ff852b, security_stamp: 638829903908897420, permission: test.permission, nbf: 1747393591, exp: 1747397191, iss: identity-service, aud: vms-api
2025-05-16 16:36:31.657 +05:30 [INF] Successfully extracted user ID: "849d8d7d-8c16-4125-be2d-e40d6eaa9e7a"
2025-05-16 16:36:31.666 +05:30 [INF] Executed DbCommand (8ms) [Parameters=[@p17='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?', @p2='?', @p3='?', @p4='?' (DbType = DateTime), @p5='?' (DbType = DateTime), @p6='?', @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime), @p9='?', @p18='?' (DbType = Binary), @p10='?' (DbType = DateTime), @p11='?' (DbType = Int32), @p12='?', @p13='?' (DbType = DateTime), @p14='?', @p15='?', @p16='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
UPDATE "UserSessions" SET "CreatedAt" = @p0, "CreatedBy" = @p1, "DeviceInfo" = @p2, "EndReason" = @p3, "EndedAt" = @p4, "ExpiresAt" = @p5, "IpAddress" = @p6, "IsDeleted" = @p7, "LastActiveAt" = @p8, "RefreshToken" = @p9, "StartedAt" = @p10, "Status" = @p11, "Token" = @p12, "UpdatedAt" = @p13, "UpdatedBy" = @p14, "UserAgent" = @p15, "UserId" = @p16
WHERE "Id" = @p17 AND "RowVersion" IS NULL
RETURNING "RowVersion";
2025-05-16 16:36:31.692 +05:30 [INF] Executed DbCommand (25ms) [Parameters=[@__token_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Token" = @__token_0
LIMIT 1
2025-05-16 16:36:31.692 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 849d8d7d-8c16-4125-be2d-e40d6eaa9e7a, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 849d8d7d-8c16-4125-be2d-e40d6eaa9e7a, jti: cdc38043-5322-492a-8eb3-e37d77ff852b, security_stamp: 638829903908897420, permission: test.permission, nbf: 1747393591, exp: 1747397191, iss: identity-service, aud: vms-api
2025-05-16 16:36:31.692 +05:30 [INF] Successfully extracted user ID: "849d8d7d-8c16-4125-be2d-e40d6eaa9e7a"
2025-05-16 16:36:31.695 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@p17='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?', @p2='?', @p3='?', @p4='?' (DbType = DateTime), @p5='?' (DbType = DateTime), @p6='?', @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime), @p9='?', @p18='?' (DbType = Binary), @p10='?' (DbType = DateTime), @p11='?' (DbType = Int32), @p12='?', @p13='?' (DbType = DateTime), @p14='?', @p15='?', @p16='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
UPDATE "UserSessions" SET "CreatedAt" = @p0, "CreatedBy" = @p1, "DeviceInfo" = @p2, "EndReason" = @p3, "EndedAt" = @p4, "ExpiresAt" = @p5, "IpAddress" = @p6, "IsDeleted" = @p7, "LastActiveAt" = @p8, "RefreshToken" = @p9, "StartedAt" = @p10, "Status" = @p11, "Token" = @p12, "UpdatedAt" = @p13, "UpdatedBy" = @p14, "UserAgent" = @p15, "UserId" = @p16
WHERE "Id" = @p17 AND "RowVersion" IS NULL
RETURNING "RowVersion";
2025-05-16 16:36:31.696 +05:30 [DBG] Updated last active time for session "03218d62-0da9-42ed-8648-42ebfb654ac7"
2025-05-16 16:36:31.696 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 849d8d7d-8c16-4125-be2d-e40d6eaa9e7a, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 849d8d7d-8c16-4125-be2d-e40d6eaa9e7a, jti: cdc38043-5322-492a-8eb3-e37d77ff852b, security_stamp: 638829903908897420, permission: test.permission, nbf: 1747393591, exp: 1747397191, iss: identity-service, aud: vms-api
2025-05-16 16:36:31.697 +05:30 [INF] Successfully extracted user ID: "849d8d7d-8c16-4125-be2d-e40d6eaa9e7a"
2025-05-16 16:36:31.697 +05:30 [INF] User ID from token: "849d8d7d-8c16-4125-be2d-e40d6eaa9e7a"
2025-05-16 16:36:31.697 +05:30 [INF] Executing endpoint 'IdentityService.API.Controllers.RolesController.CreateRole (IdentityService.API)'
2025-05-16 16:36:31.697 +05:30 [INF] Route matched with {action = "CreateRole", controller = "Roles"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[IdentityService.Application.DTOs.Roles.RoleResponse]] CreateRole(IdentityService.Application.DTOs.Roles.CreateRoleRequest) on controller IdentityService.API.Controllers.RolesController (IdentityService.API).
2025-05-16 16:36:31.701 +05:30 [INF] Retrieved user ID from HttpContext.Items: "849d8d7d-8c16-4125-be2d-e40d6eaa9e7a"
2025-05-16 16:36:31.701 +05:30 [INF] Setting UserId "849d8d7d-8c16-4125-be2d-e40d6eaa9e7a" on request of type CreateRoleCommand
2025-05-16 16:36:31.701 +05:30 [INF] Retrieved user ID from HttpContext.Items: "849d8d7d-8c16-4125-be2d-e40d6eaa9e7a"
2025-05-16 16:36:31.701 +05:30 [INF] Validating request of type CreateRoleCommand
2025-05-16 16:36:31.704 +05:30 [INF] Request content: {
  "Request": {
    "Name": "Admin",
    "Description": "Administrator role with full access",
    "PermissionIds": null
  },
  "UserId": "849d8d7d-8c16-4125-be2d-e40d6eaa9e7a"
}
2025-05-16 16:36:31.704 +05:30 [INF] Request UserId property value: "849d8d7d-8c16-4125-be2d-e40d6eaa9e7a"
2025-05-16 16:36:31.704 +05:30 [INF] Validation passed for request of type CreateRoleCommand
2025-05-16 16:36:31.706 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 849d8d7d-8c16-4125-be2d-e40d6eaa9e7a, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 849d8d7d-8c16-4125-be2d-e40d6eaa9e7a, jti: cdc38043-5322-492a-8eb3-e37d77ff852b, security_stamp: 638829903908897420, permission: test.permission, nbf: 1747393591, exp: 1747397191, iss: identity-service, aud: vms-api
2025-05-16 16:36:31.706 +05:30 [INF] Successfully extracted user ID: "849d8d7d-8c16-4125-be2d-e40d6eaa9e7a"
2025-05-16 16:36:31.709 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = DateTime), @p2='?', @p3='?', @p4='?' (DbType = Boolean), @p5='?', @p6='?' (DbType = Binary), @p7='?' (DbType = DateTime), @p8='?'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Roles" ("Id", "CreatedAt", "CreatedBy", "Description", "IsDeleted", "Name", "RowVersion", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8);
2025-05-16 16:36:31.710 +05:30 [INF] Executing CreatedAtActionResult, writing value of type 'IdentityService.Application.DTOs.Roles.RoleResponse'.
2025-05-16 16:36:31.711 +05:30 [INF] Executed action IdentityService.API.Controllers.RolesController.CreateRole (IdentityService.API) in 13.4967ms
2025-05-16 16:36:31.711 +05:30 [INF] Executed endpoint 'IdentityService.API.Controllers.RolesController.CreateRole (IdentityService.API)'
2025-05-16 16:36:31.711 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5263/api/roles - 201 null application/json; charset=utf-8 72.4105ms
2025-05-16 16:36:31.718 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/api/roles - null null
2025-05-16 16:36:31.719 +05:30 [INF] Token received and is being processed
2025-05-16 16:36:31.719 +05:30 [INF] Token validated successfully with claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 849d8d7d-8c16-4125-be2d-e40d6eaa9e7a, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 849d8d7d-8c16-4125-be2d-e40d6eaa9e7a, jti: cdc38043-5322-492a-8eb3-e37d77ff852b, security_stamp: 638829903908897420, permission: test.permission, nbf: 1747393591, exp: 1747397191, iss: identity-service, aud: vms-api
2025-05-16 16:36:31.719 +05:30 [INF] NameIdentifier claim found: 849d8d7d-8c16-4125-be2d-e40d6eaa9e7a
2025-05-16 16:36:31.772 +05:30 [INF] Authorization failed. These requirements were not met:
ClaimsAuthorizationRequirement:Claim.Type=permission and Claim.Value is one of the following values: (Roles.View)
2025-05-16 16:36:31.772 +05:30 [INF] AuthenticationScheme: Bearer was forbidden.
2025-05-16 16:36:31.772 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/api/roles - 403 0 null 53.6451ms
2025-05-16 16:40:24.262 +05:30 [DBG] Starting session cleanup
2025-05-16 16:40:24.274 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-16 16:40:24.276 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-16 16:40:24.280 +05:30 [INF] Expired session "ac54b93e-4d26-4a45-bb66-1be13c0b56e8" for user "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:40:24.281 +05:30 [WRN] HttpContext is null
2025-05-16 16:40:24.285 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@p17='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?', @p2='?', @p3='?', @p4='?' (DbType = DateTime), @p5='?' (DbType = DateTime), @p6='?', @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime), @p9='?', @p18='?' (DbType = Binary), @p10='?' (DbType = DateTime), @p11='?' (DbType = Int32), @p12='?', @p13='?' (DbType = DateTime), @p14='?', @p15='?', @p16='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
UPDATE "UserSessions" SET "CreatedAt" = @p0, "CreatedBy" = @p1, "DeviceInfo" = @p2, "EndReason" = @p3, "EndedAt" = @p4, "ExpiresAt" = @p5, "IpAddress" = @p6, "IsDeleted" = @p7, "LastActiveAt" = @p8, "RefreshToken" = @p9, "StartedAt" = @p10, "Status" = @p11, "Token" = @p12, "UpdatedAt" = @p13, "UpdatedBy" = @p14, "UserAgent" = @p15, "UserId" = @p16
WHERE "Id" = @p17 AND "RowVersion" IS NULL
RETURNING "RowVersion";
2025-05-16 16:40:24.288 +05:30 [INF] Expired 1 idle or expired sessions
2025-05-16 16:40:24.288 +05:30 [DBG] Session cleanup completed
2025-05-16 16:45:24.297 +05:30 [DBG] Starting session cleanup
2025-05-16 16:45:24.401 +05:30 [INF] Executed DbCommand (6ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-16 16:45:24.403 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-16 16:45:24.404 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-16 16:45:24.404 +05:30 [DBG] Session cleanup completed
2025-05-16 16:50:24.407 +05:30 [DBG] Starting session cleanup
2025-05-16 16:50:24.421 +05:30 [INF] Executed DbCommand (8ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-16 16:50:24.435 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-16 16:50:24.437 +05:30 [INF] Expired session "3a38e051-b212-4ec8-82d3-c3fe07b4ca2a" for user "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:50:24.438 +05:30 [INF] Expired session "83863f57-**************-7454547a2322" for user "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:50:24.438 +05:30 [WRN] HttpContext is null
2025-05-16 16:50:24.439 +05:30 [WRN] HttpContext is null
2025-05-16 16:50:24.444 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@p17='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?', @p2='?', @p3='?', @p4='?' (DbType = DateTime), @p5='?' (DbType = DateTime), @p6='?', @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime), @p9='?', @p18='?' (DbType = Binary), @p10='?' (DbType = DateTime), @p11='?' (DbType = Int32), @p12='?', @p13='?' (DbType = DateTime), @p14='?', @p15='?', @p16='?' (DbType = Guid), @p36='?' (DbType = Guid), @p19='?' (DbType = DateTime), @p20='?', @p21='?', @p22='?', @p23='?' (DbType = DateTime), @p24='?' (DbType = DateTime), @p25='?', @p26='?' (DbType = Boolean), @p27='?' (DbType = DateTime), @p28='?', @p37='?' (DbType = Binary), @p29='?' (DbType = DateTime), @p30='?' (DbType = Int32), @p31='?', @p32='?' (DbType = DateTime), @p33='?', @p34='?', @p35='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
UPDATE "UserSessions" SET "CreatedAt" = @p0, "CreatedBy" = @p1, "DeviceInfo" = @p2, "EndReason" = @p3, "EndedAt" = @p4, "ExpiresAt" = @p5, "IpAddress" = @p6, "IsDeleted" = @p7, "LastActiveAt" = @p8, "RefreshToken" = @p9, "StartedAt" = @p10, "Status" = @p11, "Token" = @p12, "UpdatedAt" = @p13, "UpdatedBy" = @p14, "UserAgent" = @p15, "UserId" = @p16
WHERE "Id" = @p17 AND "RowVersion" IS NULL
RETURNING "RowVersion";
UPDATE "UserSessions" SET "CreatedAt" = @p19, "CreatedBy" = @p20, "DeviceInfo" = @p21, "EndReason" = @p22, "EndedAt" = @p23, "ExpiresAt" = @p24, "IpAddress" = @p25, "IsDeleted" = @p26, "LastActiveAt" = @p27, "RefreshToken" = @p28, "StartedAt" = @p29, "Status" = @p30, "Token" = @p31, "UpdatedAt" = @p32, "UpdatedBy" = @p33, "UserAgent" = @p34, "UserId" = @p35
WHERE "Id" = @p36 AND "RowVersion" IS NULL
RETURNING "RowVersion";
2025-05-16 16:50:24.446 +05:30 [INF] Expired 2 idle or expired sessions
2025-05-16 16:50:24.446 +05:30 [DBG] Session cleanup completed
2025-05-16 16:55:24.448 +05:30 [DBG] Starting session cleanup
2025-05-16 16:55:24.526 +05:30 [INF] Executed DbCommand (5ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-16 16:55:24.529 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-16 16:55:24.529 +05:30 [INF] Expired session "0ca94f3a-f3ba-41bb-bf71-16253f3ae9f1" for user "382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e"
2025-05-16 16:55:24.529 +05:30 [WRN] HttpContext is null
2025-05-16 16:55:24.532 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@p17='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?', @p2='?', @p3='?', @p4='?' (DbType = DateTime), @p5='?' (DbType = DateTime), @p6='?', @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime), @p9='?', @p18='?' (DbType = Binary), @p10='?' (DbType = DateTime), @p11='?' (DbType = Int32), @p12='?', @p13='?' (DbType = DateTime), @p14='?', @p15='?', @p16='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
UPDATE "UserSessions" SET "CreatedAt" = @p0, "CreatedBy" = @p1, "DeviceInfo" = @p2, "EndReason" = @p3, "EndedAt" = @p4, "ExpiresAt" = @p5, "IpAddress" = @p6, "IsDeleted" = @p7, "LastActiveAt" = @p8, "RefreshToken" = @p9, "StartedAt" = @p10, "Status" = @p11, "Token" = @p12, "UpdatedAt" = @p13, "UpdatedBy" = @p14, "UserAgent" = @p15, "UserId" = @p16
WHERE "Id" = @p17 AND "RowVersion" IS NULL
RETURNING "RowVersion";
2025-05-16 16:55:24.533 +05:30 [INF] Expired 1 idle or expired sessions
2025-05-16 16:55:24.533 +05:30 [DBG] Session cleanup completed
2025-05-16 17:00:24.535 +05:30 [DBG] Starting session cleanup
2025-05-16 17:00:24.542 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-16 17:00:24.550 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-16 17:00:24.550 +05:30 [INF] Expired session "68706a0d-86ba-4775-8310-9ab0dd83ff1c" for user "d8cb6df7-6f21-4e43-8da0-8a5b3ee866c8"
2025-05-16 17:00:24.550 +05:30 [INF] Expired session "3b2aac8f-871b-4368-ae8a-0a1dde59ea8a" for user "fecf1012-4d02-425a-a9b2-62375c16c5ae"
2025-05-16 17:00:24.551 +05:30 [WRN] HttpContext is null
2025-05-16 17:00:24.551 +05:30 [WRN] HttpContext is null
2025-05-16 17:00:24.554 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@p17='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?', @p2='?', @p3='?', @p4='?' (DbType = DateTime), @p5='?' (DbType = DateTime), @p6='?', @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime), @p9='?', @p18='?' (DbType = Binary), @p10='?' (DbType = DateTime), @p11='?' (DbType = Int32), @p12='?', @p13='?' (DbType = DateTime), @p14='?', @p15='?', @p16='?' (DbType = Guid), @p36='?' (DbType = Guid), @p19='?' (DbType = DateTime), @p20='?', @p21='?', @p22='?', @p23='?' (DbType = DateTime), @p24='?' (DbType = DateTime), @p25='?', @p26='?' (DbType = Boolean), @p27='?' (DbType = DateTime), @p28='?', @p37='?' (DbType = Binary), @p29='?' (DbType = DateTime), @p30='?' (DbType = Int32), @p31='?', @p32='?' (DbType = DateTime), @p33='?', @p34='?', @p35='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
UPDATE "UserSessions" SET "CreatedAt" = @p0, "CreatedBy" = @p1, "DeviceInfo" = @p2, "EndReason" = @p3, "EndedAt" = @p4, "ExpiresAt" = @p5, "IpAddress" = @p6, "IsDeleted" = @p7, "LastActiveAt" = @p8, "RefreshToken" = @p9, "StartedAt" = @p10, "Status" = @p11, "Token" = @p12, "UpdatedAt" = @p13, "UpdatedBy" = @p14, "UserAgent" = @p15, "UserId" = @p16
WHERE "Id" = @p17 AND "RowVersion" IS NULL
RETURNING "RowVersion";
UPDATE "UserSessions" SET "CreatedAt" = @p19, "CreatedBy" = @p20, "DeviceInfo" = @p21, "EndReason" = @p22, "EndedAt" = @p23, "ExpiresAt" = @p24, "IpAddress" = @p25, "IsDeleted" = @p26, "LastActiveAt" = @p27, "RefreshToken" = @p28, "StartedAt" = @p29, "Status" = @p30, "Token" = @p31, "UpdatedAt" = @p32, "UpdatedBy" = @p33, "UserAgent" = @p34, "UserId" = @p35
WHERE "Id" = @p36 AND "RowVersion" IS NULL
RETURNING "RowVersion";
2025-05-16 17:00:24.555 +05:30 [INF] Expired 2 idle or expired sessions
2025-05-16 17:00:24.555 +05:30 [DBG] Session cleanup completed
