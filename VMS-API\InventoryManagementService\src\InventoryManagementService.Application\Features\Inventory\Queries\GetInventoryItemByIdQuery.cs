using AutoMapper;
using InventoryManagementService.Application.DTOs;
using InventoryManagementService.Domain.Interfaces;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using VMS.Contracts.Common.Authorization;

namespace InventoryManagementService.Application.Features.Inventory.Queries;

public class GetInventoryItemByIdQuery : IRequest<InventoryItemDto?>
{
    public Guid InventoryItemId { get; set; }
}

public class GetInventoryItemByIdQueryHandler : IRequestHandler<GetInventoryItemByIdQuery, InventoryItemDto?>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly ILogger<GetInventoryItemByIdQueryHandler> _logger;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public GetInventoryItemByIdQueryHandler(
        IUnitOfWork unitOfWork,
        IMapper mapper,
        ILogger<GetInventoryItemByIdQueryHandler> logger,
        IHttpContextAccessor httpContextAccessor)
    {
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _logger = logger;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<InventoryItemDto?> Handle(GetInventoryItemByIdQuery request, CancellationToken cancellationToken)
    {
        // Check permissions - Admin roles bypass, others need explicit permission
        var user = _httpContextAccessor.HttpContext?.User;
        if (!PermissionHelper.IsAdmin(user))
        {
            PermissionHelper.ValidateAccess(user, "Inventory.View");
        }

        _logger.LogInformation("Getting inventory item by ID: {InventoryItemId}", request.InventoryItemId);

        var inventoryItem = await _unitOfWork.Inventory.GetByIdAsync(request.InventoryItemId);

        if (inventoryItem == null)
        {
            _logger.LogWarning("Inventory item not found with ID: {InventoryItemId}", request.InventoryItemId);
            return null;
        }

        _logger.LogInformation("Inventory item found with ID: {InventoryItemId}", inventoryItem.Id);
        return _mapper.Map<InventoryItemDto>(inventoryItem);
    }
}
