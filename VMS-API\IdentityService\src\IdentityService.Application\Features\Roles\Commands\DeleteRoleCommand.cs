using System;
using System.Threading;
using System.Threading.Tasks;
using IdentityService.Application.Interfaces;
using IdentityService.Domain.Interfaces;
using MediatR;

namespace IdentityService.Application.Features.Roles.Commands;

public class DeleteRoleCommand : IRequest<bool>
{
    public Guid RoleId { get; set; }
}

public class DeleteRoleCommandHandler : IRequestHandler<DeleteRoleCommand, bool>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICurrentUserService _currentUserService;
    private readonly IAuditLogService _auditLogService;

    public DeleteRoleCommandHandler(
        IUnitOfWork unitOfWork,
        ICurrentUserService currentUserService,
        IAuditLogService auditLogService)
    {
        _unitOfWork = unitOfWork;
        _currentUserService = currentUserService;
        _auditLogService = auditLogService;
    }

    public async Task<bool> Handle(DeleteRoleCommand command, CancellationToken cancellationToken)
    {
        var role = await _unitOfWork.RoleRepository.GetByIdAsync(command.RoleId);
        if (role == null)
            throw new InvalidOperationException($"Role with ID {command.RoleId} not found");

        // Check if role is in use
        var usersWithRole = await _unitOfWork.UserRepository.GetUsersByRoleAsync(role.Name);
        if (usersWithRole.Count > 0)
            throw new InvalidOperationException($"Cannot delete role '{role.Name}' because it is assigned to {usersWithRole.Count} users");

        // Track role data for audit log
        var oldValues = System.Text.Json.JsonSerializer.Serialize(new
        {
            role.Id,
            role.Name,
            role.Description
        });

        // Delete role
        await _unitOfWork.RoleRepository.DeleteAsync(role);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        // Create audit log
        await _auditLogService.CreateAuditLogAsync(
            "Delete",
            "Role",
            role.Id.ToString(),
            oldValues,
            string.Empty,
            "Id,Name,Description",
            _currentUserService.UserId ?? Guid.Empty);

        return true;
    }
}
