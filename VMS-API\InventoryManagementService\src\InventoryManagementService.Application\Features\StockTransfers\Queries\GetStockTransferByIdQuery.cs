using AutoMapper;
using InventoryManagementService.Application.DTOs;
using InventoryManagementService.Domain.Interfaces;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using VMS.Contracts.Common.Authorization;

namespace InventoryManagementService.Application.Features.StockTransfers.Queries;

public class GetStockTransferByIdQuery : IRequest<StockTransferDto?>
{
    public Guid TransferId { get; set; }
}

public class GetStockTransferByIdQueryHandler : IRequestHandler<GetStockTransferByIdQuery, StockTransferDto?>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly ILogger<GetStockTransferByIdQueryHandler> _logger;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public GetStockTransferByIdQueryHandler(
        IUnitOfWork unitOfWork,
        IMapper mapper,
        ILogger<GetStockTransferByIdQueryHandler> logger,
        IHttpContextAccessor httpContextAccessor)
    {
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _logger = logger;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<StockTransferDto?> Handle(GetStockTransferByIdQuery request, CancellationToken cancellationToken)
    {
        // Check permissions - Admin roles bypass, others need explicit permission
        var user = _httpContextAccessor.HttpContext?.User;
        if (!PermissionHelper.IsAdmin(user))
        {
            PermissionHelper.ValidateAccess(user, "Transfers.View");
        }

        _logger.LogInformation("Getting stock transfer by ID: {TransferId}", request.TransferId);

        var transfer = await _unitOfWork.StockTransfers.GetByIdAsync(request.TransferId);

        if (transfer == null)
        {
            _logger.LogWarning("Stock transfer not found with ID: {TransferId}", request.TransferId);
            return null;
        }

        _logger.LogInformation("Stock transfer found with ID: {TransferId}", transfer.Id);
        return _mapper.Map<StockTransferDto>(transfer);
    }
}
