using System;
using IdentityService.Domain.Enums;
using Microsoft.AspNetCore.Authorization;

namespace IdentityService.Infrastructure.Authorization;

public class SubscriptionTierRequirement : IAuthorizationRequirement
{
    public SubscriptionTier MinimumTier { get; }

    public SubscriptionTierRequirement(SubscriptionTier minimumTier)
    {
        MinimumTier = minimumTier;
    }
}

public class SubscriptionFeatureRequirement : IAuthorizationRequirement
{
    public string FeatureName { get; }

    public SubscriptionFeatureRequirement(string featureName)
    {
        FeatureName = featureName ?? throw new ArgumentNullException(nameof(featureName));
    }
}

public class PermissionRequirement : IAuthorizationRequirement
{
    public string PermissionName { get; }

    public PermissionRequirement(string permissionName)
    {
        PermissionName = permissionName ?? throw new ArgumentNullException(nameof(permissionName));
    }
}
