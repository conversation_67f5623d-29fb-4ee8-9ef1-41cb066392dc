using System;
using IdentityService.Domain.Common;

namespace IdentityService.Domain.Entities;

public class UserRole : BaseEntity
{
    public Guid UserId { get; private set; }
    public Guid RoleId { get; private set; }
    public User User { get; private set; }
    public Role Role { get; private set; }

    private UserRole()
    {
        User = null!;
        Role = null!;
    }

    public static UserRole Create(Guid userId, Guid roleId, string createdBy, User user = null!, Role role = null!)
    {
        return new UserRole
        {
            UserId = userId,
            RoleId = roleId,
            CreatedBy = createdBy,
            User = user,
            Role = role
        };
    }
} 