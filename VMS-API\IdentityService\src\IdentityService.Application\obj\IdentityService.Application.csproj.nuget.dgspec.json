{"format": 1, "restore": {"D:\\PROJECTS\\VMS\\VMS-API\\IdentityService\\src\\IdentityService.Application\\IdentityService.Application.csproj": {}}, "projects": {"D:\\PROJECTS\\VMS\\VMS-API\\IdentityService\\src\\IdentityService.Application\\IdentityService.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\PROJECTS\\VMS\\VMS-API\\IdentityService\\src\\IdentityService.Application\\IdentityService.Application.csproj", "projectName": "IdentityService.Application", "projectPath": "D:\\PROJECTS\\VMS\\VMS-API\\IdentityService\\src\\IdentityService.Application\\IdentityService.Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\PROJECTS\\VMS\\VMS-API\\IdentityService\\src\\IdentityService.Application\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\PROJECTS\\VMS\\VMS-API\\IdentityService\\src\\IdentityService.Domain\\IdentityService.Domain.csproj": {"projectPath": "D:\\PROJECTS\\VMS\\VMS-API\\IdentityService\\src\\IdentityService.Domain\\IdentityService.Domain.csproj"}, "D:\\PROJECTS\\VMS\\VMS-API\\VMSContracts\\VMSContracts.csproj": {"projectPath": "D:\\PROJECTS\\VMS\\VMS-API\\VMSContracts\\VMSContracts.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"FluentValidation": {"target": "Package", "version": "[11.11.0, )"}, "MediatR": {"target": "Package", "version": "[11.1.0, )"}, "MediatR.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[11.1.0, )"}, "Microsoft.AspNetCore.Http.Abstractions": {"target": "Package", "version": "[2.3.0, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Options.ConfigurationExtensions": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.406/PortableRuntimeIdentifierGraph.json"}}}, "D:\\PROJECTS\\VMS\\VMS-API\\IdentityService\\src\\IdentityService.Domain\\IdentityService.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\PROJECTS\\VMS\\VMS-API\\IdentityService\\src\\IdentityService.Domain\\IdentityService.Domain.csproj", "projectName": "IdentityService.Domain", "projectPath": "D:\\PROJECTS\\VMS\\VMS-API\\IdentityService\\src\\IdentityService.Domain\\IdentityService.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\PROJECTS\\VMS\\VMS-API\\IdentityService\\src\\IdentityService.Domain\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": {"target": "Package", "version": "[8.0.2, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Options.ConfigurationExtensions": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.406/PortableRuntimeIdentifierGraph.json"}}}, "D:\\PROJECTS\\VMS\\VMS-API\\VMSContracts\\VMSContracts.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\PROJECTS\\VMS\\VMS-API\\VMSContracts\\VMSContracts.csproj", "projectName": "VMSContracts", "projectPath": "D:\\PROJECTS\\VMS\\VMS-API\\VMSContracts\\VMSContracts.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\PROJECTS\\VMS\\VMS-API\\VMSContracts\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"MassTransit": {"target": "Package", "version": "[8.1.3, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Http": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Http.Polly": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.406/PortableRuntimeIdentifierGraph.json"}}}}}