using FluentValidation;
using InventoryManagementService.Domain.Interfaces;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using VMS.Contracts.Common.Authorization;

namespace InventoryManagementService.Application.Features.StockTransfers.Commands;

public class CancelTransferCommand : IRequest
{
    public Guid TransferId { get; set; }
    public string Reason { get; set; } = string.Empty;
    public string CancelledBy { get; set; } = string.Empty;
}

public class CancelTransferCommandValidator : AbstractValidator<CancelTransferCommand>
{
    public CancelTransferCommandValidator()
    {
        RuleFor(x => x.TransferId)
            .NotEmpty().WithMessage("Transfer ID is required");

        RuleFor(x => x.Reason)
            .NotEmpty().WithMessage("Cancellation reason is required")
            .MaximumLength(500).WithMessage("Reason cannot exceed 500 characters");

        RuleFor(x => x.CancelledBy)
            .NotEmpty().WithMessage("CancelledBy is required");
    }
}

public class CancelTransferCommandHandler : IRequestHandler<CancelTransferCommand>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<CancelTransferCommandHandler> _logger;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly IEventPublisher _eventPublisher;

    public CancelTransferCommandHandler(
        IUnitOfWork unitOfWork,
        ILogger<CancelTransferCommandHandler> logger,
        IHttpContextAccessor httpContextAccessor,
        IEventPublisher eventPublisher)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
        _httpContextAccessor = httpContextAccessor;
        _eventPublisher = eventPublisher;
    }

    public async Task Handle(CancelTransferCommand request, CancellationToken cancellationToken)
    {
        // Check permissions - Admin roles bypass, others need explicit permission
        var user = _httpContextAccessor.HttpContext?.User;
        if (!PermissionHelper.IsAdmin(user))
        {
            PermissionHelper.ValidateAccess(user, "Transfers.Update");
        }

        _logger.LogInformation("Cancelling transfer {TransferId} with reason: {Reason}", 
            request.TransferId, request.Reason);

        await _unitOfWork.BeginTransactionAsync();

        try
        {
            var transfer = await _unitOfWork.StockTransfers.GetByIdAsync(request.TransferId);

            if (transfer == null)
            {
                throw new KeyNotFoundException($"Stock transfer with ID {request.TransferId} not found");
            }

            // If transfer was already shipped, we need to restore the stock to source branch
            if (transfer.Status == Domain.Enums.TransferStatus.InTransit)
            {
                foreach (var item in transfer.Items.Where(i => i.ShippedQuantity.HasValue && i.ShippedQuantity > 0))
                {
                    var inventoryItem = await _unitOfWork.Inventory.GetByPartAndBranchAsync(item.PartId, transfer.SourceBranchId);
                    if (inventoryItem != null)
                    {
                        inventoryItem.AddStock(item.ShippedQuantity!.Value, request.CancelledBy, $"Transfer cancellation - {transfer.TransferNumber}");
                    }
                }
            }

            transfer.Cancel(request.CancelledBy, request.Reason);

            await _unitOfWork.SaveChangesAsync();
            await _unitOfWork.CommitTransactionAsync();

            _logger.LogInformation("Transfer {TransferId} cancelled successfully", transfer.Id);

            // Publish event
            await _eventPublisher.PublishTransferCancelledEvent(
                transfer.Id,
                transfer.TransferNumber,
                request.Reason);
        }
        catch
        {
            await _unitOfWork.RollbackTransactionAsync();
            throw;
        }
    }
}
