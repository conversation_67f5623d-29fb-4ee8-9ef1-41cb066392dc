using IdentityService.Domain.Entities;
using IdentityService.Domain.ValueObjects;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace IdentityService.Infrastructure.Persistence.Configurations;

public class UserConfiguration : IEntityTypeConfiguration<User>
{
    public void Configure(EntityTypeBuilder<User> builder)
    {
        builder.HasKey(u => u.Id);

        builder.Property(u => u.Email)
            .IsRequired()
            .HasMaxLength(256);

        builder.Property(u => u.PhoneNumber)
            .IsRequired()
            .HasMaxLength(20);

        // Configure PasswordHash as owned entity
        builder.OwnsOne(u => u.PasswordHash, passwordBuilder =>
        {
            passwordBuilder.Property(p => p.Hash)
                .HasColumnName("PasswordHash")
                .IsRequired(false);
        });

        builder.Property(u => u.EmailVerified)
            .IsRequired()
            .HasDefaultValue(false);

        builder.Property(u => u.PhoneNumberVerified)
            .IsRequired()
            .HasDefaultValue(false);

        builder.Property(u => u.RememberMe)
            .IsRequired()
            .HasDefaultValue(false);

        builder.Property(u => u.IsDeleted)
            .IsRequired()
            .HasDefaultValue(false);

        // Configure VendorId and PrimaryBranchId properties
        builder.Property(u => u.VendorId)
            .IsRequired(false);

        builder.Property(u => u.PrimaryBranchId)
            .IsRequired(false);

        // Configure concurrency token
        builder.Property(u => u.RowVersion)
            .IsRowVersion()
            .IsConcurrencyToken()
            .ValueGeneratedOnAddOrUpdate(); // Ensure it's generated by the database

        // Configure relationships
        builder.HasMany(u => u.UserRoles)
            .WithOne(ur => ur.User)
            .HasForeignKey(ur => ur.UserId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(u => u.PasswordResetTokens)
            .WithOne(prt => prt.User)
            .HasForeignKey(prt => prt.UserId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(u => u.Subscriptions)
            .WithOne(s => s.User)
            .HasForeignKey(s => s.UserId)
            .OnDelete(DeleteBehavior.Cascade);

        // Add global query filter for soft delete
        builder.HasQueryFilter(u => !u.IsDeleted);
    }
}
