using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using IdentityService.Application.Interfaces;
using IdentityService.Domain.Interfaces;
using MediatR;
using Microsoft.AspNetCore.Http;
using IdentityService.Application.Common.Authorization;

namespace IdentityService.Application.Features.Permissions.Commands;

public class DeletePermissionCommand : IRequest<Unit>
{
    public Guid PermissionId { get; set; }
}

public class DeletePermissionCommandHandler : IRequestHandler<DeletePermissionCommand, Unit>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICurrentUserService _currentUserService;
    private readonly IAuditLogService _auditLogService;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public DeletePermissionCommandHandler(
        IUnitOfWork unitOfWork,
        ICurrentUserService currentUserService,
        IAuditLogService auditLogService,
        IHttpContextAccessor httpContextAccessor)
    {
        _unitOfWork = unitOfWork;
        _currentUserService = currentUserService;
        _auditLogService = auditLogService;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<Unit> Handle(DeletePermissionCommand command, CancellationToken cancellationToken)
    {
        // Check permissions - Admin roles bypass, others need explicit permission
        var user = _httpContextAccessor.HttpContext?.User;
        if (!PermissionHelper.IsAdmin(user))
        {
            await PermissionHelper.ValidateAccess(user, "Permissions.Delete", _unitOfWork.UserRepository);
        }

        var permission = await _unitOfWork.PermissionRepository.GetByIdAsync(command.PermissionId);
        if (permission == null)
            throw new InvalidOperationException($"Permission with ID {command.PermissionId} not found");
            
        // Check if permission is assigned to any roles
        var rolePermissions = await _unitOfWork.RolePermissionRepository.GetAllAsync();
        var isAssigned = rolePermissions.Any(rp => rp.PermissionId == command.PermissionId);
        if (isAssigned)
            throw new InvalidOperationException("Cannot delete a permission that is assigned to roles. Remove the permission from all roles first.");
            
        // Check if permission is assigned to any menus
        var menuPermissions = await _unitOfWork.MenuPermissionRepository.GetAllAsync();
        var isAssignedToMenu = menuPermissions.Any(mp => mp.PermissionId == command.PermissionId);
        if (isAssignedToMenu)
            throw new InvalidOperationException("Cannot delete a permission that is assigned to menus. Remove the permission from all menus first.");
            
        // Track old values for audit log
        var oldValues = System.Text.Json.JsonSerializer.Serialize(new { 
            permission.Id, 
            permission.Name, 
            permission.Description, 
            permission.Resource, 
            permission.Action 
        });
        
        // Delete permission
        await _unitOfWork.PermissionRepository.DeleteAsync(permission);
        await _unitOfWork.SaveChangesAsync(cancellationToken);
        
        // Create audit log
        await _auditLogService.CreateAuditLogAsync(
            "Delete",
            "Permission",
            permission.Id.ToString(),
            oldValues,
            string.Empty,
            "Id,Name,Description,Resource,Action",
            _currentUserService.UserId ?? Guid.Empty);
        
        return Unit.Value;
    }
}
