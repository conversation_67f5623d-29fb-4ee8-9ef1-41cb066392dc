namespace SubscriptionService.Application.DTOs;

/// <summary>
/// DTO for Vendor Subscription Terms Acceptance
/// </summary>
public class VendorSubscriptionTermsAcceptanceDto
{
    public Guid Id { get; set; }
    public Guid VendorId { get; set; }
    public Guid TermsAndConditionsId { get; set; }
    public string TermsVersion { get; set; } = string.Empty;
    public DateTime AcceptedAt { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public DateTime? UpdatedAt { get; set; }
    public string? UpdatedBy { get; set; }
    
    // Navigation properties
    public TermsAndConditionsDto? TermsAndConditions { get; set; }
}
