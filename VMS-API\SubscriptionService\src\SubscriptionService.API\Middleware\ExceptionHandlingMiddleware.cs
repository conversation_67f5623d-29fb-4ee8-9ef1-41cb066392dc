using System.Net;
using System.Text.Json;
using Microsoft.EntityFrameworkCore;

namespace SubscriptionService.API.Middleware;

public class ExceptionHandlingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<ExceptionHandlingMiddleware> _logger;

    public ExceptionHandlingMiddleware(RequestDelegate next, ILogger<ExceptionHandlingMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An unhandled exception occurred");
            await HandleExceptionAsync(context, ex);
        }
    }

    private static async Task HandleExceptionAsync(HttpContext context, Exception exception)
    {
        context.Response.ContentType = "application/json";
        
        var response = new ErrorResponse();
        
        switch (exception)
        {
            case DbUpdateException dbUpdateEx:
                // Handle database update exceptions (including null constraint violations)
                context.Response.StatusCode = (int)HttpStatusCode.BadRequest;
                response.Message = "A database error occurred while processing your request.";
                response.Details = GetDbUpdateExceptionDetails(dbUpdateEx);
                break;
                
            case InvalidOperationException invalidOpEx:
                // Handle validation and business rule exceptions
                context.Response.StatusCode = (int)HttpStatusCode.BadRequest;
                response.Message = invalidOpEx.Message;
                break;
                
            default:
                // Handle all other exceptions
                context.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                response.Message = "An error occurred while processing your request.";
                break;
        }
        
        var json = JsonSerializer.Serialize(response);
        await context.Response.WriteAsync(json);
    }
    
    private static string GetDbUpdateExceptionDetails(DbUpdateException exception)
    {
        // Extract useful information from the database exception
        if (exception.InnerException != null)
        {
            var message = exception.InnerException.Message;
            
            // Check for null constraint violations
            if (message.Contains("NOT NULL") || message.Contains("null value"))
            {
                // Try to extract the column name from the error message
                var columnMatch = System.Text.RegularExpressions.Regex.Match(message, @"column ['""]?([^'""]+)['""]?");
                if (columnMatch.Success)
                {
                    var columnName = columnMatch.Groups[1].Value;
                    return $"The {columnName} field is required and cannot be null.";
                }
                
                return "One or more required fields are missing. Please ensure all required fields have values.";
            }
            
            return message;
        }
        
        return exception.Message;
    }
}

public class ErrorResponse
{
    public string Message { get; set; } = string.Empty;
    public string? Details { get; set; }
}

// Extension method to add the middleware to the request pipeline
public static class ExceptionHandlingMiddlewareExtensions
{
    public static IApplicationBuilder UseExceptionHandlingMiddleware(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<ExceptionHandlingMiddleware>();
    }
}
