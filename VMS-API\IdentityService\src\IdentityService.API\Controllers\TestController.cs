using System;
using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;
using IdentityService.Application.Interfaces;
using IdentityService.Domain.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace IdentityService.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class TestController : ControllerBase
    {
        private readonly ILogger<TestController> _logger;
        private readonly ITokenService _tokenService;
        private readonly ICurrentUserService _currentUserService;

        public TestController(
            ILogger<TestController> logger,
            ITokenService tokenService,
            ICurrentUserService currentUserService)
        {
            _logger = logger;
            _tokenService = tokenService;
            _currentUserService = currentUserService;
        }

        [HttpGet("token")]
        [AllowAnonymous]
        public IActionResult GenerateTestToken()
        {
            try
            {
                // Generate a test token with a fixed user ID
                var userId = Guid.NewGuid();
                var email = "<EMAIL>";
                var roles = new List<string> { "Admin", "User" };
                var permissions = new List<string> { "Users.View", "Users.Create", "Users.Update" };

                // Generate a token using the overload that takes strings
                var token = _tokenService.GenerateAccessToken(
                    userId.ToString(),
                    email,
                    roles);

                return Ok(new {
                    token,
                    userId,
                    message = "Use this token in the Authorize dialog with the prefix 'Bearer '"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating test token");
                return StatusCode(500, new { message = "An error occurred while generating the test token" });
            }
        }

        [HttpGet("user-id")]
        [Authorize]
        public IActionResult GetCurrentUserId()
        {
            try
            {
                // Get the current user ID from the token
                var userId = _currentUserService.UserId;
                var userName = _currentUserService.UserName;

                // Log all claims for debugging
                var claims = User.Claims.Select(c => new { Type = c.Type, Value = c.Value }).ToList();

                return Ok(new {
                    userId,
                    userName,
                    claims,
                    message = "This is the user ID extracted from the token"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting current user ID");
                return StatusCode(500, new { message = "An error occurred while getting the current user ID" });
            }
        }
    }
}
