using AutoMapper;
using InventoryManagementService.Application.DTOs;
using InventoryManagementService.Domain.Enums;
using InventoryManagementService.Domain.Interfaces;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using VMS.Contracts.Common.Authorization;

namespace InventoryManagementService.Application.Features.Parts.Queries;

public class SearchPartsQuery : IRequest<List<PartDto>>
{
    public string? SearchTerm { get; set; }
    public PartCategory? Category { get; set; }
    public string? Manufacturer { get; set; }
    public string? Make { get; set; }
    public string? Model { get; set; }
    public int? Year { get; set; }
    public bool IncludeInactive { get; set; } = false;
}

public class SearchPartsQueryHandler : IRequestHandler<SearchPartsQuery, List<PartDto>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly ILogger<SearchPartsQueryHandler> _logger;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public SearchPartsQueryHandler(
        IUnitOfWork unitOfWork,
        IMapper mapper,
        ILogger<SearchPartsQueryHandler> logger,
        IHttpContextAccessor httpContextAccessor)
    {
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _logger = logger;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<List<PartDto>> Handle(SearchPartsQuery request, CancellationToken cancellationToken)
    {
        // Check permissions - Admin roles bypass, others need explicit permission
        var user = _httpContextAccessor.HttpContext?.User;
        if (!PermissionHelper.IsAdmin(user))
        {
            PermissionHelper.ValidateAccess(user, "Parts.View");
        }

        _logger.LogInformation("Searching parts with criteria");

        List<Domain.Entities.Part> parts = new();

        // Search by different criteria
        if (!string.IsNullOrWhiteSpace(request.Make) && !string.IsNullOrWhiteSpace(request.Model) && request.Year.HasValue)
        {
            // Search by vehicle compatibility
            parts = await _unitOfWork.Parts.GetByCompatibilityAsync(
                request.Make, 
                request.Model, 
                request.Year.Value, 
                request.IncludeInactive);
        }
        else if (request.Category.HasValue)
        {
            // Search by category
            parts = await _unitOfWork.Parts.GetByCategoryAsync(request.Category.Value, request.IncludeInactive);
        }
        else if (!string.IsNullOrWhiteSpace(request.Manufacturer))
        {
            // Search by manufacturer
            parts = await _unitOfWork.Parts.GetByManufacturerAsync(request.Manufacturer, request.IncludeInactive);
        }
        else if (!string.IsNullOrWhiteSpace(request.SearchTerm))
        {
            // General search
            parts = await _unitOfWork.Parts.SearchAsync(request.SearchTerm, request.IncludeInactive);
        }
        else
        {
            // Get all parts
            parts = await _unitOfWork.Parts.GetAllAsync(request.IncludeInactive);
        }

        // Apply additional filters if needed
        if (!string.IsNullOrWhiteSpace(request.SearchTerm) && request.Category.HasValue)
        {
            parts = parts.Where(p => 
                p.Category == request.Category.Value &&
                (p.Name.Contains(request.SearchTerm, StringComparison.OrdinalIgnoreCase) ||
                 p.Description.Contains(request.SearchTerm, StringComparison.OrdinalIgnoreCase) ||
                 p.SKU.Contains(request.SearchTerm, StringComparison.OrdinalIgnoreCase)))
                .ToList();
        }

        _logger.LogInformation("Found {Count} parts matching search criteria", parts.Count);

        return _mapper.Map<List<PartDto>>(parts);
    }
}
