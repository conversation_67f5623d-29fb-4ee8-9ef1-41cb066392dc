# Test script to check if migration works
Write-Host "Testing Identity Service Migration Fix..." -ForegroundColor Cyan

# Navigate to API project
Set-Location "src\IdentityService.API"

# Try to run the application for a short time
Write-Host "Starting application to test migration..." -ForegroundColor Yellow

# Start the process and capture output
$process = Start-Process -FilePath "dotnet" -ArgumentList "run" -PassThru -RedirectStandardOutput "migration-test-output.txt" -RedirectStandardError "migration-test-error.txt" -NoNewWindow

# Wait for 20 seconds
Start-Sleep -Seconds 20

# Stop the process if it's still running
if (!$process.HasExited) {
    $process.Kill()
    Write-Host "Stopped application after 20 seconds" -ForegroundColor Yellow
}

# Check the output files
if (Test-Path "migration-test-output.txt") {
    Write-Host "=== Application Output ===" -ForegroundColor Green
    Get-Content "migration-test-output.txt" | Select-Object -Last 20
}

if (Test-Path "migration-test-error.txt") {
    Write-Host "=== Application Errors ===" -ForegroundColor Red
    Get-Content "migration-test-error.txt" | Select-Object -Last 20
}

# Check if the application started successfully
if ($process.ExitCode -eq 0 -or $process.ExitCode -eq $null) {
    Write-Host "Migration test completed - check output above for results" -ForegroundColor Green
} else {
    Write-Host "Application failed to start - Exit Code: $($process.ExitCode)" -ForegroundColor Red
}

# Clean up
Remove-Item "migration-test-output.txt" -ErrorAction SilentlyContinue
Remove-Item "migration-test-error.txt" -ErrorAction SilentlyContinue
