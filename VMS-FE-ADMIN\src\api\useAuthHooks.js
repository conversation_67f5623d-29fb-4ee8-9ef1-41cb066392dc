import { useMutation, useQuery } from "@tanstack/react-query";
import axiosInstance from "./axiosInstance";
import { API_ENDPOINTS } from "@constants/apiEndpoints";
import { deflateParams } from "@utils/helper";

// Login Mutation
export const useLogin = (options) =>
  useMutation({
    mutationFn: (data) => axiosInstance.post(API_ENDPOINTS.LOGIN, data),
    ...options,
  });

// Register Mutation
export const useRegister = (options) =>
  useMutation({
    mutationFn: (data) => axiosInstance.post(API_ENDPOINTS.REGISTER, data),
    ...options,
  });

// Logout Mutation
export const useLogout = (options) =>
  useMutation({
    mutationFn: () => axiosInstance.post(API_ENDPOINTS.LOGOUT),
    ...options,
  });

// Refresh Token Mutation
export const useRefreshToken = (options) =>
  useMutation({
    mutationFn: (data) => axiosInstance.post(API_ENDPOINTS.REFRESH_TOKEN, data),
    ...options,
  });

// Forgot Password Mutation
export const useForgotPassword = (options) =>
  useMutation({
    mutationFn: (data) => axiosInstance.post(API_ENDPOINTS.FORGOT_PASSWORD, data),
    ...options,
  });

// Reset Password Mutation
export const useResetPassword = (options) =>
  useMutation({
    mutationFn: (data) => axiosInstance.post(API_ENDPOINTS.RESET_PASSWORD, data),
    ...options,
  });

// Get User Profile Query
export const useUserProfile = (params, options) =>
  useQuery({
    queryKey: ["userProfile", deflateParams(params)],
    queryFn: () => axiosInstance.get(API_ENDPOINTS.USER_PROFILE, { params }),
    ...options,
  });

// Get User by ID Query
export const useUserById = (userId, options) =>
  useQuery({
    queryKey: ["userById", userId],
    queryFn: () => axiosInstance.get(API_ENDPOINTS.UMS_USER_BY_ID(userId)),
    enabled: !!userId,
    ...options,
  });

// Update Profile Mutation
export const useUpdateProfile = (options) =>
  useMutation({
    mutationFn: (data) => axiosInstance.put(API_ENDPOINTS.UPDATE_PROFILE, data),
    ...options,
  });

// Update User by ID Mutation
export const useUpdateUserById = (options) =>
  useMutation({
    mutationFn: ({ userId, userData }) => axiosInstance.put(API_ENDPOINTS.UMS_USER_UPDATE(userId), userData),
    ...options,
  });

// Change Password Mutation
export const useChangePassword = (options) =>
  useMutation({
    mutationFn: (data) => axiosInstance.post(API_ENDPOINTS.CHANGE_PASSWORD, data),
    ...options,
  });

// Vendor Registration Mutation
export const useVendorRegistration = (options) =>
  useMutation({
    mutationFn: (data) => axiosInstance.post(API_ENDPOINTS.VENDOR_REGISTRATION, data),
    ...options,
  });

// Get Vendor Profile Query
export const useVendorProfile = (options) =>
  useQuery({
    queryKey: ["vendorProfile"],
    queryFn: () => axiosInstance.get(API_ENDPOINTS.VENDOR_PROFILE),
    ...options,
  });
