using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MediatR;
using SubscriptionService.Application.DTOs;
using SubscriptionService.Application.Features.TermsAndConditions.Commands;
using SubscriptionService.Application.Features.TermsAndConditions.Queries;
using SubscriptionService.API.Models;
using System.Security.Claims;

namespace SubscriptionService.API.Controllers;

/// <summary>
/// Controller for managing Subscription Terms and Conditions
/// </summary>
[ApiController]
[Route("api/subscription-terms")]
[Authorize]
public class SubscriptionTermsController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<SubscriptionTermsController> _logger;

    public SubscriptionTermsController(
        IMediator mediator,
        ILogger<SubscriptionTermsController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Get current active subscription terms and conditions
    /// </summary>
    /// <returns>Current active subscription terms</returns>
    [HttpGet("current")]
    [AllowAnonymous]
    public async Task<ActionResult<ApiResponse<TermsAndConditionsDto>>> GetCurrentSubscriptionTerms()
    {
        try
        {
            _logger.LogInformation("Getting current subscription terms and conditions");

            var query = new GetCurrentSubscriptionTermsQuery();
            var result = await _mediator.Send(query);

            if (result == null)
            {
                return Ok(new ApiResponse<TermsAndConditionsDto>
                {
                    Success = false,
                    Message = "No active subscription terms and conditions found",
                    Data = null
                });
            }

            return Ok(new ApiResponse<TermsAndConditionsDto>
            {
                Success = true,
                Message = "Current subscription terms retrieved successfully",
                Data = result
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving current subscription terms");
            return StatusCode(500, new ApiResponse<TermsAndConditionsDto>
            {
                Success = false,
                Message = "An error occurred while retrieving current subscription terms",
                Errors = new List<string> { ex.Message }
            });
        }
    }

    /// <summary>
    /// Get subscription terms and conditions by ID
    /// </summary>
    /// <param name="id">Terms and conditions ID</param>
    /// <returns>Subscription terms by ID</returns>
    [HttpGet("{id}")]
    [Authorize(Roles = "SuperAdmin")]
    public async Task<ActionResult<ApiResponse<TermsAndConditionsDto>>> GetSubscriptionTermsById(Guid id)
    {
        try
        {
            _logger.LogInformation("Getting subscription terms by ID {TermsId}", id);

            var query = new GetSubscriptionTermsByIdQuery { Id = id };
            var result = await _mediator.Send(query);

            if (result == null)
            {
                return NotFound(new ApiResponse<TermsAndConditionsDto>
                {
                    Success = false,
                    Message = $"Subscription terms with ID {id} not found"
                });
            }

            return Ok(new ApiResponse<TermsAndConditionsDto>
            {
                Success = true,
                Message = "Subscription terms retrieved successfully",
                Data = result
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving subscription terms by ID {TermsId}", id);
            return StatusCode(500, new ApiResponse<TermsAndConditionsDto>
            {
                Success = false,
                Message = "An error occurred while retrieving subscription terms",
                Errors = new List<string> { ex.Message }
            });
        }
    }

    /// <summary>
    /// Get paginated list of subscription terms and conditions
    /// </summary>
    /// <param name="queryParams">Query parameters for filtering and pagination</param>
    /// <returns>Paginated list of subscription terms</returns>
    [HttpGet]
    [Authorize(Roles = "SuperAdmin")]
    public async Task<ActionResult<PaginatedResponse<SubscriptionTermsListDto>>> GetSubscriptionTermsList([FromQuery] TermsListQueryParams queryParams)
    {
        try
        {
            _logger.LogInformation("Getting subscription terms list - Page: {PageNumber}, Size: {PageSize}", 
                queryParams.PageNumber, queryParams.PageSize);

            var query = new GetSubscriptionTermsListQuery
            {
                PageNumber = queryParams.PageNumber,
                PageSize = queryParams.PageSize,
                SortBy = queryParams.SortBy,
                SortDirection = queryParams.SortDirection
            };

            var result = await _mediator.Send(query);

            return Ok(new PaginatedResponse<SubscriptionTermsListDto>
            {
                Success = true,
                Message = "Subscription terms list retrieved successfully",
                Data = result,
                TotalCount = result.TotalCount,
                PageNumber = result.PageNumber,
                PageSize = result.PageSize,
                TotalPages = result.TotalPages,
                HasPreviousPage = result.HasPreviousPage,
                HasNextPage = result.HasNextPage
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving subscription terms list");
            return StatusCode(500, new PaginatedResponse<SubscriptionTermsListDto>
            {
                Success = false,
                Message = "An error occurred while retrieving subscription terms list",
                Errors = new List<string> { ex.Message }
            });
        }
    }

    /// <summary>
    /// Create new subscription terms and conditions (Admin only)
    /// </summary>
    /// <param name="request">Create subscription terms request</param>
    /// <returns>Created subscription terms</returns>
    [HttpPost]
    [Authorize(Roles = "SuperAdmin")]
    public async Task<ActionResult<ApiResponse<TermsAndConditionsDto>>> CreateSubscriptionTerms([FromBody] CreateSubscriptionTermsRequest request)
    {
        try
        {
            _logger.LogInformation("Creating new subscription terms with version {Version}", request.Version);

            var command = new CreateSubscriptionTermsAndConditionsCommand
            {
                TermsAndConditions = new CreateSubscriptionTermsAndConditionsDto
                {
                    Title = request.Title,
                    Content = request.Content,
                    Version = request.Version,
                    EffectiveDate = request.EffectiveDate
                }
            };

            var result = await _mediator.Send(command);

            return CreatedAtAction(
                nameof(GetSubscriptionTermsById),
                new { id = result.Id },
                new ApiResponse<TermsAndConditionsDto>
                {
                    Success = true,
                    Message = "Subscription terms created successfully",
                    Data = result
                });
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while creating subscription terms");
            return BadRequest(new ApiResponse<TermsAndConditionsDto>
            {
                Success = false,
                Message = ex.Message
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating subscription terms");
            return StatusCode(500, new ApiResponse<TermsAndConditionsDto>
            {
                Success = false,
                Message = "An error occurred while creating subscription terms",
                Errors = new List<string> { ex.Message }
            });
        }
    }

    /// <summary>
    /// Update subscription terms and conditions (Admin only)
    /// </summary>
    /// <param name="id">Terms and conditions ID</param>
    /// <param name="request">Update subscription terms request</param>
    /// <returns>Updated subscription terms</returns>
    [HttpPut("{id}")]
    [Authorize(Roles = "SuperAdmin")]
    public async Task<ActionResult<ApiResponse<TermsAndConditionsDto>>> UpdateSubscriptionTerms(Guid id, [FromBody] UpdateSubscriptionTermsRequest request)
    {
        try
        {
            _logger.LogInformation("Updating subscription terms with ID {TermsId}", id);

            var command = new UpdateSubscriptionTermsAndConditionsCommand
            {
                Id = id,
                TermsAndConditions = new UpdateSubscriptionTermsAndConditionsDto
                {
                    Title = request.Title,
                    Content = request.Content
                }
            };

            var result = await _mediator.Send(command);

            return Ok(new ApiResponse<TermsAndConditionsDto>
            {
                Success = true,
                Message = "Subscription terms updated successfully",
                Data = result
            });
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while updating subscription terms");
            return BadRequest(new ApiResponse<TermsAndConditionsDto>
            {
                Success = false,
                Message = ex.Message
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating subscription terms with ID {TermsId}", id);
            return StatusCode(500, new ApiResponse<TermsAndConditionsDto>
            {
                Success = false,
                Message = "An error occurred while updating subscription terms",
                Errors = new List<string> { ex.Message }
            });
        }
    }

    /// <summary>
    /// Accept current subscription terms and conditions (Vendor)
    /// </summary>
    /// <param name="request">Accept subscription terms request</param>
    /// <returns>Acceptance record</returns>
    [HttpPost("accept")]
    [Authorize(Roles = "VendorAdmin")]
    public async Task<ActionResult<ApiResponse<VendorSubscriptionTermsAcceptanceDto>>> AcceptSubscriptionTerms([FromBody] AcceptSubscriptionTermsRequest request)
    {
        try
        {
            var ipAddress = HttpContext.Connection?.RemoteIpAddress?.ToString() ?? "Unknown";
            var userAgent = HttpContext.Request.Headers["User-Agent"].ToString();

            _logger.LogInformation("Vendor {VendorId} accepting subscription terms from IP {IpAddress}",
                request.VendorId, ipAddress);

            var command = new AcceptSubscriptionTermsCommand
            {
                Acceptance = new AcceptSubscriptionTermsDto
                {
                    VendorId = request.VendorId
                }
            };

            var result = await _mediator.Send(command);

            return Ok(new ApiResponse<VendorSubscriptionTermsAcceptanceDto>
            {
                Success = true,
                Message = "Subscription terms accepted successfully",
                Data = result
            });
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation while accepting subscription terms");
            return BadRequest(new ApiResponse<VendorSubscriptionTermsAcceptanceDto>
            {
                Success = false,
                Message = ex.Message
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error accepting subscription terms for vendor {VendorId}", request.VendorId);
            return StatusCode(500, new ApiResponse<VendorSubscriptionTermsAcceptanceDto>
            {
                Success = false,
                Message = "An error occurred while accepting subscription terms",
                Errors = new List<string> { ex.Message }
            });
        }
    }

    /// <summary>
    /// Get vendor's current subscription terms acceptance status
    /// </summary>
    /// <param name="vendorId">Vendor ID</param>
    /// <returns>Vendor's acceptance status</returns>
    [HttpGet("acceptance/{vendorId}")]
    [Authorize(Roles = "SuperAdmin,VendorAdmin")]
    public async Task<ActionResult<ApiResponse<VendorSubscriptionTermsAcceptanceDto>>> GetVendorTermsAcceptance(Guid vendorId)
    {
        try
        {
            _logger.LogInformation("Getting subscription terms acceptance for vendor {VendorId}", vendorId);

            var query = new GetVendorTermsAcceptanceQuery { VendorId = vendorId };
            var result = await _mediator.Send(query);

            if (result == null)
            {
                return Ok(new ApiResponse<VendorSubscriptionTermsAcceptanceDto>
                {
                    Success = false,
                    Message = "No subscription terms acceptance found for this vendor",
                    Data = null
                });
            }

            return Ok(new ApiResponse<VendorSubscriptionTermsAcceptanceDto>
            {
                Success = true,
                Message = "Vendor subscription terms acceptance retrieved successfully",
                Data = result
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving subscription terms acceptance for vendor {VendorId}", vendorId);
            return StatusCode(500, new ApiResponse<VendorSubscriptionTermsAcceptanceDto>
            {
                Success = false,
                Message = "An error occurred while retrieving vendor acceptance status",
                Errors = new List<string> { ex.Message }
            });
        }
    }

    /// <summary>
    /// Check if vendor has accepted current subscription terms
    /// </summary>
    /// <param name="vendorId">Vendor ID</param>
    /// <returns>Acceptance status</returns>
    [HttpGet("check-acceptance/{vendorId}")]
    [Authorize]
    public async Task<ActionResult<ApiResponse<bool>>> CheckVendorTermsAcceptance(Guid vendorId)
    {
        try
        {
            _logger.LogInformation("Checking subscription terms acceptance for vendor {VendorId}", vendorId);

            var query = new CheckVendorTermsAcceptanceQuery { VendorId = vendorId };
            var result = await _mediator.Send(query);

            return Ok(new ApiResponse<bool>
            {
                Success = true,
                Message = result ? "Vendor has accepted current subscription terms" : "Vendor has not accepted current subscription terms",
                Data = result
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking subscription terms acceptance for vendor {VendorId}", vendorId);
            return StatusCode(500, new ApiResponse<bool>
            {
                Success = false,
                Message = "An error occurred while checking vendor acceptance status",
                Errors = new List<string> { ex.Message }
            });
        }
    }




}
