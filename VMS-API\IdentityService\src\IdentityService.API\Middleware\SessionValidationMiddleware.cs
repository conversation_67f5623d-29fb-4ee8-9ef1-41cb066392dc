using System;
using System.Threading.Tasks;
using IdentityService.Application.Interfaces;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace IdentityService.API.Middleware;

/// <summary>
/// Middleware for validating user sessions
/// </summary>
public class SessionValidationMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<SessionValidationMiddleware> _logger;

    public SessionValidationMiddleware(RequestDelegate next, ILogger<SessionValidationMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context, ISessionManagementService sessionManagementService)
    {
        try
        {
            // Skip session validation for authentication endpoints
            if (context.Request.Path.StartsWithSegments("/api/auth") ||
                context.Request.Path.StartsWithSegments("/swagger") ||
                context.Request.Path.StartsWithSegments("/health"))
            {
                await _next(context);
                return;
            }

            // Get the token from the Authorization header
            var authHeader = context.Request.Headers["Authorization"].ToString();
            if (string.IsNullOrEmpty(authHeader) || !authHeader.StartsWith("Bearer "))
            {
                // No token, continue with the pipeline
                await _next(context);
                return;
            }

            var token = authHeader.Substring("Bearer ".Length).Trim();
            if (string.IsNullOrEmpty(token))
            {
                // No token, continue with the pipeline
                await _next(context);
                return;
            }

            // Validate the session
            var isValid = await sessionManagementService.ValidateSessionAsync(token);
            if (!isValid)
            {
                _logger.LogWarning("Invalid session for token {Token}", token);
                context.Response.StatusCode = StatusCodes.Status401Unauthorized;
                await context.Response.WriteAsJsonAsync(new { message = "Your session has expired. Please log in again." });
                return;
            }

            // Update the session activity
            await sessionManagementService.UpdateSessionActivityAsync(token);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating session");
        }

        // Continue with the pipeline
        await _next(context);
    }
}

/// <summary>
/// Extension methods for the session validation middleware
/// </summary>
public static class SessionValidationMiddlewareExtensions
{
    /// <summary>
    /// Adds the session validation middleware to the pipeline
    /// </summary>
    public static IApplicationBuilder UseSessionValidation(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<SessionValidationMiddleware>();
    }
}
