using BranchManagementService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BranchManagementService.Infrastructure.Persistence.Configurations
{
    public class BranchConfiguration : IEntityTypeConfiguration<Branch>
    {
        public void Configure(EntityTypeBuilder<Branch> builder)
        {
            builder.HasKey(b => b.Id);

            builder.Property(b => b.Name)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(b => b.Email)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(b => b.<PERSON><PERSON>erson)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(b => b.MobileNumber)
                .IsRequired()
                .HasMaxLength(20);

            builder.Property(b => b.Address)
                .IsRequired()
                .HasMaxLength(200);

            builder.Property(b => b.City)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(b => b.State)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(b => b.ZipCode)
                .IsRequired()
                .HasMaxLength(10);

            builder.Property(b => b.VehicleTypeSupport)
                .IsRequired()
                .HasConversion<int>();

            builder.Property(b => b.IsActive)
                .IsRequired()
                .HasDefaultValue(true);

            builder.Property(b => b.CreatedBy)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(b => b.CreatedAt)
                .IsRequired();

            builder.Property(b => b.UpdatedBy)
                .HasMaxLength(100);
        }
    }
}
