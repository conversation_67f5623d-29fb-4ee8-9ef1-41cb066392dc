using System;
using System.Collections.Generic;
using IdentityService.Domain.Common;

namespace IdentityService.Domain.Entities;

public class Role : BaseEntity
{
    public string Name { get; private set; }
    public string Description { get; private set; }
    public List<RolePermission> RolePermissions { get; private set; }
    public List<UserRole> UserRoles { get; private set; }

    protected Role()
    {
        RolePermissions = new List<RolePermission>();
        UserRoles = new List<UserRole>();
    }

    public static Role Create(string name, string description, string createdBy)
    {
        return new Role
        {
            Name = name,
            Description = description,
            CreatedBy = createdBy
        };
    }

    public void AddPermission(Permission permission, string addedBy)
    {
        RolePermissions.Add(RolePermission.Create(this, permission, addedBy));
    }

    public void RemovePermission(Permission permission)
    {
        RolePermissions.RemoveAll(rp => rp.PermissionId == permission.Id);
    }

    public void ClearPermissions()
    {
        RolePermissions.Clear();
    }

    public void UpdateDetails(string name, string description, string updatedBy)
    {
        Name = name;
        Description = description;
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;
    }
}