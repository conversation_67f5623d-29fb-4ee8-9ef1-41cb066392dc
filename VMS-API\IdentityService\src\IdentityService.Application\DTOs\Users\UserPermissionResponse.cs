using IdentityService.Domain.Enums;

namespace IdentityService.Application.DTOs.Users
{
    /// <summary>
    /// Response for user permission operations
    /// </summary>
    public class UserPermissionResponse
    {
        /// <summary>
        /// User permission ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// User ID
        /// </summary>
        public Guid UserId { get; set; }

        /// <summary>
        /// Permission ID
        /// </summary>
        public Guid PermissionId { get; set; }

        /// <summary>
        /// Permission name
        /// </summary>
        public string PermissionName { get; set; } = string.Empty;

        /// <summary>
        /// Permission type (Grant or Deny)
        /// </summary>
        public PermissionType Type { get; set; }

        /// <summary>
        /// Optional expiration date
        /// </summary>
        public DateTime? ExpiresAt { get; set; }

        /// <summary>
        /// Reason for assignment
        /// </summary>
        public string? Reason { get; set; }

        /// <summary>
        /// Whether the permission is currently active
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// When the permission was created
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Who created the permission
        /// </summary>
        public string CreatedBy { get; set; } = string.Empty;

        /// <summary>
        /// When the permission was last updated
        /// </summary>
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// Who last updated the permission
        /// </summary>
        public string? UpdatedBy { get; set; }
    }
}
