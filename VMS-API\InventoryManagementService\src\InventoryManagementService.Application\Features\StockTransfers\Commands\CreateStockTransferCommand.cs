using AutoMapper;
using FluentValidation;
using InventoryManagementService.Application.DTOs;
using InventoryManagementService.Domain.Entities;
using InventoryManagementService.Domain.Interfaces;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using VMS.Contracts.Common.Authorization;

namespace InventoryManagementService.Application.Features.StockTransfers.Commands;

public class CreateStockTransferCommand : IRequest<StockTransferDto>
{
    public CreateStockTransferDto Transfer { get; set; } = null!;
    public string CreatedBy { get; set; } = string.Empty;
}

public class CreateStockTransferCommandValidator : AbstractValidator<CreateStockTransferCommand>
{
    public CreateStockTransferCommandValidator()
    {
        RuleFor(x => x.Transfer.VendorId)
            .NotEmpty().WithMessage("Vendor ID is required");

        RuleFor(x => x.Transfer.SourceBranchId)
            .NotEmpty().WithMessage("Source branch ID is required");

        RuleFor(x => x.Transfer.DestinationBranchId)
            .NotEmpty().WithMessage("Destination branch ID is required");

        RuleFor(x => x.Transfer)
            .Must(x => x.SourceBranchId != x.DestinationBranchId)
            .WithMessage("Source and destination branches cannot be the same");

        RuleFor(x => x.Transfer.Notes)
            .MaximumLength(1000).WithMessage("Notes cannot exceed 1000 characters");

        RuleFor(x => x.CreatedBy)
            .NotEmpty().WithMessage("CreatedBy is required");
    }
}

public class CreateStockTransferCommandHandler : IRequestHandler<CreateStockTransferCommand, StockTransferDto>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly ILogger<CreateStockTransferCommandHandler> _logger;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly IEventPublisher _eventPublisher;

    public CreateStockTransferCommandHandler(
        IUnitOfWork unitOfWork,
        IMapper mapper,
        ILogger<CreateStockTransferCommandHandler> logger,
        IHttpContextAccessor httpContextAccessor,
        IEventPublisher eventPublisher)
    {
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _logger = logger;
        _httpContextAccessor = httpContextAccessor;
        _eventPublisher = eventPublisher;
    }

    public async Task<StockTransferDto> Handle(CreateStockTransferCommand request, CancellationToken cancellationToken)
    {
        // Check permissions - Admin roles bypass, others need explicit permission
        var user = _httpContextAccessor.HttpContext?.User;
        if (!PermissionHelper.IsAdmin(user))
        {
            PermissionHelper.ValidateAccess(user, "Transfers.Create");
        }

        _logger.LogInformation("Creating stock transfer from branch {SourceBranchId} to {DestinationBranchId}", 
            request.Transfer.SourceBranchId, request.Transfer.DestinationBranchId);

        // Generate transfer number
        var transferNumber = await GenerateTransferNumber();

        var stockTransfer = StockTransfer.Create(
            request.Transfer.VendorId,
            request.Transfer.SourceBranchId,
            request.Transfer.DestinationBranchId,
            transferNumber,
            request.CreatedBy,
            request.Transfer.Notes);

        await _unitOfWork.StockTransfers.AddAsync(stockTransfer);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("Stock transfer created successfully with ID: {TransferId}", stockTransfer.Id);

        // Publish event
        await _eventPublisher.PublishTransferCreatedEvent(
            stockTransfer.Id,
            stockTransfer.TransferNumber,
            stockTransfer.SourceBranchId,
            stockTransfer.DestinationBranchId);

        return _mapper.Map<StockTransferDto>(stockTransfer);
    }

    private async Task<string> GenerateTransferNumber()
    {
        // Generate a unique transfer number
        var timestamp = DateTime.UtcNow.ToString("yyyyMMddHHmmss");
        var random = new Random().Next(1000, 9999);
        var transferNumber = $"TRF-{timestamp}-{random}";

        // Check if it already exists (very unlikely but good practice)
        while (await _unitOfWork.StockTransfers.TransferNumberExistsAsync(transferNumber))
        {
            random = new Random().Next(1000, 9999);
            transferNumber = $"TRF-{timestamp}-{random}";
        }

        return transferNumber;
    }
}
