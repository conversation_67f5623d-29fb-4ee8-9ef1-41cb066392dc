using InventoryManagementService.Domain.Entities;
using InventoryManagementService.Domain.Enums;
using System.Linq.Expressions;

namespace InventoryManagementService.Domain.Interfaces;

public interface IPartRepository
{
    Task<Part?> GetByIdAsync(Guid id);
    Task<Part?> GetBySkuAsync(string sku);
    Task<List<Part>> GetAllAsync(bool includeInactive = false);
    Task<List<Part>> GetByCategoryAsync(PartCategory category, bool includeInactive = false);
    Task<List<Part>> GetByManufacturerAsync(string manufacturer, bool includeInactive = false);
    Task<List<Part>> GetByCompatibilityAsync(string make, string model, int year, bool includeInactive = false);
    Task<List<Part>> SearchAsync(string searchTerm, bool includeInactive = false);
    Task<List<Part>> GetAsync(Expression<Func<Part, bool>> predicate);
    Task AddAsync(Part part);
    Task UpdateAsync(Part part);
    Task DeleteAsync(Guid id);
    Task<bool> ExistsAsync(Guid id);
    Task<bool> SkuExistsAsync(string sku, Guid? excludeId = null);
}
