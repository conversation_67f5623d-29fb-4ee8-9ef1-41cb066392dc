using System;
using System.Threading.Tasks;
using IdentityService.Application.DTOs.Auth;
using IdentityService.Application.Features.Auth.Commands;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace IdentityService.API.Controllers;

[ApiController]
[Route("api/[controller]")]
public class AuthController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<AuthController> _logger;

    public AuthController(IMediator mediator, ILogger<AuthController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    [HttpPost("login")]
    public async Task<ActionResult<LoginResponse>> Login([FromBody] LoginRequest request)
    {
        try
        {
            var command = new LoginCommand { Request = request };
            var response = await _mediator.Send(command);
            return Ok(response);
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Lo<PERSON> failed for user");
            return Unauthorized(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during login");
            return StatusCode(500, new { message = "An error occurred during login" });
        }
    }

    [Authorize]
    [HttpPost("change-password")]
    public async Task<ActionResult<bool>> ChangePassword([FromBody] ChangePasswordRequest request)
    {
        try
        {
            // The UserId will be automatically set by the UserIdPropagationBehavior
            var command = new ChangePasswordCommand { Request = request };

            // Log the command for debugging
            _logger.LogInformation("Sending ChangePasswordCommand");

            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access during password change");
            return Unauthorized(new { message = ex.Message });
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid argument during password change");
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during password change");
            return StatusCode(500, new { message = "An error occurred while changing password" });
        }
    }

    [HttpPost("forgot-password")]
    public async Task<ActionResult<bool>> ForgotPassword([FromBody] ForgotPasswordRequest request)
    {
        try
        {
            var command = new ForgotPasswordCommand { Request = request };
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (InvalidOperationException ex) when (ex.Message.Contains("deleted by another user"))
        {
            // This is a concurrency exception - the token was deleted by another process
            _logger.LogWarning(ex, "Concurrency conflict during forgot password request");

            // Return true to prevent email enumeration attacks
            // This is the same behavior as a successful request
            return Ok(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during forgot password request");
            return StatusCode(500, new { message = "An error occurred while processing forgot password request" });
        }
    }

    [HttpPost("reset-password")]
    public async Task<ActionResult<bool>> ResetPassword([FromBody] ResetPasswordRequest request)
    {
        try
        {
            var command = new ResetPasswordCommand { Request = request };
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid argument during password reset");
            return BadRequest(new { message = ex.Message });
        }
        catch (InvalidOperationException ex) when (ex.Message.Contains("deleted by another user"))
        {
            // This is a concurrency exception - the token was deleted by another process
            _logger.LogWarning(ex, "Concurrency conflict during password reset");
            return BadRequest(new { message = "Invalid or expired reset token. Please request a new password reset link." });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during password reset");
            return StatusCode(500, new { message = "An error occurred while resetting password" });
        }
    }

    [HttpPost("refresh-token")]
    public async Task<ActionResult<LoginResponse>> RefreshToken([FromBody] RefreshTokenRequest request)
    {
        try
        {
            var command = new RefreshTokenCommand { Request = request };
            var response = await _mediator.Send(command);
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during token refresh");
            return StatusCode(500, new { message = "An error occurred while refreshing token" });
        }
    }

    [Authorize]
    [HttpPost("logout")]
    public async Task<ActionResult<bool>> Logout([FromQuery] bool logoutAllSessions = false)
    {
        try
        {
            var authHeader = HttpContext.Request.Headers["Authorization"].ToString();
            if (string.IsNullOrEmpty(authHeader) || !authHeader.StartsWith("Bearer "))
            {
                return BadRequest(new { message = "Invalid authorization header" });
            }

            var token = authHeader.Substring("Bearer ".Length).Trim();
            var command = new LogoutCommand
            {
                Token = token,
                LogoutAllSessions = logoutAllSessions
            };

            var result = await _mediator.Send(command);
            return Ok(new { success = result, message = result ? "Logged out successfully" : "Logout failed" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during logout");
            return StatusCode(500, new { message = "An error occurred during logout" });
        }
    }
}