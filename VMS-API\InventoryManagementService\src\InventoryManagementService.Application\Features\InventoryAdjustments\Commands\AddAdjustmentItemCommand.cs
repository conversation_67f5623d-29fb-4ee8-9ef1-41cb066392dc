using FluentValidation;
using InventoryManagementService.Domain.Interfaces;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using VMS.Contracts.Common.Authorization;

namespace InventoryManagementService.Application.Features.InventoryAdjustments.Commands;

public class AddAdjustmentItemCommand : IRequest
{
    public Guid AdjustmentId { get; set; }
    public Guid PartId { get; set; }
    public int PreviousQuantity { get; set; }
    public int NewQuantity { get; set; }
    public string? Notes { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
}

public class AddAdjustmentItemCommandValidator : AbstractValidator<AddAdjustmentItemCommand>
{
    public AddAdjustmentItemCommandValidator()
    {
        RuleFor(x => x.AdjustmentId)
            .NotEmpty().WithMessage("Adjustment ID is required");

        RuleFor(x => x.PartId)
            .NotEmpty().WithMessage("Part ID is required");

        RuleFor(x => x.PreviousQuantity)
            .GreaterThanOrEqualTo(0).WithMessage("Previous quantity cannot be negative");

        RuleFor(x => x.NewQuantity)
            .GreaterThanOrEqualTo(0).WithMessage("New quantity cannot be negative");

        RuleFor(x => x.Notes)
            .MaximumLength(500).WithMessage("Notes cannot exceed 500 characters");

        RuleFor(x => x.CreatedBy)
            .NotEmpty().WithMessage("CreatedBy is required");
    }
}

public class AddAdjustmentItemCommandHandler : IRequestHandler<AddAdjustmentItemCommand>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<AddAdjustmentItemCommandHandler> _logger;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public AddAdjustmentItemCommandHandler(
        IUnitOfWork unitOfWork,
        ILogger<AddAdjustmentItemCommandHandler> logger,
        IHttpContextAccessor httpContextAccessor)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task Handle(AddAdjustmentItemCommand request, CancellationToken cancellationToken)
    {
        // Check permissions - Admin roles bypass, others need explicit permission
        var user = _httpContextAccessor.HttpContext?.User;
        if (!PermissionHelper.IsAdmin(user))
        {
            PermissionHelper.ValidateAccess(user, "Adjustments.Update");
        }

        _logger.LogInformation("Adding item to adjustment {AdjustmentId}: Part {PartId}", 
            request.AdjustmentId, request.PartId);

        var adjustment = await _unitOfWork.InventoryAdjustments.GetByIdAsync(request.AdjustmentId);

        if (adjustment == null)
        {
            throw new KeyNotFoundException($"Inventory adjustment with ID {request.AdjustmentId} not found");
        }

        // Check if part exists
        if (!await _unitOfWork.Parts.ExistsAsync(request.PartId))
        {
            throw new KeyNotFoundException($"Part with ID {request.PartId} not found");
        }

        adjustment.AddItem(
            request.PartId,
            request.PreviousQuantity,
            request.NewQuantity,
            request.CreatedBy,
            request.Notes);

        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("Item added successfully to adjustment {AdjustmentId}", adjustment.Id);
    }
}
