# Use the official .NET 8 SDK image for building
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# Copy solution file
COPY InventoryManagementService.sln ./

# Copy project files
COPY src/InventoryManagementService.API/InventoryManagementService.API.csproj src/InventoryManagementService.API/
COPY src/InventoryManagementService.Application/InventoryManagementService.Application.csproj src/InventoryManagementService.Application/
COPY src/InventoryManagementService.Domain/InventoryManagementService.Domain.csproj src/InventoryManagementService.Domain/
COPY src/InventoryManagementService.Infrastructure/InventoryManagementService.Infrastructure.csproj src/InventoryManagementService.Infrastructure/

# Copy VMSContracts project
COPY ../VMSContracts/VMSContracts.csproj ../VMSContracts/

# Restore dependencies
RUN dotnet restore

# Copy the rest of the source code
COPY . .

# Build the application
WORKDIR /src/src/InventoryManagementService.API
RUN dotnet build -c Release -o /app/build

# Publish the application
RUN dotnet publish -c Release -o /app/publish

# Use the official .NET 8 runtime image for running
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS runtime
WORKDIR /app

# Copy the published application
COPY --from=build /app/publish .

# Expose the port
EXPOSE 8080

# Set the entry point
ENTRYPOINT ["dotnet", "InventoryManagementService.API.dll"]
