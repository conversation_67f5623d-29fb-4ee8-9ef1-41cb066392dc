using InventoryManagementService.Domain.Entities;
using InventoryManagementService.Domain.Enums;
using InventoryManagementService.Domain.Interfaces;
using InventoryManagementService.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;

namespace InventoryManagementService.Infrastructure.Repositories;

public class PartRepository : IPartRepository
{
    private readonly InventoryDbContext _context;

    public PartRepository(InventoryDbContext context)
    {
        _context = context;
    }

    public async Task<Part?> GetByIdAsync(Guid id)
    {
        return await _context.Parts
            .Include(p => p.Compatibilities)
            .FirstOrDefaultAsync(p => p.Id == id);
    }

    public async Task<Part?> GetBySkuAsync(string sku)
    {
        return await _context.Parts
            .Include(p => p.Compatibilities)
            .FirstOrDefaultAsync(p => p.SKU == sku.ToUpper());
    }

    public async Task<List<Part>> GetAllAsync(bool includeInactive = false)
    {
        IQueryable<Part> query = _context.Parts.Include(p => p.Compatibilities);

        if (!includeInactive)
        {
            query = query.Where(p => p.IsActive);
        }

        return await query.OrderBy(p => p.Name).ToListAsync();
    }

    public async Task<List<Part>> GetByCategoryAsync(PartCategory category, bool includeInactive = false)
    {
        IQueryable<Part> query = _context.Parts
            .Include(p => p.Compatibilities)
            .Where(p => p.Category == category);

        if (!includeInactive)
        {
            query = query.Where(p => p.IsActive);
        }

        return await query.OrderBy(p => p.Name).ToListAsync();
    }

    public async Task<List<Part>> GetByManufacturerAsync(string manufacturer, bool includeInactive = false)
    {
        IQueryable<Part> query = _context.Parts
            .Include(p => p.Compatibilities)
            .Where(p => p.Manufacturer.ToLower().Contains(manufacturer.ToLower()));

        if (!includeInactive)
        {
            query = query.Where(p => p.IsActive);
        }

        return await query.OrderBy(p => p.Name).ToListAsync();
    }

    public async Task<List<Part>> GetByCompatibilityAsync(string make, string model, int year, bool includeInactive = false)
    {
        IQueryable<Part> query = _context.Parts
            .Include(p => p.Compatibilities)
            .Where(p => p.Compatibilities.Any(c =>
                c.Make.ToLower() == make.ToLower() &&
                c.Model.ToLower() == model.ToLower() &&
                c.YearFrom <= year &&
                c.YearTo >= year));

        if (!includeInactive)
        {
            query = query.Where(p => p.IsActive);
        }

        return await query.OrderBy(p => p.Name).ToListAsync();
    }

    public async Task<List<Part>> SearchAsync(string searchTerm, bool includeInactive = false)
    {
        IQueryable<Part> query = _context.Parts
            .Include(p => p.Compatibilities)
            .Where(p =>
                p.Name.ToLower().Contains(searchTerm.ToLower()) ||
                p.Description.ToLower().Contains(searchTerm.ToLower()) ||
                p.SKU.ToLower().Contains(searchTerm.ToLower()) ||
                p.Manufacturer.ToLower().Contains(searchTerm.ToLower()) ||
                (p.PartNumber != null && p.PartNumber.ToLower().Contains(searchTerm.ToLower())));

        if (!includeInactive)
        {
            query = query.Where(p => p.IsActive);
        }

        return await query.OrderBy(p => p.Name).ToListAsync();
    }

    public async Task<List<Part>> GetAsync(Expression<Func<Part, bool>> predicate)
    {
        return await _context.Parts
            .Include(p => p.Compatibilities)
            .Where(predicate)
            .OrderBy(p => p.Name)
            .ToListAsync();
    }

    public async Task AddAsync(Part part)
    {
        await _context.Parts.AddAsync(part);
    }

    public async Task UpdateAsync(Part part)
    {
        _context.Parts.Update(part);
        await Task.CompletedTask;
    }

    public async Task DeleteAsync(Guid id)
    {
        var part = await _context.Parts.FindAsync(id);
        if (part != null)
        {
            _context.Parts.Remove(part);
        }
    }

    public async Task<bool> ExistsAsync(Guid id)
    {
        return await _context.Parts.AnyAsync(p => p.Id == id);
    }

    public async Task<bool> SkuExistsAsync(string sku, Guid? excludeId = null)
    {
        var query = _context.Parts.Where(p => p.SKU == sku.ToUpper());
        
        if (excludeId.HasValue)
        {
            query = query.Where(p => p.Id != excludeId.Value);
        }

        return await query.AnyAsync();
    }
}
