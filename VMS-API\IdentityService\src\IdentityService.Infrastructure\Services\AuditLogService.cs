using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using IdentityService.Application.Interfaces;
using IdentityService.Domain.Entities;
using IdentityService.Domain.Interfaces;
using Microsoft.AspNetCore.Http;

namespace IdentityService.Infrastructure.Services;

public class AuditLogService : IAuditLogService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly ICurrentUserService _currentUserService;

    public AuditLogService(
        IUnitOfWork unitOfWork,
        IHttpContextAccessor httpContextAccessor,
        ICurrentUserService currentUserService)
    {
        _unitOfWork = unitOfWork;
        _httpContextAccessor = httpContextAccessor;
        _currentUserService = currentUserService;
    }

    public async Task<AuditLog> CreateAuditLogAsync(
        string action,
        string entityName,
        string entityId,
        string oldValues,
        string newValues,
        string affectedColumns,
        Guid userId)
    {
        var user = await _unitOfWork.UserRepository.GetByIdAsync(userId);
        if (user == null)
            throw new ArgumentException($"User with ID {userId} not found");

        var ipAddress = _httpContextAccessor.HttpContext?.Connection?.RemoteIpAddress?.ToString() ?? "Unknown";
        var userAgent = _httpContextAccessor.HttpContext?.Request?.Headers["User-Agent"].ToString() ?? "Unknown";
        var createdBy = _currentUserService.UserId.ToString() ?? "System";

        var auditLog = AuditLog.Create(
            action,
            entityName,
            entityId,
            oldValues,
            newValues,
            affectedColumns,
            ipAddress,
            userAgent,
            userId,
            user,
            createdBy);

        await _unitOfWork.AuditLogRepository.AddAsync(auditLog);
        await _unitOfWork.SaveChangesAsync();

        return auditLog;
    }

    public async Task<(IReadOnlyList<AuditLog> Items, int TotalCount)> GetFilteredAuditLogsAsync(
        string? action = null,
        string? entityName = null,
        string? entityId = null,
        Guid? userId = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        int pageNumber = 1,
        int pageSize = 10)
    {
        return await _unitOfWork.AuditLogRepository.GetFilteredAsync(
            action,
            entityName,
            entityId,
            userId,
            fromDate,
            toDate,
            pageNumber,
            pageSize);
    }

    public async Task<AuditLog?> GetAuditLogByIdAsync(Guid id)
    {
        return await _unitOfWork.AuditLogRepository.GetByIdAsync(id);
    }

    public async Task<byte[]> ExportToCsvAsync(IReadOnlyList<AuditLog> auditLogs)
    {
        return await _unitOfWork.AuditLogRepository.ExportToCsvAsync(auditLogs);
    }

    public async Task<byte[]> ExportToPdfAsync(IReadOnlyList<AuditLog> auditLogs)
    {
        return await _unitOfWork.AuditLogRepository.ExportToPdfAsync(auditLogs);
    }
}
