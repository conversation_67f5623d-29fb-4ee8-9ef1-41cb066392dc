using FluentValidation;
using InventoryManagementService.Application.DTOs;
using InventoryManagementService.Domain.Interfaces;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using VMS.Contracts.Common.Authorization;

namespace InventoryManagementService.Application.Features.Parts.Commands;

public class AddPartCompatibilityCommand : IRequest
{
    public Guid PartId { get; set; }
    public AddPartCompatibilityDto Compatibility { get; set; } = null!;
    public string CreatedBy { get; set; } = string.Empty;
}

public class AddPartCompatibilityCommandValidator : AbstractValidator<AddPartCompatibilityCommand>
{
    public AddPartCompatibilityCommandValidator()
    {
        RuleFor(x => x.PartId)
            .NotEmpty().WithMessage("Part ID is required");

        RuleFor(x => x.Compatibility.Make)
            .NotEmpty().WithMessage("Make is required")
            .MaximumLength(50).WithMessage("Make cannot exceed 50 characters");

        RuleFor(x => x.Compatibility.Model)
            .NotEmpty().WithMessage("Model is required")
            .MaximumLength(50).WithMessage("Model cannot exceed 50 characters");

        RuleFor(x => x.Compatibility.YearFrom)
            .GreaterThan(1900).WithMessage("Year from must be greater than 1900")
            .LessThanOrEqualTo(DateTime.Now.Year + 1).WithMessage("Year from cannot be in the future");

        RuleFor(x => x.Compatibility.YearTo)
            .GreaterThan(1900).WithMessage("Year to must be greater than 1900")
            .LessThanOrEqualTo(DateTime.Now.Year + 1).WithMessage("Year to cannot be in the future");

        RuleFor(x => x.Compatibility)
            .Must(x => x.YearTo >= x.YearFrom)
            .WithMessage("Year to must be greater than or equal to year from");

        RuleFor(x => x.CreatedBy)
            .NotEmpty().WithMessage("CreatedBy is required");
    }
}

public class AddPartCompatibilityCommandHandler : IRequestHandler<AddPartCompatibilityCommand>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<AddPartCompatibilityCommandHandler> _logger;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public AddPartCompatibilityCommandHandler(
        IUnitOfWork unitOfWork,
        ILogger<AddPartCompatibilityCommandHandler> logger,
        IHttpContextAccessor httpContextAccessor)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task Handle(AddPartCompatibilityCommand request, CancellationToken cancellationToken)
    {
        // Check permissions - Admin roles bypass, others need explicit permission
        var user = _httpContextAccessor.HttpContext?.User;
        if (!PermissionHelper.IsAdmin(user))
        {
            PermissionHelper.ValidateAccess(user, "Parts.Update");
        }

        _logger.LogInformation("Adding compatibility to part with ID: {PartId}", request.PartId);

        var part = await _unitOfWork.Parts.GetByIdAsync(request.PartId);

        if (part == null)
        {
            throw new KeyNotFoundException($"Part with ID {request.PartId} not found");
        }

        part.AddCompatibility(
            request.Compatibility.Make,
            request.Compatibility.Model,
            request.Compatibility.YearFrom,
            request.Compatibility.YearTo,
            request.CreatedBy);

        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("Compatibility added successfully to part with ID: {PartId}", part.Id);
    }
}
