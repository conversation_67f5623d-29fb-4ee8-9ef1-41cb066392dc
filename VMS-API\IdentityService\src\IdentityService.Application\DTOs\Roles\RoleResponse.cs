using System;
using System.Collections.Generic;

namespace IdentityService.Application.DTOs.Roles;

public class RoleResponse
{
    public Guid Id { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreatedBy { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string? UpdatedBy { get; set; }
    public List<RolePermissionResponse> Permissions { get; set; } = new List<RolePermissionResponse>();
}

public class RolePermissionResponse
{
    public Guid Id { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
    public string Resource { get; set; }
    public string Action { get; set; }
}
