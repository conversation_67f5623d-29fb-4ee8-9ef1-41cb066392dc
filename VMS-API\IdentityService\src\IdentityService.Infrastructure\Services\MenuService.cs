using IdentityService.Domain.Entities;
using IdentityService.Domain.Interfaces;
using IdentityService.Domain.Services;
using Microsoft.Extensions.Logging;

namespace IdentityService.Infrastructure.Services;

/// <summary>
/// Implementation of dynamic menu service
/// </summary>
public class MenuService : IMenuService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IPermissionService _permissionService;
    private readonly ILogger<MenuService> _logger;

    public MenuService(
        IUnitOfWork unitOfWork, 
        IPermissionService permissionService,
        ILogger<MenuService> logger)
    {
        _unitOfWork = unitOfWork;
        _permissionService = permissionService;
        _logger = logger;
    }

    public async Task<List<MenuItemDto>> GetUserMenuAsync(Guid userId)
    {
        try
        {
            var allMenus = await _unitOfWork.MenuRepository.GetAllAsync();
            var userPermissions = await _permissionService.GetEffectivePermissionsAsync(userId);
            var userPermissionNames = userPermissions.Select(p => p.Name).ToHashSet();

            var menuDtos = new List<MenuItemDto>();

            // Build menu hierarchy
            var rootMenus = allMenus.Where(m => m.ParentId == null).OrderBy(m => m.Order);

            foreach (var menu in rootMenus)
            {
                var menuDto = await BuildMenuItemAsync(menu, allMenus, userPermissionNames);
                if (menuDto.HasAccess || menuDto.Children.Any(c => c.HasAccess))
                {
                    menuDtos.Add(menuDto);
                }
            }

            _logger.LogDebug("Built menu structure with {Count} root items for user {UserId}", 
                menuDtos.Count, userId);

            return menuDtos;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error building user menu for user {UserId}", userId);
            throw;
        }
    }

    public async Task<List<MenuItemDto>> GetAllMenusAsync()
    {
        try
        {
            var allMenus = await _unitOfWork.MenuRepository.GetAllAsync();
            var menuDtos = new List<MenuItemDto>();

            var rootMenus = allMenus.Where(m => m.ParentId == null).OrderBy(m => m.Order);

            foreach (var menu in rootMenus)
            {
                var menuDto = await BuildMenuItemAsync(menu, allMenus, new HashSet<string>(), true);
                menuDtos.Add(menuDto);
            }

            return menuDtos;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all menus");
            throw;
        }
    }

    public async Task<MenuItemDto> CreateMenuAsync(CreateMenuDto menuDto, string createdBy)
    {
        try
        {
            var menu = Menu.Create(
                menuDto.Name,
                menuDto.DisplayName,
                menuDto.Path,
                menuDto.Icon,
                menuDto.Order,
                menuDto.ParentId,
                createdBy);

            await _unitOfWork.MenuRepository.AddAsync(menu);

            // Assign permissions if provided
            if (menuDto.PermissionIds.Any())
            {
                foreach (var permissionId in menuDto.PermissionIds)
                {
                    var permission = await _unitOfWork.PermissionRepository.GetByIdAsync(permissionId);
                    if (permission != null)
                    {
                        menu.AddPermission(permission, createdBy);
                    }
                }
            }

            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Created menu {MenuName} by {CreatedBy}", menuDto.Name, createdBy);

            return new MenuItemDto
            {
                Id = menu.Id,
                Name = menu.Name,
                DisplayName = menu.DisplayName,
                Path = menu.Path,
                Icon = menu.Icon,
                Order = menu.Order,
                ParentId = menu.ParentId,
                IsVisible = true,
                HasAccess = true
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating menu {MenuName}", menuDto.Name);
            throw;
        }
    }

    public async Task<MenuItemDto> UpdateMenuAsync(Guid menuId, UpdateMenuDto menuDto, string updatedBy)
    {
        try
        {
            var menu = await _unitOfWork.MenuRepository.GetByIdAsync(menuId);
            if (menu == null)
                throw new InvalidOperationException($"Menu {menuId} not found");

            menu.Update(
                menuDto.DisplayName,
                menuDto.Path,
                menuDto.Icon,
                menuDto.Order,
                menuDto.ParentId,
                updatedBy);

            await _unitOfWork.MenuRepository.UpdateAsync(menu);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Updated menu {MenuId} by {UpdatedBy}", menuId, updatedBy);

            return new MenuItemDto
            {
                Id = menu.Id,
                Name = menu.Name,
                DisplayName = menu.DisplayName,
                Path = menu.Path,
                Icon = menu.Icon,
                Order = menu.Order,
                ParentId = menu.ParentId,
                IsVisible = true,
                HasAccess = true
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating menu {MenuId}", menuId);
            throw;
        }
    }

    public async Task AssignMenuPermissionsAsync(Guid menuId, List<Guid> permissionIds, string assignedBy)
    {
        try
        {
            var menu = await _unitOfWork.MenuRepository.GetByIdAsync(menuId);
            if (menu == null)
                throw new InvalidOperationException($"Menu {menuId} not found");

            // Clear existing permissions
            menu.ClearPermissions();

            // Add new permissions
            foreach (var permissionId in permissionIds)
            {
                var permission = await _unitOfWork.PermissionRepository.GetByIdAsync(permissionId);
                if (permission != null)
                {
                    menu.AddPermission(permission, assignedBy);
                }
            }

            await _unitOfWork.MenuRepository.UpdateAsync(menu);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Assigned {Count} permissions to menu {MenuId} by {AssignedBy}", 
                permissionIds.Count, menuId, assignedBy);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assigning permissions to menu {MenuId}", menuId);
            throw;
        }
    }

    public async Task<bool> HasMenuAccessAsync(Guid userId, Guid menuId)
    {
        try
        {
            var menu = await _unitOfWork.MenuRepository.GetByIdAsync(menuId);
            if (menu == null)
                return false;

            // If menu has no permissions, it's public
            if (!menu.MenuPermissions.Any())
                return true;

            // Check if user has any of the required permissions
            foreach (var menuPermission in menu.MenuPermissions)
            {
                if (menuPermission.PermissionId.HasValue)
                {
                    var permission = await _unitOfWork.PermissionRepository.GetByIdAsync(menuPermission.PermissionId.Value);
                    if (permission != null)
                    {
                        var hasPermission = await _permissionService.HasPermissionAsync(userId, permission.Name);
                        if (hasPermission)
                            return true;
                    }
                }
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking menu access for user {UserId} and menu {MenuId}", userId, menuId);
            return false;
        }
    }

    public async Task<List<BreadcrumbItem>> GetBreadcrumbAsync(Guid userId, string path)
    {
        try
        {
            var breadcrumbs = new List<BreadcrumbItem>();
            var allMenus = await _unitOfWork.MenuRepository.GetAllAsync();
            
            // Find the menu item for the current path
            var currentMenu = allMenus.FirstOrDefault(m => m.Path == path);
            if (currentMenu == null)
                return breadcrumbs;

            // Build breadcrumb trail
            var menuChain = new List<Menu>();
            var menu = currentMenu;
            
            while (menu != null)
            {
                menuChain.Insert(0, menu);
                menu = menu.ParentId.HasValue ? allMenus.FirstOrDefault(m => m.Id == menu.ParentId.Value) : null;
            }

            // Convert to breadcrumb items
            for (int i = 0; i < menuChain.Count; i++)
            {
                var menuItem = menuChain[i];
                var hasAccess = await HasMenuAccessAsync(userId, menuItem.Id);
                
                if (hasAccess)
                {
                    breadcrumbs.Add(new BreadcrumbItem
                    {
                        DisplayName = menuItem.DisplayName,
                        Path = menuItem.Path,
                        IsActive = i == menuChain.Count - 1
                    });
                }
            }

            return breadcrumbs;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error building breadcrumb for path {Path}", path);
            return new List<BreadcrumbItem>();
        }
    }

    private async Task<MenuItemDto> BuildMenuItemAsync(
        Menu menu, 
        IEnumerable<Menu> allMenus, 
        HashSet<string> userPermissionNames, 
        bool isAdminView = false)
    {
        var hasAccess = isAdminView || await CheckMenuAccess(menu, userPermissionNames);
        
        var menuDto = new MenuItemDto
        {
            Id = menu.Id,
            Name = menu.Name,
            DisplayName = menu.DisplayName,
            Path = menu.Path,
            Icon = menu.Icon,
            Order = menu.Order,
            ParentId = menu.ParentId,
            IsVisible = !menu.IsDeleted,
            HasAccess = hasAccess,
            RequiredPermissions = menu.MenuPermissions
                .Where(mp => mp.Permission != null)
                .Select(mp => mp.Permission.Name)
                .ToList()
        };

        // Build children
        var children = allMenus.Where(m => m.ParentId == menu.Id).OrderBy(m => m.Order);
        foreach (var child in children)
        {
            var childDto = await BuildMenuItemAsync(child, allMenus, userPermissionNames, isAdminView);
            if (isAdminView || childDto.HasAccess || childDto.Children.Any(c => c.HasAccess))
            {
                menuDto.Children.Add(childDto);
            }
        }

        return menuDto;
    }

    private async Task<bool> CheckMenuAccess(Menu menu, HashSet<string> userPermissionNames)
    {
        // If menu has no permissions, it's public
        if (!menu.MenuPermissions.Any())
            return true;

        // Check if user has any of the required permissions
        foreach (var menuPermission in menu.MenuPermissions)
        {
            if (menuPermission.Permission != null &&
                userPermissionNames.Contains(menuPermission.Permission.Name))
            {
                return true;
            }
        }

        return false;
    }

    public async Task<List<MenuItemDto>> GetAdminMenuAsync()
    {
        try
        {
            _logger.LogInformation("Getting admin menu (all menus)");

            var allMenus = await _unitOfWork.MenuRepository.GetAllAsync();
            var rootMenus = allMenus.Where(m => m.ParentId == null && !m.IsDeleted).OrderBy(m => m.Order);

            var menuItems = new List<MenuItemDto>();
            foreach (var menu in rootMenus)
            {
                var menuItem = await BuildMenuItemAsync(menu, allMenus, new HashSet<string>(), isAdminView: true);
                menuItems.Add(menuItem);
            }

            _logger.LogInformation("Successfully retrieved admin menu with {Count} items", menuItems.Count);
            return menuItems;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting admin menu");
            throw;
        }
    }

    public async Task<List<BreadcrumbItem>> GenerateBreadcrumbsAsync(string currentPath, Guid userId)
    {
        try
        {
            _logger.LogInformation("Generating breadcrumbs for path: {Path}, user: {UserId}", currentPath, userId);

            var breadcrumbs = new List<BreadcrumbItem>();

            if (string.IsNullOrEmpty(currentPath))
            {
                return breadcrumbs;
            }

            // Get all menus
            var allMenus = await _unitOfWork.MenuRepository.GetAllAsync();

            // Find the menu that matches the current path
            var currentMenu = allMenus.FirstOrDefault(m => m.Path.Equals(currentPath, StringComparison.OrdinalIgnoreCase));

            if (currentMenu == null)
            {
                return breadcrumbs;
            }

            // Build breadcrumb trail by traversing up the hierarchy
            var menuHierarchy = new List<Menu>();
            var menu = currentMenu;

            while (menu != null)
            {
                menuHierarchy.Insert(0, menu);
                menu = menu.ParentId.HasValue ? allMenus.FirstOrDefault(m => m.Id == menu.ParentId.Value) : null;
            }

            // Convert to breadcrumb items
            for (int i = 0; i < menuHierarchy.Count; i++)
            {
                var menuItem = menuHierarchy[i];
                breadcrumbs.Add(new BreadcrumbItem
                {
                    DisplayName = menuItem.DisplayName,
                    Path = menuItem.Path,
                    IsActive = i == menuHierarchy.Count - 1 // Last item is active
                });
            }

            _logger.LogInformation("Generated {Count} breadcrumb items for path: {Path}", breadcrumbs.Count, currentPath);
            return breadcrumbs;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating breadcrumbs for path: {Path}", currentPath);
            throw;
        }
    }
}
