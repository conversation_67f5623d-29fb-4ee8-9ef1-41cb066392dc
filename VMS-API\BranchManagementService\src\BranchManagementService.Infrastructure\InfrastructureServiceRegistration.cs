using BranchManagementService.Domain.Interfaces;
using BranchManagementService.Infrastructure.Messaging.Consumers;
using BranchManagementService.Infrastructure.Messaging.Publishers;
using BranchManagementService.Infrastructure.Persistence;
using BranchManagementService.Infrastructure.Persistence.Repositories;
using MassTransit;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System;

namespace BranchManagementService.Infrastructure
{
    public static class InfrastructureServiceRegistration
    {
        public static IServiceCollection AddInfrastructureServices(this IServiceCollection services, IConfiguration configuration)
        {
            // Add DbContext
            services.AddDbContext<BranchDbContext>(options =>
                options.UseNpgsql(configuration.GetConnectionString("DefaultConnection")));

            // Add repositories
            services.AddScoped<IBranchRepository, BranchRepository>();

            // Add Unit of Work
            services.AddScoped<IUnitOfWork, UnitOfWork>();

            // Add event publisher
            services.AddScoped<IEventPublisher, EventPublisher>();

            // Add MassTransit with RabbitMQ
            services.AddMassTransitWithRabbitMq(configuration);

            return services;
        }

        private static IServiceCollection AddMassTransitWithRabbitMq(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddMassTransit(config =>
            {
                // Register consumers
                config.AddConsumer<SubscriptionCreatedConsumer>();
                config.AddConsumer<SubscriptionUpdatedConsumer>();

                config.UsingRabbitMq((ctx, cfg) =>
                {
                    cfg.Host(configuration["RabbitMQ:Host"], h =>
                    {
                        h.Username(configuration["RabbitMQ:Username"]);
                        h.Password(configuration["RabbitMQ:Password"]);
                    });

                    // Configure endpoints
                    cfg.ConfigureEndpoints(ctx);
                });
            });

            return services;
        }
    }
}
