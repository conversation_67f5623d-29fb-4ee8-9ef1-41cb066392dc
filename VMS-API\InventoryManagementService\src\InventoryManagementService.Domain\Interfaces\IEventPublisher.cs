namespace InventoryManagementService.Domain.Interfaces;

public interface IEventPublisher
{
    // Part Events
    Task PublishPartCreatedEvent(Guid partId, string sku, string name, string manufacturer);
    Task PublishPartUpdatedEvent(Guid partId, string sku, string name, string manufacturer);
    Task PublishPartActivatedEvent(Guid partId, string sku);
    Task PublishPartDeactivatedEvent(Guid partId, string sku);

    // Inventory Events
    Task PublishInventoryCreatedEvent(Guid inventoryItemId, Guid partId, Guid branchId, int initialStock);
    Task PublishStockUpdatedEvent(Guid inventoryItemId, Guid partId, Guid branchId, int previousStock, int newStock, string reason);
    Task PublishLowStockAlertEvent(Guid inventoryItemId, Guid partId, Guid branchId, int currentStock, int reorderLevel);

    // Transfer Events
    Task PublishTransferCreatedEvent(Guid transferId, string transferNumber, Guid sourceBranchId, Guid destinationBranchId);
    Task PublishTransferShippedEvent(Guid transferId, string transferNumber, DateTime shippedDate);
    Task PublishTransferCompletedEvent(Guid transferId, string transferNumber, DateTime completedDate);
    Task PublishTransferCancelledEvent(Guid transferId, string transferNumber, string reason);

    // Adjustment Events
    Task PublishAdjustmentCreatedEvent(Guid adjustmentId, string adjustmentNumber, Guid branchId);
    Task PublishAdjustmentApprovedEvent(Guid adjustmentId, string adjustmentNumber, DateTime approvedDate);
}
