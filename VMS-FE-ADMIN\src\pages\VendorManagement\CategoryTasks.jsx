import React, { useState } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { Container, Row, Col } from "reactstrap";
import { MainHeading } from "../../components/common";
import { useFilter } from "../../contexts/FilterContext";
import ServiceTaskCreateForm from "./components/ServiceTaskCreateForm";
import ServiceTaskEditForm from "./components/ServiceTaskEditForm";
import DeleteConfirmationModal from "./components/DeleteConfirmationModal";
import {
  FaChevronRight,
  FaEdit,
  FaSpinner,
  FaEye
} from "react-icons/fa";
import toast from "react-hot-toast";
import {
  useServiceTasksAccessible,
  useCategoriesAccessible,
  useDeleteServiceTask,
} from "@api/useTenantHooks";

const CategoryTasks = () => {
  const { branchId, categoryId } = useParams();
  const navigate = useNavigate();
  const { openSidebar, closeSidebar } = useFilter();

  // State for delete modal
  const [deleteModal, setDeleteModal] = useState({
    isOpen: false,
    serviceTask: null,
    isLoading: false,
  });

  // Fetch category details
  const {
    data: categoriesData,
    isLoading: categoriesLoading,
    error: categoriesError,
  } = useCategoriesAccessible(null, branchId, {
    enabled: !!branchId,
  });

  // Find the specific category
  const category = categoriesData?.find(cat => cat.id === categoryId);

  // Fetch service tasks for this category
  const {
    data: serviceTasksData,
    isLoading: serviceTasksLoading,
    error: serviceTasksError,
    refetch: refetchServiceTasks,
  } = useServiceTasksAccessible(
    null,
    branchId,
    categoryId,
    {
      enabled: !!branchId && !!categoryId,
    }
  );

  // Delete service task mutation
  const deleteServiceTaskMutation = useDeleteServiceTask({
    onSuccess: () => {
      toast.success("Service task deleted successfully!");
      setDeleteModal({ isOpen: false, serviceTask: null, isLoading: false });
      refetchServiceTasks();
    },
    onError: (error) => {
      const errorMessage = error.response?.data?.message || "Failed to delete service task";
      toast.error(errorMessage);
      setDeleteModal(prev => ({ ...prev, isLoading: false }));
    },
  });

  // Handle create service task
  const handleCreateServiceTask = () => {
    const createForm = (
      <ServiceTaskCreateForm
        categoryId={categoryId}
        onSubmit={(createdServiceTask) => {
          console.log("Service task created:", createdServiceTask);
          refetchServiceTasks();
          closeSidebar();
        }}
        onCancel={() => {
          closeSidebar();
        }}
      />
    );
    openSidebar(createForm, "Create New Service Task", "plus");
  };

  // Handle edit service task
  const handleEditServiceTask = (serviceTask) => {
    const editForm = (
      <ServiceTaskEditForm
        serviceTaskData={serviceTask}
        onSubmit={(updatedServiceTask) => {
          console.log("Service task updated:", updatedServiceTask);
          refetchServiceTasks();
          closeSidebar();
        }}
        onCancel={() => {
          closeSidebar();
        }}
      />
    );
    openSidebar(editForm, "Edit Service Task", "edit");
  };

  // Handle delete service task
  const handleDeleteServiceTask = (serviceTask) => {
    setDeleteModal({
      isOpen: true,
      serviceTask: serviceTask,
      isLoading: false
    });
  };

  // Handle delete confirmation
  const handleDeleteConfirm = async () => {
    setDeleteModal(prev => ({ ...prev, isLoading: true }));
    try {
      await deleteServiceTaskMutation.mutateAsync(deleteModal.serviceTask.id);
    } catch (error) {
      console.error("Error deleting service task:", error);
    }
  };

  // Handle delete cancel
  const handleDeleteCancel = () => {
    setDeleteModal({ isOpen: false, serviceTask: null, isLoading: false });
  };

  const handleBackClick = () => {
    navigate(-1);
  };

  return (
    <div className="page-with-header">
      <MainHeading
        title={`${category?.name || 'Category'} - Service Tasks`}
        subtitle="Manage service tasks for this category"
        showBackButton={true}
        onBackClick={handleBackClick}
        showFilter={false}
      />
      <div className="page-content-scrollable">
        <div className="vm-branch-details-container">
          
          {/* Category Info Card */}
          <div className="vm-branch-details-card">
            <div className="vm-branch-card-header">
              <div className="vm-branch-header-left">
                <h3>Category Information</h3>
              </div>
            </div>
            <div className="vm-branch-card-body">
              <div className="vm-branch-info-grid">
                <div className="vm-branch-field">
                  <label>Category Name</label>
                  <span>{category?.name || "Loading..."}</span>
                </div>
                <div className="vm-branch-field">
                  <label>Description</label>
                  <span>{category?.description || "No description"}</span>
                </div>
                <div className="vm-branch-field">
                  <label>Vehicle Type</label>
                  <span>{category?.vehicleType || "All Types"}</span>
                </div>
                <div className="vm-branch-field">
                  <label>Total Tasks</label>
                  <span>{serviceTasksData?.length || 0}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Service Tasks Table */}
          <div className="vm-branch-tabs-container">
            <div className="vm-branch-tab-content">
              <div className="vm-tab-panel">
                <div>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
                    <div>
                      <h3>Service Tasks</h3>
                      <p>Manage tasks for {category?.name || 'this category'}</p>
                    </div>
                    <button className="btn btn-primary" onClick={handleCreateServiceTask}>
                      Create Task
                    </button>
                  </div>

                  {serviceTasksLoading ? (
                    <div style={{ textAlign: 'center', padding: '2rem' }}>
                      <FaSpinner style={{ animation: 'spin 1s linear infinite' }} />
                      <p>Loading tasks...</p>
                    </div>
                  ) : serviceTasksError ? (
                    <div style={{ textAlign: 'center', padding: '2rem' }}>
                      <p>Error loading tasks: {serviceTasksError.message}</p>
                      <button onClick={() => refetchServiceTasks()} className="btn btn-secondary">
                        Retry
                      </button>
                    </div>
                  ) : !serviceTasksData || serviceTasksData.length === 0 ? (
                    <div style={{ textAlign: 'center', padding: '2rem' }}>
                      <h4>No Tasks Available</h4>
                      <p>No service tasks are currently available in this category.</p>
                      <button className="btn btn-primary" onClick={handleCreateServiceTask}>
                        Create First Task
                      </button>
                    </div>
                  ) : (
                    <div style={{ background: 'white', border: '1px solid #e5e7eb', borderRadius: '8px', overflow: 'hidden' }}>
                      <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                        <thead style={{ background: '#f8fafc' }}>
                          <tr>
                            <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #e5e7eb' }}>Task Name</th>
                            <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #e5e7eb' }}>Description</th>
                            <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #e5e7eb' }}>Price</th>
                            <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #e5e7eb' }}>Duration</th>
                            <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #e5e7eb' }}>Type</th>
                            <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #e5e7eb' }}>Status</th>
                            <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #e5e7eb' }}>Actions</th>
                          </tr>
                        </thead>
                        <tbody>
                          {serviceTasksData.map((task) => (
                            <tr key={task.id} style={{ borderBottom: '1px solid #f3f4f6' }}>
                              <td style={{ padding: '12px' }}>
                                <strong>{task.name}</strong>
                              </td>
                              <td style={{ padding: '12px' }}>
                                {task.description || "No description"}
                              </td>
                              <td style={{ padding: '12px' }}>
                                ₹{task.price || "0"}
                              </td>
                              <td style={{ padding: '12px' }}>
                                {task.estimatedDuration || "N/A"}
                              </td>
                              <td style={{ padding: '12px' }}>
                                {task.serviceType || "General"}
                              </td>
                              <td style={{ padding: '12px' }}>
                                <span style={{
                                  padding: '4px 8px',
                                  borderRadius: '12px',
                                  fontSize: '12px',
                                  fontWeight: '600',
                                  background: task.isActive ? '#dcfce7' : '#fef2f2',
                                  color: task.isActive ? '#166534' : '#dc2626'
                                }}>
                                  {task.isActive ? 'Active' : 'Inactive'}
                                </span>
                              </td>
                              <td style={{ padding: '12px' }}>
                                <div style={{ display: 'flex', gap: '8px' }}>
                                  <button
                                    style={{ padding: '4px 8px', border: '1px solid #d1d5db', borderRadius: '4px', background: 'white' }}
                                    title="Edit"
                                    onClick={() => handleEditServiceTask(task)}
                                  >
                                    <FaEdit />
                                  </button>
                                  <button
                                    style={{ padding: '4px 8px', border: '1px solid #d1d5db', borderRadius: '4px', background: 'white' }}
                                    title="Delete"
                                    onClick={() => handleDeleteServiceTask(task)}
                                  >
                                    <FaChevronRight />
                                  </button>
                                </div>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        isOpen={deleteModal.isOpen}
        onClose={handleDeleteCancel}
        onConfirm={handleDeleteConfirm}
        title="Delete Service Task"
        message="Are you sure you want to delete this service task?"
        itemName={deleteModal.serviceTask?.name}
        isLoading={deleteModal.isLoading}
      />
    </div>
  );
};

export default CategoryTasks;
