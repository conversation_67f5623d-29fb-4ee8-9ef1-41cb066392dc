{"AllowedOrigins": ["http://localhost:3000", "http://localhost:3001", "http://localhost:3002"], "ConnectionStrings": {"DefaultConnection": "Host=**************;Port=5432;Database=vms_branchmanagementservice;Username=postgres;Password=************"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}}, "Jwt": {"Key": "12345678901234567890123456789012", "Issuer": "identity-service", "Audience": "vms-api", "ExpiryMinutes": 60}, "RabbitMQ": {"Host": "localhost", "Username": "guest", "Password": "guest"}, "AllowedHosts": "*"}