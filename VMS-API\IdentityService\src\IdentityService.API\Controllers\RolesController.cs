using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using IdentityService.Application.DTOs.Roles;
using IdentityService.Application.Features.Roles.Commands;
using IdentityService.Application.Features.Roles.Queries;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace IdentityService.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class RolesController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<RolesController> _logger;

    public RolesController(IMediator mediator, ILogger<RolesController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    [HttpGet]
    [Authorize(Policy = "Roles.View")]
    public async Task<ActionResult<List<RoleResponse>>> GetRoles()
    {
        try
        {
            var query = new GetRolesQuery();
            var roles = await _mediator.Send(query);
            return Ok(roles);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving roles");
            return StatusCode(500, new { message = "An error occurred while retrieving roles" });
        }
    }

    [HttpGet("{id}")]
    [Authorize(Policy = "Roles.View")]
    public async Task<ActionResult<RoleResponse>> GetRoleById(Guid id)
    {
        try
        {
            var query = new GetRoleByIdQuery { RoleId = id };
            var role = await _mediator.Send(query);
            return Ok(role);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Role not found");
            return NotFound(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving role");
            return StatusCode(500, new { message = "An error occurred while retrieving the role" });
        }
    }

    [HttpPost]
    [Authorize(Roles = "SuperAdmin")]
    public async Task<ActionResult<RoleResponse>> CreateRole([FromBody] CreateRoleRequest request)
    {
        try
        {
            var command = new CreateRoleCommand { Request = request };
            var role = await _mediator.Send(command);
            return CreatedAtAction(nameof(GetRoleById), new { id = role.Id }, role);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation during role creation");
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating role");
            return StatusCode(500, new { message = "An error occurred while creating the role" });
        }
    }

    [HttpPut("{id}")]
    [Authorize(Roles = "SuperAdmin")]
    public async Task<ActionResult<RoleResponse>> UpdateRole(Guid id, [FromBody] UpdateRoleRequest request)
    {
        try
        {
            var command = new UpdateRoleCommand { RoleId = id, Request = request };
            var role = await _mediator.Send(command);
            return Ok(role);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation during role update");
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating role");
            return StatusCode(500, new { message = "An error occurred while updating the role" });
        }
    }

    [HttpDelete("{id}")]
    [Authorize(Roles = "SuperAdmin")]
    public async Task<ActionResult<bool>> DeleteRole(Guid id)
    {
        try
        {
            var command = new DeleteRoleCommand { RoleId = id };
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation during role deletion");
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting role");
            return StatusCode(500, new { message = "An error occurred while deleting the role" });
        }
    }

    [HttpPost("{id}/permissions")]
    [Authorize(Roles = "SuperAdmin")]
    public async Task<ActionResult<RoleResponse>> AssignPermissions(Guid id, [FromBody] AssignPermissionRequest request)
    {
        try
        {
            var command = new AssignPermissionCommand { RoleId = id, Request = request };
            var role = await _mediator.Send(command);
            return Ok(role);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation during permission assignment");
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assigning permissions");
            return StatusCode(500, new { message = "An error occurred while assigning permissions" });
        }
    }
}
