using InventoryManagementService.Domain.Entities;
using InventoryManagementService.Domain.Interfaces;
using InventoryManagementService.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;

namespace InventoryManagementService.Infrastructure.Repositories;

public class InventoryRepository : IInventoryRepository
{
    private readonly InventoryDbContext _context;

    public InventoryRepository(InventoryDbContext context)
    {
        _context = context;
    }

    public async Task<InventoryItem?> GetByIdAsync(Guid id)
    {
        return await _context.InventoryItems
            .Include(i => i.Part)
            .Include(i => i.Transactions)
            .FirstOrDefaultAsync(i => i.Id == id);
    }

    public async Task<InventoryItem?> GetByPartAndBranchAsync(Guid partId, Guid branchId)
    {
        return await _context.InventoryItems
            .Include(i => i.Part)
            .Include(i => i.Transactions)
            .FirstOrDefaultAsync(i => i.PartId == partId && i.BranchId == branchId);
    }

    public async Task<List<InventoryItem>> GetAllAsync()
    {
        return await _context.InventoryItems
            .Include(i => i.Part)
            .Where(i => i.IsActive)
            .OrderBy(i => i.Part.Name)
            .ToListAsync();
    }

    public async Task<List<InventoryItem>> GetByBranchAsync(Guid branchId)
    {
        return await _context.InventoryItems
            .Include(i => i.Part)
            .Where(i => i.BranchId == branchId && i.IsActive)
            .OrderBy(i => i.Part.Name)
            .ToListAsync();
    }

    public async Task<List<InventoryItem>> GetByPartAsync(Guid partId)
    {
        return await _context.InventoryItems
            .Include(i => i.Part)
            .Where(i => i.PartId == partId && i.IsActive)
            .OrderBy(i => i.StorageLocation)
            .ToListAsync();
    }

    public async Task<List<InventoryItem>> GetByVendorAsync(Guid vendorId)
    {
        return await _context.InventoryItems
            .Include(i => i.Part)
            .Where(i => i.VendorId == vendorId && i.IsActive)
            .OrderBy(i => i.Part.Name)
            .ToListAsync();
    }

    public async Task<List<InventoryItem>> GetLowStockItemsAsync(Guid branchId)
    {
        return await _context.InventoryItems
            .Include(i => i.Part)
            .Where(i => i.BranchId == branchId && 
                       i.IsActive && 
                       i.CurrentStock <= i.ReorderLevel)
            .OrderBy(i => i.CurrentStock)
            .ToListAsync();
    }

    public async Task<List<InventoryItem>> GetOutOfStockItemsAsync(Guid branchId)
    {
        return await _context.InventoryItems
            .Include(i => i.Part)
            .Where(i => i.BranchId == branchId && 
                       i.IsActive && 
                       i.CurrentStock <= 0)
            .OrderBy(i => i.Part.Name)
            .ToListAsync();
    }

    public async Task<List<InventoryItem>> GetAsync(Expression<Func<InventoryItem, bool>> predicate)
    {
        return await _context.InventoryItems
            .Include(i => i.Part)
            .Where(predicate)
            .OrderBy(i => i.Part.Name)
            .ToListAsync();
    }

    public async Task AddAsync(InventoryItem inventoryItem)
    {
        await _context.InventoryItems.AddAsync(inventoryItem);
    }

    public async Task UpdateAsync(InventoryItem inventoryItem)
    {
        _context.InventoryItems.Update(inventoryItem);
        await Task.CompletedTask;
    }

    public async Task DeleteAsync(Guid id)
    {
        var inventoryItem = await _context.InventoryItems.FindAsync(id);
        if (inventoryItem != null)
        {
            _context.InventoryItems.Remove(inventoryItem);
        }
    }

    public async Task<bool> ExistsAsync(Guid id)
    {
        return await _context.InventoryItems.AnyAsync(i => i.Id == id);
    }

    public async Task<bool> ExistsForPartAndBranchAsync(Guid partId, Guid branchId)
    {
        return await _context.InventoryItems
            .AnyAsync(i => i.PartId == partId && i.BranchId == branchId);
    }
}
