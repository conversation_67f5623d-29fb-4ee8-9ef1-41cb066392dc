using System;
using System.Threading;
using System.Threading.Tasks;
using IdentityService.Application.Common;
using IdentityService.Application.DTOs.Permissions;
using IdentityService.Domain.Interfaces;
using MediatR;

namespace IdentityService.Application.Features.Permissions.Queries;

public class GetPermissionByIdQuery : BaseRequest<PermissionResponse>
{
    public Guid PermissionId { get; set; }
}

public class GetPermissionByIdQueryHandler : IRequestHandler<GetPermissionByIdQuery, PermissionResponse>
{
    private readonly IUnitOfWork _unitOfWork;

    public GetPermissionByIdQueryHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
    }

    public async Task<PermissionResponse> Handle(GetPermissionByIdQuery query, CancellationToken cancellationToken)
    {
        var permission = await _unitOfWork.PermissionRepository.GetByIdAsync(query.PermissionId);
        if (permission == null)
            throw new InvalidOperationException($"Permission with ID {query.PermissionId} not found");

        return new PermissionResponse
        {
            Id = permission.Id,
            Name = permission.Name,
            Description = permission.Description,
            Resource = permission.Resource,
            Action = permission.Action,
            CreatedAt = permission.CreatedAt,
            CreatedBy = permission.CreatedBy,
            UpdatedAt = permission.UpdatedAt,
            UpdatedBy = permission.UpdatedBy
        };
    }
}
