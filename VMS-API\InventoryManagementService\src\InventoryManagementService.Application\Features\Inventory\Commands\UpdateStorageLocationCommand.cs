using FluentValidation;
using InventoryManagementService.Domain.Interfaces;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using VMS.Contracts.Common.Authorization;

namespace InventoryManagementService.Application.Features.Inventory.Commands;

public class UpdateStorageLocationCommand : IRequest
{
    public Guid InventoryItemId { get; set; }
    public string StorageLocation { get; set; } = string.Empty;
    public string? Bin { get; set; }
    public string? Shelf { get; set; }
    public string UpdatedBy { get; set; } = string.Empty;
}

public class UpdateStorageLocationCommandValidator : AbstractValidator<UpdateStorageLocationCommand>
{
    public UpdateStorageLocationCommandValidator()
    {
        RuleFor(x => x.InventoryItemId)
            .NotEmpty().WithMessage("Inventory item ID is required");

        RuleFor(x => x.StorageLocation)
            .NotEmpty().WithMessage("Storage location is required")
            .MaximumLength(100).WithMessage("Storage location cannot exceed 100 characters");

        RuleFor(x => x.Bin)
            .MaximumLength(50).WithMessage("Bin cannot exceed 50 characters");

        RuleFor(x => x.Shelf)
            .MaximumLength(50).WithMessage("Shelf cannot exceed 50 characters");

        RuleFor(x => x.UpdatedBy)
            .NotEmpty().WithMessage("UpdatedBy is required");
    }
}

public class UpdateStorageLocationCommandHandler : IRequestHandler<UpdateStorageLocationCommand>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<UpdateStorageLocationCommandHandler> _logger;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public UpdateStorageLocationCommandHandler(
        IUnitOfWork unitOfWork,
        ILogger<UpdateStorageLocationCommandHandler> logger,
        IHttpContextAccessor httpContextAccessor)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task Handle(UpdateStorageLocationCommand request, CancellationToken cancellationToken)
    {
        // Check permissions - Admin roles bypass, others need explicit permission
        var user = _httpContextAccessor.HttpContext?.User;
        if (!PermissionHelper.IsAdmin(user))
        {
            PermissionHelper.ValidateAccess(user, "Inventory.Update");
        }

        _logger.LogInformation("Updating storage location for inventory item: {InventoryItemId}", request.InventoryItemId);

        var inventoryItem = await _unitOfWork.Inventory.GetByIdAsync(request.InventoryItemId);

        if (inventoryItem == null)
        {
            throw new KeyNotFoundException($"Inventory item with ID {request.InventoryItemId} not found");
        }

        inventoryItem.UpdateStorageLocation(request.StorageLocation, request.UpdatedBy, request.Bin, request.Shelf);

        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("Storage location updated successfully for inventory item {InventoryItemId}", inventoryItem.Id);
    }
}
