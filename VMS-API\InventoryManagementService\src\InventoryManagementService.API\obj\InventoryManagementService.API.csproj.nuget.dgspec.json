{"format": 1, "restore": {"E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\InventoryManagementService\\src\\InventoryManagementService.API\\InventoryManagementService.API.csproj": {}}, "projects": {"E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\InventoryManagementService\\src\\InventoryManagementService.API\\InventoryManagementService.API.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\InventoryManagementService\\src\\InventoryManagementService.API\\InventoryManagementService.API.csproj", "projectName": "InventoryManagementService.API", "projectPath": "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\InventoryManagementService\\src\\InventoryManagementService.API\\InventoryManagementService.API.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\InventoryManagementService\\src\\InventoryManagementService.API\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\InventoryManagementService\\src\\InventoryManagementService.Application\\InventoryManagementService.Application.csproj": {"projectPath": "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\InventoryManagementService\\src\\InventoryManagementService.Application\\InventoryManagementService.Application.csproj"}, "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\InventoryManagementService\\src\\InventoryManagementService.Infrastructure\\InventoryManagementService.Infrastructure.csproj": {"projectPath": "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\InventoryManagementService\\src\\InventoryManagementService.Infrastructure\\InventoryManagementService.Infrastructure.csproj"}, "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\VMSContracts\\VMSContracts.csproj": {"projectPath": "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\VMSContracts\\VMSContracts.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.AspNetCore.Authorization": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.0, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[8.0.0, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[5.0.1, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[5.0.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.5.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101/PortableRuntimeIdentifierGraph.json"}}}, "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\InventoryManagementService\\src\\InventoryManagementService.Application\\InventoryManagementService.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\InventoryManagementService\\src\\InventoryManagementService.Application\\InventoryManagementService.Application.csproj", "projectName": "InventoryManagementService.Application", "projectPath": "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\InventoryManagementService\\src\\InventoryManagementService.Application\\InventoryManagementService.Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\InventoryManagementService\\src\\InventoryManagementService.Application\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\InventoryManagementService\\src\\InventoryManagementService.Domain\\InventoryManagementService.Domain.csproj": {"projectPath": "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\InventoryManagementService\\src\\InventoryManagementService.Domain\\InventoryManagementService.Domain.csproj"}, "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\VMSContracts\\VMSContracts.csproj": {"projectPath": "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\VMSContracts\\VMSContracts.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[12.0.1, )"}, "AutoMapper.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[12.0.1, )"}, "FluentValidation": {"target": "Package", "version": "[11.8.0, )"}, "FluentValidation.DependencyInjectionExtensions": {"target": "Package", "version": "[11.8.0, )"}, "MediatR": {"target": "Package", "version": "[12.2.0, )"}, "Microsoft.AspNetCore.Http.Abstractions": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101/PortableRuntimeIdentifierGraph.json"}}}, "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\InventoryManagementService\\src\\InventoryManagementService.Domain\\InventoryManagementService.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\InventoryManagementService\\src\\InventoryManagementService.Domain\\InventoryManagementService.Domain.csproj", "projectName": "InventoryManagementService.Domain", "projectPath": "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\InventoryManagementService\\src\\InventoryManagementService.Domain\\InventoryManagementService.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\InventoryManagementService\\src\\InventoryManagementService.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\VMSContracts\\VMSContracts.csproj": {"projectPath": "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\VMSContracts\\VMSContracts.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101/PortableRuntimeIdentifierGraph.json"}}}, "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\InventoryManagementService\\src\\InventoryManagementService.Infrastructure\\InventoryManagementService.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\InventoryManagementService\\src\\InventoryManagementService.Infrastructure\\InventoryManagementService.Infrastructure.csproj", "projectName": "InventoryManagementService.Infrastructure", "projectPath": "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\InventoryManagementService\\src\\InventoryManagementService.Infrastructure\\InventoryManagementService.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\InventoryManagementService\\src\\InventoryManagementService.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\InventoryManagementService\\src\\InventoryManagementService.Application\\InventoryManagementService.Application.csproj": {"projectPath": "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\InventoryManagementService\\src\\InventoryManagementService.Application\\InventoryManagementService.Application.csproj"}, "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\InventoryManagementService\\src\\InventoryManagementService.Domain\\InventoryManagementService.Domain.csproj": {"projectPath": "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\InventoryManagementService\\src\\InventoryManagementService.Domain\\InventoryManagementService.Domain.csproj"}, "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\VMSContracts\\VMSContracts.csproj": {"projectPath": "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\VMSContracts\\VMSContracts.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"MassTransit": {"target": "Package", "version": "[8.1.3, )"}, "MassTransit.RabbitMQ": {"target": "Package", "version": "[8.1.3, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Design": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Tools": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101/PortableRuntimeIdentifierGraph.json"}}}, "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\VMSContracts\\VMSContracts.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\VMSContracts\\VMSContracts.csproj", "projectName": "VMSContracts", "projectPath": "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\VMSContracts\\VMSContracts.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\VMSContracts\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"MassTransit": {"target": "Package", "version": "[8.1.3, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Http": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Http.Polly": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101/PortableRuntimeIdentifierGraph.json"}}}}}