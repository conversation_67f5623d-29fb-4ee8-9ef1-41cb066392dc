import React, { useState } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate, useLocation } from "react-router-dom";
import { Container, Row, Col, Button } from "reactstrap";
import { MainHeading } from "../../components/common";
import EditVendorForm from "./components/EditVendorModal";
import VendorSidebar from "./components/VendorSidebar";
import {
  useVendorDetails,
  useSubscriptionHistory,
  useViewDocument,
  useDownloadDocument,
} from "../../api/useTenantHooks";
import {
  FaBuilding,
  FaUser,
  FaMapMarkerAlt,
  FaFileAlt,
  FaCheckCircle,
  FaEye,
  FaDownload,
  FaCrown,
  FaStore,
  FaUsers,
  FaSpinner,
  FaChevronDown,
  FaChevronUp,
} from "react-icons/fa";
import { motion } from "framer-motion";
// CSS moved to styles/custom.css - use vm-vendor-details-* classes
import ROUTES from "@constants/routes";
import { useFilter } from "../../contexts/FilterContext";

// Component for handling multiple values with show more functionality
const MultiValueDisplay = ({ items, label, maxVisible = 3 }) => {
  const [showDropdown, setShowDropdown] = useState(false);

  if (!items || items.length === 0) {
    return <span className="value">No {label.toLowerCase()} specified</span>;
  }

  const visibleItems = items.slice(0, maxVisible);
  const hiddenItems = items.slice(maxVisible);
  const hasMore = hiddenItems.length > 0;

  return (
    <div className="multi-value-display">
      <div className="visible-tags">
        {visibleItems.map((item, index) => (
          <span key={index} className="service-tag">
            {item}
          </span>
        ))}
        {hasMore && (
          <div className="more-dropdown-container">
            <button
              className="more-btn"
              onClick={() => setShowDropdown(!showDropdown)}
              type="button"
            >
              +{hiddenItems.length} more
              {showDropdown ? <FaChevronUp /> : <FaChevronDown />}
            </button>
            {showDropdown && (
              <div className="dropdown-list">
                {hiddenItems.map((item, index) => (
                  <div key={index} className="dropdown-item">
                    {item}
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

const VendorDetails = () => {
  const { vendorId } = useParams();
  const navigate = useNavigate();
  const { openSidebar } = useFilter();
  const location = useLocation();
  const vendorDetails = location.state?.vendor;
  const [showEditModal, setShowEditModal] = useState(false);
  const [activeSection, setActiveSection] = useState("profile");

  // Fixed: Removed useUpdateVendorProfile reference

  const {
    data: vendorData,
    isLoading: vendorLoading,
    refetch: refetchVendorData,
  } = useVendorDetails({
    id: vendorDetails?.vendorId,
  });

  const { data: subscriptionHistory, isLoading: historyLoading } =
    useSubscriptionHistory(vendorDetails?.vendorId);

  // Document hooks
  const viewDocumentMutation = useViewDocument();
  const downloadDocumentMutation = useDownloadDocument();

  // Track loading state for individual documents
  const [loadingStates, setLoadingStates] = useState({
    viewing: null, // stores document id that is being viewed
    downloading: null // stores document id that is being downloaded
  });

  // Menu items matching VMS_FE Profile design
  const menuItems = [
    {
      id: "profile",
      label: "Profile Information",
      icon: FaUser,
      description: "View and manage vendor details",
    },
    {
      id: "documents",
      label: "Documents",
      icon: FaFileAlt,
      description: "View uploaded documents",
      count: vendorData?.documents?.length || 0,
    },
    {
      id: "branches",
      label: "Branches",
      icon: FaStore,
      description: "Manage service branches",
      count: vendorData?.branches?.length || 0,
    },
    {
      id: "subscriptions",
      label: "Subscriptions",
      icon: FaCrown,
      description: "View subscription plans",
      count: vendorData?.subscriptions?.length || 0,
    },
  ];

  if (vendorLoading) {
    return (
      <div className="vm-loading-container">
        <div className="vm-loading-content">
          <div className="vm-loading-spinner"></div>
          <p className="vm-loading-text">Loading data...</p>
        </div>
      </div>
    );
  }

  const handleBackClick = () => {
    navigate(-1);
  };

  const handleEditVendor = () => {
    const editForm = (
      <EditVendorForm
        vendorData={vendorData}
        onCancel={() => {}}
        onSuccess={(response) => {
          refetchVendorData();
        }}
      />
    );
    openSidebar(editForm, "Edit Vendor Profile", "edit");
  };
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const formatFileSize = (bytes) => {
    if (!bytes) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  // Handle document view
  const handleViewDocument = (document) => {
    setLoadingStates(prev => ({ ...prev, viewing: document.id }));
    viewDocumentMutation.mutate({
      vendorId: vendorDetails?.vendorId,
      category: document.documentType,
      filename: document.fileName,
    }, {
      onSettled: () => {
        setLoadingStates(prev => ({ ...prev, viewing: null }));
      }
    });
  };

  // Handle document download
  const handleDownloadDocument = (document) => {
    setLoadingStates(prev => ({ ...prev, downloading: document.id }));
    downloadDocumentMutation.mutate({
      vendorId: vendorDetails?.vendorId,
      category: document.documentType,
      filename: document.fileName,
    }, {
      onSettled: () => {
        setLoadingStates(prev => ({ ...prev, downloading: null }));
      }
    });
  };

  const renderProfileSection = () => (
    <div className="vm-vendor-profile-content">
      {/* Profile Header */}
      <div className="vm-profile-header">
        <div className="vm-profile-title">
          <h2>Profile Information</h2>
          <p>View and manage vendor details</p>
        </div>
        <button className="vm-edit-profile-btn" onClick={() => handleEditVendor()}>
          Edit Profile
        </button>
      </div>

      {/* Profile Cards Grid */}
      <div className="vm-profile-cards-grid">
        {/* Business Information Card */}
        <div className="vm-profile-card">
          <div className="vm-profile-card-header">
            <div className="vm-card-icon business" style={{ background: '#6366f1' }}>
              <FaBuilding />
            </div>
            <div className="vm-card-title">
              <h3>Business Information</h3>
              <p>Company details and establishment info</p>
            </div>
          </div>
          <div className="vm-profile-card-body">
            <div className="vm-info-row">
              <span className="vm-info-label">Business Name:</span>
              <span className="vm-info-value">{vendorData?.businessName || "F2 Cars"}</span>
            </div>
            <div className="vm-info-row">
              <span className="vm-info-label">Type:</span>
              <span className="vm-info-value">{vendorData?.businessType || "MultibranchWorkshop"}</span>
            </div>
            <div className="vm-info-row">
              <span className="vm-info-label">Established:</span>
              <span className="vm-info-value">{vendorData?.yearEstablished || "2011"}</span>
            </div>
            <div className="vm-info-row">
              <span className="vm-info-label">Status:</span>
              <span className="vm-info-value vm-status-incomplete">
                {vendorData?.status || "INCOMPLETE"}
              </span>
            </div>
            <div className="vm-info-row">
              <span className="vm-info-label">Description:</span>
              <span className="vm-info-value">{vendorData?.businessDescription || "Test aaaaaaaaaaaaa"}</span>
            </div>
          </div>
        </div>

        {/* Services & Capabilities Card */}
        <div className="vm-profile-card">
          <div className="vm-profile-card-header">
            <div className="vm-card-icon services" style={{ background: '#10b981' }}>
              <FaCheckCircle />
            </div>
            <div className="vm-card-title">
              <h3>Services & Capabilities</h3>
              <p>Service offerings and expertise</p>
            </div>
          </div>
          <div className="vm-profile-card-body">
            <div className="vm-info-row">
              <span className="vm-info-label">Brands Serviced:</span>
              <span className="vm-info-value vm-not-specified">
                {vendorData?.profile?.brandsServiced?.length > 0
                  ? vendorData.profile.brandsServiced.join(", ")
                  : "No brands specified"
                }
              </span>
            </div>
            <div className="vm-info-row">
              <span className="vm-info-label">Vehicle Types:</span>
              <span className="vm-info-value vm-not-specified">
                {vendorData?.profile?.vehicleTypesSupported?.length > 0
                  ? vendorData.profile.vehicleTypesSupported.join(", ")
                  : "No vehicle types specified"
                }
              </span>
            </div>
          </div>
        </div>

        {/* Contact Information Card */}
        <div className="vm-profile-card">
          <div className="vm-profile-card-header">
            <div className="vm-card-icon contact" style={{ background: '#ec4899' }}>
              <FaUser />
            </div>
            <div className="vm-card-title">
              <h3>Contact Information</h3>
              <p>Primary contact details</p>
            </div>
          </div>
          <div className="vm-profile-card-body">
            <div className="vm-info-row">
              <span className="vm-info-label">Contact Person:</span>
              <span className="vm-info-value">{vendorData?.primaryContactName || "Prakash"}</span>
            </div>
            <div className="vm-info-row">
              <span className="vm-info-label">Designation:</span>
              <span className="vm-info-value">{vendorData?.designation || "Manager"}</span>
            </div>
          </div>
        </div>

        {/* Address Information Card */}
        <div className="vm-profile-card">
          <div className="vm-profile-card-header">
            <div className="vm-card-icon address" style={{ background: '#f59e0b' }}>
              <FaMapMarkerAlt />
            </div>
            <div className="vm-card-title">
              <h3>Address Information</h3>
              <p>Business location and contact details</p>
            </div>
          </div>
          <div className="vm-profile-card-body">
            <div className="vm-info-row">
              <span className="vm-info-label">Address:</span>
              <span className="vm-info-value">
                {vendorData?.address || "Not provided"}
              </span>
            </div>
            <div className="vm-info-row">
              <span className="vm-info-label">City:</span>
              <span className="vm-info-value">{vendorData?.city || "Not provided"}</span>
            </div>
            <div className="vm-info-row">
              <span className="vm-info-label">State:</span>
              <span className="vm-info-value">{vendorData?.state || "Not provided"}</span>
            </div>
            <div className="vm-info-row">
              <span className="vm-info-label">Pin Code:</span>
              <span className="vm-info-value">{vendorData?.pinCode || "Not provided"}</span>
            </div>
            <div className="vm-info-row">
              <span className="vm-info-label">Country:</span>
              <span className="vm-info-value">{vendorData?.country || "Not provided"}</span>
            </div>
          </div>
        </div>

        {/* Compliance & Registration Card */}
        <div className="vm-profile-card">
          <div className="vm-profile-card-header">
            <div className="vm-card-icon compliance" style={{ background: '#10b981' }}>
              <FaCheckCircle />
            </div>
            <div className="vm-card-title">
              <h3>Compliance & Registration</h3>
              <p>Legal and tax information</p>
            </div>
          </div>
          <div className="vm-profile-card-body">
            <div className="vm-info-row">
              <span className="vm-info-label">Business PAN:</span>
              <span className="vm-info-value vm-not-provided">
                {vendorData?.profile?.panNumber || "Not provided"}
              </span>
            </div>
            <div className="vm-info-row">
              <span className="vm-info-label">GST Number:</span>
              <span className="vm-info-value vm-not-provided">
                {vendorData?.profile?.gstNumber || "Not provided"}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderDocumentsSection = () => (
    <div className="vm-vendor-profile-content">
      <div className="vm-profile-header">
        <div className="vm-profile-title">
          <h2>Documents</h2>
          <p>View uploaded documents</p>
        </div>
      </div>

      <div className="vm-section-content">
        {vendorData?.documents?.length > 0 ? (
          <div className="vm-documents-list">
            {vendorData.documents.map((document) => (
              <div key={document.id} className="vm-document-item">
                <div className="vm-document-icon">
                  <FaFileAlt />
                </div>
                <div className="vm-document-info">
                  <h4>{document.fileName}</h4>
                  <span className="vm-document-meta">
                    {document.documentType} • {formatFileSize(document.fileSize)} • {formatDate(document.createdAt)}
                  </span>
                </div>
                <span className={`vm-document-status ${document.isVerified ? "verified" : "pending"}`}>
                  {document.isVerified ? "Verified" : "Pending"}
                </span>
                <div className="vm-document-actions">
                  <button
                    className="vm-action-btn"
                    title="View"
                    onClick={() => handleViewDocument(document)}
                    disabled={loadingStates.viewing === document.id}
                  >
                    {loadingStates.viewing === document.id ? <FaSpinner className="fa-spin" /> : <FaEye />}
                  </button>
                  <button
                    className="vm-action-btn"
                    title="Download"
                    onClick={() => handleDownloadDocument(document)}
                    disabled={loadingStates.downloading === document.id}
                  >
                    {loadingStates.downloading === document.id ? <FaSpinner className="fa-spin" /> : <FaDownload />}
                  </button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="vm-empty-state">
            <FaFileAlt className="vm-empty-icon" />
            <p>No documents uploaded yet</p>
          </div>
        )}
      </div>
    </div>
  );

  const renderBranchesSection = () => (
    <div className="vm-vendor-profile-content">
      <div className="vm-profile-header">
        <div className="vm-profile-title">
          <h2>Branches</h2>
          <p>Manage service branches</p>
        </div>
      </div>

      <div className="vm-section-content">
        {vendorData?.branches?.length > 0 ? (
          <div className="vm-branches-list">
            {vendorData.branches.map((branch) => (
              <div
                key={branch.id}
                className="vm-branch-item"
                onClick={() =>
                  navigate(ROUTES?.BRANCH_DETAILS, {
                    state: { branch },
                  })
                }
              >
                <div className="vm-branch-icon">
                  <FaStore />
                </div>
                <div className="vm-branch-info">
                  <h4>{branch.name}</h4>
                  <span className="vm-branch-meta">
                    {branch.code} • {branch.address}, {branch.city}
                  </span>
                </div>
                <span className={`vm-branch-status ${branch.isActive ? "active" : "inactive"}`}>
                  {branch.isActive ? "Active" : "Inactive"}
                </span>
                <div className="vm-branch-stats">
                  <span>
                    <FaUsers /> {branch.userCount} Users
                  </span>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="vm-empty-state">
            <FaStore className="vm-empty-icon" />
            <h4>No Branches Found</h4>
            <p>This vendor hasn't created any branches yet. Branches will appear here once they're added to the system.</p>
          </div>
        )}
      </div>
    </div>
  );

  const renderSubscriptionsSection = () => (
    <div className="vm-vendor-profile-content">
      <div className="vm-profile-header">
        <div className="vm-profile-title">
          <h2>Subscriptions</h2>
          <p>View subscription plans</p>
        </div>
      </div>

      <div className="vm-section-content">
        {vendorData?.subscriptions?.length > 0 ? (
          <div className="vm-subscriptions-redesigned">
            {vendorData.subscriptions.map((subscription, index) => (
              <div key={subscription.id || index} className="vm-subscription-card-redesigned">
                {/* Card Header */}
                <div className="vm-subscription-card-header">
                  <div className="vm-subscription-plan-info">
                    <div className="vm-plan-icon-wrapper">
                      <FaCrown className="vm-plan-icon" />
                    </div>
                    <div className="vm-plan-details">
                      <h3 className="vm-plan-name">{subscription.subscriptionPlan?.name || "Subscription Plan"}</h3>
                      <p className="vm-plan-description">{subscription.subscriptionPlan?.description || "No description available"}</p>
                    </div>
                  </div>
                  <div className="vm-subscription-badges">
                    <span className={`vm-status-badge-redesigned ${subscription.status?.toLowerCase()}`}>
                      {subscription.status}
                    </span>
                    <span className="vm-tier-badge">
                      Tier {subscription.subscriptionPlan?.tier || "1"}
                    </span>
                  </div>
                </div>

                {/* Card Body */}
                <div className="vm-subscription-card-body">
                  <div className="vm-pricing-section">
                    <div className="vm-price-item">
                      <span className="vm-price-label">Current Price</span>
                      <span className="vm-price-value vm-current-price">
                        ${subscription.currentPrice?.toFixed(2) || "0.00"}
                      </span>
                    </div>
                    <div className="vm-price-item">
                      <span className="vm-price-label">Base Price</span>
                      <span className="vm-price-value">
                        ${subscription.subscriptionPlan?.price?.toFixed(2) || "0.00"}
                      </span>
                    </div>
                    <div className="vm-price-item">
                      <span className="vm-price-label">Billing Cycle</span>
                      <span className="vm-price-value vm-billing-cycle">
                        {subscription.billingCycle || "Monthly"}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Card Footer */}
                <div className="vm-subscription-card-footer">
                  <div className="vm-subscription-dates">
                    <span className="vm-date-info">
                      Started: {subscription.startDate ? new Date(subscription.startDate).toLocaleDateString() : "N/A"}
                    </span>
                    {subscription.endDate && (
                      <span className="vm-date-info">
                        Expires: {new Date(subscription.endDate).toLocaleDateString()}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="vm-empty-state-redesigned">
            <div className="vm-empty-icon-wrapper">
              <FaCrown className="vm-empty-icon" />
            </div>
            <h4 className="vm-empty-title">No Active Subscriptions</h4>
            <p className="vm-empty-description">This vendor doesn't have any active subscription plans.</p>
          </div>
        )}
      </div>
    </div>
  );

  const renderContent = () => {
    switch (activeSection) {
      case "profile":
        return renderProfileSection();
      case "documents":
        return renderDocumentsSection();
      case "branches":
        return renderBranchesSection();
      case "subscriptions":
        return renderSubscriptionsSection();
      default:
        return renderProfileSection();
    }
  };

  return (
    <div className="page-with-header">
      {vendorLoading && <FaSpinner /> }
      <MainHeading
        title={"Vendor Details"}
        subtitle="View and manage vendor information"
        showBackButton={true}
        onBackClick={handleBackClick}
        showFilter={false}
      />
      <div className="page-content-scrollable">
        <div className="vm-vendor-details-page">
          <Container fluid className="vm-vendor-details-page-container">
            <Row className="h-100">
              {/* Left Sidebar */}
              <Col lg={3} className="vm-vendor-details-page-sidebar-col">
                <VendorSidebar
                  vendor={vendorData}
                  menuItems={menuItems}
                  activeSection={activeSection}
                  onSectionChange={setActiveSection}
                />
              </Col>

              {/* Right Content Area */}
              <Col lg={9} className="vm-vendor-details-page-content-col">
                <div className="vm-vendor-details-page-content">
                  {renderContent()}
                </div>
              </Col>
            </Row>
          </Container>
        </div>
      </div>
    </div>
  );
};

export default VendorDetails;
