# API Gateway

This is the API Gateway service for the Vehicle Management System. It uses Ocelot to route requests to the appropriate microservices and provides a unified Swagger UI for all services.

## Services

The API Gateway routes requests to the following services:

1. Branch Management Service (Port: 5001)
2. Identity Service (Port: 5002)
3. Subscription Service (Port: 5003)
4. Tenant Management Service (Port: 5004)
5. Vehicle Management Service (Port: 5005)

## Setup

1. Make sure all microservices are running on their respective ports
2. Run the API Gateway:
   ```bash
   dotnet run --project ApiGateway.API
   ```

## Accessing Swagger UI

Once the API Gateway is running, you can access the Swagger UI at:
```
http://localhost:5000/swagger
```

This will show all available APIs from the different microservices in a single interface.

## API Endpoints

The API Gateway provides the following base URLs for each service:

- Branch Management: `http://localhost:5000/branch`
- Identity: `http://localhost:5000/identity`
- Subscription: `http://localhost:5000/subscription`
- Tenant Management: `http://localhost:5000/tenant`
- Vehicle Management: `http://localhost:5000/vehicle`

## Configuration

The routing configuration is defined in `ocelot.json`. You can modify this file to:
- Add new routes
- Change service endpoints
- Configure authentication
- Add rate limiting
- Configure load balancing

## Development

To modify the API Gateway:

1. Update `ocelot.json` for routing changes
2. Modify `Program.cs` for middleware and service configuration
3. Update `appsettings.json` for application settings

## Security

The API Gateway includes:
- CORS configuration
- JWT Bearer token authentication
- HTTPS redirection 