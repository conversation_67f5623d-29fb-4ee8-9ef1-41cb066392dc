{"AllowedOrigins": ["http://localhost:3000", "http://localhost:3001", "http://localhost:3002", "http://localhost:4200", "https://localhost:3000", "https://localhost:3001", "https://localhost:3002", "https://localhost:4200", "http://localhost:5008", "https://localhost:7008"], "ConnectionStrings": {"DefaultConnection": "Host=**************;Port=5432;Database=vms_inventorymanagementservice;Username=postgres;Password=************"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "Serilog": {"MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Information", "System": "Information"}}}, "RabbitMQ": {"Host": "localhost", "Username": "guest", "Password": "guest"}, "Kestrel": {"Limits": {"MaxRequestBodySize": 10485760, "MaxRequestHeadersTotalSize": 32768, "MaxRequestLineSize": 8192, "MaxRequestBufferSize": 10485760, "MaxResponseBufferSize": 10485760}}}