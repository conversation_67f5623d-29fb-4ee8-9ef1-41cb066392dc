2025-06-10 15:41:26.319 +05:30 [INF] Ensuring database exists...
2025-06-10 15:41:31.697 +05:30 [INF] Executed DbCommand (241ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-06-10 15:41:31.728 +05:30 [INF] Database ensured successfully
2025-06-10 15:41:31.730 +05:30 [INF] Checking database connection...
2025-06-10 15:41:32.846 +05:30 [INF] Database connection successful
2025-06-10 15:41:32.858 +05:30 [INF] Checking if database needs to be created...
2025-06-10 15:41:34.179 +05:30 [INF] Executed DbCommand (163ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-06-10 15:41:34.369 +05:30 [INF] Executed DbCommand (165ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-06-10 15:41:34.383 +05:30 [INF] Pending migrations: 20250610074453_InitialCreate
2025-06-10 15:41:34.386 +05:30 [INF] Applying migrations...
2025-06-10 15:41:35.649 +05:30 [INF] Executed DbCommand (166ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-06-10 15:41:35.833 +05:30 [INF] Executed DbCommand (159ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-06-10 15:41:37.174 +05:30 [INF] Executed DbCommand (162ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-06-10 15:41:38.451 +05:30 [INF] Executed DbCommand (158ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-06-10 15:41:38.631 +05:30 [INF] Executed DbCommand (159ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-06-10 15:41:38.686 +05:30 [INF] Applying migration '20250610074453_InitialCreate'.
2025-06-10 15:41:38.961 +05:30 [ERR] Failed executing DbCommand (174ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "InventoryAdjustments" (
    "Id" uuid NOT NULL,
    "VendorId" uuid NOT NULL,
    "BranchId" uuid NOT NULL,
    "AdjustmentNumber" character varying(50) NOT NULL,
    "Reason" integer NOT NULL,
    "ReasonDescription" character varying(200) NOT NULL,
    "AdjustmentDate" timestamp with time zone NOT NULL,
    "Notes" character varying(1000),
    "ApprovedBy" character varying(100),
    "ApprovedDate" timestamp with time zone,
    "IsApproved" boolean NOT NULL,
    "CreatedAt" timestamp with time zone NOT NULL,
    "CreatedBy" character varying(100) NOT NULL,
    "UpdatedAt" timestamp with time zone,
    "UpdatedBy" character varying(100),
    "IsActive" boolean NOT NULL,
    CONSTRAINT "PK_InventoryAdjustments" PRIMARY KEY ("Id")
);
2025-06-10 15:41:39.150 +05:30 [WRN] Migration failed, but continuing with seeding. This might be expected if database was created with EnsureCreated.
Npgsql.PostgresException (0x80004005): 42P07: relation "InventoryAdjustments" already exists
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteNonQuery(Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.MigrateAsync(String targetMigration, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Migrations.Internal.NpgsqlMigrator.MigrateAsync(String targetMigration, CancellationToken cancellationToken)
   at InventoryManagementService.Infrastructure.InfrastructureServiceRegistration.InitializeDatabaseAsync(IServiceProvider serviceProvider) in E:\Shri\AutomobilesGenerative02062025\AutomobilesGenerative\VMS-API\InventoryManagementService\src\InventoryManagementService.Infrastructure\InfrastructureServiceRegistration.cs:line 126
  Exception data:
    Severity: ERROR
    SqlState: 42P07
    MessageText: relation "InventoryAdjustments" already exists
    File: heap.c
    Line: 1160
    Routine: heap_create_with_catalog
2025-06-10 15:41:39.197 +05:30 [INF] Starting inventory database seeding...
2025-06-10 15:41:40.052 +05:30 [INF] Executed DbCommand (157ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT s."Id", s."CreatedAt", s."CreatedBy", s."DestinationBranchId", s."IsActive", s."Notes", s."ReceivedBy", s."ReceivedDate", s."RequestedDate", s."ShippedBy", s."ShippedDate", s."SourceBranchId", s."Status", s."TransferNumber", s."UpdatedAt", s."UpdatedBy", s."VendorId"
FROM "StockTransfers" AS s
2025-06-10 15:41:40.228 +05:30 [INF] Executed DbCommand (158ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT i."Id", i."CreatedAt", i."CreatedBy", i."InventoryItemId", i."IsActive", i."NewStock", i."PreviousStock", i."Quantity", i."Reason", i."Reference", i."RelatedTransactionId", i."TransactionDate", i."TransactionType", i."UpdatedAt", i."UpdatedBy"
FROM "InventoryTransactions" AS i
2025-06-10 15:41:40.416 +05:30 [INF] Executed DbCommand (159ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT i."Id", i."Bin", i."BranchId", i."CreatedAt", i."CreatedBy", i."CurrentStock", i."IsActive", i."LastStockUpdate", i."MaximumStock", i."MinimumStock", i."PartId", i."ReorderLevel", i."Shelf", i."StorageLocation", i."UpdatedAt", i."UpdatedBy", i."VendorId"
FROM "InventoryItems" AS i
2025-06-10 15:41:40.679 +05:30 [INF] Executed DbCommand (160ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."CreatedAt", p."CreatedBy", p."EngineType", p."IsActive", p."Make", p."Model", p."PartId", p."UpdatedAt", p."UpdatedBy", p."Variant", p."YearFrom", p."YearTo"
FROM "PartCompatibilities" AS p
2025-06-10 15:41:40.874 +05:30 [INF] Executed DbCommand (159ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Barcode", p."Category", p."CostPrice", p."CreatedAt", p."CreatedBy", p."Description", p."ImageUrl", p."IsActive", p."Manufacturer", p."Name", p."Notes", p."PartNumber", p."RetailPrice", p."SKU", p."UpdatedAt", p."UpdatedBy", p."WarrantyMonths"
FROM "Parts" AS p
2025-06-10 15:41:41.223 +05:30 [INF] Executed DbCommand (171ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = Guid), @p2='?' (DbType = Guid), @p3='?' (DbType = Guid), @p4='?' (DbType = Guid), @p5='?' (DbType = Guid), @p6='?' (DbType = Guid), @p7='?' (DbType = Guid), @p8='?' (DbType = Guid), @p9='?' (DbType = Guid), @p10='?' (DbType = Guid), @p11='?' (DbType = Guid), @p12='?' (DbType = Guid), @p13='?' (DbType = Guid), @p14='?' (DbType = Guid), @p15='?' (DbType = Guid), @p16='?' (DbType = Guid), @p17='?' (DbType = Guid), @p18='?' (DbType = Guid), @p19='?' (DbType = Guid), @p20='?' (DbType = Guid), @p21='?' (DbType = Guid), @p22='?' (DbType = Guid), @p23='?' (DbType = Guid), @p24='?' (DbType = Guid), @p25='?' (DbType = Guid), @p26='?' (DbType = Guid), @p27='?' (DbType = Guid), @p28='?' (DbType = Guid), @p29='?' (DbType = Guid), @p30='?' (DbType = Guid), @p31='?' (DbType = Guid), @p32='?' (DbType = Guid), @p33='?' (DbType = Guid), @p34='?' (DbType = Guid), @p35='?' (DbType = Guid), @p36='?' (DbType = Guid), @p37='?' (DbType = Guid), @p38='?' (DbType = Guid), @p39='?' (DbType = Guid), @p40='?' (DbType = Guid), @p41='?' (DbType = Guid), @p42='?' (DbType = Guid), @p43='?' (DbType = Guid), @p44='?' (DbType = Guid), @p45='?' (DbType = Guid), @p46='?' (DbType = Guid), @p47='?' (DbType = Guid), @p48='?' (DbType = Guid), @p49='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
DELETE FROM "InventoryItems"
WHERE "Id" = @p0;
DELETE FROM "InventoryItems"
WHERE "Id" = @p1;
DELETE FROM "InventoryItems"
WHERE "Id" = @p2;
DELETE FROM "InventoryItems"
WHERE "Id" = @p3;
DELETE FROM "InventoryItems"
WHERE "Id" = @p4;
DELETE FROM "InventoryItems"
WHERE "Id" = @p5;
DELETE FROM "InventoryItems"
WHERE "Id" = @p6;
DELETE FROM "InventoryItems"
WHERE "Id" = @p7;
DELETE FROM "InventoryItems"
WHERE "Id" = @p8;
DELETE FROM "InventoryItems"
WHERE "Id" = @p9;
DELETE FROM "InventoryItems"
WHERE "Id" = @p10;
DELETE FROM "InventoryItems"
WHERE "Id" = @p11;
DELETE FROM "InventoryItems"
WHERE "Id" = @p12;
DELETE FROM "InventoryItems"
WHERE "Id" = @p13;
DELETE FROM "InventoryItems"
WHERE "Id" = @p14;
DELETE FROM "InventoryItems"
WHERE "Id" = @p15;
DELETE FROM "InventoryItems"
WHERE "Id" = @p16;
DELETE FROM "InventoryItems"
WHERE "Id" = @p17;
DELETE FROM "InventoryItems"
WHERE "Id" = @p18;
DELETE FROM "InventoryItems"
WHERE "Id" = @p19;
DELETE FROM "PartCompatibilities"
WHERE "Id" = @p20;
DELETE FROM "PartCompatibilities"
WHERE "Id" = @p21;
DELETE FROM "PartCompatibilities"
WHERE "Id" = @p22;
DELETE FROM "PartCompatibilities"
WHERE "Id" = @p23;
DELETE FROM "PartCompatibilities"
WHERE "Id" = @p24;
DELETE FROM "PartCompatibilities"
WHERE "Id" = @p25;
DELETE FROM "PartCompatibilities"
WHERE "Id" = @p26;
DELETE FROM "PartCompatibilities"
WHERE "Id" = @p27;
DELETE FROM "PartCompatibilities"
WHERE "Id" = @p28;
DELETE FROM "PartCompatibilities"
WHERE "Id" = @p29;
DELETE FROM "Parts"
WHERE "Id" = @p30;
DELETE FROM "Parts"
WHERE "Id" = @p31;
DELETE FROM "Parts"
WHERE "Id" = @p32;
DELETE FROM "Parts"
WHERE "Id" = @p33;
DELETE FROM "Parts"
WHERE "Id" = @p34;
DELETE FROM "Parts"
WHERE "Id" = @p35;
DELETE FROM "Parts"
WHERE "Id" = @p36;
DELETE FROM "Parts"
WHERE "Id" = @p37;
DELETE FROM "Parts"
WHERE "Id" = @p38;
DELETE FROM "Parts"
WHERE "Id" = @p39;
DELETE FROM "Parts"
WHERE "Id" = @p40;
DELETE FROM "Parts"
WHERE "Id" = @p41;
DELETE FROM "Parts"
WHERE "Id" = @p42;
DELETE FROM "Parts"
WHERE "Id" = @p43;
DELETE FROM "Parts"
WHERE "Id" = @p44;
DELETE FROM "Parts"
WHERE "Id" = @p45;
DELETE FROM "Parts"
WHERE "Id" = @p46;
DELETE FROM "Parts"
WHERE "Id" = @p47;
DELETE FROM "Parts"
WHERE "Id" = @p48;
DELETE FROM "Parts"
WHERE "Id" = @p49;
2025-06-10 15:41:41.657 +05:30 [INF] Executed DbCommand (174ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?' (DbType = Int32), @p3='?' (DbType = Decimal), @p4='?' (DbType = DateTime), @p5='?', @p6='?', @p7='?', @p8='?' (DbType = Boolean), @p9='?', @p10='?', @p11='?', @p12='?', @p13='?' (DbType = Decimal), @p14='?', @p15='?' (DbType = DateTime), @p16='?', @p17='?' (DbType = Int32), @p18='?' (DbType = Guid), @p19='?', @p20='?' (DbType = Int32), @p21='?' (DbType = Decimal), @p22='?' (DbType = DateTime), @p23='?', @p24='?', @p25='?', @p26='?' (DbType = Boolean), @p27='?', @p28='?', @p29='?', @p30='?', @p31='?' (DbType = Decimal), @p32='?', @p33='?' (DbType = DateTime), @p34='?', @p35='?' (DbType = Int32), @p36='?' (DbType = Guid), @p37='?', @p38='?' (DbType = Int32), @p39='?' (DbType = Decimal), @p40='?' (DbType = DateTime), @p41='?', @p42='?', @p43='?', @p44='?' (DbType = Boolean), @p45='?', @p46='?', @p47='?', @p48='?', @p49='?' (DbType = Decimal), @p50='?', @p51='?' (DbType = DateTime), @p52='?', @p53='?' (DbType = Int32), @p54='?' (DbType = Guid), @p55='?', @p56='?' (DbType = Int32), @p57='?' (DbType = Decimal), @p58='?' (DbType = DateTime), @p59='?', @p60='?', @p61='?', @p62='?' (DbType = Boolean), @p63='?', @p64='?', @p65='?', @p66='?', @p67='?' (DbType = Decimal), @p68='?', @p69='?' (DbType = DateTime), @p70='?', @p71='?' (DbType = Int32), @p72='?' (DbType = Guid), @p73='?', @p74='?' (DbType = Int32), @p75='?' (DbType = Decimal), @p76='?' (DbType = DateTime), @p77='?', @p78='?', @p79='?', @p80='?' (DbType = Boolean), @p81='?', @p82='?', @p83='?', @p84='?', @p85='?' (DbType = Decimal), @p86='?', @p87='?' (DbType = DateTime), @p88='?', @p89='?' (DbType = Int32), @p90='?' (DbType = Guid), @p91='?', @p92='?' (DbType = Int32), @p93='?' (DbType = Decimal), @p94='?' (DbType = DateTime), @p95='?', @p96='?', @p97='?', @p98='?' (DbType = Boolean), @p99='?', @p100='?', @p101='?', @p102='?', @p103='?' (DbType = Decimal), @p104='?', @p105='?' (DbType = DateTime), @p106='?', @p107='?' (DbType = Int32), @p108='?' (DbType = Guid), @p109='?', @p110='?' (DbType = Int32), @p111='?' (DbType = Decimal), @p112='?' (DbType = DateTime), @p113='?', @p114='?', @p115='?', @p116='?' (DbType = Boolean), @p117='?', @p118='?', @p119='?', @p120='?', @p121='?' (DbType = Decimal), @p122='?', @p123='?' (DbType = DateTime), @p124='?', @p125='?' (DbType = Int32), @p126='?' (DbType = Guid), @p127='?', @p128='?' (DbType = Int32), @p129='?' (DbType = Decimal), @p130='?' (DbType = DateTime), @p131='?', @p132='?', @p133='?', @p134='?' (DbType = Boolean), @p135='?', @p136='?', @p137='?', @p138='?', @p139='?' (DbType = Decimal), @p140='?', @p141='?' (DbType = DateTime), @p142='?', @p143='?' (DbType = Int32), @p144='?' (DbType = Guid), @p145='?', @p146='?' (DbType = Int32), @p147='?' (DbType = Decimal), @p148='?' (DbType = DateTime), @p149='?', @p150='?', @p151='?', @p152='?' (DbType = Boolean), @p153='?', @p154='?', @p155='?', @p156='?', @p157='?' (DbType = Decimal), @p158='?', @p159='?' (DbType = DateTime), @p160='?', @p161='?' (DbType = Int32), @p162='?' (DbType = Guid), @p163='?', @p164='?' (DbType = Int32), @p165='?' (DbType = Decimal), @p166='?' (DbType = DateTime), @p167='?', @p168='?', @p169='?', @p170='?' (DbType = Boolean), @p171='?', @p172='?', @p173='?', @p174='?', @p175='?' (DbType = Decimal), @p176='?', @p177='?' (DbType = DateTime), @p178='?', @p179='?' (DbType = Int32), @p180='?' (DbType = Guid), @p181='?', @p182='?' (DbType = Int32), @p183='?' (DbType = Decimal), @p184='?' (DbType = DateTime), @p185='?', @p186='?', @p187='?', @p188='?' (DbType = Boolean), @p189='?', @p190='?', @p191='?', @p192='?', @p193='?' (DbType = Decimal), @p194='?', @p195='?' (DbType = DateTime), @p196='?', @p197='?' (DbType = Int32), @p198='?' (DbType = Guid), @p199='?', @p200='?' (DbType = Int32), @p201='?' (DbType = Decimal), @p202='?' (DbType = DateTime), @p203='?', @p204='?', @p205='?', @p206='?' (DbType = Boolean), @p207='?', @p208='?', @p209='?', @p210='?', @p211='?' (DbType = Decimal), @p212='?', @p213='?' (DbType = DateTime), @p214='?', @p215='?' (DbType = Int32), @p216='?' (DbType = Guid), @p217='?', @p218='?' (DbType = Int32), @p219='?' (DbType = Decimal), @p220='?' (DbType = DateTime), @p221='?', @p222='?', @p223='?', @p224='?' (DbType = Boolean), @p225='?', @p226='?', @p227='?', @p228='?', @p229='?' (DbType = Decimal), @p230='?', @p231='?' (DbType = DateTime), @p232='?', @p233='?' (DbType = Int32), @p234='?' (DbType = Guid), @p235='?', @p236='?' (DbType = Int32), @p237='?' (DbType = Decimal), @p238='?' (DbType = DateTime), @p239='?', @p240='?', @p241='?', @p242='?' (DbType = Boolean), @p243='?', @p244='?', @p245='?', @p246='?', @p247='?' (DbType = Decimal), @p248='?', @p249='?' (DbType = DateTime), @p250='?', @p251='?' (DbType = Int32), @p252='?' (DbType = Guid), @p253='?', @p254='?' (DbType = Int32), @p255='?' (DbType = Decimal), @p256='?' (DbType = DateTime), @p257='?', @p258='?', @p259='?', @p260='?' (DbType = Boolean), @p261='?', @p262='?', @p263='?', @p264='?', @p265='?' (DbType = Decimal), @p266='?', @p267='?' (DbType = DateTime), @p268='?', @p269='?' (DbType = Int32), @p270='?' (DbType = Guid), @p271='?', @p272='?' (DbType = Int32), @p273='?' (DbType = Decimal), @p274='?' (DbType = DateTime), @p275='?', @p276='?', @p277='?', @p278='?' (DbType = Boolean), @p279='?', @p280='?', @p281='?', @p282='?', @p283='?' (DbType = Decimal), @p284='?', @p285='?' (DbType = DateTime), @p286='?', @p287='?' (DbType = Int32), @p288='?' (DbType = Guid), @p289='?', @p290='?' (DbType = Int32), @p291='?' (DbType = Decimal), @p292='?' (DbType = DateTime), @p293='?', @p294='?', @p295='?', @p296='?' (DbType = Boolean), @p297='?', @p298='?', @p299='?', @p300='?', @p301='?' (DbType = Decimal), @p302='?', @p303='?' (DbType = DateTime), @p304='?', @p305='?' (DbType = Int32), @p306='?' (DbType = Guid), @p307='?', @p308='?' (DbType = Int32), @p309='?' (DbType = Decimal), @p310='?' (DbType = DateTime), @p311='?', @p312='?', @p313='?', @p314='?' (DbType = Boolean), @p315='?', @p316='?', @p317='?', @p318='?', @p319='?' (DbType = Decimal), @p320='?', @p321='?' (DbType = DateTime), @p322='?', @p323='?' (DbType = Int32), @p324='?' (DbType = Guid), @p325='?', @p326='?' (DbType = Int32), @p327='?' (DbType = Decimal), @p328='?' (DbType = DateTime), @p329='?', @p330='?', @p331='?', @p332='?' (DbType = Boolean), @p333='?', @p334='?', @p335='?', @p336='?', @p337='?' (DbType = Decimal), @p338='?', @p339='?' (DbType = DateTime), @p340='?', @p341='?' (DbType = Int32), @p342='?' (DbType = Guid), @p343='?', @p344='?' (DbType = Int32), @p345='?' (DbType = Decimal), @p346='?' (DbType = DateTime), @p347='?', @p348='?', @p349='?', @p350='?' (DbType = Boolean), @p351='?', @p352='?', @p353='?', @p354='?', @p355='?' (DbType = Decimal), @p356='?', @p357='?' (DbType = DateTime), @p358='?', @p359='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Parts" ("Id", "Barcode", "Category", "CostPrice", "CreatedAt", "CreatedBy", "Description", "ImageUrl", "IsActive", "Manufacturer", "Name", "Notes", "PartNumber", "RetailPrice", "SKU", "UpdatedAt", "UpdatedBy", "WarrantyMonths")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17);
INSERT INTO "Parts" ("Id", "Barcode", "Category", "CostPrice", "CreatedAt", "CreatedBy", "Description", "ImageUrl", "IsActive", "Manufacturer", "Name", "Notes", "PartNumber", "RetailPrice", "SKU", "UpdatedAt", "UpdatedBy", "WarrantyMonths")
VALUES (@p18, @p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34, @p35);
INSERT INTO "Parts" ("Id", "Barcode", "Category", "CostPrice", "CreatedAt", "CreatedBy", "Description", "ImageUrl", "IsActive", "Manufacturer", "Name", "Notes", "PartNumber", "RetailPrice", "SKU", "UpdatedAt", "UpdatedBy", "WarrantyMonths")
VALUES (@p36, @p37, @p38, @p39, @p40, @p41, @p42, @p43, @p44, @p45, @p46, @p47, @p48, @p49, @p50, @p51, @p52, @p53);
INSERT INTO "Parts" ("Id", "Barcode", "Category", "CostPrice", "CreatedAt", "CreatedBy", "Description", "ImageUrl", "IsActive", "Manufacturer", "Name", "Notes", "PartNumber", "RetailPrice", "SKU", "UpdatedAt", "UpdatedBy", "WarrantyMonths")
VALUES (@p54, @p55, @p56, @p57, @p58, @p59, @p60, @p61, @p62, @p63, @p64, @p65, @p66, @p67, @p68, @p69, @p70, @p71);
INSERT INTO "Parts" ("Id", "Barcode", "Category", "CostPrice", "CreatedAt", "CreatedBy", "Description", "ImageUrl", "IsActive", "Manufacturer", "Name", "Notes", "PartNumber", "RetailPrice", "SKU", "UpdatedAt", "UpdatedBy", "WarrantyMonths")
VALUES (@p72, @p73, @p74, @p75, @p76, @p77, @p78, @p79, @p80, @p81, @p82, @p83, @p84, @p85, @p86, @p87, @p88, @p89);
INSERT INTO "Parts" ("Id", "Barcode", "Category", "CostPrice", "CreatedAt", "CreatedBy", "Description", "ImageUrl", "IsActive", "Manufacturer", "Name", "Notes", "PartNumber", "RetailPrice", "SKU", "UpdatedAt", "UpdatedBy", "WarrantyMonths")
VALUES (@p90, @p91, @p92, @p93, @p94, @p95, @p96, @p97, @p98, @p99, @p100, @p101, @p102, @p103, @p104, @p105, @p106, @p107);
INSERT INTO "Parts" ("Id", "Barcode", "Category", "CostPrice", "CreatedAt", "CreatedBy", "Description", "ImageUrl", "IsActive", "Manufacturer", "Name", "Notes", "PartNumber", "RetailPrice", "SKU", "UpdatedAt", "UpdatedBy", "WarrantyMonths")
VALUES (@p108, @p109, @p110, @p111, @p112, @p113, @p114, @p115, @p116, @p117, @p118, @p119, @p120, @p121, @p122, @p123, @p124, @p125);
INSERT INTO "Parts" ("Id", "Barcode", "Category", "CostPrice", "CreatedAt", "CreatedBy", "Description", "ImageUrl", "IsActive", "Manufacturer", "Name", "Notes", "PartNumber", "RetailPrice", "SKU", "UpdatedAt", "UpdatedBy", "WarrantyMonths")
VALUES (@p126, @p127, @p128, @p129, @p130, @p131, @p132, @p133, @p134, @p135, @p136, @p137, @p138, @p139, @p140, @p141, @p142, @p143);
INSERT INTO "Parts" ("Id", "Barcode", "Category", "CostPrice", "CreatedAt", "CreatedBy", "Description", "ImageUrl", "IsActive", "Manufacturer", "Name", "Notes", "PartNumber", "RetailPrice", "SKU", "UpdatedAt", "UpdatedBy", "WarrantyMonths")
VALUES (@p144, @p145, @p146, @p147, @p148, @p149, @p150, @p151, @p152, @p153, @p154, @p155, @p156, @p157, @p158, @p159, @p160, @p161);
INSERT INTO "Parts" ("Id", "Barcode", "Category", "CostPrice", "CreatedAt", "CreatedBy", "Description", "ImageUrl", "IsActive", "Manufacturer", "Name", "Notes", "PartNumber", "RetailPrice", "SKU", "UpdatedAt", "UpdatedBy", "WarrantyMonths")
VALUES (@p162, @p163, @p164, @p165, @p166, @p167, @p168, @p169, @p170, @p171, @p172, @p173, @p174, @p175, @p176, @p177, @p178, @p179);
INSERT INTO "Parts" ("Id", "Barcode", "Category", "CostPrice", "CreatedAt", "CreatedBy", "Description", "ImageUrl", "IsActive", "Manufacturer", "Name", "Notes", "PartNumber", "RetailPrice", "SKU", "UpdatedAt", "UpdatedBy", "WarrantyMonths")
VALUES (@p180, @p181, @p182, @p183, @p184, @p185, @p186, @p187, @p188, @p189, @p190, @p191, @p192, @p193, @p194, @p195, @p196, @p197);
INSERT INTO "Parts" ("Id", "Barcode", "Category", "CostPrice", "CreatedAt", "CreatedBy", "Description", "ImageUrl", "IsActive", "Manufacturer", "Name", "Notes", "PartNumber", "RetailPrice", "SKU", "UpdatedAt", "UpdatedBy", "WarrantyMonths")
VALUES (@p198, @p199, @p200, @p201, @p202, @p203, @p204, @p205, @p206, @p207, @p208, @p209, @p210, @p211, @p212, @p213, @p214, @p215);
INSERT INTO "Parts" ("Id", "Barcode", "Category", "CostPrice", "CreatedAt", "CreatedBy", "Description", "ImageUrl", "IsActive", "Manufacturer", "Name", "Notes", "PartNumber", "RetailPrice", "SKU", "UpdatedAt", "UpdatedBy", "WarrantyMonths")
VALUES (@p216, @p217, @p218, @p219, @p220, @p221, @p222, @p223, @p224, @p225, @p226, @p227, @p228, @p229, @p230, @p231, @p232, @p233);
INSERT INTO "Parts" ("Id", "Barcode", "Category", "CostPrice", "CreatedAt", "CreatedBy", "Description", "ImageUrl", "IsActive", "Manufacturer", "Name", "Notes", "PartNumber", "RetailPrice", "SKU", "UpdatedAt", "UpdatedBy", "WarrantyMonths")
VALUES (@p234, @p235, @p236, @p237, @p238, @p239, @p240, @p241, @p242, @p243, @p244, @p245, @p246, @p247, @p248, @p249, @p250, @p251);
INSERT INTO "Parts" ("Id", "Barcode", "Category", "CostPrice", "CreatedAt", "CreatedBy", "Description", "ImageUrl", "IsActive", "Manufacturer", "Name", "Notes", "PartNumber", "RetailPrice", "SKU", "UpdatedAt", "UpdatedBy", "WarrantyMonths")
VALUES (@p252, @p253, @p254, @p255, @p256, @p257, @p258, @p259, @p260, @p261, @p262, @p263, @p264, @p265, @p266, @p267, @p268, @p269);
INSERT INTO "Parts" ("Id", "Barcode", "Category", "CostPrice", "CreatedAt", "CreatedBy", "Description", "ImageUrl", "IsActive", "Manufacturer", "Name", "Notes", "PartNumber", "RetailPrice", "SKU", "UpdatedAt", "UpdatedBy", "WarrantyMonths")
VALUES (@p270, @p271, @p272, @p273, @p274, @p275, @p276, @p277, @p278, @p279, @p280, @p281, @p282, @p283, @p284, @p285, @p286, @p287);
INSERT INTO "Parts" ("Id", "Barcode", "Category", "CostPrice", "CreatedAt", "CreatedBy", "Description", "ImageUrl", "IsActive", "Manufacturer", "Name", "Notes", "PartNumber", "RetailPrice", "SKU", "UpdatedAt", "UpdatedBy", "WarrantyMonths")
VALUES (@p288, @p289, @p290, @p291, @p292, @p293, @p294, @p295, @p296, @p297, @p298, @p299, @p300, @p301, @p302, @p303, @p304, @p305);
INSERT INTO "Parts" ("Id", "Barcode", "Category", "CostPrice", "CreatedAt", "CreatedBy", "Description", "ImageUrl", "IsActive", "Manufacturer", "Name", "Notes", "PartNumber", "RetailPrice", "SKU", "UpdatedAt", "UpdatedBy", "WarrantyMonths")
VALUES (@p306, @p307, @p308, @p309, @p310, @p311, @p312, @p313, @p314, @p315, @p316, @p317, @p318, @p319, @p320, @p321, @p322, @p323);
INSERT INTO "Parts" ("Id", "Barcode", "Category", "CostPrice", "CreatedAt", "CreatedBy", "Description", "ImageUrl", "IsActive", "Manufacturer", "Name", "Notes", "PartNumber", "RetailPrice", "SKU", "UpdatedAt", "UpdatedBy", "WarrantyMonths")
VALUES (@p324, @p325, @p326, @p327, @p328, @p329, @p330, @p331, @p332, @p333, @p334, @p335, @p336, @p337, @p338, @p339, @p340, @p341);
INSERT INTO "Parts" ("Id", "Barcode", "Category", "CostPrice", "CreatedAt", "CreatedBy", "Description", "ImageUrl", "IsActive", "Manufacturer", "Name", "Notes", "PartNumber", "RetailPrice", "SKU", "UpdatedAt", "UpdatedBy", "WarrantyMonths")
VALUES (@p342, @p343, @p344, @p345, @p346, @p347, @p348, @p349, @p350, @p351, @p352, @p353, @p354, @p355, @p356, @p357, @p358, @p359);
2025-06-10 15:41:42.085 +05:30 [INF] Executed DbCommand (161ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = DateTime), @p2='?', @p3='?', @p4='?' (DbType = Boolean), @p5='?', @p6='?', @p7='?' (DbType = Guid), @p8='?' (DbType = DateTime), @p9='?', @p10='?', @p11='?' (DbType = Int32), @p12='?' (DbType = Int32), @p13='?' (DbType = Guid), @p14='?' (DbType = DateTime), @p15='?', @p16='?', @p17='?' (DbType = Boolean), @p18='?', @p19='?', @p20='?' (DbType = Guid), @p21='?' (DbType = DateTime), @p22='?', @p23='?', @p24='?' (DbType = Int32), @p25='?' (DbType = Int32), @p26='?' (DbType = Guid), @p27='?' (DbType = DateTime), @p28='?', @p29='?', @p30='?' (DbType = Boolean), @p31='?', @p32='?', @p33='?' (DbType = Guid), @p34='?' (DbType = DateTime), @p35='?', @p36='?', @p37='?' (DbType = Int32), @p38='?' (DbType = Int32), @p39='?' (DbType = Guid), @p40='?' (DbType = DateTime), @p41='?', @p42='?', @p43='?' (DbType = Boolean), @p44='?', @p45='?', @p46='?' (DbType = Guid), @p47='?' (DbType = DateTime), @p48='?', @p49='?', @p50='?' (DbType = Int32), @p51='?' (DbType = Int32), @p52='?' (DbType = Guid), @p53='?' (DbType = DateTime), @p54='?', @p55='?', @p56='?' (DbType = Boolean), @p57='?', @p58='?', @p59='?' (DbType = Guid), @p60='?' (DbType = DateTime), @p61='?', @p62='?', @p63='?' (DbType = Int32), @p64='?' (DbType = Int32), @p65='?' (DbType = Guid), @p66='?' (DbType = DateTime), @p67='?', @p68='?', @p69='?' (DbType = Boolean), @p70='?', @p71='?', @p72='?' (DbType = Guid), @p73='?' (DbType = DateTime), @p74='?', @p75='?', @p76='?' (DbType = Int32), @p77='?' (DbType = Int32), @p78='?' (DbType = Guid), @p79='?' (DbType = DateTime), @p80='?', @p81='?', @p82='?' (DbType = Boolean), @p83='?', @p84='?', @p85='?' (DbType = Guid), @p86='?' (DbType = DateTime), @p87='?', @p88='?', @p89='?' (DbType = Int32), @p90='?' (DbType = Int32), @p91='?' (DbType = Guid), @p92='?' (DbType = DateTime), @p93='?', @p94='?', @p95='?' (DbType = Boolean), @p96='?', @p97='?', @p98='?' (DbType = Guid), @p99='?' (DbType = DateTime), @p100='?', @p101='?', @p102='?' (DbType = Int32), @p103='?' (DbType = Int32), @p104='?' (DbType = Guid), @p105='?' (DbType = DateTime), @p106='?', @p107='?', @p108='?' (DbType = Boolean), @p109='?', @p110='?', @p111='?' (DbType = Guid), @p112='?' (DbType = DateTime), @p113='?', @p114='?', @p115='?' (DbType = Int32), @p116='?' (DbType = Int32), @p117='?' (DbType = Guid), @p118='?' (DbType = DateTime), @p119='?', @p120='?', @p121='?' (DbType = Boolean), @p122='?', @p123='?', @p124='?' (DbType = Guid), @p125='?' (DbType = DateTime), @p126='?', @p127='?', @p128='?' (DbType = Int32), @p129='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "PartCompatibilities" ("Id", "CreatedAt", "CreatedBy", "EngineType", "IsActive", "Make", "Model", "PartId", "UpdatedAt", "UpdatedBy", "Variant", "YearFrom", "YearTo")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12);
INSERT INTO "PartCompatibilities" ("Id", "CreatedAt", "CreatedBy", "EngineType", "IsActive", "Make", "Model", "PartId", "UpdatedAt", "UpdatedBy", "Variant", "YearFrom", "YearTo")
VALUES (@p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24, @p25);
INSERT INTO "PartCompatibilities" ("Id", "CreatedAt", "CreatedBy", "EngineType", "IsActive", "Make", "Model", "PartId", "UpdatedAt", "UpdatedBy", "Variant", "YearFrom", "YearTo")
VALUES (@p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34, @p35, @p36, @p37, @p38);
INSERT INTO "PartCompatibilities" ("Id", "CreatedAt", "CreatedBy", "EngineType", "IsActive", "Make", "Model", "PartId", "UpdatedAt", "UpdatedBy", "Variant", "YearFrom", "YearTo")
VALUES (@p39, @p40, @p41, @p42, @p43, @p44, @p45, @p46, @p47, @p48, @p49, @p50, @p51);
INSERT INTO "PartCompatibilities" ("Id", "CreatedAt", "CreatedBy", "EngineType", "IsActive", "Make", "Model", "PartId", "UpdatedAt", "UpdatedBy", "Variant", "YearFrom", "YearTo")
VALUES (@p52, @p53, @p54, @p55, @p56, @p57, @p58, @p59, @p60, @p61, @p62, @p63, @p64);
INSERT INTO "PartCompatibilities" ("Id", "CreatedAt", "CreatedBy", "EngineType", "IsActive", "Make", "Model", "PartId", "UpdatedAt", "UpdatedBy", "Variant", "YearFrom", "YearTo")
VALUES (@p65, @p66, @p67, @p68, @p69, @p70, @p71, @p72, @p73, @p74, @p75, @p76, @p77);
INSERT INTO "PartCompatibilities" ("Id", "CreatedAt", "CreatedBy", "EngineType", "IsActive", "Make", "Model", "PartId", "UpdatedAt", "UpdatedBy", "Variant", "YearFrom", "YearTo")
VALUES (@p78, @p79, @p80, @p81, @p82, @p83, @p84, @p85, @p86, @p87, @p88, @p89, @p90);
INSERT INTO "PartCompatibilities" ("Id", "CreatedAt", "CreatedBy", "EngineType", "IsActive", "Make", "Model", "PartId", "UpdatedAt", "UpdatedBy", "Variant", "YearFrom", "YearTo")
VALUES (@p91, @p92, @p93, @p94, @p95, @p96, @p97, @p98, @p99, @p100, @p101, @p102, @p103);
INSERT INTO "PartCompatibilities" ("Id", "CreatedAt", "CreatedBy", "EngineType", "IsActive", "Make", "Model", "PartId", "UpdatedAt", "UpdatedBy", "Variant", "YearFrom", "YearTo")
VALUES (@p104, @p105, @p106, @p107, @p108, @p109, @p110, @p111, @p112, @p113, @p114, @p115, @p116);
INSERT INTO "PartCompatibilities" ("Id", "CreatedAt", "CreatedBy", "EngineType", "IsActive", "Make", "Model", "PartId", "UpdatedAt", "UpdatedBy", "Variant", "YearFrom", "YearTo")
VALUES (@p117, @p118, @p119, @p120, @p121, @p122, @p123, @p124, @p125, @p126, @p127, @p128, @p129);
2025-06-10 15:41:42.492 +05:30 [INF] Executed DbCommand (162ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Barcode", p."Category", p."CostPrice", p."CreatedAt", p."CreatedBy", p."Description", p."ImageUrl", p."IsActive", p."Manufacturer", p."Name", p."Notes", p."PartNumber", p."RetailPrice", p."SKU", p."UpdatedAt", p."UpdatedBy", p."WarrantyMonths"
FROM "Parts" AS p
2025-06-10 15:41:42.743 +05:30 [INF] Executed DbCommand (175ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?' (DbType = Guid), @p3='?' (DbType = DateTime), @p4='?', @p5='?' (DbType = Int32), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime), @p8='?' (DbType = Int32), @p9='?' (DbType = Int32), @p10='?' (DbType = Guid), @p11='?' (DbType = Int32), @p12='?', @p13='?', @p14='?' (DbType = DateTime), @p15='?', @p16='?' (DbType = Guid), @p17='?' (DbType = Guid), @p18='?', @p19='?' (DbType = Guid), @p20='?' (DbType = DateTime), @p21='?', @p22='?' (DbType = Int32), @p23='?' (DbType = Boolean), @p24='?' (DbType = DateTime), @p25='?' (DbType = Int32), @p26='?' (DbType = Int32), @p27='?' (DbType = Guid), @p28='?' (DbType = Int32), @p29='?', @p30='?', @p31='?' (DbType = DateTime), @p32='?', @p33='?' (DbType = Guid), @p34='?' (DbType = Guid), @p35='?', @p36='?' (DbType = Guid), @p37='?' (DbType = DateTime), @p38='?', @p39='?' (DbType = Int32), @p40='?' (DbType = Boolean), @p41='?' (DbType = DateTime), @p42='?' (DbType = Int32), @p43='?' (DbType = Int32), @p44='?' (DbType = Guid), @p45='?' (DbType = Int32), @p46='?', @p47='?', @p48='?' (DbType = DateTime), @p49='?', @p50='?' (DbType = Guid), @p51='?' (DbType = Guid), @p52='?', @p53='?' (DbType = Guid), @p54='?' (DbType = DateTime), @p55='?', @p56='?' (DbType = Int32), @p57='?' (DbType = Boolean), @p58='?' (DbType = DateTime), @p59='?' (DbType = Int32), @p60='?' (DbType = Int32), @p61='?' (DbType = Guid), @p62='?' (DbType = Int32), @p63='?', @p64='?', @p65='?' (DbType = DateTime), @p66='?', @p67='?' (DbType = Guid), @p68='?' (DbType = Guid), @p69='?', @p70='?' (DbType = Guid), @p71='?' (DbType = DateTime), @p72='?', @p73='?' (DbType = Int32), @p74='?' (DbType = Boolean), @p75='?' (DbType = DateTime), @p76='?' (DbType = Int32), @p77='?' (DbType = Int32), @p78='?' (DbType = Guid), @p79='?' (DbType = Int32), @p80='?', @p81='?', @p82='?' (DbType = DateTime), @p83='?', @p84='?' (DbType = Guid), @p85='?' (DbType = Guid), @p86='?', @p87='?' (DbType = Guid), @p88='?' (DbType = DateTime), @p89='?', @p90='?' (DbType = Int32), @p91='?' (DbType = Boolean), @p92='?' (DbType = DateTime), @p93='?' (DbType = Int32), @p94='?' (DbType = Int32), @p95='?' (DbType = Guid), @p96='?' (DbType = Int32), @p97='?', @p98='?', @p99='?' (DbType = DateTime), @p100='?', @p101='?' (DbType = Guid), @p102='?' (DbType = Guid), @p103='?', @p104='?' (DbType = Guid), @p105='?' (DbType = DateTime), @p106='?', @p107='?' (DbType = Int32), @p108='?' (DbType = Boolean), @p109='?' (DbType = DateTime), @p110='?' (DbType = Int32), @p111='?' (DbType = Int32), @p112='?' (DbType = Guid), @p113='?' (DbType = Int32), @p114='?', @p115='?', @p116='?' (DbType = DateTime), @p117='?', @p118='?' (DbType = Guid), @p119='?' (DbType = Guid), @p120='?', @p121='?' (DbType = Guid), @p122='?' (DbType = DateTime), @p123='?', @p124='?' (DbType = Int32), @p125='?' (DbType = Boolean), @p126='?' (DbType = DateTime), @p127='?' (DbType = Int32), @p128='?' (DbType = Int32), @p129='?' (DbType = Guid), @p130='?' (DbType = Int32), @p131='?', @p132='?', @p133='?' (DbType = DateTime), @p134='?', @p135='?' (DbType = Guid), @p136='?' (DbType = Guid), @p137='?', @p138='?' (DbType = Guid), @p139='?' (DbType = DateTime), @p140='?', @p141='?' (DbType = Int32), @p142='?' (DbType = Boolean), @p143='?' (DbType = DateTime), @p144='?' (DbType = Int32), @p145='?' (DbType = Int32), @p146='?' (DbType = Guid), @p147='?' (DbType = Int32), @p148='?', @p149='?', @p150='?' (DbType = DateTime), @p151='?', @p152='?' (DbType = Guid), @p153='?' (DbType = Guid), @p154='?', @p155='?' (DbType = Guid), @p156='?' (DbType = DateTime), @p157='?', @p158='?' (DbType = Int32), @p159='?' (DbType = Boolean), @p160='?' (DbType = DateTime), @p161='?' (DbType = Int32), @p162='?' (DbType = Int32), @p163='?' (DbType = Guid), @p164='?' (DbType = Int32), @p165='?', @p166='?', @p167='?' (DbType = DateTime), @p168='?', @p169='?' (DbType = Guid), @p170='?' (DbType = Guid), @p171='?', @p172='?' (DbType = Guid), @p173='?' (DbType = DateTime), @p174='?', @p175='?' (DbType = Int32), @p176='?' (DbType = Boolean), @p177='?' (DbType = DateTime), @p178='?' (DbType = Int32), @p179='?' (DbType = Int32), @p180='?' (DbType = Guid), @p181='?' (DbType = Int32), @p182='?', @p183='?', @p184='?' (DbType = DateTime), @p185='?', @p186='?' (DbType = Guid), @p187='?' (DbType = Guid), @p188='?', @p189='?' (DbType = Guid), @p190='?' (DbType = DateTime), @p191='?', @p192='?' (DbType = Int32), @p193='?' (DbType = Boolean), @p194='?' (DbType = DateTime), @p195='?' (DbType = Int32), @p196='?' (DbType = Int32), @p197='?' (DbType = Guid), @p198='?' (DbType = Int32), @p199='?', @p200='?', @p201='?' (DbType = DateTime), @p202='?', @p203='?' (DbType = Guid), @p204='?' (DbType = Guid), @p205='?', @p206='?' (DbType = Guid), @p207='?' (DbType = DateTime), @p208='?', @p209='?' (DbType = Int32), @p210='?' (DbType = Boolean), @p211='?' (DbType = DateTime), @p212='?' (DbType = Int32), @p213='?' (DbType = Int32), @p214='?' (DbType = Guid), @p215='?' (DbType = Int32), @p216='?', @p217='?', @p218='?' (DbType = DateTime), @p219='?', @p220='?' (DbType = Guid), @p221='?' (DbType = Guid), @p222='?', @p223='?' (DbType = Guid), @p224='?' (DbType = DateTime), @p225='?', @p226='?' (DbType = Int32), @p227='?' (DbType = Boolean), @p228='?' (DbType = DateTime), @p229='?' (DbType = Int32), @p230='?' (DbType = Int32), @p231='?' (DbType = Guid), @p232='?' (DbType = Int32), @p233='?', @p234='?', @p235='?' (DbType = DateTime), @p236='?', @p237='?' (DbType = Guid), @p238='?' (DbType = Guid), @p239='?', @p240='?' (DbType = Guid), @p241='?' (DbType = DateTime), @p242='?', @p243='?' (DbType = Int32), @p244='?' (DbType = Boolean), @p245='?' (DbType = DateTime), @p246='?' (DbType = Int32), @p247='?' (DbType = Int32), @p248='?' (DbType = Guid), @p249='?' (DbType = Int32), @p250='?', @p251='?', @p252='?' (DbType = DateTime), @p253='?', @p254='?' (DbType = Guid), @p255='?' (DbType = Guid), @p256='?', @p257='?' (DbType = Guid), @p258='?' (DbType = DateTime), @p259='?', @p260='?' (DbType = Int32), @p261='?' (DbType = Boolean), @p262='?' (DbType = DateTime), @p263='?' (DbType = Int32), @p264='?' (DbType = Int32), @p265='?' (DbType = Guid), @p266='?' (DbType = Int32), @p267='?', @p268='?', @p269='?' (DbType = DateTime), @p270='?', @p271='?' (DbType = Guid), @p272='?' (DbType = Guid), @p273='?', @p274='?' (DbType = Guid), @p275='?' (DbType = DateTime), @p276='?', @p277='?' (DbType = Int32), @p278='?' (DbType = Boolean), @p279='?' (DbType = DateTime), @p280='?' (DbType = Int32), @p281='?' (DbType = Int32), @p282='?' (DbType = Guid), @p283='?' (DbType = Int32), @p284='?', @p285='?', @p286='?' (DbType = DateTime), @p287='?', @p288='?' (DbType = Guid), @p289='?' (DbType = Guid), @p290='?', @p291='?' (DbType = Guid), @p292='?' (DbType = DateTime), @p293='?', @p294='?' (DbType = Int32), @p295='?' (DbType = Boolean), @p296='?' (DbType = DateTime), @p297='?' (DbType = Int32), @p298='?' (DbType = Int32), @p299='?' (DbType = Guid), @p300='?' (DbType = Int32), @p301='?', @p302='?', @p303='?' (DbType = DateTime), @p304='?', @p305='?' (DbType = Guid), @p306='?' (DbType = Guid), @p307='?', @p308='?' (DbType = Guid), @p309='?' (DbType = DateTime), @p310='?', @p311='?' (DbType = Int32), @p312='?' (DbType = Boolean), @p313='?' (DbType = DateTime), @p314='?' (DbType = Int32), @p315='?' (DbType = Int32), @p316='?' (DbType = Guid), @p317='?' (DbType = Int32), @p318='?', @p319='?', @p320='?' (DbType = DateTime), @p321='?', @p322='?' (DbType = Guid), @p323='?' (DbType = Guid), @p324='?', @p325='?' (DbType = Guid), @p326='?' (DbType = DateTime), @p327='?', @p328='?' (DbType = Int32), @p329='?' (DbType = Boolean), @p330='?' (DbType = DateTime), @p331='?' (DbType = Int32), @p332='?' (DbType = Int32), @p333='?' (DbType = Guid), @p334='?' (DbType = Int32), @p335='?', @p336='?', @p337='?' (DbType = DateTime), @p338='?', @p339='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "InventoryItems" ("Id", "Bin", "BranchId", "CreatedAt", "CreatedBy", "CurrentStock", "IsActive", "LastStockUpdate", "MaximumStock", "MinimumStock", "PartId", "ReorderLevel", "Shelf", "StorageLocation", "UpdatedAt", "UpdatedBy", "VendorId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16);
INSERT INTO "InventoryItems" ("Id", "Bin", "BranchId", "CreatedAt", "CreatedBy", "CurrentStock", "IsActive", "LastStockUpdate", "MaximumStock", "MinimumStock", "PartId", "ReorderLevel", "Shelf", "StorageLocation", "UpdatedAt", "UpdatedBy", "VendorId")
VALUES (@p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33);
INSERT INTO "InventoryItems" ("Id", "Bin", "BranchId", "CreatedAt", "CreatedBy", "CurrentStock", "IsActive", "LastStockUpdate", "MaximumStock", "MinimumStock", "PartId", "ReorderLevel", "Shelf", "StorageLocation", "UpdatedAt", "UpdatedBy", "VendorId")
VALUES (@p34, @p35, @p36, @p37, @p38, @p39, @p40, @p41, @p42, @p43, @p44, @p45, @p46, @p47, @p48, @p49, @p50);
INSERT INTO "InventoryItems" ("Id", "Bin", "BranchId", "CreatedAt", "CreatedBy", "CurrentStock", "IsActive", "LastStockUpdate", "MaximumStock", "MinimumStock", "PartId", "ReorderLevel", "Shelf", "StorageLocation", "UpdatedAt", "UpdatedBy", "VendorId")
VALUES (@p51, @p52, @p53, @p54, @p55, @p56, @p57, @p58, @p59, @p60, @p61, @p62, @p63, @p64, @p65, @p66, @p67);
INSERT INTO "InventoryItems" ("Id", "Bin", "BranchId", "CreatedAt", "CreatedBy", "CurrentStock", "IsActive", "LastStockUpdate", "MaximumStock", "MinimumStock", "PartId", "ReorderLevel", "Shelf", "StorageLocation", "UpdatedAt", "UpdatedBy", "VendorId")
VALUES (@p68, @p69, @p70, @p71, @p72, @p73, @p74, @p75, @p76, @p77, @p78, @p79, @p80, @p81, @p82, @p83, @p84);
INSERT INTO "InventoryItems" ("Id", "Bin", "BranchId", "CreatedAt", "CreatedBy", "CurrentStock", "IsActive", "LastStockUpdate", "MaximumStock", "MinimumStock", "PartId", "ReorderLevel", "Shelf", "StorageLocation", "UpdatedAt", "UpdatedBy", "VendorId")
VALUES (@p85, @p86, @p87, @p88, @p89, @p90, @p91, @p92, @p93, @p94, @p95, @p96, @p97, @p98, @p99, @p100, @p101);
INSERT INTO "InventoryItems" ("Id", "Bin", "BranchId", "CreatedAt", "CreatedBy", "CurrentStock", "IsActive", "LastStockUpdate", "MaximumStock", "MinimumStock", "PartId", "ReorderLevel", "Shelf", "StorageLocation", "UpdatedAt", "UpdatedBy", "VendorId")
VALUES (@p102, @p103, @p104, @p105, @p106, @p107, @p108, @p109, @p110, @p111, @p112, @p113, @p114, @p115, @p116, @p117, @p118);
INSERT INTO "InventoryItems" ("Id", "Bin", "BranchId", "CreatedAt", "CreatedBy", "CurrentStock", "IsActive", "LastStockUpdate", "MaximumStock", "MinimumStock", "PartId", "ReorderLevel", "Shelf", "StorageLocation", "UpdatedAt", "UpdatedBy", "VendorId")
VALUES (@p119, @p120, @p121, @p122, @p123, @p124, @p125, @p126, @p127, @p128, @p129, @p130, @p131, @p132, @p133, @p134, @p135);
INSERT INTO "InventoryItems" ("Id", "Bin", "BranchId", "CreatedAt", "CreatedBy", "CurrentStock", "IsActive", "LastStockUpdate", "MaximumStock", "MinimumStock", "PartId", "ReorderLevel", "Shelf", "StorageLocation", "UpdatedAt", "UpdatedBy", "VendorId")
VALUES (@p136, @p137, @p138, @p139, @p140, @p141, @p142, @p143, @p144, @p145, @p146, @p147, @p148, @p149, @p150, @p151, @p152);
INSERT INTO "InventoryItems" ("Id", "Bin", "BranchId", "CreatedAt", "CreatedBy", "CurrentStock", "IsActive", "LastStockUpdate", "MaximumStock", "MinimumStock", "PartId", "ReorderLevel", "Shelf", "StorageLocation", "UpdatedAt", "UpdatedBy", "VendorId")
VALUES (@p153, @p154, @p155, @p156, @p157, @p158, @p159, @p160, @p161, @p162, @p163, @p164, @p165, @p166, @p167, @p168, @p169);
INSERT INTO "InventoryItems" ("Id", "Bin", "BranchId", "CreatedAt", "CreatedBy", "CurrentStock", "IsActive", "LastStockUpdate", "MaximumStock", "MinimumStock", "PartId", "ReorderLevel", "Shelf", "StorageLocation", "UpdatedAt", "UpdatedBy", "VendorId")
VALUES (@p170, @p171, @p172, @p173, @p174, @p175, @p176, @p177, @p178, @p179, @p180, @p181, @p182, @p183, @p184, @p185, @p186);
INSERT INTO "InventoryItems" ("Id", "Bin", "BranchId", "CreatedAt", "CreatedBy", "CurrentStock", "IsActive", "LastStockUpdate", "MaximumStock", "MinimumStock", "PartId", "ReorderLevel", "Shelf", "StorageLocation", "UpdatedAt", "UpdatedBy", "VendorId")
VALUES (@p187, @p188, @p189, @p190, @p191, @p192, @p193, @p194, @p195, @p196, @p197, @p198, @p199, @p200, @p201, @p202, @p203);
INSERT INTO "InventoryItems" ("Id", "Bin", "BranchId", "CreatedAt", "CreatedBy", "CurrentStock", "IsActive", "LastStockUpdate", "MaximumStock", "MinimumStock", "PartId", "ReorderLevel", "Shelf", "StorageLocation", "UpdatedAt", "UpdatedBy", "VendorId")
VALUES (@p204, @p205, @p206, @p207, @p208, @p209, @p210, @p211, @p212, @p213, @p214, @p215, @p216, @p217, @p218, @p219, @p220);
INSERT INTO "InventoryItems" ("Id", "Bin", "BranchId", "CreatedAt", "CreatedBy", "CurrentStock", "IsActive", "LastStockUpdate", "MaximumStock", "MinimumStock", "PartId", "ReorderLevel", "Shelf", "StorageLocation", "UpdatedAt", "UpdatedBy", "VendorId")
VALUES (@p221, @p222, @p223, @p224, @p225, @p226, @p227, @p228, @p229, @p230, @p231, @p232, @p233, @p234, @p235, @p236, @p237);
INSERT INTO "InventoryItems" ("Id", "Bin", "BranchId", "CreatedAt", "CreatedBy", "CurrentStock", "IsActive", "LastStockUpdate", "MaximumStock", "MinimumStock", "PartId", "ReorderLevel", "Shelf", "StorageLocation", "UpdatedAt", "UpdatedBy", "VendorId")
VALUES (@p238, @p239, @p240, @p241, @p242, @p243, @p244, @p245, @p246, @p247, @p248, @p249, @p250, @p251, @p252, @p253, @p254);
INSERT INTO "InventoryItems" ("Id", "Bin", "BranchId", "CreatedAt", "CreatedBy", "CurrentStock", "IsActive", "LastStockUpdate", "MaximumStock", "MinimumStock", "PartId", "ReorderLevel", "Shelf", "StorageLocation", "UpdatedAt", "UpdatedBy", "VendorId")
VALUES (@p255, @p256, @p257, @p258, @p259, @p260, @p261, @p262, @p263, @p264, @p265, @p266, @p267, @p268, @p269, @p270, @p271);
INSERT INTO "InventoryItems" ("Id", "Bin", "BranchId", "CreatedAt", "CreatedBy", "CurrentStock", "IsActive", "LastStockUpdate", "MaximumStock", "MinimumStock", "PartId", "ReorderLevel", "Shelf", "StorageLocation", "UpdatedAt", "UpdatedBy", "VendorId")
VALUES (@p272, @p273, @p274, @p275, @p276, @p277, @p278, @p279, @p280, @p281, @p282, @p283, @p284, @p285, @p286, @p287, @p288);
INSERT INTO "InventoryItems" ("Id", "Bin", "BranchId", "CreatedAt", "CreatedBy", "CurrentStock", "IsActive", "LastStockUpdate", "MaximumStock", "MinimumStock", "PartId", "ReorderLevel", "Shelf", "StorageLocation", "UpdatedAt", "UpdatedBy", "VendorId")
VALUES (@p289, @p290, @p291, @p292, @p293, @p294, @p295, @p296, @p297, @p298, @p299, @p300, @p301, @p302, @p303, @p304, @p305);
INSERT INTO "InventoryItems" ("Id", "Bin", "BranchId", "CreatedAt", "CreatedBy", "CurrentStock", "IsActive", "LastStockUpdate", "MaximumStock", "MinimumStock", "PartId", "ReorderLevel", "Shelf", "StorageLocation", "UpdatedAt", "UpdatedBy", "VendorId")
VALUES (@p306, @p307, @p308, @p309, @p310, @p311, @p312, @p313, @p314, @p315, @p316, @p317, @p318, @p319, @p320, @p321, @p322);
INSERT INTO "InventoryItems" ("Id", "Bin", "BranchId", "CreatedAt", "CreatedBy", "CurrentStock", "IsActive", "LastStockUpdate", "MaximumStock", "MinimumStock", "PartId", "ReorderLevel", "Shelf", "StorageLocation", "UpdatedAt", "UpdatedBy", "VendorId")
VALUES (@p323, @p324, @p325, @p326, @p327, @p328, @p329, @p330, @p331, @p332, @p333, @p334, @p335, @p336, @p337, @p338, @p339);
2025-06-10 15:41:44.153 +05:30 [INF] Inventory database seeding completed successfully.
2025-06-10 15:41:44.164 +05:30 [INF] Database initialized successfully
2025-06-10 15:41:44.167 +05:30 [INF] Inventory Management Service starting up...
2025-06-10 15:41:44.169 +05:30 [INF] Service will be available at:
2025-06-10 15:41:44.171 +05:30 [INF]   HTTP:  http://localhost:5008
2025-06-10 15:41:44.174 +05:30 [INF]   HTTPS: https://localhost:7008
2025-06-10 15:41:44.179 +05:30 [INF]   Swagger: http://localhost:5008/swagger
2025-06-10 15:41:44.359 +05:30 [DBG] Starting bus instances: IBus
2025-06-10 15:41:44.365 +05:30 [DBG] Starting bus: "rabbitmq://localhost/"
2025-06-10 15:41:44.427 +05:30 [DBG] Connect: guest@localhost:5672/
2025-06-10 15:41:44.515 +05:30 [DBG] Connected: guest@localhost:5672/ (address: amqp://localhost:5672, local: 56417)
2025-06-10 15:41:44.529 +05:30 [INF] Now listening on: http://localhost:5008
2025-06-10 15:41:44.531 +05:30 [INF] Now listening on: https://localhost:7008
2025-06-10 15:41:44.534 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-06-10 15:41:44.536 +05:30 [INF] Hosting environment: Development
2025-06-10 15:41:44.539 +05:30 [INF] Content root path: E:\Shri\AutomobilesGenerative02062025\AutomobilesGenerative\VMS-API\InventoryManagementService\src\InventoryManagementService.API
2025-06-10 15:41:44.550 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/DESKTOPI7D4M6U_InventoryManagementServiceAPI_bus_ry1oyyfpkikqdr66bdq4ob3uf6?temporary=true"
2025-06-10 15:41:44.553 +05:30 [INF] Bus started: "rabbitmq://localhost/"
2025-06-10 15:41:57.349 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7008/ - null null
2025-06-10 15:41:57.408 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7008/ - 404 0 null 61.4118ms
2025-06-10 15:41:57.446 +05:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7008/, Response status code: 404
2025-06-10 15:42:08.519 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7008/ - null null
2025-06-10 15:42:08.546 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7008/ - 404 0 null 31.8038ms
2025-06-10 15:42:08.580 +05:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7008/, Response status code: 404
2025-06-10 15:42:32.138 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5008/ - null null
2025-06-10 15:42:32.225 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5008/ - 404 0 null 86.937ms
2025-06-10 15:42:32.491 +05:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5008/, Response status code: 404
2025-06-10 15:42:41.205 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5008/swagger/index.html - null null
2025-06-10 15:42:41.317 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5008/swagger/index.html - 200 null text/html;charset=utf-8 111.8743ms
2025-06-10 15:42:41.448 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5008/swagger/v1/swagger.json - null null
2025-06-10 15:42:41.590 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5008/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 141.3589ms
2025-06-10 15:43:15.213 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5008/api/Parts/096ebe02-8404-4886-a39c-183667a23752 - null null
2025-06-10 15:43:15.263 +05:30 [DBG] User is not authenticated
2025-06-10 15:43:15.281 +05:30 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
VMS.Contracts.Common.Authorization.PermissionRequirement
2025-06-10 15:43:15.296 +05:30 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: No authenticationScheme was specified, and there was no DefaultChallengeScheme found. The default schemes can be set using either AddAuthentication(string defaultScheme) or AddAuthentication(Action<AuthenticationOptions> configureOptions).
   at Microsoft.AspNetCore.Authentication.AuthenticationService.ChallengeAsync(HttpContext context, String scheme, AuthenticationProperties properties)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.<>c__DisplayClass0_0.<<HandleAsync>g__Handle|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-10 15:43:15.359 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5008/api/Parts/096ebe02-8404-4886-a39c-183667a23752 - 500 null text/plain; charset=utf-8 145.4694ms
2025-06-10 17:24:48.630 +05:30 [INF] Application is shutting down...
2025-06-10 17:24:48.649 +05:30 [DBG] Stopping bus instances: IBus
2025-06-10 17:24:48.656 +05:30 [DBG] Stopping bus: "rabbitmq://localhost/"
2025-06-10 17:24:48.668 +05:30 [DBG] Endpoint Stopping: "rabbitmq://localhost/DESKTOPI7D4M6U_InventoryManagementServiceAPI_bus_ry1oyyfpkikqdr66bdq4ob3uf6?temporary=true"
2025-06-10 17:24:48.673 +05:30 [DBG] Endpoint Completed: "rabbitmq://localhost/DESKTOPI7D4M6U_InventoryManagementServiceAPI_bus_ry1oyyfpkikqdr66bdq4ob3uf6?temporary=true"
2025-06-10 17:24:48.703 +05:30 [DBG] Disconnect: guest@localhost:5672/
2025-06-10 17:24:48.710 +05:30 [DBG] Disconnected: guest@localhost:5672/
2025-06-10 17:24:48.713 +05:30 [INF] Bus stopped: "rabbitmq://localhost/"
2025-06-10 17:24:48.720 +05:30 [INF] Inventory Management Service shut down complete
