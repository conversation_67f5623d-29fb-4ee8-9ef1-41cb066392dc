# PowerShell script to fix Identity Service migration issues and ensure proper seeding
# This script addresses the PrimaryBranchId column conflict and ensures proper database state

Write-Host "🔧 Starting Identity Service Database Fix..." -ForegroundColor Cyan
Write-Host "=" * 60 -ForegroundColor Gray

# Set error action preference
$ErrorActionPreference = "Stop"

try {
    # Step 1: Navigate to the Identity Service directory
    $identityServicePath = Join-Path $PSScriptRoot "."
    Write-Host "📁 Working directory: $identityServicePath" -ForegroundColor Yellow
    Set-Location $identityServicePath

    # Step 2: Stop any running Identity Service processes
    Write-Host "🛑 Stopping any running Identity Service processes..." -ForegroundColor Yellow
    Get-Process | Where-Object { $_.ProcessName -like "*IdentityService*" } | Stop-Process -Force -ErrorAction SilentlyContinue
    Start-Sleep -Seconds 2

    # Step 3: Run the database fix script
    Write-Host "🔧 Running database fix script..." -ForegroundColor Yellow
    
    # Check if PostgreSQL is accessible
    $connectionString = "Host=localhost;Database=VMS_Identity;Username=postgres;Password=*****"
    
    # Run the fix script using psql
    $fixScriptPath = Join-Path $PSScriptRoot "fix-database.sql"
    if (Test-Path $fixScriptPath) {
        Write-Host "📄 Executing fix-database.sql..." -ForegroundColor Green
        
        # Use psql to execute the script
        $env:PGPASSWORD = "*****"
        & psql -h localhost -U postgres -d VMS_Identity -f $fixScriptPath
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Database fix script executed successfully!" -ForegroundColor Green
        } else {
            Write-Host "❌ Database fix script failed with exit code: $LASTEXITCODE" -ForegroundColor Red
            throw "Database fix script execution failed"
        }
    } else {
        Write-Host "❌ Fix script not found at: $fixScriptPath" -ForegroundColor Red
        throw "Fix script file not found"
    }

    # Step 4: Clean and rebuild the project
    Write-Host "🧹 Cleaning and rebuilding the project..." -ForegroundColor Yellow
    Set-Location "src\IdentityService.API"
    
    dotnet clean
    if ($LASTEXITCODE -ne 0) {
        throw "dotnet clean failed"
    }
    
    dotnet build
    if ($LASTEXITCODE -ne 0) {
        throw "dotnet build failed"
    }

    # Step 5: Run the application to trigger seeding
    Write-Host "🚀 Starting Identity Service to trigger seeding..." -ForegroundColor Yellow
    Write-Host "⏳ This will run for 30 seconds to allow seeding to complete..." -ForegroundColor Cyan
    
    # Start the application in background
    $process = Start-Process -FilePath "dotnet" -ArgumentList "run" -PassThru -NoNewWindow
    
    # Wait for 30 seconds to allow seeding
    Start-Sleep -Seconds 30
    
    # Stop the process
    if (!$process.HasExited) {
        $process.Kill()
        Write-Host "🛑 Stopped Identity Service after seeding period" -ForegroundColor Yellow
    }

    # Step 6: Verify the seeding results
    Write-Host "🔍 Verifying seeding results..." -ForegroundColor Yellow
    
    # Query to check users count
    $verifyQuery = @"
SELECT 
    r.Name as RoleName,
    COUNT(ur.UserId) as UserCount
FROM Roles r
LEFT JOIN UserRoles ur ON r.Id = ur.RoleId
LEFT JOIN Users u ON ur.UserId = u.Id
WHERE u.IsDeleted = false OR u.IsDeleted IS NULL
GROUP BY r.Id, r.Name
ORDER BY r.Name;
"@
    
    Write-Host "📊 User count by role:" -ForegroundColor Cyan
    $env:PGPASSWORD = "*****"
    & psql -h localhost -U postgres -d VMS_Identity -c $verifyQuery
    
    # Check total users
    $totalUsersQuery = "SELECT COUNT(*) as TotalUsers FROM Users WHERE IsDeleted = false;"
    Write-Host "📊 Total users in database:" -ForegroundColor Cyan
    & psql -h localhost -U postgres -d VMS_Identity -c $totalUsersQuery

    Write-Host "=" * 60 -ForegroundColor Gray
    Write-Host "✅ Identity Service fix and seeding completed successfully!" -ForegroundColor Green
    Write-Host "🔑 Default password for all users: Admin@123" -ForegroundColor Cyan
    Write-Host "📧 Users created with pattern: [role][number]@vms.com" -ForegroundColor Cyan
    Write-Host "📱 Phone numbers created with pattern: 90000000[X][Y]" -ForegroundColor Cyan
    
} catch {
    Write-Host "=" * 60 -ForegroundColor Gray
    Write-Host "❌ Error occurred during fix process:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    Write-Host "💡 Please check the logs and try running the script again." -ForegroundColor Yellow
    exit 1
}

Write-Host "=" * 60 -ForegroundColor Gray
Write-Host "🎉 Script execution completed!" -ForegroundColor Green
