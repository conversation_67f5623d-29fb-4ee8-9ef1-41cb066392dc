using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using IdentityService.Domain.Entities;

namespace IdentityService.Application.Interfaces;

/// <summary>
/// Service for managing user sessions
/// </summary>
public interface ISessionManagementService
{
    /// <summary>
    /// Creates a new user session
    /// </summary>
    Task<UserSession> CreateSessionAsync(
        Guid userId, 
        string token, 
        string refreshToken, 
        DateTime expiresAt, 
        string ipAddress, 
        string userAgent, 
        string deviceInfo);
    
    /// <summary>
    /// Validates a session
    /// </summary>
    Task<bool> ValidateSessionAsync(string token);
    
    /// <summary>
    /// Gets a session by token
    /// </summary>
    Task<UserSession> GetSessionByTokenAsync(string token);
    
    /// <summary>
    /// Gets all active sessions for a user
    /// </summary>
    Task<IEnumerable<UserSession>> GetActiveSessionsForUserAsync(Guid userId);
    
    /// <summary>
    /// Updates the last active time for a session
    /// </summary>
    Task UpdateSessionActivityAsync(string token);
    
    /// <summary>
    /// Ends a session (normal logout)
    /// </summary>
    Task EndSessionAsync(string token);
    
    /// <summary>
    /// Ends all sessions for a user
    /// </summary>
    Task EndAllSessionsForUserAsync(Guid userId, string currentToken = null);
    
    /// <summary>
    /// Terminates a session for security reasons
    /// </summary>
    Task TerminateSessionAsync(string token, string reason);
    
    /// <summary>
    /// Checks if a session is idle
    /// </summary>
    Task<bool> IsSessionIdleAsync(string token);
    
    /// <summary>
    /// Expires idle sessions
    /// </summary>
    Task ExpireIdleSessionsAsync();
    
    /// <summary>
    /// Gets the session history for a user
    /// </summary>
    Task<IEnumerable<UserSession>> GetSessionHistoryForUserAsync(
        Guid userId, 
        DateTime? fromDate = null, 
        DateTime? toDate = null, 
        int pageNumber = 1, 
        int pageSize = 10);
}
