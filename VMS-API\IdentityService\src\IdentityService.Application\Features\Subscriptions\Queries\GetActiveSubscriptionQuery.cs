using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using IdentityService.Application.Common;
using IdentityService.Application.DTOs.Subscriptions;
using IdentityService.Domain.Interfaces;
using MediatR;

namespace IdentityService.Application.Features.Subscriptions.Queries;

public class GetActiveSubscriptionQuery : BaseRequest<UserSubscriptionResponse>
{
    // Note: This UserId is the target user's ID, not the current user's ID
    // The current user's ID will be set by the UserIdPropagationBehavior
    public new Guid UserId { get; set; }
}

public class GetActiveSubscriptionQueryHandler : IRequestHandler<GetActiveSubscriptionQuery, UserSubscriptionResponse>
{
    private readonly IUnitOfWork _unitOfWork;

    public GetActiveSubscriptionQueryHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
    }

    public async Task<UserSubscriptionResponse> Handle(GetActiveSubscriptionQuery query, CancellationToken cancellationToken)
    {
        var subscription = await _unitOfWork.UserRepository.GetActiveSubscriptionAsync(query.UserId);

        if (subscription == null)
            return null;

        return new UserSubscriptionResponse
        {
            Id = subscription.Id,
            UserId = subscription.UserId,
            SubscriptionId = subscription.SubscriptionId,
            PlanName = subscription.PlanName,
            Tier = subscription.Tier,
            StartDate = subscription.StartDate,
            EndDate = subscription.EndDate,
            TrialEndDate = subscription.TrialEndDate,
            Status = subscription.Status,
            IsActive = subscription.IsActive(),
            Features = subscription.Features.Select(f => new SubscriptionFeatureResponse
            {
                Id = f.Id,
                FeatureName = f.FeatureName,
                IsEnabled = f.IsEnabled,
                UsageLimit = f.UsageLimit
            }).ToList(),
            CreatedAt = subscription.CreatedAt,
            CreatedBy = subscription.CreatedBy,
            UpdatedAt = subscription.UpdatedAt,
            UpdatedBy = subscription.UpdatedBy ?? string.Empty
        };
    }
}
