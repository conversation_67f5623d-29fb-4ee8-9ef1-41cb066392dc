using InventoryManagementService.Domain.Common;

namespace InventoryManagementService.Domain.Entities;

public class InventoryAdjustmentItem : BaseEntity
{
    public Guid InventoryAdjustmentId { get; private set; }
    public Guid PartId { get; private set; }
    public int PreviousQuantity { get; private set; }
    public int NewQuantity { get; private set; }
    public int AdjustmentQuantity => NewQuantity - PreviousQuantity;
    public string? Notes { get; private set; }

    // Navigation properties
    public InventoryAdjustment InventoryAdjustment { get; private set; } = null!;
    public Part Part { get; private set; } = null!;

    private InventoryAdjustmentItem() { }

    public static InventoryAdjustmentItem Create(
        Guid inventoryAdjustmentId,
        Guid partId,
        int previousQuantity,
        int newQuantity,
        string createdBy,
        string? notes = null)
    {
        return new InventoryAdjustmentItem
        {
            InventoryAdjustmentId = inventoryAdjustmentId,
            PartId = partId,
            PreviousQuantity = previousQuantity,
            NewQuantity = newQuantity,
            Notes = notes?.Trim(),
            CreatedBy = createdBy
        };
    }

    public void UpdateQuantities(int previousQuantity, int newQuantity, string updatedBy)
    {
        PreviousQuantity = previousQuantity;
        NewQuantity = newQuantity;
        SetUpdatedBy(updatedBy);
    }

    public void UpdateNotes(string notes, string updatedBy)
    {
        Notes = notes?.Trim();
        SetUpdatedBy(updatedBy);
    }
}
