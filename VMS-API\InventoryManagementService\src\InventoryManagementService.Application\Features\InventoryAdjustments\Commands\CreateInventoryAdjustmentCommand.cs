using AutoMapper;
using FluentValidation;
using InventoryManagementService.Application.DTOs;
using InventoryManagementService.Domain.Entities;
using InventoryManagementService.Domain.Interfaces;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using VMS.Contracts.Common.Authorization;

namespace InventoryManagementService.Application.Features.InventoryAdjustments.Commands;

public class CreateInventoryAdjustmentCommand : IRequest<InventoryAdjustmentDto>
{
    public CreateInventoryAdjustmentDto Adjustment { get; set; } = null!;
    public string CreatedBy { get; set; } = string.Empty;
}

public class CreateInventoryAdjustmentCommandValidator : AbstractValidator<CreateInventoryAdjustmentCommand>
{
    public CreateInventoryAdjustmentCommandValidator()
    {
        RuleFor(x => x.Adjustment.VendorId)
            .NotEmpty().WithMessage("Vendor ID is required");

        RuleFor(x => x.Adjustment.BranchId)
            .NotEmpty().WithMessage("Branch ID is required");

        RuleFor(x => x.Adjustment.Reason)
            .IsInEnum().WithMessage("Invalid adjustment reason");

        RuleFor(x => x.Adjustment.ReasonDescription)
            .NotEmpty().WithMessage("Reason description is required")
            .MaximumLength(200).WithMessage("Reason description cannot exceed 200 characters");

        RuleFor(x => x.Adjustment.Notes)
            .MaximumLength(1000).WithMessage("Notes cannot exceed 1000 characters");

        RuleFor(x => x.Adjustment.Items)
            .NotEmpty().WithMessage("At least one adjustment item is required");

        RuleForEach(x => x.Adjustment.Items).ChildRules(item =>
        {
            item.RuleFor(x => x.PartId)
                .NotEmpty().WithMessage("Part ID is required");

            item.RuleFor(x => x.PreviousQuantity)
                .GreaterThanOrEqualTo(0).WithMessage("Previous quantity cannot be negative");

            item.RuleFor(x => x.NewQuantity)
                .GreaterThanOrEqualTo(0).WithMessage("New quantity cannot be negative");

            item.RuleFor(x => x.Notes)
                .MaximumLength(500).WithMessage("Item notes cannot exceed 500 characters");
        });

        RuleFor(x => x.CreatedBy)
            .NotEmpty().WithMessage("CreatedBy is required");
    }
}

public class CreateInventoryAdjustmentCommandHandler : IRequestHandler<CreateInventoryAdjustmentCommand, InventoryAdjustmentDto>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly ILogger<CreateInventoryAdjustmentCommandHandler> _logger;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly IEventPublisher _eventPublisher;

    public CreateInventoryAdjustmentCommandHandler(
        IUnitOfWork unitOfWork,
        IMapper mapper,
        ILogger<CreateInventoryAdjustmentCommandHandler> logger,
        IHttpContextAccessor httpContextAccessor,
        IEventPublisher eventPublisher)
    {
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _logger = logger;
        _httpContextAccessor = httpContextAccessor;
        _eventPublisher = eventPublisher;
    }

    public async Task<InventoryAdjustmentDto> Handle(CreateInventoryAdjustmentCommand request, CancellationToken cancellationToken)
    {
        // Check permissions - Admin roles bypass, others need explicit permission
        var user = _httpContextAccessor.HttpContext?.User;
        if (!PermissionHelper.IsAdmin(user))
        {
            PermissionHelper.ValidateAccess(user, "Adjustments.Create");
        }

        _logger.LogInformation("Creating inventory adjustment for branch {BranchId}", request.Adjustment.BranchId);

        // Generate adjustment number
        var adjustmentNumber = await GenerateAdjustmentNumber();

        // Validate that all parts exist
        foreach (var item in request.Adjustment.Items)
        {
            if (!await _unitOfWork.Parts.ExistsAsync(item.PartId))
            {
                throw new KeyNotFoundException($"Part with ID {item.PartId} not found");
            }
        }

        var inventoryAdjustment = InventoryAdjustment.Create(
            request.Adjustment.VendorId,
            request.Adjustment.BranchId,
            adjustmentNumber,
            request.Adjustment.Reason,
            request.Adjustment.ReasonDescription,
            request.CreatedBy,
            request.Adjustment.Notes);

        // Add adjustment items
        foreach (var itemDto in request.Adjustment.Items)
        {
            inventoryAdjustment.AddItem(
                itemDto.PartId,
                itemDto.PreviousQuantity,
                itemDto.NewQuantity,
                request.CreatedBy,
                itemDto.Notes);
        }

        await _unitOfWork.InventoryAdjustments.AddAsync(inventoryAdjustment);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("Inventory adjustment created successfully with ID: {AdjustmentId}", inventoryAdjustment.Id);

        // Publish event
        await _eventPublisher.PublishAdjustmentCreatedEvent(
            inventoryAdjustment.Id,
            inventoryAdjustment.AdjustmentNumber,
            inventoryAdjustment.BranchId);

        return _mapper.Map<InventoryAdjustmentDto>(inventoryAdjustment);
    }

    private async Task<string> GenerateAdjustmentNumber()
    {
        // Generate a unique adjustment number
        var timestamp = DateTime.UtcNow.ToString("yyyyMMddHHmmss");
        var random = new Random().Next(1000, 9999);
        var adjustmentNumber = $"ADJ-{timestamp}-{random}";

        // Check if it already exists (very unlikely but good practice)
        while (await _unitOfWork.InventoryAdjustments.AdjustmentNumberExistsAsync(adjustmentNumber))
        {
            random = new Random().Next(1000, 9999);
            adjustmentNumber = $"ADJ-{timestamp}-{random}";
        }

        return adjustmentNumber;
    }
}
