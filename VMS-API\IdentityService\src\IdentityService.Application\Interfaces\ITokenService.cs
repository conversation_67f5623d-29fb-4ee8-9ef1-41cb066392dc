using System;
using System.Threading.Tasks;
using IdentityService.Domain.Entities;
using System.Collections.Generic;

namespace IdentityService.Application.Interfaces;

public interface ITokenService
{
    Task<(string accessToken, string refreshToken, DateTime accessTokenExpiry, DateTime refreshTokenExpiry)>
        GenerateTokensAsync(User user);

    Task<(string accessToken, string refreshToken, DateTime accessTokenExpiry, DateTime refreshTokenExpiry)>
        RefreshTokensAsync(string accessToken, string refreshToken);

    Task<bool> ValidateTokenAsync(string token);
    Task<Guid> GetUserIdFromTokenAsync(string token);

    string GenerateAccessToken(string userId, string email, IEnumerable<string> roles, IEnumerable<string> permissions = null, Guid? vendorId = null, Guid? branchId = null);
    string GenerateRefreshToken();
    bool ValidateRefreshToken(string refreshToken);
}