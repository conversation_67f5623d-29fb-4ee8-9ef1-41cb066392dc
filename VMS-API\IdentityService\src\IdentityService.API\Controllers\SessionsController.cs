using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using IdentityService.Application.Interfaces;
using IdentityService.Domain.Entities;
using IdentityService.Domain.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace IdentityService.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class SessionsController : ControllerBase
{
    private readonly ISessionManagementService _sessionManagementService;
    private readonly ICurrentUserService _currentUserService;
    private readonly ILogger<SessionsController> _logger;

    public SessionsController(
        ISessionManagementService sessionManagementService,
        ICurrentUserService currentUserService,
        ILogger<SessionsController> logger)
    {
        _sessionManagementService = sessionManagementService;
        _currentUserService = currentUserService;
        _logger = logger;
    }

    [HttpGet("active")]
    public async Task<ActionResult<IEnumerable<UserSession>>> GetActiveSessions()
    {
        try
        {
            var userId = _currentUserService.UserId;
            if (!userId.HasValue)
            {
                return Unauthorized(new { message = "User ID not found in token" });
            }

            var sessions = await _sessionManagementService.GetActiveSessionsForUserAsync(userId.Value);
            return Ok(sessions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving active sessions");
            return StatusCode(500, new { message = "An error occurred while retrieving active sessions" });
        }
    }

    [HttpGet("history")]
    public async Task<ActionResult<IEnumerable<UserSession>>> GetSessionHistory(
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10)
    {
        try
        {
            var userId = _currentUserService.UserId;
            if (!userId.HasValue)
            {
                return Unauthorized(new { message = "User ID not found in token" });
            }

            var sessions = await _sessionManagementService.GetSessionHistoryForUserAsync(
                userId.Value, fromDate, toDate, pageNumber, pageSize);
            return Ok(sessions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving session history");
            return StatusCode(500, new { message = "An error occurred while retrieving session history" });
        }
    }

    [HttpPost("logout")]
    public async Task<ActionResult> Logout()
    {
        try
        {
            var authHeader = HttpContext.Request.Headers["Authorization"].ToString();
            if (string.IsNullOrEmpty(authHeader) || !authHeader.StartsWith("Bearer "))
            {
                return BadRequest(new { message = "Invalid authorization header" });
            }

            var token = authHeader.Substring("Bearer ".Length).Trim();
            await _sessionManagementService.EndSessionAsync(token);

            return Ok(new { message = "Logged out successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during logout");
            return StatusCode(500, new { message = "An error occurred during logout" });
        }
    }

    [HttpPost("logout-all")]
    public async Task<ActionResult> LogoutAll()
    {
        try
        {
            var userId = _currentUserService.UserId;
            if (!userId.HasValue)
            {
                return Unauthorized(new { message = "User ID not found in token" });
            }

            var authHeader = HttpContext.Request.Headers["Authorization"].ToString();
            string currentToken = null;
            if (!string.IsNullOrEmpty(authHeader) && authHeader.StartsWith("Bearer "))
            {
                currentToken = authHeader.Substring("Bearer ".Length).Trim();
            }

            await _sessionManagementService.EndAllSessionsForUserAsync(userId.Value, currentToken);

            return Ok(new { message = "Logged out of all sessions successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during logout from all sessions");
            return StatusCode(500, new { message = "An error occurred during logout from all sessions" });
        }
    }

    [HttpPost("terminate/{sessionId}")]
    [Authorize(Policy = "Users.Update")]
    public async Task<ActionResult> TerminateSession(Guid sessionId, [FromBody] string reason)
    {
        try
        {
            var session = await _sessionManagementService.GetSessionByTokenAsync(sessionId.ToString());
            if (session == null)
            {
                return NotFound(new { message = "Session not found" });
            }

            var userId = _currentUserService.UserId;
            if (!userId.HasValue || session.UserId != userId.Value)
            {
                return Unauthorized(new { message = "You are not authorized to terminate this session" });
            }

            await _sessionManagementService.TerminateSessionAsync(sessionId.ToString(), reason);

            return Ok(new { message = "Session terminated successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error terminating session");
            return StatusCode(500, new { message = "An error occurred while terminating the session" });
        }
    }
}
