using System;
using System.Collections.Generic;
using IdentityService.Domain.Enums;

namespace IdentityService.Application.DTOs.Subscriptions;

public class UserSubscriptionResponse
{
    public Guid Id { get; set; }
    public Guid UserId { get; set; }
    public Guid SubscriptionId { get; set; }
    public string PlanName { get; set; }
    public SubscriptionTier Tier { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public DateTime? TrialEndDate { get; set; }
    public SubscriptionStatus Status { get; set; }
    public bool IsActive { get; set; }
    public List<SubscriptionFeatureResponse> Features { get; set; } = new List<SubscriptionFeatureResponse>();
    public DateTime CreatedAt { get; set; }
    public string CreatedBy { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string UpdatedBy { get; set; }
}

public class SubscriptionFeatureResponse
{
    public Guid Id { get; set; }
    public string FeatureName { get; set; }
    public bool IsEnabled { get; set; }
    public int? UsageLimit { get; set; }
}
