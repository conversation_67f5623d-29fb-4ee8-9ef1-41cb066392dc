using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SubscriptionService.Application.DTOs;
using SubscriptionService.Application.Features.SubscriptionAccess.Commands;
using SubscriptionService.Application.Features.SubscriptionAccess.Queries;
using SubscriptionService.Application.Interfaces;

namespace SubscriptionService.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class SubscriptionAccessController : ControllerBase
    {
        private readonly IMediator _mediator;
        private readonly ISubscriptionAccessService _subscriptionAccessService;
        private readonly ILogger<SubscriptionAccessController> _logger;

        public SubscriptionAccessController(
            IMediator mediator,
            ISubscriptionAccessService subscriptionAccessService,
            ILogger<SubscriptionAccessController> logger)
        {
            _mediator = mediator;
            _subscriptionAccessService = subscriptionAccessService;
            _logger = logger;
        }

        /// <summary>
        /// Configure subscription access for a plan (Admin only)
        /// </summary>
        [HttpPost("configure")]
        [Authorize(Policy = "AdminOnly")]
        public async Task<ActionResult<SubscriptionAccessConfigurationDto>> ConfigureSubscriptionAccess(
            [FromBody] ConfigureSubscriptionAccessRequest request)
        {
            try
            {
                var command = new ConfigureSubscriptionAccessCommand { Request = request };
                var result = await _mediator.Send(command);
                return Ok(result);
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Invalid request for configuring subscription access");
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error configuring subscription access for plan {PlanId}", request.SubscriptionPlanId);
                return StatusCode(500, "An error occurred while configuring subscription access");
            }
        }

        /// <summary>
        /// Update subscription access for a plan (Admin only)
        /// </summary>
        [HttpPut("plan/{planId}")]
        [Authorize(Policy = "AdminOnly")]
        public async Task<ActionResult<SubscriptionAccessConfigurationDto>> UpdateSubscriptionAccess(
            Guid planId,
            [FromBody] UpdateSubscriptionAccessRequest request)
        {
            try
            {
                var command = new UpdateSubscriptionAccessCommand
                {
                    SubscriptionPlanId = planId,
                    MenuAccesses = request.MenuAccesses,
                    PermissionAccesses = request.PermissionAccesses
                };
                var result = await _mediator.Send(command);
                return Ok(result);
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Invalid request for updating subscription access");
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating subscription access for plan {PlanId}", planId);
                return StatusCode(500, "An error occurred while updating subscription access");
            }
        }

        /// <summary>
        /// Get subscription access configuration for a plan (Admin only)
        /// </summary>
        [HttpGet("plan/{planId}")]
        [Authorize(Policy = "AdminOnly")]
        public async Task<ActionResult<SubscriptionAccessConfigurationDto>> GetSubscriptionAccess(Guid planId)
        {
            try
            {
                var query = new GetSubscriptionAccessQuery { SubscriptionPlanId = planId };
                var result = await _mediator.Send(query);
                return Ok(result);
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Subscription plan not found: {PlanId}", planId);
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting subscription access for plan {PlanId}", planId);
                return StatusCode(500, "An error occurred while retrieving subscription access");
            }
        }

        /// <summary>
        /// Get user's subscription access based on vendor ID
        /// </summary>
        [HttpGet("user/{vendorId}")]
        public async Task<ActionResult<UserSubscriptionAccessDto>> GetUserSubscriptionAccess(Guid vendorId)
        {
            try
            {
                var query = new GetUserSubscriptionAccessQuery { VendorId = vendorId };
                var result = await _mediator.Send(query);
                
                if (result == null)
                {
                    return NotFound($"No active subscription found for vendor {vendorId}");
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user subscription access for vendor {VendorId}", vendorId);
                return StatusCode(500, "An error occurred while retrieving user subscription access");
            }
        }

        /// <summary>
        /// Get menu visibility for frontend rendering
        /// </summary>
        [HttpGet("menu-visibility/{vendorId}")]
        public async Task<ActionResult<List<MenuVisibilityDto>>> GetMenuVisibility(
            Guid vendorId,
            [FromQuery] bool includeChildren = true,
            [FromQuery] bool includeHiddenMenus = false)
        {
            try
            {
                var query = new GetMenuVisibilityQuery 
                { 
                    VendorId = vendorId,
                    IncludeChildren = includeChildren,
                    IncludeHiddenMenus = includeHiddenMenus
                };
                var result = await _mediator.Send(query);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting menu visibility for vendor {VendorId}", vendorId);
                return StatusCode(500, "An error occurred while retrieving menu visibility");
            }
        }

        /// <summary>
        /// Check if a specific menu is visible for a user
        /// </summary>
        [HttpGet("menu-visible/{vendorId}/{menuId}")]
        public async Task<ActionResult<bool>> IsMenuVisible(Guid vendorId, Guid menuId)
        {
            try
            {
                var result = await _subscriptionAccessService.IsMenuVisibleAsync(vendorId, menuId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking menu visibility for vendor {VendorId}, menu {MenuId}", vendorId, menuId);
                return StatusCode(500, "An error occurred while checking menu visibility");
            }
        }

        /// <summary>
        /// Check if a specific menu is editable for a user
        /// </summary>
        [HttpGet("menu-editable/{vendorId}/{menuId}")]
        public async Task<ActionResult<bool>> IsMenuEditable(Guid vendorId, Guid menuId)
        {
            try
            {
                var result = await _subscriptionAccessService.IsMenuEditableAsync(vendorId, menuId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking menu editability for vendor {VendorId}, menu {MenuId}", vendorId, menuId);
                return StatusCode(500, "An error occurred while checking menu editability");
            }
        }

        /// <summary>
        /// Check if a user has a specific permission
        /// </summary>
        [HttpGet("has-permission/{vendorId}")]
        public async Task<ActionResult<bool>> HasPermission(
            Guid vendorId,
            [FromQuery] string resource,
            [FromQuery] string action)
        {
            try
            {
                if (string.IsNullOrEmpty(resource) || string.IsNullOrEmpty(action))
                {
                    return BadRequest("Resource and action parameters are required");
                }

                var result = await _subscriptionAccessService.HasPermissionAsync(vendorId, resource, action);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking permission for vendor {VendorId}, resource {Resource}, action {Action}", 
                    vendorId, resource, action);
                return StatusCode(500, "An error occurred while checking permission");
            }
        }

        /// <summary>
        /// Check if user's subscription is in trial mode
        /// </summary>
        [HttpGet("is-trial/{vendorId}")]
        public async Task<ActionResult<bool>> IsTrialSubscription(Guid vendorId)
        {
            try
            {
                var result = await _subscriptionAccessService.IsTrialSubscriptionAsync(vendorId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking trial status for vendor {VendorId}", vendorId);
                return StatusCode(500, "An error occurred while checking trial status");
            }
        }

        /// <summary>
        /// Get trial end date for a trial subscription
        /// </summary>
        [HttpGet("trial-end-date/{vendorId}")]
        public async Task<ActionResult<DateTime?>> GetTrialEndDate(Guid vendorId)
        {
            try
            {
                var result = await _subscriptionAccessService.GetTrialEndDateAsync(vendorId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting trial end date for vendor {VendorId}", vendorId);
                return StatusCode(500, "An error occurred while retrieving trial end date");
            }
        }
    }

    public class UpdateSubscriptionAccessRequest
    {
        public List<MenuAccessRequest> MenuAccesses { get; set; } = new();
        public List<PermissionAccessRequest> PermissionAccesses { get; set; } = new();
    }
}
