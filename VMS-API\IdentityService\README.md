# Identity Service

This is the Identity Service microservice for the Vehicle Management System. It handles user authentication, authorization, and identity management.

## Features

- Email & Password Authentication
- Mobile Number & OTP Authentication
- Forgot Password with Email Reset Link
- Remember Me Feature
- Password Management (Set, Change, Reset)
- Role-Based Access Control (RBAC)
- JWT Token Authentication
- Refresh Token Support

## Prerequisites

- .NET 8.0 SDK
- Docker and Docker Compose
- PostgreSQL 16
- RabbitMQ
- Node.js (for development)

## Getting Started

1. Clone the repository
2. Navigate to the IdentityService directory
3. Update the configuration in `src/IdentityService.API/appsettings.json`
4. Run the following commands:

```bash
# Build and run using Docker Compose
docker-compose up --build

# Or run locally
cd src/IdentityService.API
dotnet restore
dotnet run
```

The service will be available at:
- API: http://localhost:5001
- Swagger UI: http://localhost:5001/swagger
- RabbitMQ Management: http://localhost:15672
- Prometheus: http://localhost:9090
- Grafana: http://localhost:3000

## API Endpoints

### Authentication

- POST /api/auth/login - Login with email/password or phone/OTP
- POST /api/auth/refresh-token - Refresh access token
- POST /api/auth/forgot-password - Request password reset
- POST /api/auth/reset-password - Reset password with token
- POST /api/auth/change-password - Change password (requires authentication)

### User Management

- GET /api/users/me - Get current user profile
- PUT /api/users/me - Update current user profile
- POST /api/users/verify-email - Verify email address
- POST /api/users/verify-phone - Verify phone number

## Development

### Project Structure

```
IdentityService/
├── src/
│   ├── IdentityService.API/           # API layer
│   ├── IdentityService.Application/   # Application layer
│   ├── IdentityService.Domain/        # Domain layer
│   ├── IdentityService.Infrastructure/# Infrastructure layer
│   └── IdentityService.Shared/        # Shared components
├── tests/                            # Test projects
└── docker/                           # Docker configuration
```

### Running Tests

```bash
dotnet test
```

### Database Migrations

```bash
cd src/IdentityService.Infrastructure
dotnet ef migrations add InitialCreate
dotnet ef database update
```

## Monitoring

The service includes:
- Prometheus metrics endpoint
- Grafana dashboards
- Serilog logging
- Health checks

## Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## License

This project is licensed under the MIT License. 