using System.Reflection;
using IdentityService.Application.Interfaces;
using IdentityService.Infrastructure.Messaging.Consumers;
using MassTransit;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace IdentityService.Infrastructure.Messaging;

/// <summary>
/// Configuration for MassTransit
/// </summary>
public static class MassTransitConfiguration
{
    /// <summary>
    /// Adds MassTransit with RabbitMQ to the service collection
    /// </summary>
    public static IServiceCollection AddMassTransitWithRabbitMq(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        services.AddMassTransit(config =>
        {
            // Register consumers
            config.AddConsumer<SubscriptionCreatedConsumer>();
            config.AddConsumer<SubscriptionStatusChangedConsumer>();
            config.AddConsumer<VendorRegisteredConsumer>();

            // Configure RabbitMQ
            config.UsingRabbitMq((context, cfg) =>
            {
                var rabbitMqConfig = configuration.GetSection("RabbitMQ");
                var host = rabbitMqConfig["HostName"] ?? "localhost";
                var username = rabbitMqConfig["UserName"] ?? "guest";
                var password = rabbitMqConfig["Password"] ?? "guest";
                var virtualHost = rabbitMqConfig["VirtualHost"] ?? "/";

                cfg.Host(host, virtualHost, h =>
                {
                    h.Username(username);
                    h.Password(password);
                });

                // Configure retry policy
                cfg.UseMessageRetry(r =>
                {
                    r.Interval(3, TimeSpan.FromSeconds(5));
                    r.Ignore<InvalidOperationException>(); // Ignore business logic exceptions
                });

                // Configure error handling
                cfg.UseMessageRetry(r =>
                {
                    r.Exponential(
                        retryLimit: 3,
                        minInterval: TimeSpan.FromSeconds(1),
                        maxInterval: TimeSpan.FromSeconds(10),
                        intervalDelta: TimeSpan.FromSeconds(2));
                });
                cfg.ReceiveEndpoint("VendorRegistered", e =>
                   {
                       e.ConfigureConsumer<VendorRegisteredConsumer>(context);
                   });
                // Configure consumer endpoints
                cfg.ConfigureEndpoints(context);
            });
        });

        // Register the event publisher
        services.AddScoped<IEventPublisher, EventPublisher>();

        return services;
    }
}
