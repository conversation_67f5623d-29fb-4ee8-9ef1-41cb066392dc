using InventoryManagementService.Application.DTOs;
using InventoryManagementService.Application.Features.StockTransfers.Commands;
using InventoryManagementService.Application.Features.StockTransfers.Queries;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace InventoryManagementService.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class StockTransfersController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<StockTransfersController> _logger;

    public StockTransfersController(IMediator mediator, ILogger<StockTransfersController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Get stock transfer by ID
    /// </summary>
    [HttpGet("{id}")]
    [Authorize(Policy = "Transfers.View")]
    public async Task<ActionResult<StockTransferDto>> GetStockTransfer(Guid id)
    {
        try
        {
            var query = new GetStockTransferByIdQuery { TransferId = id };
            var transfer = await _mediator.Send(query);

            if (transfer == null)
            {
                return NotFound($"Stock transfer with ID {id} not found");
            }

            return Ok(transfer);
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access attempt to get stock transfer {TransferId}", id);
            return Forbid(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving stock transfer {TransferId}", id);
            return StatusCode(500, new { message = "An error occurred while retrieving the stock transfer" });
        }
    }

    /// <summary>
    /// Get outgoing transfers for a branch
    /// </summary>
    [HttpGet("branch/{branchId}/outgoing")]
    [Authorize(Policy = "Transfers.View")]
    public async Task<ActionResult<List<StockTransferDto>>> GetOutgoingTransfers(Guid branchId)
    {
        try
        {
            var query = new GetOutgoingTransfersQuery { SourceBranchId = branchId };
            var transfers = await _mediator.Send(query);
            return Ok(transfers);
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access attempt to get outgoing transfers for branch {BranchId}", branchId);
            return Forbid(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving outgoing transfers for branch {BranchId}", branchId);
            return StatusCode(500, new { message = "An error occurred while retrieving outgoing transfers" });
        }
    }

    /// <summary>
    /// Get incoming transfers for a branch
    /// </summary>
    [HttpGet("branch/{branchId}/incoming")]
    [Authorize(Policy = "Transfers.View")]
    public async Task<ActionResult<List<StockTransferDto>>> GetIncomingTransfers(Guid branchId)
    {
        try
        {
            var query = new GetIncomingTransfersQuery { DestinationBranchId = branchId };
            var transfers = await _mediator.Send(query);
            return Ok(transfers);
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access attempt to get incoming transfers for branch {BranchId}", branchId);
            return Forbid(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving incoming transfers for branch {BranchId}", branchId);
            return StatusCode(500, new { message = "An error occurred while retrieving incoming transfers" });
        }
    }

    /// <summary>
    /// Get pending transfers
    /// </summary>
    [HttpGet("pending")]
    [Authorize(Policy = "Transfers.View")]
    public async Task<ActionResult<List<StockTransferDto>>> GetPendingTransfers()
    {
        try
        {
            var query = new GetPendingTransfersQuery();
            var transfers = await _mediator.Send(query);
            return Ok(transfers);
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access attempt to get pending transfers");
            return Forbid(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving pending transfers");
            return StatusCode(500, new { message = "An error occurred while retrieving pending transfers" });
        }
    }

    /// <summary>
    /// Create a new stock transfer
    /// </summary>
    [HttpPost]
    [Authorize(Policy = "Transfers.Create")]
    public async Task<ActionResult<StockTransferDto>> CreateStockTransfer([FromBody] CreateStockTransferDto createTransferDto)
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "System";
            
            var command = new CreateStockTransferCommand
            {
                Transfer = createTransferDto,
                CreatedBy = userId
            };

            var transfer = await _mediator.Send(command);
            return CreatedAtAction(nameof(GetStockTransfer), new { id = transfer.Id }, transfer);
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access attempt to create stock transfer");
            return Forbid(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation during stock transfer creation");
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating stock transfer");
            return StatusCode(500, new { message = "An error occurred while creating the stock transfer" });
        }
    }

    /// <summary>
    /// Add item to stock transfer
    /// </summary>
    [HttpPost("{id}/items")]
    [Authorize(Policy = "Transfers.Update")]
    public async Task<ActionResult> AddTransferItem(Guid id, [FromBody] AddTransferItemDto addItemDto)
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "System";
            
            var command = new AddTransferItemCommand
            {
                TransferId = id,
                PartId = addItemDto.PartId,
                Quantity = addItemDto.Quantity,
                CreatedBy = userId
            };

            await _mediator.Send(command);
            return NoContent();
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access attempt to add item to transfer {TransferId}", id);
            return Forbid(ex.Message);
        }
        catch (KeyNotFoundException ex)
        {
            _logger.LogWarning(ex, "Transfer not found for adding item: {TransferId}", id);
            return NotFound(new { message = ex.Message });
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation during item addition to transfer {TransferId}", id);
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding item to transfer {TransferId}", id);
            return StatusCode(500, new { message = "An error occurred while adding item to transfer" });
        }
    }

    /// <summary>
    /// Ship stock transfer
    /// </summary>
    [HttpPost("{id}/ship")]
    [Authorize(Policy = "Transfers.Update")]
    public async Task<ActionResult> ShipTransfer(Guid id, [FromBody] ShipTransferDto shipTransferDto)
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "System";
            
            var command = new ShipTransferCommand
            {
                TransferId = id,
                Items = shipTransferDto.Items,
                ShippedBy = userId
            };

            await _mediator.Send(command);
            return NoContent();
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access attempt to ship transfer {TransferId}", id);
            return Forbid(ex.Message);
        }
        catch (KeyNotFoundException ex)
        {
            _logger.LogWarning(ex, "Transfer not found for shipping: {TransferId}", id);
            return NotFound(new { message = ex.Message });
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation during transfer shipping {TransferId}", id);
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error shipping transfer {TransferId}", id);
            return StatusCode(500, new { message = "An error occurred while shipping the transfer" });
        }
    }

    /// <summary>
    /// Complete stock transfer
    /// </summary>
    [HttpPost("{id}/complete")]
    [Authorize(Policy = "Transfers.Update")]
    public async Task<ActionResult> CompleteTransfer(Guid id, [FromBody] CompleteTransferDto completeTransferDto)
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "System";
            
            var command = new CompleteTransferCommand
            {
                TransferId = id,
                Items = completeTransferDto.Items,
                ReceivedBy = userId
            };

            await _mediator.Send(command);
            return NoContent();
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access attempt to complete transfer {TransferId}", id);
            return Forbid(ex.Message);
        }
        catch (KeyNotFoundException ex)
        {
            _logger.LogWarning(ex, "Transfer not found for completion: {TransferId}", id);
            return NotFound(new { message = ex.Message });
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation during transfer completion {TransferId}", id);
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error completing transfer {TransferId}", id);
            return StatusCode(500, new { message = "An error occurred while completing the transfer" });
        }
    }

    /// <summary>
    /// Cancel stock transfer
    /// </summary>
    [HttpPost("{id}/cancel")]
    [Authorize(Policy = "Transfers.Update")]
    public async Task<ActionResult> CancelTransfer(Guid id, [FromBody] CancelTransferDto cancelTransferDto)
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "System";
            
            var command = new CancelTransferCommand
            {
                TransferId = id,
                Reason = cancelTransferDto.Reason,
                CancelledBy = userId
            };

            await _mediator.Send(command);
            return NoContent();
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access attempt to cancel transfer {TransferId}", id);
            return Forbid(ex.Message);
        }
        catch (KeyNotFoundException ex)
        {
            _logger.LogWarning(ex, "Transfer not found for cancellation: {TransferId}", id);
            return NotFound(new { message = ex.Message });
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation during transfer cancellation {TransferId}", id);
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cancelling transfer {TransferId}", id);
            return StatusCode(500, new { message = "An error occurred while cancelling the transfer" });
        }
    }
}
