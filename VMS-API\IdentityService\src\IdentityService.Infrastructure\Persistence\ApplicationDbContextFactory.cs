using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using Microsoft.Extensions.Configuration;
using System;
using System.IO;
using IdentityService.Domain.Interfaces;

namespace IdentityService.Infrastructure.Persistence
{
    // Mock implementation of ICurrentUserService for design-time factory
    public class DesignTimeCurrentUserService : ICurrentUserService
    {
        public Guid? UserId => null;
        public string? UserName => "System";
    }

    // Mock implementation of IDateTime for design-time factory
    public class DesignTimeDateTime : IDateTime
    {
        public DateTime Now => DateTime.UtcNow;
    }

    public class ApplicationDbContextFactory : IDesignTimeDbContextFactory<ApplicationDbContext>
    {
        public ApplicationDbContext CreateDbContext(string[] args)
        {
            // Build configuration from appsettings.json in the API project
            var configurationBuilder = new ConfigurationBuilder()
                .SetBasePath(Path.Combine(Directory.GetCurrentDirectory(), "../IdentityService.API"))
                .AddJsonFile("appsettings.json", optional: false)
                .AddJsonFile("appsettings.Development.json", optional: true);

            var configuration = configurationBuilder.Build();
            var connectionString = configuration.GetConnectionString("DefaultConnection");

            var optionsBuilder = new DbContextOptionsBuilder<ApplicationDbContext>();

            // Using PostgreSQL as per the connection string
            optionsBuilder.UseNpgsql(connectionString);

            // Create mock services for design-time factory
            var currentUserService = new DesignTimeCurrentUserService();
            var dateTimeService = new DesignTimeDateTime();

            return new ApplicationDbContext(
                optionsBuilder.Options,
                currentUserService,
                dateTimeService);
        }
    }
}
