using FluentValidation;
using InventoryManagementService.Domain.Interfaces;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using VMS.Contracts.Common.Authorization;

namespace InventoryManagementService.Application.Features.Parts.Commands;

public class RemovePartCompatibilityCommand : IRequest
{
    public Guid PartId { get; set; }
    public Guid CompatibilityId { get; set; }
}

public class RemovePartCompatibilityCommandValidator : AbstractValidator<RemovePartCompatibilityCommand>
{
    public RemovePartCompatibilityCommandValidator()
    {
        RuleFor(x => x.PartId)
            .NotEmpty().WithMessage("Part ID is required");

        RuleFor(x => x.CompatibilityId)
            .NotEmpty().WithMessage("Compatibility ID is required");
    }
}

public class RemovePartCompatibilityCommandHandler : IRequestHandler<RemovePartCompatibilityCommand>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<RemovePartCompatibilityCommandHandler> _logger;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public RemovePartCompatibilityCommandHandler(
        IUnitOfWork unitOfWork,
        ILogger<RemovePartCompatibilityCommandHandler> logger,
        IHttpContextAccessor httpContextAccessor)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task Handle(RemovePartCompatibilityCommand request, CancellationToken cancellationToken)
    {
        // Check permissions - Admin roles bypass, others need explicit permission
        var user = _httpContextAccessor.HttpContext?.User;
        if (!PermissionHelper.IsAdmin(user))
        {
            PermissionHelper.ValidateAccess(user, "Parts.Update");
        }

        _logger.LogInformation("Removing compatibility {CompatibilityId} from part with ID: {PartId}", 
            request.CompatibilityId, request.PartId);

        var part = await _unitOfWork.Parts.GetByIdAsync(request.PartId);

        if (part == null)
        {
            throw new KeyNotFoundException($"Part with ID {request.PartId} not found");
        }

        var compatibility = part.Compatibilities.FirstOrDefault(c => c.Id == request.CompatibilityId);
        if (compatibility == null)
        {
            throw new KeyNotFoundException($"Compatibility with ID {request.CompatibilityId} not found for part {request.PartId}");
        }

        part.RemoveCompatibility(request.CompatibilityId);

        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("Compatibility removed successfully from part with ID: {PartId}", part.Id);
    }
}
