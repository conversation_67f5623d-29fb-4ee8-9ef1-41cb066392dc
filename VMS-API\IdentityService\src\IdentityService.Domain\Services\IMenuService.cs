using IdentityService.Domain.Entities;

namespace IdentityService.Domain.Services;

/// <summary>
/// Service for dynamic menu rendering based on user permissions
/// </summary>
public interface IMenuService
{
    /// <summary>
    /// Gets the menu structure for a user based on their permissions
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <returns>Hierarchical menu structure</returns>
    Task<List<MenuItemDto>> GetUserMenuAsync(Guid userId);

    /// <summary>
    /// Gets all available menus (admin view)
    /// </summary>
    /// <returns>All menu items</returns>
    Task<List<MenuItemDto>> GetAllMenusAsync();

    /// <summary>
    /// Creates a new menu item
    /// </summary>
    /// <param name="menuDto">Menu data</param>
    /// <param name="createdBy">Who created the menu</param>
    /// <returns>Created menu</returns>
    Task<MenuItemDto> CreateMenuAsync(CreateMenuDto menuDto, string createdBy);

    /// <summary>
    /// Updates an existing menu item
    /// </summary>
    /// <param name="menuId">Menu ID</param>
    /// <param name="menuDto">Updated menu data</param>
    /// <param name="updatedBy">Who updated the menu</param>
    /// <returns>Updated menu</returns>
    Task<MenuItemDto> UpdateMenuAsync(Guid menuId, UpdateMenuDto menuDto, string updatedBy);

    /// <summary>
    /// Assigns permissions to a menu item
    /// </summary>
    /// <param name="menuId">Menu ID</param>
    /// <param name="permissionIds">Permission IDs to assign</param>
    /// <param name="assignedBy">Who assigned the permissions</param>
    Task AssignMenuPermissionsAsync(Guid menuId, List<Guid> permissionIds, string assignedBy);

    /// <summary>
    /// Checks if a user has access to a specific menu item
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="menuId">Menu ID</param>
    /// <returns>True if user has access</returns>
    Task<bool> HasMenuAccessAsync(Guid userId, Guid menuId);

    /// <summary>
    /// Gets menu breadcrumb for a given path
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="path">Current path</param>
    /// <returns>Breadcrumb items</returns>
    Task<List<BreadcrumbItem>> GetBreadcrumbAsync(Guid userId, string path);

    /// <summary>
    /// Gets admin menu structure (all menus regardless of permissions)
    /// </summary>
    /// <returns>All menu items for admin view</returns>
    Task<List<MenuItemDto>> GetAdminMenuAsync();

    /// <summary>
    /// Generates breadcrumbs for a given path
    /// </summary>
    /// <param name="currentPath">Current path</param>
    /// <param name="userId">User ID</param>
    /// <returns>Breadcrumb items</returns>
    Task<List<BreadcrumbItem>> GenerateBreadcrumbsAsync(string currentPath, Guid userId);
}

/// <summary>
/// DTO for menu item display
/// </summary>
public class MenuItemDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Path { get; set; } = string.Empty;
    public string Icon { get; set; } = string.Empty;
    public int Order { get; set; }
    public Guid? ParentId { get; set; }
    public bool IsVisible { get; set; }
    public bool HasAccess { get; set; }
    public List<MenuItemDto> Children { get; set; } = new();
    public List<string> RequiredPermissions { get; set; } = new();
}

/// <summary>
/// DTO for creating a menu
/// </summary>
public class CreateMenuDto
{
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Path { get; set; } = string.Empty;
    public string Icon { get; set; } = string.Empty;
    public int Order { get; set; }
    public Guid? ParentId { get; set; }
    public List<Guid> PermissionIds { get; set; } = new();
}

/// <summary>
/// DTO for updating a menu
/// </summary>
public class UpdateMenuDto
{
    public string DisplayName { get; set; } = string.Empty;
    public string Path { get; set; } = string.Empty;
    public string Icon { get; set; } = string.Empty;
    public int Order { get; set; }
    public Guid? ParentId { get; set; }
}

/// <summary>
/// Breadcrumb item for navigation
/// </summary>
public class BreadcrumbItem
{
    public string DisplayName { get; set; } = string.Empty;
    public string Path { get; set; } = string.Empty;
    public bool IsActive { get; set; }
}
