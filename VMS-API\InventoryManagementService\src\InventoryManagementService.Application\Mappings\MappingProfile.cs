using AutoMapper;
using InventoryManagementService.Application.DTOs;
using InventoryManagementService.Domain.Entities;

namespace InventoryManagementService.Application.Mappings;

public class MappingProfile : Profile
{
    public MappingProfile()
    {
        // Part mappings
        CreateMap<Part, PartDto>()
            .ForMember(dest => dest.Compatibilities, opt => opt.MapFrom(src => src.Compatibilities));
        CreateMap<PartCompatibility, PartCompatibilityDto>();

        // Inventory mappings
        CreateMap<InventoryItem, InventoryItemDto>()
            .ForMember(dest => dest.IsLowStock, opt => opt.MapFrom(src => src.IsLowStock))
            .ForMember(dest => dest.IsOutOfStock, opt => opt.MapFrom(src => src.IsOutOfStock))
            .ForMember(dest => dest.Part, opt => opt.MapFrom(src => src.Part));
        
        CreateMap<InventoryTransaction, InventoryTransactionDto>();

        // Stock Transfer mappings
        CreateMap<StockTransfer, StockTransferDto>()
            .ForMember(dest => dest.Items, opt => opt.MapFrom(src => src.Items));
        
        CreateMap<StockTransferItem, StockTransferItemDto>()
            .ForMember(dest => dest.Part, opt => opt.MapFrom(src => src.Part));

        // Inventory Adjustment mappings
        CreateMap<InventoryAdjustment, InventoryAdjustmentDto>()
            .ForMember(dest => dest.Items, opt => opt.MapFrom(src => src.Items));
        
        CreateMap<InventoryAdjustmentItem, InventoryAdjustmentItemDto>()
            .ForMember(dest => dest.AdjustmentQuantity, opt => opt.MapFrom(src => src.AdjustmentQuantity))
            .ForMember(dest => dest.Part, opt => opt.MapFrom(src => src.Part));
    }
}
