using FluentValidation;
using InventoryManagementService.Domain.Interfaces;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using VMS.Contracts.Common.Authorization;

namespace InventoryManagementService.Application.Features.Inventory.Commands;

public class RemoveStockCommand : IRequest
{
    public Guid InventoryItemId { get; set; }
    public int Quantity { get; set; }
    public string Reason { get; set; } = string.Empty;
    public string UpdatedBy { get; set; } = string.Empty;
}

public class RemoveStockCommandValidator : AbstractValidator<RemoveStockCommand>
{
    public RemoveStockCommandValidator()
    {
        RuleFor(x => x.InventoryItemId)
            .NotEmpty().WithMessage("Inventory item ID is required");

        RuleFor(x => x.Quantity)
            .GreaterThan(0).WithMessage("Quantity must be greater than 0");

        RuleFor(x => x.Reason)
            .NotEmpty().WithMessage("Reason is required")
            .MaximumLength(200).WithMessage("Reason cannot exceed 200 characters");

        RuleFor(x => x.UpdatedBy)
            .NotEmpty().WithMessage("UpdatedBy is required");
    }
}

public class RemoveStockCommandHandler : IRequestHandler<RemoveStockCommand>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<RemoveStockCommandHandler> _logger;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly IEventPublisher _eventPublisher;

    public RemoveStockCommandHandler(
        IUnitOfWork unitOfWork,
        ILogger<RemoveStockCommandHandler> logger,
        IHttpContextAccessor httpContextAccessor,
        IEventPublisher eventPublisher)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
        _httpContextAccessor = httpContextAccessor;
        _eventPublisher = eventPublisher;
    }

    public async Task Handle(RemoveStockCommand request, CancellationToken cancellationToken)
    {
        // Check permissions - Admin roles bypass, others need explicit permission
        var user = _httpContextAccessor.HttpContext?.User;
        if (!PermissionHelper.IsAdmin(user))
        {
            PermissionHelper.ValidateAccess(user, "Inventory.Update");
        }

        _logger.LogInformation("Removing {Quantity} stock from inventory item: {InventoryItemId}", 
            request.Quantity, request.InventoryItemId);

        var inventoryItem = await _unitOfWork.Inventory.GetByIdAsync(request.InventoryItemId);

        if (inventoryItem == null)
        {
            throw new KeyNotFoundException($"Inventory item with ID {request.InventoryItemId} not found");
        }

        var previousStock = inventoryItem.CurrentStock;
        var success = inventoryItem.RemoveStock(request.Quantity, request.UpdatedBy, request.Reason);

        if (!success)
        {
            throw new InvalidOperationException($"Insufficient stock. Available: {inventoryItem.CurrentStock}, Requested: {request.Quantity}");
        }

        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("Stock removed successfully from inventory item {InventoryItemId}. Previous: {PreviousStock}, New: {NewStock}", 
            inventoryItem.Id, previousStock, inventoryItem.CurrentStock);

        // Publish stock updated event
        await _eventPublisher.PublishStockUpdatedEvent(
            inventoryItem.Id,
            inventoryItem.PartId,
            inventoryItem.BranchId,
            previousStock,
            inventoryItem.CurrentStock,
            request.Reason);

        // Check if item is now low stock and publish alert if needed
        if (inventoryItem.IsLowStock)
        {
            await _eventPublisher.PublishLowStockAlertEvent(
                inventoryItem.Id,
                inventoryItem.PartId,
                inventoryItem.BranchId,
                inventoryItem.CurrentStock,
                inventoryItem.ReorderLevel);
        }
    }
}
