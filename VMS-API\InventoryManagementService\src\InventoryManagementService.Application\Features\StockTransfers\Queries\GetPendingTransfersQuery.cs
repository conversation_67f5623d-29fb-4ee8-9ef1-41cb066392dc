using AutoMapper;
using InventoryManagementService.Application.DTOs;
using InventoryManagementService.Domain.Interfaces;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using VMS.Contracts.Common.Authorization;

namespace InventoryManagementService.Application.Features.StockTransfers.Queries;

public class GetPendingTransfersQuery : IRequest<List<StockTransferDto>>
{
}

public class GetPendingTransfersQueryHandler : IRequestHandler<GetPendingTransfersQuery, List<StockTransferDto>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly ILogger<GetPendingTransfersQueryHandler> _logger;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public GetPendingTransfersQueryHandler(
        IUnitOfWork unitOfWork,
        IMapper mapper,
        ILogger<GetPendingTransfersQueryHandler> logger,
        IHttpContextAccessor httpContextAccessor)
    {
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _logger = logger;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<List<StockTransferDto>> Handle(GetPendingTransfersQuery request, CancellationToken cancellationToken)
    {
        // Check permissions - Admin roles bypass, others need explicit permission
        var user = _httpContextAccessor.HttpContext?.User;
        if (!PermissionHelper.IsAdmin(user))
        {
            PermissionHelper.ValidateAccess(user, "Transfers.View");
        }

        _logger.LogInformation("Getting pending transfers");

        var transfers = await _unitOfWork.StockTransfers.GetPendingTransfersAsync();

        _logger.LogInformation("Found {Count} pending transfers", transfers.Count);

        return _mapper.Map<List<StockTransferDto>>(transfers);
    }
}
