using System;
using FluentValidation;
using IdentityService.Application.Interfaces;

namespace IdentityService.Application.Validators;

public abstract class BaseValidator<T> : AbstractValidator<T>
{
    protected readonly IPasswordComplexityService _passwordComplexityService;

    protected BaseValidator(IPasswordComplexityService passwordComplexityService = null)
    {
        _passwordComplexityService = passwordComplexityService;
    }

    protected void ValidateRequiredString(IRuleBuilder<T, string> ruleBuilder, string propertyName)
    {
        ruleBuilder
            .NotEmpty().WithMessage($"{propertyName} is required.")
            .NotNull().WithMessage($"{propertyName} cannot be null.");
    }

    protected void ValidateRequiredGuid(IRuleBuilder<T, Guid> ruleBuilder, string propertyName)
    {
        ruleBuilder
            .NotEmpty().WithMessage($"{propertyName} is required.")
            .NotEqual(Guid.Empty).WithMessage($"{propertyName} cannot be empty.");
    }

    protected void ValidateRequiredDateTime(IRuleBuilder<T, DateTime> ruleBuilder, string propertyName)
    {
        ruleBuilder
            .NotEmpty().WithMessage($"{propertyName} is required.")
            .Must(date => date != default).WithMessage($"{propertyName} cannot be default.");
    }

    protected void ValidateEmail(IRuleBuilder<T, string> ruleBuilder)
    {
        ruleBuilder
            .NotEmpty().WithMessage("Email is required.")
            .EmailAddress().WithMessage("A valid email address is required.");
    }

    protected void ValidatePhoneNumber(IRuleBuilder<T, string> ruleBuilder)
    {
        ruleBuilder
            .NotEmpty().WithMessage("Phone number is required.")
            .Matches(@"^\+?[0-9\s\-\(\)]+$").WithMessage("A valid phone number is required.");
    }

    protected void ValidatePassword(IRuleBuilder<T, string> ruleBuilder, string usernamePropertyName = null)
    {
        ruleBuilder
            .NotEmpty().WithMessage("Password is required.");

        if (_passwordComplexityService != null)
        {
            // Use the password complexity service for validation
            ruleBuilder.Custom((password, context) =>
            {
                string username = null;
                if (!string.IsNullOrEmpty(usernamePropertyName))
                {
                    // Try to get the username property value
                    var instanceToValidate = context.InstanceToValidate;
                    if (instanceToValidate != null)
                    {
                        var usernameProperty = instanceToValidate.GetType().GetProperty(usernamePropertyName);
                        if (usernameProperty != null)
                        {
                            username = usernameProperty.GetValue(instanceToValidate) as string;
                        }
                    }
                }

                if (!string.IsNullOrEmpty(password))
                {
                    var (isValid, errors) = _passwordComplexityService.ValidatePassword(password, username);
                    if (!isValid)
                    {
                        foreach (var error in errors)
                        {
                            context.AddFailure(error);
                        }
                    }
                }
            });
        }
        else
        {
            // Fallback to basic validation if service is not available
            ruleBuilder
                .MinimumLength(8).WithMessage("Password must be at least 8 characters long.")
                .Matches("[A-Z]").WithMessage("Password must contain at least one uppercase letter.")
                .Matches("[a-z]").WithMessage("Password must contain at least one lowercase letter.")
                .Matches("[0-9]").WithMessage("Password must contain at least one number.")
                .Matches("[^a-zA-Z0-9]").WithMessage("Password must contain at least one special character.");
        }
    }
}
