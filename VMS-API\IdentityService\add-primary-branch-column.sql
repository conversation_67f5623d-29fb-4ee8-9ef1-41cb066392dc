-- <PERSON><PERSON><PERSON> to add the PrimaryBranchId column to the Users table

-- Check if the PrimaryBranchId column exists in the Users table
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'Users'
        AND column_name = 'PrimaryBranchId'
    ) THEN
        -- Add the PrimaryBranchId column if it doesn't exist
        ALTER TABLE "Users" ADD COLUMN "PrimaryBranchId" uuid NULL;
        
        -- Log the change
        RAISE NOTICE 'Added PrimaryBranchId column to Users table';
    ELSE
        RAISE NOTICE 'PrimaryBranchId column already exists in Users table';
    END IF;
END $$;
