using IdentityService.Domain.Interfaces;
using IdentityService.Domain.Services;
using MediatR;
using Microsoft.Extensions.Logging;

namespace IdentityService.Application.Features.Menus.Queries;

/// <summary>
/// Query to get user menu with breadcrumbs
/// </summary>
public class GetUserMenuWithBreadcrumbsQuery : IRequest<MenuWithBreadcrumbsResponse>
{
    /// <summary>
    /// Current path for breadcrumb generation
    /// </summary>
    public string CurrentPath { get; set; } = string.Empty;
}

/// <summary>
/// Handler for getting user menu with breadcrumbs
/// </summary>
public class GetUserMenuWithBreadcrumbsQueryHandler : IRequestHandler<GetUserMenuWithBreadcrumbsQuery, MenuWithBreadcrumbsResponse>
{
    private readonly IMenuService _menuService;
    private readonly ICurrentUserService _currentUserService;
    private readonly ILogger<GetUserMenuWithBreadcrumbsQueryHandler> _logger;

    public GetUserMenuWithBreadcrumbsQueryHandler(
        IMenuService menuService,
        ICurrentUserService currentUserService,
        ILogger<GetUserMenuWithBreadcrumbsQueryHandler> logger)
    {
        _menuService = menuService;
        _currentUserService = currentUserService;
        _logger = logger;
    }

    public async Task<MenuWithBreadcrumbsResponse> Handle(GetUserMenuWithBreadcrumbsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var userId = _currentUserService.UserId;
            if (!userId.HasValue)
            {
                throw new UnauthorizedAccessException("User not authenticated");
            }

            _logger.LogInformation("Getting user menu with breadcrumbs for user {UserId}, path: {Path}", 
                userId.Value, request.CurrentPath);

            // Get user menu
            var menuItems = await _menuService.GetUserMenuAsync(userId.Value);

            // Generate breadcrumbs
            var domainBreadcrumbs = await _menuService.GenerateBreadcrumbsAsync(request.CurrentPath, userId.Value);

            var response = new MenuWithBreadcrumbsResponse
            {
                Menu = menuItems.Select(MapToResponse).ToList(),
                Breadcrumbs = domainBreadcrumbs.Select(MapBreadcrumbToResponse).ToList()
            };

            _logger.LogInformation("Successfully retrieved user menu with breadcrumbs for user {UserId}", userId.Value);

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user menu with breadcrumbs for path: {Path}", request.CurrentPath);
            throw;
        }
    }

    private static MenuItemResponse MapToResponse(MenuItemDto menuItem)
    {
        return new MenuItemResponse
        {
            Id = menuItem.Id,
            Name = menuItem.Name,
            DisplayName = menuItem.DisplayName,
            Path = menuItem.Path,
            Icon = menuItem.Icon,
            Order = menuItem.Order,
            ParentId = menuItem.ParentId,
            IsVisible = menuItem.IsVisible,
            HasAccess = menuItem.HasAccess,
            RequiredPermissions = menuItem.RequiredPermissions,
            Children = menuItem.Children.Select(MapToResponse).ToList()
        };
    }

    private static BreadcrumbItem MapBreadcrumbToResponse(IdentityService.Domain.Services.BreadcrumbItem domainItem)
    {
        return new BreadcrumbItem
        {
            Name = domainItem.DisplayName,
            Path = domainItem.Path,
            IsActive = domainItem.IsActive
        };
    }
}
