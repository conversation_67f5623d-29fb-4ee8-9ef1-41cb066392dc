using System;
using IdentityService.Domain.Common;

namespace IdentityService.Domain.Entities;

public class RolePermission : BaseEntity
{
    public Guid RoleId { get; private set; }
    public Guid PermissionId { get; private set; }
    public Role Role { get; private set; }
    public Permission Permission { get; private set; }

    private RolePermission()
    {
        Role = null!;
        Permission = null!;
    }

    public static RolePermission Create(Role role, Permission permission, string createdBy)
    {
        return new RolePermission
        {
            RoleId = role.Id,
            PermissionId = permission.Id,
            Role = role,
            Permission = permission,
            CreatedBy = createdBy
        };
    }
} 