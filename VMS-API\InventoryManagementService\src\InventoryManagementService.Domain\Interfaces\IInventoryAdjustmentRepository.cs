using InventoryManagementService.Domain.Entities;
using InventoryManagementService.Domain.Enums;
using System.Linq.Expressions;

namespace InventoryManagementService.Domain.Interfaces;

public interface IInventoryAdjustmentRepository
{
    Task<InventoryAdjustment?> GetByIdAsync(Guid id);
    Task<InventoryAdjustment?> GetByAdjustmentNumberAsync(string adjustmentNumber);
    Task<List<InventoryAdjustment>> GetAllAsync();
    Task<List<InventoryAdjustment>> GetByBranchAsync(Guid branchId);
    Task<List<InventoryAdjustment>> GetByVendorAsync(Guid vendorId);
    Task<List<InventoryAdjustment>> GetByReasonAsync(AdjustmentReason reason);
    Task<List<InventoryAdjustment>> GetPendingAdjustmentsAsync();
    Task<List<InventoryAdjustment>> GetApprovedAdjustmentsAsync();
    Task<List<InventoryAdjustment>> GetAsync(Expression<Func<InventoryAdjustment, bool>> predicate);
    Task AddAsync(InventoryAdjustment inventoryAdjustment);
    Task UpdateAsync(InventoryAdjustment inventoryAdjustment);
    Task DeleteAsync(Guid id);
    Task<bool> ExistsAsync(Guid id);
    Task<bool> AdjustmentNumberExistsAsync(string adjustmentNumber, Guid? excludeId = null);
}
