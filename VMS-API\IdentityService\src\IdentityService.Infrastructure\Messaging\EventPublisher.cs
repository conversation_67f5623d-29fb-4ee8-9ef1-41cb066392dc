using IdentityService.Application.Interfaces;
using MassTransit;
using Microsoft.Extensions.Logging;
using VMSContracts.Common;

namespace IdentityService.Infrastructure.Messaging;

/// <summary>
/// Implementation of IEventPublisher using MassTransit
/// </summary>
public class EventPublisher : IEventPublisher
{
    private readonly IPublishEndpoint _publishEndpoint;
    private readonly ILogger<EventPublisher> _logger;

    public EventPublisher(IPublishEndpoint publishEndpoint, ILogger<EventPublisher> logger)
    {
        _publishEndpoint = publishEndpoint;
        _logger = logger;
    }

    /// <inheritdoc />
    public async Task PublishAsync<TEvent>(TEvent @event, CancellationToken cancellationToken = default) 
        where TEvent : class, IEvent
    {
        _logger.LogInformation("Publishing event {EventType} with ID {EventId}", 
            typeof(TEvent).Name, @event.EventId);
        
        await _publishEndpoint.Publish(@event, cancellationToken);
        
        _logger.LogInformation("Published event {EventType} with ID {EventId}", 
            typeof(TEvent).Name, @event.EventId);
    }
}
