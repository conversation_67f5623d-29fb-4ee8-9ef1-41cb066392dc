using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using IdentityService.Application.Common;
using IdentityService.Application.DTOs.Permissions;
using IdentityService.Domain.Interfaces;
using MediatR;

namespace IdentityService.Application.Features.Permissions.Queries;

public class GetPermissionsQuery : BaseRequest<List<PermissionResponse>>
{
}

public class GetPermissionsQueryHandler : IRequestHandler<GetPermissionsQuery, List<PermissionResponse>>
{
    private readonly IUnitOfWork _unitOfWork;

    public GetPermissionsQueryHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
    }

    public async Task<List<PermissionResponse>> Handle(GetPermissionsQuery query, CancellationToken cancellationToken)
    {
        var permissions = await _unitOfWork.PermissionRepository.GetAllAsync();

        return permissions.Select(p => new PermissionResponse
        {
            Id = p.Id,
            Name = p.Name,
            Description = p.Description,
            Resource = p.Resource,
            Action = p.Action,
            CreatedAt = p.CreatedAt,
            CreatedBy = p.CreatedBy,
            UpdatedAt = p.UpdatedAt,
            UpdatedBy = p.UpdatedBy
        }).ToList();
    }
}
