namespace InventoryManagementService.Domain.Interfaces;

public interface IUnitOfWork : IDisposable
{
    IPartRepository Parts { get; }
    IInventoryRepository Inventory { get; }
    IStockTransferRepository StockTransfers { get; }
    IInventoryAdjustmentRepository InventoryAdjustments { get; }
    
    Task<int> SaveChangesAsync();
    Task BeginTransactionAsync();
    Task CommitTransactionAsync();
    Task RollbackTransactionAsync();
}
