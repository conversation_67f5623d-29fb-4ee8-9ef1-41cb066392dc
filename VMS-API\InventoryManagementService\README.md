# Inventory Management Service

This microservice is part of the Vehicle Management System and handles comprehensive inventory management for automobile service garages, including parts management, stock tracking, transfers, and adjustments.

## Features

### Part Management
- Create, update, activate/deactivate parts with SKU, name, description, manufacturer, pricing, and category
- Vehicle compatibility management (make, model, year ranges)
- Search parts by name, category, manufacturer, or vehicle compatibility
- Comprehensive part catalog with warranty information and images

### Inventory Management
- Create inventory items for parts at specific branches
- Track stock levels with minimum, maximum, and reorder level settings
- Add/remove stock with transaction logging and reasons
- Storage location management (location, bin, shelf)
- Low stock alerts and out-of-stock tracking

### Stock Transfer Management
- Initiate transfers between branches with approval workflow
- Add multiple items to transfers with requested quantities
- Ship transfers with actual shipped quantities
- Complete transfers with received quantities tracking
- Cancel transfers with reason tracking

### Inventory Adjustment
- Manual inventory adjustments for audits and discrepancies
- Support for multiple adjustment reasons (physical count, damage, loss, etc.)
- Approval workflow for adjustments
- Before/after quantity tracking with detailed notes

### Role-Based Access Control
- **SuperAdmin/VendorAdmin/BranchAdmin**: Full access to all operations (bypass permission checks)
- **Service Advisor**: Check parts availability, request parts allocation for job cards
- **Technician**: Request parts for assigned jobs, record parts usage
- **Inventory Manager**: Full inventory management, allocate parts, process orders
- **Cashier**: View parts for billing purposes (read-only access)

## Prerequisites

- .NET 8.0 SDK
- Docker and Docker Compose
- PostgreSQL 16
- RabbitMQ

## Getting Started

1. Clone the repository
2. Navigate to the project directory
3. Run the following commands:

```bash
# Build and run the service using Docker Compose
docker-compose up --build
```

The service will be available at:
- API: http://localhost:5006
- Swagger UI: http://localhost:5006/swagger
- Database: localhost:5438 (PostgreSQL)
- RabbitMQ Management: http://localhost:15672

## Project Structure

```
InventoryManagementService/
├── docker/
├── src/
│   ├── InventoryManagementService.API/
│   ├── InventoryManagementService.Application/
│   ├── InventoryManagementService.Domain/
│   └── InventoryManagementService.Infrastructure/
└── tests/
```

## Technology Stack

- .NET 8.0
- Entity Framework Core with PostgreSQL
- MediatR for CQRS pattern
- FluentValidation for validation
- AutoMapper for object mapping
- MassTransit with RabbitMQ for messaging
- Serilog for logging
- Swagger for API documentation

## API Endpoints

### Part Management
- `GET /api/parts/{id}` - Get a part by ID
- `GET /api/parts/search` - Search parts with various criteria
- `POST /api/parts` - Create a new part
- `PUT /api/parts/{id}` - Update an existing part
- `PATCH /api/parts/{id}/activate` - Activate a part
- `PATCH /api/parts/{id}/deactivate` - Deactivate a part
- `POST /api/parts/{id}/compatibility` - Add vehicle compatibility
- `DELETE /api/parts/{id}/compatibility/{compatibilityId}` - Remove compatibility

### Inventory Management
- `GET /api/inventory/{id}` - Get inventory item by ID
- `GET /api/inventory/branch/{branchId}` - Get inventory by branch
- `GET /api/inventory/part/{partId}` - Get inventory by part
- `GET /api/inventory/branch/{branchId}/low-stock` - Get low stock items
- `POST /api/inventory` - Create inventory item
- `POST /api/inventory/{id}/add-stock` - Add stock
- `POST /api/inventory/{id}/remove-stock` - Remove stock
- `PUT /api/inventory/{id}/stock` - Update stock quantity
- `PUT /api/inventory/{id}/storage-location` - Update storage location

### Stock Transfer Management
- `GET /api/stocktransfers/{id}` - Get transfer by ID
- `GET /api/stocktransfers/branch/{branchId}/outgoing` - Get outgoing transfers
- `GET /api/stocktransfers/branch/{branchId}/incoming` - Get incoming transfers
- `GET /api/stocktransfers/pending` - Get pending transfers
- `POST /api/stocktransfers` - Create transfer
- `POST /api/stocktransfers/{id}/items` - Add item to transfer
- `POST /api/stocktransfers/{id}/ship` - Ship transfer
- `POST /api/stocktransfers/{id}/complete` - Complete transfer
- `POST /api/stocktransfers/{id}/cancel` - Cancel transfer

### Inventory Adjustment
- `GET /api/inventoryadjustments/{id}` - Get adjustment by ID
- `GET /api/inventoryadjustments/branch/{branchId}` - Get adjustments by branch
- `GET /api/inventoryadjustments/pending` - Get pending adjustments
- `POST /api/inventoryadjustments` - Create adjustment
- `POST /api/inventoryadjustments/{id}/items` - Add item to adjustment
- `POST /api/inventoryadjustments/{id}/approve` - Approve adjustment
- `PUT /api/inventoryadjustments/{id}/notes` - Update adjustment notes

## Development

### Running Locally

```bash
cd src/InventoryManagementService.API
dotnet run
```

### Database Migrations

```bash
cd src/InventoryManagementService.API
dotnet ef migrations add InitialCreate
dotnet ef database update
```

### Database Seeding

The service automatically seeds the database with sample data on startup:
- 20 sample parts across different categories
- Vehicle compatibility data for parts
- Sample inventory items for demonstration

## Testing

```bash
dotnet test
```

## Sample Data

The service includes comprehensive sample data:

### Parts Categories
- Engine parts (oil filters, air filters, spark plugs)
- Brake components (pads, rotors)
- Suspension parts (shock absorbers)
- Electrical components (batteries, alternators)
- Filters and fluids
- Belts and hoses
- Tires and lights

### Vehicle Compatibility
- Toyota Camry (2015-2023)
- Honda Accord (2013-2023)
- Nissan Altima (2013-2023)
- Mazda 6 (2014-2023)
- Hyundai Sonata (2015-2023)

## Business Logic

### Inventory Flow
1. **Part Creation**: Parts are created with basic information and vehicle compatibility
2. **Inventory Setup**: Inventory items are created for parts at specific branches
3. **Stock Management**: Stock levels are maintained through add/remove operations
4. **Transfer Process**: Parts can be transferred between branches with approval workflow
5. **Adjustment Process**: Manual adjustments handle discrepancies and audits

### Permission Model
- Admin roles have full access to all operations
- Branch users have role-specific permissions:
  - Service Advisors: Check availability, request allocations
  - Technicians: Request and record usage
  - Inventory Managers: Full inventory control
  - Cashiers: Read-only access for billing

### Event-Driven Architecture
The service publishes events for:
- Part lifecycle (created, updated, activated, deactivated)
- Inventory changes (stock updates, low stock alerts)
- Transfer lifecycle (created, shipped, completed, cancelled)
- Adjustment lifecycle (created, approved)

## Configuration

Key configuration sections in `appsettings.json`:
- `ConnectionStrings`: Database connection
- `RabbitMQ`: Message broker settings
- `JWT`: Authentication settings
- `Serilog`: Logging configuration
