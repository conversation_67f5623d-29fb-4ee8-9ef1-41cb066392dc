using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using IdentityService.Application.Common;
using IdentityService.Application.DTOs.Roles;
using IdentityService.Domain.Interfaces;
using MediatR;

namespace IdentityService.Application.Features.Roles.Queries;

public class GetRolesQuery : BaseRequest<List<RoleResponse>>
{
}

public class GetRolesQueryHandler : IRequestHandler<GetRolesQuery, List<RoleResponse>>
{
    private readonly IUnitOfWork _unitOfWork;

    public GetRolesQueryHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
    }

    public async Task<List<RoleResponse>> Handle(GetRolesQuery query, CancellationToken cancellationToken)
    {
        var roles = await _unitOfWork.RoleRepository.GetAllAsync();
        var responses = new List<RoleResponse>();

        foreach (var role in roles)
        {
            var permissions = new List<RolePermissionResponse>();
            foreach (var rp in role.RolePermissions)
            {
                var permission = await _unitOfWork.PermissionRepository.GetByIdAsync(rp.PermissionId);
                if (permission != null)
                {
                    permissions.Add(new RolePermissionResponse
                    {
                        Id = permission.Id,
                        Name = permission.Name,
                        Description = permission.Description,
                        Resource = permission.Resource,
                        Action = permission.Action
                    });
                }
            }

            responses.Add(new RoleResponse
            {
                Id = role.Id,
                Name = role.Name,
                Description = role.Description,
                CreatedAt = role.CreatedAt,
                CreatedBy = role.CreatedBy,
                UpdatedAt = role.UpdatedAt,
                UpdatedBy = role.UpdatedBy,
                Permissions = permissions
            });
        }

        return responses;
    }
}
