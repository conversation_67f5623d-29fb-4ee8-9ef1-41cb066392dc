2025-06-10 12:42:27.798 +05:30 [INF] Starting database operations...
2025-06-10 12:42:28.045 +05:30 [INF] Checking database connection...
2025-06-10 12:42:31.096 +05:30 [WRN] Database is not accessible. Skipping migration and seeding.
2025-06-10 12:42:31.141 +05:30 [INF] Inventory Management Service starting up...
2025-06-10 12:42:31.159 +05:30 [INF] Service will be available at:
2025-06-10 12:42:31.175 +05:30 [INF]   HTTP:  http://localhost:5006
2025-06-10 12:42:31.184 +05:30 [INF]   HTTPS: https://localhost:7006
2025-06-10 12:42:31.191 +05:30 [INF]   Swagger: http://localhost:5006/swagger
2025-06-10 12:42:31.806 +05:30 [ERR] Hosting failed to start
System.IO.IOException: Failed to bind to address http://127.0.0.1:5006: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: Only one usage of each socket address (protocol/network address/port) is normally permitted.
 ---> System.Net.Sockets.SocketException (10048): Only one usage of each socket address (protocol/network address/port) is normally permitted.
   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
2025-06-10 12:42:31.983 +05:30 [INF] Bus started: "rabbitmq://localhost/"
2025-06-10 12:42:32.115 +05:30 [INF] Bus stopped: "rabbitmq://localhost/"
2025-06-10 12:45:01.439 +05:30 [INF] Starting database operations...
2025-06-10 12:45:01.708 +05:30 [INF] Checking database connection...
2025-06-10 12:45:05.139 +05:30 [WRN] Database is not accessible. Skipping migration and seeding.
2025-06-10 12:45:05.161 +05:30 [INF] Inventory Management Service starting up...
2025-06-10 12:45:05.169 +05:30 [INF] Service will be available at:
2025-06-10 12:45:05.182 +05:30 [INF]   HTTP:  http://localhost:5008
2025-06-10 12:45:05.188 +05:30 [INF]   HTTPS: https://localhost:7008
2025-06-10 12:45:05.200 +05:30 [INF]   Swagger: http://localhost:5008/swagger
2025-06-10 12:45:05.750 +05:30 [INF] Bus started: "rabbitmq://localhost/"
2025-06-10 12:46:17.222 +05:30 [INF] Bus stopped: "rabbitmq://localhost/"
2025-06-10 12:46:17.237 +05:30 [INF] Inventory Management Service shut down complete
