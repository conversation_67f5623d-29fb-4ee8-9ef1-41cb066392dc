using Microsoft.AspNetCore.Builder;

namespace IdentityService.API.Middleware
{
    public static class MiddlewareExtensions
    {
        public static IApplicationBuilder UseUserIdPropagation(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<UserIdPropagationMiddleware>();
        }

        public static IApplicationBuilder UseSecurityStampValidation(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<SecurityStampValidationMiddleware>();
        }
    }
}
