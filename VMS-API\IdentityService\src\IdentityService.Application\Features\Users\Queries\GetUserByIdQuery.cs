using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using IdentityService.Application.Common;
using IdentityService.Application.DTOs.Users;
using IdentityService.Domain.Interfaces;
using MediatR;

namespace IdentityService.Application.Features.Users.Queries;

public class GetUserByIdQuery : BaseRequest<UserResponse>
{
    // This UserId is the target user's ID, not the current user's ID
    public new Guid UserId { get; set; }
}

public class GetUserByIdQueryHandler : IRequestHandler<GetUserByIdQuery, UserResponse>
{
    private readonly IUnitOfWork _unitOfWork;

    public GetUserByIdQueryHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
    }

    public async Task<UserResponse> Handle(GetUserByIdQuery query, CancellationToken cancellationToken)
    {
        var user = await _unitOfWork.UserRepository.GetByIdAsync(query.UserId);
        if (user == null)
            throw new InvalidOperationException($"User with ID {query.UserId} not found");

        var roles = await _unitOfWork.UserRepository.GetUserRolesAsync(user.Id);

        return new UserResponse
        {
            Id = user.Id,
            Email = user.Email,
            PhoneNumber = user.PhoneNumber,
            EmailVerified = user.EmailVerified,
            PhoneNumberVerified = user.PhoneNumberVerified,
            IsActive = !user.IsDeleted,
            LastLoginAt = user.LastLoginAt,
            CreatedAt = user.CreatedAt,
            CreatedBy = user.CreatedBy,
            UpdatedAt = user.UpdatedAt,
            UpdatedBy = user.UpdatedBy,
            Roles = new List<UserRoleResponse>()
        };
    }
}
