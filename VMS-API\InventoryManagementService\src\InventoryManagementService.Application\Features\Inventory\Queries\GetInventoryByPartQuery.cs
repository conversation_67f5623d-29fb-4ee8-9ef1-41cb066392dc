using AutoMapper;
using InventoryManagementService.Application.DTOs;
using InventoryManagementService.Domain.Interfaces;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using VMS.Contracts.Common.Authorization;

namespace InventoryManagementService.Application.Features.Inventory.Queries;

public class GetInventoryByPartQuery : IRequest<List<InventoryItemDto>>
{
    public Guid PartId { get; set; }
}

public class GetInventoryByPartQueryHandler : IRequestHandler<GetInventoryByPartQuery, List<InventoryItemDto>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly ILogger<GetInventoryByPartQueryHandler> _logger;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public GetInventoryByPartQueryHandler(
        IUnitOfWork unitOfWork,
        IMapper mapper,
        ILogger<GetInventoryByPartQueryHandler> logger,
        IHttpContextAccessor httpContextAccessor)
    {
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _logger = logger;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<List<InventoryItemDto>> Handle(GetInventoryByPartQuery request, CancellationToken cancellationToken)
    {
        // Check permissions - Admin roles bypass, others need explicit permission
        var user = _httpContextAccessor.HttpContext?.User;
        if (!PermissionHelper.IsAdmin(user))
        {
            PermissionHelper.ValidateAccess(user, "Inventory.View");
        }

        _logger.LogInformation("Getting inventory items for part: {PartId}", request.PartId);

        var inventoryItems = await _unitOfWork.Inventory.GetByPartAsync(request.PartId);

        _logger.LogInformation("Found {Count} inventory items for part {PartId}", inventoryItems.Count, request.PartId);

        return _mapper.Map<List<InventoryItemDto>>(inventoryItems);
    }
}
