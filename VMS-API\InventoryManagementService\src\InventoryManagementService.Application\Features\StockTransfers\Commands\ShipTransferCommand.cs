using FluentValidation;
using InventoryManagementService.Application.DTOs;
using InventoryManagementService.Domain.Interfaces;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using VMS.Contracts.Common.Authorization;

namespace InventoryManagementService.Application.Features.StockTransfers.Commands;

public class ShipTransferCommand : IRequest
{
    public Guid TransferId { get; set; }
    public List<ShipTransferItemDto> Items { get; set; } = new();
    public string ShippedBy { get; set; } = string.Empty;
}

public class ShipTransferCommandValidator : AbstractValidator<ShipTransferCommand>
{
    public ShipTransferCommandValidator()
    {
        RuleFor(x => x.TransferId)
            .NotEmpty().WithMessage("Transfer ID is required");

        RuleFor(x => x.Items)
            .NotEmpty().WithMessage("At least one item is required");

        RuleForEach(x => x.Items).ChildRules(item =>
        {
            item.RuleFor(x => x.ItemId)
                .NotEmpty().WithMessage("Item ID is required");

            item.RuleFor(x => x.ShippedQuantity)
                .GreaterThan(0).WithMessage("Shipped quantity must be greater than 0");
        });

        RuleFor(x => x.ShippedBy)
            .NotEmpty().WithMessage("ShippedBy is required");
    }
}

public class ShipTransferCommandHandler : IRequestHandler<ShipTransferCommand>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<ShipTransferCommandHandler> _logger;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly IEventPublisher _eventPublisher;

    public ShipTransferCommandHandler(
        IUnitOfWork unitOfWork,
        ILogger<ShipTransferCommandHandler> logger,
        IHttpContextAccessor httpContextAccessor,
        IEventPublisher eventPublisher)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
        _httpContextAccessor = httpContextAccessor;
        _eventPublisher = eventPublisher;
    }

    public async Task Handle(ShipTransferCommand request, CancellationToken cancellationToken)
    {
        // Check permissions - Admin roles bypass, others need explicit permission
        var user = _httpContextAccessor.HttpContext?.User;
        if (!PermissionHelper.IsAdmin(user))
        {
            PermissionHelper.ValidateAccess(user, "Transfers.Update");
        }

        _logger.LogInformation("Shipping transfer {TransferId}", request.TransferId);

        await _unitOfWork.BeginTransactionAsync();

        try
        {
            var transfer = await _unitOfWork.StockTransfers.GetByIdAsync(request.TransferId);

            if (transfer == null)
            {
                throw new KeyNotFoundException($"Stock transfer with ID {request.TransferId} not found");
            }

            // Update shipped quantities for each item
            foreach (var itemDto in request.Items)
            {
                var transferItem = transfer.Items.FirstOrDefault(i => i.Id == itemDto.ItemId);
                if (transferItem == null)
                {
                    throw new KeyNotFoundException($"Transfer item with ID {itemDto.ItemId} not found");
                }

                if (itemDto.ShippedQuantity > transferItem.RequestedQuantity)
                {
                    throw new InvalidOperationException($"Shipped quantity ({itemDto.ShippedQuantity}) cannot exceed requested quantity ({transferItem.RequestedQuantity})");
                }

                transferItem.SetShippedQuantity(itemDto.ShippedQuantity, request.ShippedBy);

                // Remove stock from source branch
                var inventoryItem = await _unitOfWork.Inventory.GetByPartAndBranchAsync(transferItem.PartId, transfer.SourceBranchId);
                if (inventoryItem != null)
                {
                    var success = inventoryItem.RemoveStock(itemDto.ShippedQuantity, request.ShippedBy, $"Transfer to branch {transfer.DestinationBranchId} - {transfer.TransferNumber}");
                    if (!success)
                    {
                        throw new InvalidOperationException($"Insufficient stock for part {transferItem.PartId}. Available: {inventoryItem.CurrentStock}, Required: {itemDto.ShippedQuantity}");
                    }
                }
            }

            transfer.Ship(request.ShippedBy);

            await _unitOfWork.SaveChangesAsync();
            await _unitOfWork.CommitTransactionAsync();

            _logger.LogInformation("Transfer {TransferId} shipped successfully", transfer.Id);

            // Publish event
            await _eventPublisher.PublishTransferShippedEvent(
                transfer.Id,
                transfer.TransferNumber,
                transfer.ShippedDate!.Value);
        }
        catch
        {
            await _unitOfWork.RollbackTransactionAsync();
            throw;
        }
    }
}
