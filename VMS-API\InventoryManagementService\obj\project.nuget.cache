{"version": 2, "dgSpecHash": "VlCiVrs0ids=", "success": true, "projectFilePath": "E:\\Shri\\AutomobilesGenerative02062025\\AutomobilesGenerative\\VMS-API\\InventoryManagementService\\TestConnection.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\8.0.0\\microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.abstractions\\8.0.0\\microsoft.extensions.logging.abstractions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\npgsql\\8.0.0\\npgsql.8.0.0.nupkg.sha512"], "logs": [{"code": "NU1903", "level": "Warning", "warningLevel": 1, "message": "Package 'Npgsql' 8.0.0 has a known high severity vulnerability, https://github.com/advisories/GHSA-x9vc-6hfv-hg8c", "libraryId": "Npgsql", "targetGraphs": ["net8.0"]}]}