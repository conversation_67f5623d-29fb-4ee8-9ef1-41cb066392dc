using System;
using IdentityService.Domain.Common;
using IdentityService.Domain.Enums;

namespace IdentityService.Domain.Entities;

/// <summary>
/// Represents a direct permission assignment to a user, independent of roles
/// This allows for user-specific permission overrides and granular access control
/// </summary>
public class UserPermission : BaseEntity
{
    public Guid UserId { get; private set; }
    public Guid PermissionId { get; private set; }
    public PermissionType Type { get; private set; }
    public DateTime? ExpiresAt { get; private set; }
    public string? Reason { get; private set; }
    public bool IsActive { get; private set; } = true;
    public User User { get; private set; }
    public Permission Permission { get; private set; }

    private UserPermission()
    {
        User = null!;
        Permission = null!;
    }

    /// <summary>
    /// Creates a new user permission assignment
    /// </summary>
    /// <param name="user">The user to assign permission to</param>
    /// <param name="permission">The permission to assign</param>
    /// <param name="type">Whether this grants or denies the permission</param>
    /// <param name="expiresAt">Optional expiration date for temporary permissions</param>
    /// <param name="reason">Optional reason for the permission assignment</param>
    /// <param name="createdBy">Who created this assignment</param>
    /// <returns>New UserPermission instance</returns>
    public static UserPermission Create(
        User user, 
        Permission permission, 
        PermissionType type,
        DateTime? expiresAt,
        string? reason,
        string createdBy)
    {
        return new UserPermission
        {
            UserId = user.Id,
            PermissionId = permission.Id,
            Type = type,
            ExpiresAt = expiresAt,
            Reason = reason,
            IsActive = true,
            User = user,
            Permission = permission,
            CreatedBy = createdBy
        };
    }

    /// <summary>
    /// Updates the permission type and expiration
    /// </summary>
    public void Update(
        PermissionType type,
        DateTime? expiresAt,
        string? reason,
        string updatedBy)
    {
        Type = type;
        ExpiresAt = expiresAt;
        Reason = reason;
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Checks if this permission assignment is currently active and not expired
    /// </summary>
    public bool IsCurrentlyActive()
    {
        return IsActive && (ExpiresAt == null || ExpiresAt > DateTime.UtcNow);
    }

    /// <summary>
    /// Checks if this permission grants access
    /// </summary>
    public bool GrantsAccess()
    {
        return IsCurrentlyActive() && Type == PermissionType.Grant;
    }

    /// <summary>
    /// Checks if this permission denies access
    /// </summary>
    public bool DeniesAccess()
    {
        return IsCurrentlyActive() && Type == PermissionType.Deny;
    }

    /// <summary>
    /// Extends the expiration date
    /// </summary>
    public void ExtendExpiration(DateTime newExpiresAt, string updatedBy)
    {
        if (newExpiresAt <= DateTime.UtcNow)
            throw new ArgumentException("New expiration date must be in the future");

        ExpiresAt = newExpiresAt;
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Revokes the permission by setting it as inactive
    /// </summary>
    public void Revoke(string updatedBy)
    {
        IsActive = false;
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;
    }

}
