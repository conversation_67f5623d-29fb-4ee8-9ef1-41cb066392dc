namespace InventoryManagementService.Domain.Enums;

public enum TransactionType
{
    StockIn = 1,
    StockOut = 2,
    Transfer = 3,
    Adjustment = 4,
    Return = 5,
    Damage = 6,
    Loss = 7,
    Sale = 8,
    Purchase = 9
}

public enum TransferStatus
{
    Pending = 1,
    InTransit = 2,
    Completed = 3,
    Cancelled = 4
}

public enum AdjustmentReason
{
    PhysicalCount = 1,
    Damage = 2,
    Loss = 3,
    Found = 4,
    Correction = 5,
    Expiry = 6,
    Other = 99
}
