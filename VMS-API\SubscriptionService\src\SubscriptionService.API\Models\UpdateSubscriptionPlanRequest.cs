using System;
using System.ComponentModel.DataAnnotations;
using SubscriptionService.Domain.Enums;

namespace SubscriptionService.API.Models
{
    /// <summary>
    /// Request model for updating an existing subscription plan
    /// Note: Updates create a new version to ensure existing vendors are unaffected
    /// </summary>
    public class UpdateSubscriptionPlanRequest
    {
        /// <summary>
        /// Name of the subscription plan
        /// </summary>
        [Required(ErrorMessage = "Plan name is required")]
        [StringLength(100, MinimumLength = 3, ErrorMessage = "Plan name must be between 3 and 100 characters")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Description of the subscription plan
        /// </summary>
        [Required(ErrorMessage = "Plan description is required")]
        [StringLength(500, MinimumLength = 10, ErrorMessage = "Plan description must be between 10 and 500 characters")]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// Maximum number of branches allowed
        /// </summary>
        [Required(ErrorMessage = "Max branches is required")]
        [Range(1, 1000, ErrorMessage = "Max branches must be between 1 and 1000")]
        public int MaxBranches { get; set; }

        /// <summary>
        /// Maximum number of users allowed
        /// </summary>
        [Required(ErrorMessage = "Max users is required")]
        [Range(1, 10000, ErrorMessage = "Max users must be between 1 and 10000")]
        public int MaxUsers { get; set; }

        /// <summary>
        /// Base monthly price of the subscription plan
        /// </summary>
        [Required(ErrorMessage = "Base monthly price is required")]
        [Range(0, 999999.99, ErrorMessage = "Base monthly price must be between 0 and 999999.99")]
        public decimal BaseMonthlyPrice { get; set; }

        /// <summary>
        /// Default country code (ISO 3166-1 alpha-2)
        /// </summary>
        [StringLength(2, MinimumLength = 2, ErrorMessage = "Country code must be exactly 2 characters")]
        public string DefaultCountryCode { get; set; } = string.Empty;

        /// <summary>
        /// Default currency code (ISO 4217)
        /// </summary>
        [StringLength(3, MinimumLength = 3, ErrorMessage = "Currency code must be exactly 3 characters")]
        public string DefaultCurrencyCode { get; set; } = string.Empty;

        /// <summary>
        /// Indicates if this is a trial plan
        /// </summary>
        public bool IsTrialPlan { get; set; }

        /// <summary>
        /// Trial period in days (required if IsTrialPlan is true)
        /// </summary>
        [Range(1, 365, ErrorMessage = "Trial period must be between 1 and 365 days")]
        public int? TrialPeriodDays { get; set; }

        /// <summary>
        /// Advanced features configuration
        /// </summary>
        public UpdateAdvancedFeaturesRequest? AdvancedFeatures { get; set; }

        /// <summary>
        /// Billing cycle discount configuration
        /// </summary>
        public UpdateBillingCycleDiscountRequest? BillingCycleDiscount { get; set; }


    }

    /// <summary>
    /// Request model for updating advanced features configuration
    /// </summary>
    public class UpdateAdvancedFeaturesRequest
    {
        /// <summary>
        /// Enable custom reports feature
        /// </summary>
        public bool CustomReports { get; set; }

        /// <summary>
        /// Enable document export feature
        /// </summary>
        public bool DocumentExport { get; set; }
    }

    /// <summary>
    /// Request model for updating billing cycle discount configuration
    /// </summary>
    public class UpdateBillingCycleDiscountRequest
    {
        /// <summary>
        /// Discount percentage for quarterly billing (0-100)
        /// </summary>
        [Range(0, 100, ErrorMessage = "Quarterly discount percentage must be between 0 and 100")]
        public decimal QuarterlyDiscountPercentage { get; set; }

        /// <summary>
        /// Discount percentage for yearly billing (0-100)
        /// </summary>
        [Range(0, 100, ErrorMessage = "Yearly discount percentage must be between 0 and 100")]
        public decimal YearlyDiscountPercentage { get; set; }

        /// <summary>
        /// Indicates if discounts are active
        /// </summary>
        public bool IsActive { get; set; }
    }
}
