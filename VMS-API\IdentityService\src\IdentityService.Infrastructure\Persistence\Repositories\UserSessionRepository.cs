using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using IdentityService.Domain.Entities;
using IdentityService.Domain.Enums;
using IdentityService.Domain.Interfaces;
using IdentityService.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;

namespace IdentityService.Infrastructure.Persistence.Repositories;

/// <summary>
/// Repository for user sessions
/// </summary>
public class UserSessionRepository : GenericRepository<UserSession>, IUserSessionRepository
{
    public UserSessionRepository(ApplicationDbContext context) : base(context)
    {
    }

    /// <inheritdoc />
    public async Task<UserSession> GetByTokenAsync(string token)
    {
        return await _dbSet
            .Include(s => s.User)
            .FirstOrDefaultAsync(s => s.Token == token);
    }

    /// <inheritdoc />
    public async Task<UserSession> GetByRefreshTokenAsync(string refreshToken)
    {
        return await _dbSet
            .Include(s => s.User)
            .FirstOrDefaultAsync(s => s.RefreshToken == refreshToken);
    }

    /// <inheritdoc />
    public async Task<IEnumerable<UserSession>> GetActiveSessionsForUserAsync(Guid userId)
    {
        // Optimized: No need to include User data for session management operations
        return await _dbSet
            .Where(s => s.UserId == userId && s.Status == SessionStatus.Active && s.ExpiresAt > DateTime.UtcNow)
            .OrderByDescending(s => s.LastActiveAt)
            .ToListAsync();
    }

    /// <inheritdoc />
    public async Task<int> GetActiveSessionCountForUserAsync(Guid userId)
    {
        return await _dbSet
            .CountAsync(s => s.UserId == userId && s.Status == SessionStatus.Active && s.ExpiresAt > DateTime.UtcNow);
    }

    /// <inheritdoc />
    public async Task<IEnumerable<UserSession>> GetSessionsForUserAsync(
        Guid userId, 
        DateTime? fromDate = null, 
        DateTime? toDate = null, 
        SessionStatus? status = null, 
        int pageNumber = 1, 
        int pageSize = 10)
    {
        var query = _dbSet
            .Include(s => s.User)
            .Where(s => s.UserId == userId);

        if (fromDate.HasValue)
        {
            query = query.Where(s => s.CreatedAt >= fromDate.Value);
        }

        if (toDate.HasValue)
        {
            query = query.Where(s => s.CreatedAt <= toDate.Value);
        }

        if (status.HasValue)
        {
            query = query.Where(s => s.Status == status.Value);
        }

        return await query
            .OrderByDescending(s => s.CreatedAt)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();
    }

    /// <inheritdoc />
    public async Task<IEnumerable<UserSession>> GetIdleSessionsAsync(int idleTimeoutMinutes)
    {
        var idleThreshold = DateTime.UtcNow.AddMinutes(-idleTimeoutMinutes);

        // No need to include User data for cleanup operations - improves performance
        return await _dbSet
            .Where(s => s.Status == SessionStatus.Active &&
                   s.LastActiveAt < idleThreshold &&
                   s.ExpiresAt > DateTime.UtcNow)
            .ToListAsync();
    }

    /// <inheritdoc />
    public async Task<IEnumerable<UserSession>> GetExpiredSessionsAsync()
    {
        var now = DateTime.UtcNow;

        // No need to include User data for cleanup operations - improves performance
        return await _dbSet
            .Where(s => s.Status == SessionStatus.Active && s.ExpiresAt <= now)
            .ToListAsync();
    }
}
