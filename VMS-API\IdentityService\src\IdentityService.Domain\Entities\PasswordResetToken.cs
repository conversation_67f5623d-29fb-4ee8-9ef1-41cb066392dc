using System;
using IdentityService.Domain.Common;

namespace IdentityService.Domain.Entities;

public class PasswordResetToken : BaseEntity
{
    public string Token { get; private set; }
    public DateTime ExpiryTime { get; private set; }
    public bool IsUsed { get; private set; }
    public Guid UserId { get; private set; }
    public User User { get; private set; }

    private PasswordResetToken()
    {
        Token = string.Empty;
    }

    public static PasswordResetToken Create(
        string token,
        DateTime expiryTime,
        Guid userId,
        User user,
        string createdBy)
    {
        return new PasswordResetToken
        {
            Token = token,
            ExpiryTime = expiryTime,
            UserId = userId,
            User = user,
            CreatedBy = createdBy
        };
    }

    public void MarkAsUsed()
    {
        IsUsed = true;
        UpdatedAt = DateTime.UtcNow;
    }

    public bool IsValid()
    {
        return !IsUsed && ExpiryTime > DateTime.UtcNow;
    }
} 