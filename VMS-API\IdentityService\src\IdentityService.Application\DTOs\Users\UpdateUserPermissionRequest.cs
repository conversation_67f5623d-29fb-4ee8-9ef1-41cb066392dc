using System.ComponentModel.DataAnnotations;
using IdentityService.Domain.Enums;

namespace IdentityService.Application.DTOs.Users;

/// <summary>
/// Request to update a user permission
/// </summary>
public class UpdateUserPermissionRequest
{
    /// <summary>
    /// Type of permission (Grant or Deny)
    /// </summary>
    [Required]
    public PermissionType Type { get; set; }

    /// <summary>
    /// Optional expiration date for the permission
    /// </summary>
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// Reason for updating this permission
    /// </summary>
    [MaxLength(500)]
    public string? Reason { get; set; }
}
