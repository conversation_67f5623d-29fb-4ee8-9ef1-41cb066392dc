using IdentityService.Application.Interfaces;
using IdentityService.Domain.Enums;
using IdentityService.Domain.Interfaces;
using IdentityService.Domain.Services;
using MediatR;
using Microsoft.Extensions.Logging;

namespace IdentityService.Application.Features.Users.Queries;

/// <summary>
/// Query to get detailed permission analysis for a user
/// </summary>
public class GetUserPermissionAnalysisQuery : IRequest<UserPermissionAnalysisResponse>
{
    public Guid UserId { get; set; }
}

/// <summary>
/// Response model for permission analysis
/// </summary>
public class UserPermissionAnalysisResponse
{
    public Guid UserId { get; set; }
    public List<PermissionSourceResponse> Permissions { get; set; } = new();
    public List<string> ConflictingPermissions { get; set; } = new();
    public List<string> ExpiredPermissions { get; set; } = new();
    public PermissionSummary Summary { get; set; } = new();
}

/// <summary>
/// Permission source information
/// </summary>
public class PermissionSourceResponse
{
    public string PermissionName { get; set; } = string.Empty;
    public string Source { get; set; } = string.Empty;
    public PermissionType? Type { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public bool IsActive { get; set; }
    public bool IsEffective { get; set; }
    public string? Reason { get; set; }
}

/// <summary>
/// Summary of user permissions
/// </summary>
public class PermissionSummary
{
    public int TotalPermissions { get; set; }
    public int RoleBasedPermissions { get; set; }
    public int DirectPermissions { get; set; }
    public int GrantedPermissions { get; set; }
    public int DeniedPermissions { get; set; }
    public int ExpiredPermissions { get; set; }
    public int ConflictingPermissions { get; set; }
}

/// <summary>
/// Handler for permission analysis query
/// </summary>
public class GetUserPermissionAnalysisQueryHandler : IRequestHandler<GetUserPermissionAnalysisQuery, UserPermissionAnalysisResponse>
{
    private readonly IPermissionService _permissionService;
    private readonly ICurrentUserService _currentUserService;
    private readonly ILogger<GetUserPermissionAnalysisQueryHandler> _logger;

    public GetUserPermissionAnalysisQueryHandler(
        IPermissionService permissionService,
        ICurrentUserService currentUserService,
        ILogger<GetUserPermissionAnalysisQueryHandler> logger)
    {
        _permissionService = permissionService;
        _currentUserService = currentUserService;
        _logger = logger;
    }

    public async Task<UserPermissionAnalysisResponse> Handle(GetUserPermissionAnalysisQuery query, CancellationToken cancellationToken)
    {
        try
        {
            // Check if current user has permission to view this analysis
            var currentUserId = _currentUserService.UserId;
            if (currentUserId != query.UserId)
            {
                // Only allow if current user is admin or has specific permission
                var hasPermission = await _permissionService.HasPermissionAsync(currentUserId ?? Guid.Empty, "Users.ViewPermissions");
                if (!hasPermission)
                {
                    throw new UnauthorizedAccessException("Insufficient permissions to view user permission analysis");
                }
            }

            var analysis = await _permissionService.GetPermissionAnalysisAsync(query.UserId);

            var response = new UserPermissionAnalysisResponse
            {
                UserId = analysis.UserId,
                ConflictingPermissions = analysis.ConflictingPermissions,
                ExpiredPermissions = analysis.ExpiredPermissions,
                Permissions = analysis.Permissions.Select(p => new PermissionSourceResponse
                {
                    PermissionName = p.PermissionName,
                    Source = p.Source,
                    Type = p.Type,
                    ExpiresAt = p.ExpiresAt,
                    IsActive = p.IsActive,
                    IsEffective = p.IsEffective
                }).ToList()
            };

            // Calculate summary
            response.Summary = new PermissionSummary
            {
                TotalPermissions = response.Permissions.Count,
                RoleBasedPermissions = response.Permissions.Count(p => p.Source.StartsWith("Role:")),
                DirectPermissions = response.Permissions.Count(p => p.Source == "Direct"),
                GrantedPermissions = response.Permissions.Count(p => p.Type == PermissionType.Grant),
                DeniedPermissions = response.Permissions.Count(p => p.Type == PermissionType.Deny),
                ExpiredPermissions = response.ExpiredPermissions.Count,
                ConflictingPermissions = response.ConflictingPermissions.Count
            };

            _logger.LogDebug("Generated permission analysis for user {UserId} with {TotalPermissions} permissions",
                query.UserId, response.Summary.TotalPermissions);

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating permission analysis for user {UserId}", query.UserId);
            throw;
        }
    }
}
