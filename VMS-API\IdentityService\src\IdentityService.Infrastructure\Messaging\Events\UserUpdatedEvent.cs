using VMSContracts.Identity.Events;

namespace IdentityService.Infrastructure.Messaging.Events;

/// <summary>
/// Event published when a user is updated
/// </summary>
public class UserUpdatedEvent : BaseEvent, UserUpdated
{
    /// <inheritdoc />
    public Guid UserId { get; }
    
    /// <inheritdoc />
    public string Email { get; }
    
    /// <inheritdoc />
    public string PhoneNumber { get; }
    
    /// <inheritdoc />
    public bool EmailVerified { get; }
    
    /// <inheritdoc />
    public bool PhoneNumberVerified { get; }

    public UserUpdatedEvent(
        Guid userId,
        string email,
        string phoneNumber,
        bool emailVerified,
        bool phoneNumberVerified)
    {
        UserId = userId;
        Email = email;
        PhoneNumber = phoneNumber;
        EmailVerified = emailVerified;
        PhoneNumberVerified = phoneNumberVerified;
    }
}
