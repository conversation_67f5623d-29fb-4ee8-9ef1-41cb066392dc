using AutoMapper;
using InventoryManagementService.Application.DTOs;
using InventoryManagementService.Domain.Interfaces;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using VMS.Contracts.Common.Authorization;

namespace InventoryManagementService.Application.Features.Inventory.Queries;

public class GetInventoryByBranchQuery : IRequest<List<InventoryItemDto>>
{
    public Guid BranchId { get; set; }
}

public class GetInventoryByBranchQueryHandler : IRequestHandler<GetInventoryByBranchQuery, List<InventoryItemDto>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly ILogger<GetInventoryByBranchQueryHandler> _logger;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public GetInventoryByBranchQueryHandler(
        IUnitOfWork unitOfWork,
        IMapper mapper,
        ILogger<GetInventoryByBranchQueryHandler> logger,
        IHttpContextAccessor httpContextAccessor)
    {
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _logger = logger;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<List<InventoryItemDto>> Handle(GetInventoryByBranchQuery request, CancellationToken cancellationToken)
    {
        // Check permissions - Admin roles bypass, others need explicit permission
        var user = _httpContextAccessor.HttpContext?.User;
        if (!PermissionHelper.IsAdmin(user))
        {
            PermissionHelper.ValidateAccess(user, "Inventory.View");
        }

        _logger.LogInformation("Getting inventory items for branch: {BranchId}", request.BranchId);

        var inventoryItems = await _unitOfWork.Inventory.GetByBranchAsync(request.BranchId);

        _logger.LogInformation("Found {Count} inventory items for branch {BranchId}", inventoryItems.Count, request.BranchId);

        return _mapper.Map<List<InventoryItemDto>>(inventoryItems);
    }
}
