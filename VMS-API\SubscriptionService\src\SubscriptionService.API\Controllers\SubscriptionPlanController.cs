using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using SubscriptionService.API.Models;
using SubscriptionService.Application.DTOs;
using SubscriptionService.Application.Services;
using SubscriptionService.Domain.Entities;
using SubscriptionService.Domain.Enums;
using SubscriptionService.Domain.Interfaces;
using SubscriptionService.Domain.ValueObjects;
using SubscriptionService.Infrastructure.Persistence;

namespace SubscriptionService.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class SubscriptionPlanController : ControllerBase
    {
        private readonly SubscriptionDbContext _context;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IPricingCalculationService _pricingService;

        public SubscriptionPlanController(
            SubscriptionDbContext context,
            IUnitOfWork unitOfWork,
            IPricingCalculationService pricingService)
        {
            _context = context;
            _unitOfWork = unitOfWork;
            _pricingService = pricingService;
        }

        [HttpGet]
        [Authorize(Policy = "SubscriptionPlans.View")]
        public async Task<ActionResult<IEnumerable<SubscriptionPlanDto>>> GetSubscriptionPlans([FromQuery] string? countryCode = null)
        {
            var plans = await _context.SubscriptionPlans
                .Include(p => p.Subscriptions)
                .Include(p => p.FeatureAccesses)
                .Include(p => p.CurrencyPricings)
                .ToListAsync();

            var planDtos = plans.Select(p => p.ToDto(countryCode)).ToList();
            return Ok(planDtos);
        }

        [HttpGet("active")]
        [AllowAnonymous] // Public endpoint for plan comparison
        public async Task<ActionResult<IEnumerable<SubscriptionPlanDto>>> GetActivePlans([FromQuery] string? countryCode = null)
        {
            var plans = await _context.SubscriptionPlans
                .Where(p => p.IsActive && p.IsMasterPlan)
                .Include(p => p.FeatureAccesses)
                .Include(p => p.CurrencyPricings)
                .OrderBy(p => p.Tier)
                .ToListAsync();

            var planDtos = plans.Select(p => p.ToDto(countryCode)).ToList();
            return Ok(planDtos);
        }

        [HttpGet("comparison")]
        [AllowAnonymous] // Public endpoint for plan comparison
        public async Task<ActionResult<object>> GetPlanComparison([FromQuery] string? countryCode = null)
        {
            var plans = await _context.SubscriptionPlans
                .Where(p => p.IsActive && p.IsMasterPlan && p.Tier != SubscriptionTier.Custom && p.Tier != SubscriptionTier.Trial)
                .Include(p => p.FeatureAccesses)
                .Include(p => p.CurrencyPricings)
                .OrderBy(p => p.Tier)
                .ToListAsync();

            // Create a comparison view with plan details
            var comparison = new
            {
                Plans = plans.Select(p =>
                {
                    var pricingOptions = p.GetAllPricingOptions(countryCode);
                    var currencyInfo = GetCurrencyInfo(p, countryCode);

                    return new
                    {
                        p.Id,
                        p.Name,
                        p.Description,
                        Tier = p.Tier.ToString(),
                        p.MaxBranches,
                        p.MaxUsers,
                        BaseMonthlyPrice = currencyInfo.BasePrice,
                        Currency = currencyInfo.CurrencyCode,
                        CountryCode = currencyInfo.CountryCode,
                        IsTrialPlan = p.IsTrialPlan,
                        TrialPeriodDays = p.TrialPeriodDays,
                        // Advanced features
                        AdvancedFeatures = new
                        {
                            p.AdvancedFeatures.CustomReports,
                            p.AdvancedFeatures.DocumentExport,
                            HasAnyAdvancedFeatures = p.AdvancedFeatures.HasAnyAdvancedFeatures()
                        },
                        // Billing cycle discounts
                        BillingDiscounts = new
                        {
                            p.BillingCycleDiscount.QuarterlyDiscountPercentage,
                            p.BillingCycleDiscount.YearlyDiscountPercentage,
                            p.BillingCycleDiscount.IsActive
                        },
                        // Include pricing options for different billing cycles
                        PricingOptions = pricingOptions.Select(po => new
                        {
                            BillingCycle = po.Key.ToString(),
                            Price = po.Value,
                            FormattedPrice = FormatPriceWithCurrency(po.Value, po.Key, currencyInfo.CurrencyCode),
                            DiscountPercentage = p.BillingCycleDiscount.GetDiscountPercentage(po.Key)
                        }),
                        // Include features
                        Features = p.FeatureAccesses
                            .Where(f => f.IsEnabled)
                            .Select(f => new
                            {
                                f.FeatureName,
                                f.IsEnabled,
                                f.UsageLimit
                            })
                    };
                })
            };

            return Ok(comparison);
        }

        private string FormatPriceWithCurrency(decimal price, BillingCycle billingCycle, string currencyCode = "INR")
        {
            string currencySymbol = currencyCode switch
            {
                "USD" => "$",
                "EUR" => "€",
                "INR" => "₹",
                "GBP" => "£",
                _ => currencyCode
            };

            string cycle = billingCycle switch
            {
                BillingCycle.Monthly => "month",
                BillingCycle.Quarterly => "quarter",
                BillingCycle.Yearly => "year",
                _ => "month"
            };

            return $"{currencySymbol}{price:N0}/{cycle}";
        }

        private (string CountryCode, string CurrencyCode, decimal BasePrice) GetCurrencyInfo(
            SubscriptionPlan plan, string? countryCode)
        {
            if (string.IsNullOrWhiteSpace(countryCode))
            {
                return (plan.DefaultCountryCode, plan.DefaultCurrencyCode, plan.BaseMonthlyPrice);
            }

            var currencyPricing = plan.CurrencyPricings.FirstOrDefault(cp =>
                cp.CountryCode.Equals(countryCode, StringComparison.OrdinalIgnoreCase) && cp.IsActive);

            if (currencyPricing != null)
            {
                return (currencyPricing.CountryCode, currencyPricing.CurrencyCode, currencyPricing.LocalizedMonthlyPrice);
            }

            return (plan.DefaultCountryCode, plan.DefaultCurrencyCode, plan.BaseMonthlyPrice);
        }

        [HttpGet("{id}")]
        [Authorize(Policy = "SubscriptionPlans.View")]
        public async Task<ActionResult<SubscriptionPlanDto>> GetSubscriptionPlan(Guid id, [FromQuery] string? countryCode = null)
        {
            var subscriptionPlan = await _context.SubscriptionPlans
                .Include(p => p.Subscriptions)
                .Include(p => p.FeatureAccesses)
                .Include(p => p.CurrencyPricings)
                .FirstOrDefaultAsync(p => p.Id == id);

            if (subscriptionPlan == null)
            {
                return NotFound();
            }

            var planDto = subscriptionPlan.ToDto(countryCode);
            return Ok(planDto);
        }

        [HttpGet("tier/{tier}")]
        [Authorize(Policy = "SubscriptionPlans.View")]
        public async Task<ActionResult<IEnumerable<SubscriptionPlanDto>>> GetSubscriptionPlansByTier(SubscriptionTier tier, [FromQuery] string? countryCode = null)
        {
            var plans = await _context.SubscriptionPlans
                .Include(p => p.Subscriptions)
                .Include(p => p.FeatureAccesses)
                .Include(p => p.CurrencyPricings)
                .Where(p => p.Tier == tier)
                .ToListAsync();

            var planDtos = plans.Select(p => p.ToDto(countryCode)).ToList();
            return Ok(planDtos);
        }

        [HttpGet("{id}/pricing")]
        [AllowAnonymous] // Public endpoint for pricing information
        public async Task<ActionResult<PricingResponse>> GetPlanPricing(Guid id, [FromQuery] string? countryCode = null)
        {
            var pricingResponse = await _pricingService.GetPricingByPlanIdAsync(id, countryCode);

            if (pricingResponse == null)
            {
                return NotFound($"Subscription plan with ID {id} not found");
            }

            return Ok(pricingResponse);
        }

        [HttpGet("{id}/pricing/{billingCycle}")]
        [AllowAnonymous] // Public endpoint for specific billing cycle pricing
        public async Task<ActionResult<PricingDetail>> GetSpecificPricing(Guid id, BillingCycle billingCycle, [FromQuery] string? countryCode = null)
        {
            var subscriptionPlan = await _unitOfWork.SubscriptionPlans.GetByIdAsync(id);

            if (subscriptionPlan == null)
            {
                return NotFound($"Subscription plan with ID {id} not found");
            }

            if (!string.IsNullOrEmpty(countryCode) && !_pricingService.IsCountrySupported(subscriptionPlan, countryCode))
            {
                return BadRequest($"Country code '{countryCode}' is not supported for this plan");
            }

            var pricingDetail = _pricingService.CalculateSpecificPricing(subscriptionPlan, billingCycle, countryCode);
            return Ok(pricingDetail);
        }

        [HttpPost]
        [Authorize(Roles = "SuperAdmin,VendorAdmin")]
        public async Task<ActionResult<SubscriptionPlanDto>> CreateSubscriptionPlan([FromBody] CreateSubscriptionPlanRequest request)
        {

            // Create advanced features configuration
            var advancedFeatures = request.AdvancedFeatures != null
                ? new AdvancedFeatures(request.AdvancedFeatures.CustomReports, request.AdvancedFeatures.DocumentExport)
                : new AdvancedFeatures(false, false);

            // Create billing cycle discount configuration
            var billingCycleDiscount = request.BillingCycleDiscount != null
                ? new BillingCycleDiscount(
                    request.BillingCycleDiscount.QuarterlyDiscountPercentage,
                    request.BillingCycleDiscount.YearlyDiscountPercentage,
                    request.BillingCycleDiscount.IsActive)
                : new BillingCycleDiscount(); // Default discounts

            // Create the subscription plan
            var subscriptionPlan = new SubscriptionPlan(
                request.Name,
                request.Description,
                request.Tier,
                request.MaxBranches,
                request.MaxUsers,
                request.BaseMonthlyPrice,
                request.IsTrialPlan,
                request.TrialPeriodDays,
                advancedFeatures,
                billingCycleDiscount,
                request.DefaultCountryCode,
                request.DefaultCurrencyCode
            );

            // Add feature accesses if provided
            if (request.FeatureAccesses != null)
            {
                foreach (var featureDto in request.FeatureAccesses)
                {
                    subscriptionPlan.AddFeatureAccess(new FeatureAccess(
                        featureDto.FeatureName,
                        featureDto.IsEnabled,
                        featureDto.UsageLimit));
                }
            }

            // Add currency pricings if provided
            if (request.CurrencyPricings != null)
            {
                foreach (var currencyDto in request.CurrencyPricings)
                {
                    subscriptionPlan.AddCurrencyPricing(new CurrencyPricing(
                        currencyDto.CountryCode,
                        currencyDto.CurrencyCode,
                        currencyDto.ExchangeRate,
                        currencyDto.LocalizedMonthlyPrice));
                }
            }

            // Set audit information
            var currentUser = User.Identity?.Name ?? "System";
            subscriptionPlan.SetCreatedBy(currentUser);

            _context.SubscriptionPlans.Add(subscriptionPlan);
            await _context.SaveChangesAsync();

            var planDto = subscriptionPlan.ToDto();
            return CreatedAtAction(nameof(GetSubscriptionPlan), new { id = subscriptionPlan.Id }, planDto);
        }

        [HttpPut("{id}")]
        [Authorize(Roles = "SuperAdmin,VendorAdmin")]
        public async Task<IActionResult> UpdateSubscriptionPlan(Guid id, [FromBody] UpdateSubscriptionPlanRequest request)
        {
            // Find the existing plan
            var existingPlan = await _context.SubscriptionPlans
                .FirstOrDefaultAsync(p => p.Id == id);

            if (existingPlan == null)
            {
                return NotFound();
            }



            // Create advanced features configuration
            var advancedFeatures = request.AdvancedFeatures != null
                ? new AdvancedFeatures(request.AdvancedFeatures.CustomReports, request.AdvancedFeatures.DocumentExport)
                : existingPlan.AdvancedFeatures.Copy();

            // Create billing cycle discount configuration
            var billingCycleDiscount = request.BillingCycleDiscount != null
                ? new BillingCycleDiscount(
                    request.BillingCycleDiscount.QuarterlyDiscountPercentage,
                    request.BillingCycleDiscount.YearlyDiscountPercentage,
                    request.BillingCycleDiscount.IsActive)
                : existingPlan.BillingCycleDiscount.Copy();

            // Create a new version of the plan instead of modifying the existing one
            // This ensures existing subscriptions keep their original plan details
            var newVersionPlan = SubscriptionPlan.CreateNewVersion(
                existingPlan,
                request.Name,
                request.Description,
                request.MaxBranches,
                request.MaxUsers,
                request.BaseMonthlyPrice,
                request.IsTrialPlan,
                request.TrialPeriodDays,
                advancedFeatures,
                billingCycleDiscount
            );

            // Update default location if provided
            if (!string.IsNullOrWhiteSpace(request.DefaultCountryCode) && !string.IsNullOrWhiteSpace(request.DefaultCurrencyCode))
            {
                newVersionPlan.UpdateDefaultLocation(request.DefaultCountryCode, request.DefaultCurrencyCode);
            }

            // Set audit information
            var currentUser = User.Identity?.Name ?? "System";
            newVersionPlan.SetCreatedBy(currentUser);
            newVersionPlan.Activate();

            // Deactivate the old plan
            existingPlan.Deactivate();
            existingPlan.SetUpdatedBy(currentUser);

            // Add the new plan
            _context.SubscriptionPlans.Add(newVersionPlan);

            await _context.SaveChangesAsync();

            var planDto = newVersionPlan.ToDto();
            return CreatedAtAction(nameof(GetSubscriptionPlan), new { id = newVersionPlan.Id }, planDto);
        }

        [HttpPost("custom")]
        [Authorize(Roles = "SuperAdmin,VendorAdmin")]
        public async Task<ActionResult<SubscriptionPlanDto>> CreateCustomPlan([FromBody] CustomPlanRequest request)
        {
            // Find the base plan with features and currency pricings
            var basePlan = await _context.SubscriptionPlans
                .Include(p => p.FeatureAccesses)
                .Include(p => p.CurrencyPricings)
                .FirstOrDefaultAsync(p => p.Id == request.BasePlanId && p.IsActive);

            if (basePlan == null)
            {
                return NotFound($"Base plan with ID {request.BasePlanId} not found or not active");
            }

            // Validate branch and user counts
            if (request.BranchCount <= 0)
            {
                return BadRequest("Branch count must be greater than zero");
            }

            if (request.UsersPerBranch <= 0)
            {
                return BadRequest("Users per branch must be greater than zero");
            }

            // Calculate price per user based on plan tier
            decimal pricePerUser = basePlan.Tier switch
            {
                SubscriptionTier.Basic => 100, // ₹100 per user for Basic
                SubscriptionTier.Silver => 150, // ₹150 per user for Silver
                SubscriptionTier.Gold => 200, // ₹200 per user for Gold
                _ => 100 // Default to ₹100 for other tiers
            };

            // Calculate adjusted per-branch price based on users per branch
            // Base price + additional cost for users beyond the standard allocation
            int standardUsersPerBranch = basePlan.Tier switch
            {
                SubscriptionTier.Basic => 5,  // Standard is 5 users per branch for Basic
                SubscriptionTier.Silver => 10, // Standard is 10 users per branch for Silver
                SubscriptionTier.Gold => 15,  // Standard is 15 users per branch for Gold
                _ => 5
            };

            // Calculate extra users beyond standard allocation
            int extraUsersPerBranch = Math.Max(0, request.UsersPerBranch - standardUsersPerBranch);

            // Calculate final per-branch monthly price including extra users
            decimal customBaseMonthlyPrice = basePlan.BaseMonthlyPrice + (extraUsersPerBranch * pricePerUser);

            // Create custom plan with the calculated base monthly price
            var customPlan = SubscriptionPlan.CreateCustomPlan(
                basePlan,
                request.BranchCount,
                request.UsersPerBranch,
                customBaseMonthlyPrice
            );

            // Copy currency pricings with adjusted prices
            foreach (var currencyPricing in basePlan.CurrencyPricings.Where(cp => cp.IsActive))
            {
                // Calculate the ratio of custom price to base price
                decimal priceRatio = customBaseMonthlyPrice / basePlan.BaseMonthlyPrice;
                decimal adjustedLocalizedPrice = currencyPricing.LocalizedMonthlyPrice * priceRatio;

                customPlan.AddCurrencyPricing(new CurrencyPricing(
                    currencyPricing.CountryCode,
                    currencyPricing.CurrencyCode,
                    currencyPricing.ExchangeRate,
                    adjustedLocalizedPrice));
            }

            // Set audit information
            var currentUser = User.Identity?.Name ?? "System";
            customPlan.SetCreatedBy(currentUser);

            _context.SubscriptionPlans.Add(customPlan);
            await _context.SaveChangesAsync();

            var planDto = customPlan.ToDto();
            return CreatedAtAction(nameof(GetSubscriptionPlan), new { id = customPlan.Id }, planDto);
        }

        [HttpPost("{id}/currency-pricing")]
        [Authorize(Roles = "SuperAdmin,VendorAdmin")]
        public async Task<IActionResult> AddCurrencyPricing(Guid id, [FromBody] CurrencyPricingDto request)
        {
            var subscriptionPlan = await _context.SubscriptionPlans
                .FirstOrDefaultAsync(p => p.Id == id);

            if (subscriptionPlan == null)
            {
                return NotFound($"Subscription plan with ID {id} not found");
            }

            // Validate currency pricing data
            if (string.IsNullOrWhiteSpace(request.CountryCode) || request.CountryCode.Length != 2)
            {
                return BadRequest("Country code must be a valid 2-character ISO code");
            }

            if (string.IsNullOrWhiteSpace(request.CurrencyCode) || request.CurrencyCode.Length != 3)
            {
                return BadRequest("Currency code must be a valid 3-character ISO code");
            }

            if (request.ExchangeRate <= 0)
            {
                return BadRequest("Exchange rate must be greater than zero");
            }

            if (request.LocalizedMonthlyPrice < 0)
            {
                return BadRequest("Localized monthly price cannot be negative");
            }

            // Add the currency pricing
            var currencyPricing = new CurrencyPricing(
                request.CountryCode.ToUpperInvariant(),
                request.CurrencyCode.ToUpperInvariant(),
                request.ExchangeRate,
                request.LocalizedMonthlyPrice);

            subscriptionPlan.AddCurrencyPricing(currencyPricing);

            // Set audit information
            var currentUser = User.Identity?.Name ?? "System";
            subscriptionPlan.SetUpdatedBy(currentUser);

            await _context.SaveChangesAsync();

            return Ok(new { Message = $"Currency pricing for {request.CountryCode} ({request.CurrencyCode}) added successfully" });
        }

        [HttpPut("{id}/billing-discounts")]
        [Authorize(Roles = "SuperAdmin,VendorAdmin")]
        public async Task<IActionResult> UpdateBillingDiscounts(Guid id, [FromBody] BillingCycleDiscountDto request)
        {
            var subscriptionPlan = await _context.SubscriptionPlans
                .FirstOrDefaultAsync(p => p.Id == id);

            if (subscriptionPlan == null)
            {
                return NotFound($"Subscription plan with ID {id} not found");
            }

            // Validate discount percentages
            if (request.QuarterlyDiscountPercentage < 0 || request.QuarterlyDiscountPercentage > 100)
            {
                return BadRequest("Quarterly discount percentage must be between 0 and 100");
            }

            if (request.YearlyDiscountPercentage < 0 || request.YearlyDiscountPercentage > 100)
            {
                return BadRequest("Yearly discount percentage must be between 0 and 100");
            }

            // Update billing cycle discounts
            var newBillingCycleDiscount = new BillingCycleDiscount(
                request.QuarterlyDiscountPercentage,
                request.YearlyDiscountPercentage,
                request.IsActive);

            subscriptionPlan.UpdateBillingCycleDiscount(newBillingCycleDiscount);

            // Set audit information
            var currentUser = User.Identity?.Name ?? "System";
            subscriptionPlan.SetUpdatedBy(currentUser);

            await _context.SaveChangesAsync();

            return Ok(new { Message = "Billing cycle discounts updated successfully" });
        }

        [HttpDelete("{id}")]
        [Authorize(Roles = "SuperAdmin")]
        public async Task<IActionResult> DeleteSubscriptionPlan(Guid id)
        {
            var subscriptionPlan = await _context.SubscriptionPlans.FindAsync(id);
            if (subscriptionPlan == null)
            {
                return NotFound();
            }

            // Instead of deleting, just deactivate the plan
            // This ensures existing subscriptions can still reference it
            subscriptionPlan.Deactivate();
            await _context.SaveChangesAsync();

            return NoContent();
        }

        private bool SubscriptionPlanExists(Guid id)
        {
            return _context.SubscriptionPlans.Any(e => e.Id == id);
        }
    }
}