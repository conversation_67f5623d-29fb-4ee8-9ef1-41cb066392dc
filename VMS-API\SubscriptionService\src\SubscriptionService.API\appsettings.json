{"AllowedOrigins": ["http://localhost:3000", "http://localhost:3001", "http://localhost:3002"], "ConnectionStrings": {"DefaultConnection": "Host=**************;Port=5432;Database=vms_subscriptionservice;Username=postgres;Password=************"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "Jwt": {"Key": "********************************", "Issuer": "identity-service", "Audience": "vms-api", "ExpiryMinutes": 60}, "RabbitMQ": {"HostName": "localhost", "UserName": "guest", "Password": "guest", "VirtualHost": "/"}, "ServiceUrls": {"IdentityService": "http://localhost:5263", "TenantManagementService": "http://localhost:5003"}, "AllowedHosts": "*"}