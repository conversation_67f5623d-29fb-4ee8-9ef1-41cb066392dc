import React, { useState } from "react";
import { Formik, Form, Field, ErrorMessage } from "formik";
import * as Yup from "yup";
import toast from "react-hot-toast";
import { useUpdateUserById } from "@api/useAuthHooks";
import { useSelector, useDispatch } from "react-redux";
import { setUser } from "@store/userSlice";

const EditProfileForm = ({ userData, onClose, onSuccess }) => {
  const dispatch = useDispatch();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const updateUserMutation = useUpdateUserById({
    onSuccess: (response) => {
      toast.success("Profile updated successfully!");
      // Update user data in Redux store
      dispatch(setUser(response.data));
      onSuccess?.(response.data);
      onClose();
    },
    onError: (error) => {
      toast.error(error.response?.data?.message || "Failed to update profile");
    },
  });

  const validationSchema = Yup.object({
    firstName: Yup.string()
      .min(2, "First name must be at least 2 characters")
      .max(50, "First name must be less than 50 characters")
      .required("First name is required"),
    lastName: Yup.string()
      .min(2, "Last name must be at least 2 characters")
      .max(50, "Last name must be less than 50 characters")
      .required("Last name is required"),
    phoneNumber: Yup.string()
      .matches(/^[0-9]{10}$/, "Phone number must be exactly 10 digits")
      .required("Phone number is required"),
  });

  const initialValues = {
    firstName: userData?.firstName || "",
    lastName: userData?.lastName || "",
    email: userData?.email || "",
    phoneNumber: userData?.phoneNumber || "",
  };

  const handleSubmit = async (values) => {
    setIsSubmitting(true);
    try {
      await updateUserMutation.mutateAsync({
        userId: userData.id,
        userData: {
          firstName: values.firstName,
          lastName: values.lastName,
          phoneNumber: values.phoneNumber,
          // Email is not included as it should be disabled
        },
      });
    } catch (error) {
      console.error("Error updating profile:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="sidebar-form">
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        enableReinitialize={true}
      >
        {({ errors, touched }) => (
          <Form>
            {/* Form Fields */}
            <div className="form-fields">
              {/* First Name */}
              <div className="form-group">
                <label className="form-label">
                  First Name <span className="asterisk">*</span>
                </label>
                <Field
                  type="text"
                  name="firstName"
                  className={`form-input ${
                    errors.firstName && touched.firstName ? "error" : ""
                  }`}
                  placeholder="Enter first name"
                />
                <ErrorMessage
                  name="firstName"
                  component="span"
                  className="error-text"
                />
              </div>

              {/* Last Name */}
              <div className="form-group">
                <label className="form-label">
                  Last Name <span className="asterisk">*</span>
                </label>
                <Field
                  type="text"
                  name="lastName"
                  className={`form-input ${
                    errors.lastName && touched.lastName ? "error" : ""
                  }`}
                  placeholder="Enter last name"
                />
                <ErrorMessage
                  name="lastName"
                  component="span"
                  className="error-text"
                />
              </div>

              {/* Email (Disabled) */}
              <div className="form-group">
                <label className="form-label">Email Address</label>
                <Field
                  type="email"
                  name="email"
                  className="form-input"
                  placeholder="Email address"
                  disabled
                  style={{
                    backgroundColor: "#f8f9fa",
                    cursor: "not-allowed",
                    opacity: 0.6,
                  }}
                />
                <small className="form-text text-muted">
                  Email address cannot be changed
                </small>
              </div>

              {/* Phone Number */}
              <div className="form-group">
                <label className="form-label">
                  Phone Number <span className="asterisk">*</span>
                </label>
                <div className="phone-input-container">
                  <div className="country-flag">
                    <img
                      src="https://flagcdn.com/w20/in.png"
                      alt="India"
                      className="flag-icon"
                    />
                    <span className="country-code">+91</span>
                  </div>
                  <Field
                    type="tel"
                    name="phoneNumber"
                    className={`form-input phone-input ${
                      errors.phoneNumber && touched.phoneNumber ? "error" : ""
                    }`}
                    placeholder="Enter phone number"
                    maxLength="10"
                  />
                </div>
                <ErrorMessage
                  name="phoneNumber"
                  component="span"
                  className="error-text"
                />
              </div>
            </div>

            {/* Form Footer */}
            <div className="form-footer">
              <button
                type="button"
                className="btn btn-outline-secondary"
                onClick={onClose}
                disabled={isSubmitting}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="btn btn-dark"
                disabled={isSubmitting}
              >
                {isSubmitting ? "Updating..." : "Update Profile"}
              </button>
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default EditProfileForm;
