using System.Threading;
using System.Threading.Tasks;
using IdentityService.Application.Common;
using IdentityService.Application.Interfaces;
using MediatR;
using Microsoft.Extensions.Logging;

namespace IdentityService.Application.Behaviors
{
    public class UserIdPropagationBehavior<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse>
        where TRequest : IRequest<TResponse>
    {
        private readonly IUserIdAccessor _userIdAccessor;
        private readonly ILogger<UserIdPropagationBehavior<TRequest, TResponse>> _logger;

        public UserIdPropagationBehavior(
            IUserIdAccessor userIdAccessor,
            ILogger<UserIdPropagationBehavior<TRequest, TResponse>> logger)
        {
            _userIdAccessor = userIdAccessor;
            _logger = logger;
        }

        public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
        {
            // If the request is a BaseRequest, set the UserId
            if (request is BaseRequest<TResponse> baseRequest)
            {
                var userId = _userIdAccessor.GetUserId();
                _logger.LogInformation("Setting UserId {UserId} on request of type {RequestType}", userId, typeof(TRequest).Name);
                baseRequest.SetUserId(_userIdAccessor);
            }
            else
            {
                _logger.LogInformation("Request of type {RequestType} is not a BaseRequest, skipping UserId propagation", typeof(TRequest).Name);
            }

            // Continue with the pipeline
            return await next();
        }
    }
}
