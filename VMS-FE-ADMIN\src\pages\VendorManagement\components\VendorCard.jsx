import React from "react";
// CSS moved to styles/custom.css - use vm-vendor-card-* classes
import { useNavigate } from "react-router-dom";
import {
  FaEnvelope,
  FaPhone,
  FaMapMarkerAlt,
  FaArrowRight,
  FaBuilding,
  FaCar,
  FaCheckCircle,
  FaTimesCircle,
  FaCrown,
  FaStar
} from "react-icons/fa";
import ROUTES from "@constants/routes";

const VendorCard = ({ vendor, onClick }) => {
  const navigate = useNavigate();

  const getStatusColor = (status) => {
    return status === "Active" ? "#22c55e" : "#ef4444";
  };

  const getSubscriptionColor = (plan) => {
    switch (plan) {
      case "Premium":
        return "#8b5cf6";
      case "Standard":
        return "#3b82f6";
      case "Basic":
        return "#64748b";
      default:
        return "#64748b";
    }
  };

  const getStatusIcon = (status) => {
    return status === "Active" ? FaCheckCircle : FaTimesCircle;
  };

  const getSubscriptionIcon = (plan) => {
    switch (plan) {
      case "Premium":
        return FaCrown;
      case "Standard":
        return FaStar;
      case "Basic":
        return FaBuilding;
      default:
        return FaBuilding;
    }
  };

  // Helper function to format address from vendor properties
  const formatAddress = (vendor) => {
    // Handle legacy address format (if address is a string)
    if (typeof vendor.address === "string" && vendor.address.trim()) {
      return vendor.address;
    }

    // Handle legacy nested address object format
    if (typeof vendor.address === "object" && vendor.address !== null) {
      const parts = [];
      if (vendor.address.street) parts.push(vendor.address.street);
      if (vendor.address.city) parts.push(vendor.address.city);
      if (vendor.address.state) parts.push(vendor.address.state);
      if (vendor.address.country) parts.push(vendor.address.country);
      if (vendor.address.pinCode) parts.push(vendor.address.pinCode);

      if (parts.length > 0) return parts.join(", ");
    }

    // Handle current flat vendor properties format (VendorDto)
    const parts = [];
    if (vendor.address && typeof vendor.address === "string") parts.push(vendor.address);
    if (vendor.city) parts.push(vendor.city);
    if (vendor.state) parts.push(vendor.state);
    if (vendor.country) parts.push(vendor.country);
    if (vendor.pinCode) parts.push(vendor.pinCode);

    return parts.length > 0 ? parts.join(", ") : "Address not available";
  };

  return (
    <div className="vm-vendor-card">
      {/* Header */}
      <div className="vm-vendor-card-top">
        <div className="vm-vendor-logo">
          {vendor.logo ? (
            <img src={vendor.logo} alt={vendor.name} />
          ) : (
            <div className="vm-vendor-logo-placeholder">
              {(
                vendor?.name ||
                vendor?.displayName ||
                vendor?.businessName ||
                "V"
              )
                ?.charAt(0)
                .toUpperCase()}
            </div>
          )}
        </div>
        <div className="vm-vendor-basic-info">
          <h3 className="vm-vendor-name">
            {vendor.name ||
              vendor.displayName ||
              vendor.businessName ||
              "Unknown Vendor"}
          </h3>
          <div className="vm-vendor-status">
            <span
              className="vm-status-badge-compact"
              style={{
                backgroundColor: getStatusColor(
                  vendor.status || (vendor.status ? "Active" : "Inactive")
                ),
              }}
            >
              {vendor.status || (vendor.status ? "Active" : "Inactive")}
            </span>
            <span
              className="vm-status-badge-compact"
              style={{
                backgroundColor: getSubscriptionColor(
                  vendor.currentSubscriptionPlanName || "Basic"
                ),
              }}
            >
              {vendor.currentSubscriptionPlanName || "Basic"}
            </span>
          </div>
        </div>
      </div>

      {/* Contact Info */}
      <div className="vm-vendor-contact">
        <div className="vm-contact-item">
          <FaEnvelope className="vm-contact-icon" />
          <span className="vm-contact-text">
            {vendor.email || vendor.contactEmail || "Email not available"}
          </span>
        </div>
        <div className="vm-contact-item">
          <FaPhone className="vm-contact-icon" />
          <span className="vm-contact-text">
            {vendor.phone ||
              vendor.phoneNumber ||
              vendor.contactPhone ||
              "Phone not available"}
          </span>
        </div>
        <div className="vm-contact-item">
          <FaMapMarkerAlt className="vm-contact-icon" />
          <span className="vm-contact-text">
            {formatAddress(vendor)}
          </span>
        </div>
      </div>

      {/* Compact Stats */}
      <div className="vm-vendor-stats">
        <div className="vm-vendor-stat-compact">
          <span className="vm-stat-value">
            {vendor?.totalBranchCountOfVendor || 0}
          </span>
          <span className="vm-stat-label">Branches</span>
        </div>
        <div className="vm-vendor-stat-compact">
          <span className="vm-stat-value">
            {vendor?.totalVehicleCountOfVendor || 0}
          </span>
          <span className="vm-stat-label">Total Vehicles</span>
        </div>
      </div>

      {/* Footer */}
      <div className="vm-vendor-footer">
        <span className="vm-vendor-badges">
          Registered:{" "}
          {vendor.registrationDate || vendor.createdAt || vendor.dateRegistered
            ? new Date(
                vendor.registrationDate ||
                  vendor.createdAt ||
                  vendor.dateRegistered
              ).toLocaleDateString()
            : "Date not available"}
        </span>
        <div className="vm-vendor-actions">
          <button
            className="vm-action-btn"
            onClick={() =>
              navigate(ROUTES?.VENDOR_DETAILS, {
                state: { vendor }, // passing vendor as location state
              })
            }
          >
            <FaArrowRight />
          </button>
        </div>
      </div>
    </div>
  );
};

export default VendorCard;
