using AutoMapper;
using BranchManagementService.Application.DTOs;
using BranchManagementService.Domain.Interfaces;
using MediatR;
using Microsoft.Extensions.Logging;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace BranchManagementService.Application.Features.Branches.Queries
{
    public class GetBranchByIdQuery : IRequest<BranchDto>
    {
        public Guid Id { get; set; }
    }

    public class GetBranchByIdQueryHandler : IRequestHandler<GetBranchByIdQuery, BranchDto>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly ILogger<GetBranchByIdQueryHandler> _logger;

        public GetBranchByIdQueryHandler(
            IUnitOfWork unitOfWork,
            IMapper mapper,
            ILogger<GetBranchByIdQueryHandler> logger)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task<BranchDto> Handle(GetBranchByIdQuery request, CancellationToken cancellationToken)
        {
            try
            {
                var branch = await _unitOfWork.Branches.GetByIdAsync(request.Id);
                if (branch == null)
                {
                    throw new KeyNotFoundException($"Branch with ID {request.Id} not found");
                }

                return _mapper.Map<BranchDto>(branch);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting branch with ID {BranchId}", request.Id);
                throw;
            }
        }
    }
}
