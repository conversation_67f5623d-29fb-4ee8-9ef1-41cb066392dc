using InventoryManagementService.Domain.Enums;

namespace InventoryManagementService.Application.DTOs;

public class PartDto
{
    public Guid Id { get; set; }
    public string SKU { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Manufacturer { get; set; } = string.Empty;
    public decimal CostPrice { get; set; }
    public decimal RetailPrice { get; set; }
    public PartCategory Category { get; set; }
    public string? PartNumber { get; set; }
    public string? Barcode { get; set; }
    public int? WarrantyMonths { get; set; }
    public string? ImageUrl { get; set; }
    public string? Notes { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public DateTime? UpdatedAt { get; set; }
    public string? UpdatedBy { get; set; }
    
    public List<PartCompatibilityDto> Compatibilities { get; set; } = new();
}

public class PartCompatibilityDto
{
    public Guid Id { get; set; }
    public Guid PartId { get; set; }
    public string Make { get; set; } = string.Empty;
    public string Model { get; set; } = string.Empty;
    public int YearFrom { get; set; }
    public int YearTo { get; set; }
    public string? EngineType { get; set; }
    public string? Variant { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
}

public class CreatePartDto
{
    public string SKU { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Manufacturer { get; set; } = string.Empty;
    public decimal CostPrice { get; set; }
    public decimal RetailPrice { get; set; }
    public PartCategory Category { get; set; }
    public string? PartNumber { get; set; }
    public string? Barcode { get; set; }
    public int? WarrantyMonths { get; set; }
    public string? ImageUrl { get; set; }
    public string? Notes { get; set; }
}

public class UpdatePartDto
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Manufacturer { get; set; } = string.Empty;
    public decimal CostPrice { get; set; }
    public decimal RetailPrice { get; set; }
    public PartCategory Category { get; set; }
    public string? PartNumber { get; set; }
    public string? Barcode { get; set; }
    public int? WarrantyMonths { get; set; }
    public string? ImageUrl { get; set; }
    public string? Notes { get; set; }
}

public class AddPartCompatibilityDto
{
    public string Make { get; set; } = string.Empty;
    public string Model { get; set; } = string.Empty;
    public int YearFrom { get; set; }
    public int YearTo { get; set; }
    public string? EngineType { get; set; }
    public string? Variant { get; set; }
}
