using AutoMapper;
using InventoryManagementService.Application.DTOs;
using InventoryManagementService.Domain.Interfaces;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using VMS.Contracts.Common.Authorization;

namespace InventoryManagementService.Application.Features.Inventory.Queries;

public class GetLowStockItemsQuery : IRequest<List<InventoryItemDto>>
{
    public Guid BranchId { get; set; }
}

public class GetLowStockItemsQueryHandler : IRequestHandler<GetLowStockItemsQuery, List<InventoryItemDto>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly ILogger<GetLowStockItemsQueryHandler> _logger;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public GetLowStockItemsQueryHandler(
        IUnitOfWork unitOfWork,
        IMapper mapper,
        ILogger<GetLowStockItemsQueryHandler> logger,
        IHttpContextAccessor httpContextAccessor)
    {
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _logger = logger;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<List<InventoryItemDto>> Handle(GetLowStockItemsQuery request, CancellationToken cancellationToken)
    {
        // Check permissions - Admin roles bypass, others need explicit permission
        var user = _httpContextAccessor.HttpContext?.User;
        if (!PermissionHelper.IsAdmin(user))
        {
            PermissionHelper.ValidateAccess(user, "Inventory.View");
        }

        _logger.LogInformation("Getting low stock items for branch: {BranchId}", request.BranchId);

        var lowStockItems = await _unitOfWork.Inventory.GetLowStockItemsAsync(request.BranchId);

        _logger.LogInformation("Found {Count} low stock items for branch {BranchId}", lowStockItems.Count, request.BranchId);

        return _mapper.Map<List<InventoryItemDto>>(lowStockItems);
    }
}
