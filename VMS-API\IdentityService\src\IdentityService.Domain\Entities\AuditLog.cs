using System;
using IdentityService.Domain.Common;

namespace IdentityService.Domain.Entities;

public class AuditLog : BaseEntity
{
    public string Action { get; private set; }
    public string EntityName { get; private set; }
    public string EntityId { get; private set; }
    public string OldValues { get; private set; }
    public string NewValues { get; private set; }
    public string AffectedColumns { get; private set; }
    public string IpAddress { get; private set; }
    public string UserAgent { get; private set; }
    public Guid UserId { get; private set; }
    public User User { get; private set; }

    private AuditLog()
    {
        Action = string.Empty;
        EntityName = string.Empty;
        EntityId = string.Empty;
        OldValues = string.Empty;
        NewValues = string.Empty;
        AffectedColumns = string.Empty;
        IpAddress = string.Empty;
        UserAgent = string.Empty;
    }

    public static AuditLog Create(
        string action,
        string entityName,
        string entityId,
        string oldValues,
        string newValues,
        string affectedColumns,
        string ipAddress,
        string userAgent,
        Guid userId,
        User user,
        string createdBy)
    {
        return new AuditLog
        {
            Action = action,
            EntityName = entityName,
            EntityId = entityId,
            OldValues = oldValues,
            NewValues = newValues,
            AffectedColumns = affectedColumns,
            IpAddress = ipAddress,
            UserAgent = userAgent,
            UserId = userId,
            User = user,
            CreatedBy = createdBy
        };
    }
} 