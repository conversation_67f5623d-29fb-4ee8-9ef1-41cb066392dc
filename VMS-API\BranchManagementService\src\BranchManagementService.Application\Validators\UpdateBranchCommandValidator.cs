using BranchManagementService.Application.Features.Branches.Commands;
using FluentValidation;
using System.Text.RegularExpressions;

namespace BranchManagementService.Application.Validators
{
    public class UpdateBranchCommandValidator : AbstractValidator<UpdateBranchCommand>
    {
        public UpdateBranchCommandValidator()
        {
            RuleFor(b => b.Id)
                .NotEmpty().WithMessage("Branch ID is required");

            RuleFor(b => b.Name)
                .NotEmpty().WithMessage("Branch name is required")
                .MaximumLength(100).WithMessage("Branch name must not exceed 100 characters");

            RuleFor(b => b.Email)
                .NotEmpty().WithMessage("Email is required")
                .EmailAddress().WithMessage("A valid email address is required")
                .MaximumLength(100).WithMessage("Email must not exceed 100 characters");

            RuleFor(b => b.ContactPerson)
                .NotEmpty().WithMessage("Contact person is required")
                .MaximumLength(100).WithMessage("Contact person must not exceed 100 characters");

            RuleFor(b => b.MobileNumber)
                .NotEmpty().WithMessage("Mobile number is required")
                .Matches(new Regex(@"^\d{10}$")).WithMessage("Mobile number must be 10 digits");

            RuleFor(b => b.Address)
                .NotEmpty().WithMessage("Address is required")
                .MaximumLength(200).WithMessage("Address must not exceed 200 characters");

            RuleFor(b => b.City)
                .NotEmpty().WithMessage("City is required")
                .MaximumLength(50).WithMessage("City must not exceed 50 characters");

            RuleFor(b => b.State)
                .NotEmpty().WithMessage("State is required")
                .MaximumLength(50).WithMessage("State must not exceed 50 characters");

            RuleFor(b => b.ZipCode)
                .NotEmpty().WithMessage("Zip code is required")
                .Matches(new Regex(@"^\d{5,6}$")).WithMessage("Zip code must be 5-6 digits");
        }
    }
}
