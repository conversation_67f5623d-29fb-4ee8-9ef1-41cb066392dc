using InventoryManagementService.Domain.Entities;
using InventoryManagementService.Domain.Enums;
using InventoryManagementService.Domain.Interfaces;
using InventoryManagementService.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;

namespace InventoryManagementService.Infrastructure.Repositories;

public class StockTransferRepository : IStockTransferRepository
{
    private readonly InventoryDbContext _context;

    public StockTransferRepository(InventoryDbContext context)
    {
        _context = context;
    }

    public async Task<StockTransfer?> GetByIdAsync(Guid id)
    {
        return await _context.StockTransfers
            .Include(st => st.Items)
                .ThenInclude(sti => sti.Part)
            .FirstOrDefaultAsync(st => st.Id == id);
    }

    public async Task<StockTransfer?> GetByTransferNumberAsync(string transferNumber)
    {
        return await _context.StockTransfers
            .Include(st => st.Items)
                .ThenInclude(sti => sti.Part)
            .FirstOrDefaultAsync(st => st.TransferNumber == transferNumber);
    }

    public async Task<List<StockTransfer>> GetAllAsync()
    {
        return await _context.StockTransfers
            .Include(st => st.Items)
                .ThenInclude(sti => sti.Part)
            .Where(st => st.IsActive)
            .OrderByDescending(st => st.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<StockTransfer>> GetBySourceBranchAsync(Guid sourceBranchId)
    {
        return await _context.StockTransfers
            .Include(st => st.Items)
                .ThenInclude(sti => sti.Part)
            .Where(st => st.SourceBranchId == sourceBranchId && st.IsActive)
            .OrderByDescending(st => st.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<StockTransfer>> GetByDestinationBranchAsync(Guid destinationBranchId)
    {
        return await _context.StockTransfers
            .Include(st => st.Items)
                .ThenInclude(sti => sti.Part)
            .Where(st => st.DestinationBranchId == destinationBranchId && st.IsActive)
            .OrderByDescending(st => st.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<StockTransfer>> GetByVendorAsync(Guid vendorId)
    {
        return await _context.StockTransfers
            .Include(st => st.Items)
                .ThenInclude(sti => sti.Part)
            .Where(st => st.VendorId == vendorId && st.IsActive)
            .OrderByDescending(st => st.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<StockTransfer>> GetByStatusAsync(TransferStatus status)
    {
        return await _context.StockTransfers
            .Include(st => st.Items)
                .ThenInclude(sti => sti.Part)
            .Where(st => st.Status == status && st.IsActive)
            .OrderByDescending(st => st.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<StockTransfer>> GetPendingTransfersAsync()
    {
        return await _context.StockTransfers
            .Include(st => st.Items)
                .ThenInclude(sti => sti.Part)
            .Where(st => st.Status == TransferStatus.Pending && st.IsActive)
            .OrderBy(st => st.RequestedDate)
            .ToListAsync();
    }

    public async Task<List<StockTransfer>> GetAsync(Expression<Func<StockTransfer, bool>> predicate)
    {
        return await _context.StockTransfers
            .Include(st => st.Items)
                .ThenInclude(sti => sti.Part)
            .Where(predicate)
            .OrderByDescending(st => st.CreatedAt)
            .ToListAsync();
    }

    public async Task AddAsync(StockTransfer stockTransfer)
    {
        await _context.StockTransfers.AddAsync(stockTransfer);
    }

    public async Task UpdateAsync(StockTransfer stockTransfer)
    {
        _context.StockTransfers.Update(stockTransfer);
        await Task.CompletedTask;
    }

    public async Task DeleteAsync(Guid id)
    {
        var stockTransfer = await _context.StockTransfers.FindAsync(id);
        if (stockTransfer != null)
        {
            _context.StockTransfers.Remove(stockTransfer);
        }
    }

    public async Task<bool> ExistsAsync(Guid id)
    {
        return await _context.StockTransfers.AnyAsync(st => st.Id == id);
    }

    public async Task<bool> TransferNumberExistsAsync(string transferNumber, Guid? excludeId = null)
    {
        var query = _context.StockTransfers.Where(st => st.TransferNumber == transferNumber);
        
        if (excludeId.HasValue)
        {
            query = query.Where(st => st.Id != excludeId.Value);
        }

        return await query.AnyAsync();
    }
}
