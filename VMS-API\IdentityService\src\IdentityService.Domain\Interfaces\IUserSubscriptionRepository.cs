using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using IdentityService.Domain.Entities;
using IdentityService.Domain.Enums;

namespace IdentityService.Domain.Interfaces;

public interface IUserSubscriptionRepository : IGenericRepository<UserSubscription>
{
    Task<UserSubscription?> GetBySubscriptionIdAsync(Guid subscriptionId);
    Task<List<UserSubscription>> GetByUserIdAsync(Guid userId);
    Task<List<UserSubscription>> GetActiveSubscriptionsAsync();
    Task<List<UserSubscription>> GetSubscriptionsByStatusAsync(SubscriptionStatus status);
    Task<List<UserSubscription>> GetSubscriptionsByTierAsync(SubscriptionTier tier);
    Task<bool> AddFeatureAsync(Guid subscriptionId, string featureName, bool isEnabled, int? usageLimit);
    Task<bool> UpdateFeatureAsync(Guid subscriptionId, string featureName, bool isEnabled, int? usageLimit);
    Task<bool> RemoveFeatureAsync(Guid subscriptionId, string featureName);
}
