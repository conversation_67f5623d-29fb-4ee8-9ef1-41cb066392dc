using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using IdentityService.Application.DTOs.Subscriptions;
using IdentityService.Domain.Interfaces;
using MediatR;

namespace IdentityService.Application.Features.Subscriptions.Queries;

public class GetUserSubscriptionsQuery : IRequest<List<UserSubscriptionResponse>>
{
    public Guid UserId { get; set; }
}

public class GetUserSubscriptionsQueryHandler : IRequestHandler<GetUserSubscriptionsQuery, List<UserSubscriptionResponse>>
{
    private readonly IUnitOfWork _unitOfWork;

    public GetUserSubscriptionsQueryHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
    }

    public async Task<List<UserSubscriptionResponse>> Handle(GetUserSubscriptionsQuery query, CancellationToken cancellationToken)
    {
        var subscriptions = await _unitOfWork.UserSubscriptionRepository.GetByUserIdAsync(query.UserId);

        return subscriptions.Select(s => new UserSubscriptionResponse
        {
            Id = s.Id,
            UserId = s.UserId,
            SubscriptionId = s.SubscriptionId,
            PlanName = s.PlanName,
            Tier = s.Tier,
            StartDate = s.StartDate,
            EndDate = s.EndDate,
            TrialEndDate = s.TrialEndDate,
            Status = s.Status,
            IsActive = s.IsActive(),
            Features = s.Features.Select(f => new SubscriptionFeatureResponse
            {
                Id = f.Id,
                FeatureName = f.FeatureName,
                IsEnabled = f.IsEnabled,
                UsageLimit = f.UsageLimit
            }).ToList(),
            CreatedAt = s.CreatedAt,
            CreatedBy = s.CreatedBy,
            UpdatedAt = s.UpdatedAt,
            UpdatedBy = s.UpdatedBy ?? string.Empty
        }).ToList();
    }
}
