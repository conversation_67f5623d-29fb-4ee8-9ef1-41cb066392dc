<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <UserSecretsId>07694a2c-f1af-49be-9cc1-4ac8149a0332</UserSecretsId>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.2"/>
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.2"/>
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="8.0.2"/>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.2"/>
    <PackageReference Include="MassTransit" Version="8.1.3"/>
    <PackageReference Include="MassTransit.RabbitMQ" Version="8.1.3"/>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.2"/>
    <PackageReference Include="Microsoft.AspNetCore.WebUtilities" Version="8.0.2"/>
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.2"/>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.2"/>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\IdentityService.Application\IdentityService.Application.csproj"/>
    <ProjectReference Include="..\..\..\VMSContracts\VMSContracts.csproj"/>
  </ItemGroup>
</Project>