2025-05-19 11:12:36.352 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'AuditLog'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-19 11:12:36.420 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'PasswordResetToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-19 11:12:36.421 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserBranch'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-19 11:12:36.421 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserRole'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-19 11:12:36.421 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserSession'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-19 11:12:36.421 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserSubscription'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-19 11:12:37.445 +05:30 [INF] Executed DbCommand (88ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-05-19 11:12:37.476 +05:30 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-05-19 11:12:37.583 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-05-19 11:12:37.704 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-05-19 11:12:37.705 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-05-19 11:12:37.730 +05:30 [INF] No migrations were applied. The database is already up to date.
2025-05-19 11:12:37.863 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-19 11:12:38.220 +05:30 [INF] Configured endpoint SubscriptionCreated, Consumer: IdentityService.Infrastructure.Messaging.Consumers.SubscriptionCreatedConsumer
2025-05-19 11:12:38.230 +05:30 [INF] Configured endpoint SubscriptionStatusChanged, Consumer: IdentityService.Infrastructure.Messaging.Consumers.SubscriptionStatusChangedConsumer
2025-05-19 11:12:38.237 +05:30 [INF] Configured endpoint VendorRegistered, Consumer: IdentityService.Infrastructure.Messaging.Consumers.VendorRegisteredConsumer
2025-05-19 11:12:38.757 +05:30 [DBG] Starting bus instances: IBus
2025-05-19 11:12:38.763 +05:30 [DBG] Starting bus: "rabbitmq://localhost/"
2025-05-19 11:12:38.807 +05:30 [INF] Session cleanup service is starting
2025-05-19 11:12:38.809 +05:30 [DBG] Starting session cleanup
2025-05-19 11:12:38.857 +05:30 [DBG] Connect: guest@localhost:5672/
2025-05-19 11:12:39.175 +05:30 [DBG] Connected: guest@localhost:5672/ (address: amqp://localhost:5672, local: 52708)
2025-05-19 11:12:39.246 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/DESKTOPI7D4M6U_IdentityServiceAPI_bus_gbooyyfpkikqnyqhbdq3pf9sf4?temporary=true"
2025-05-19 11:12:39.274 +05:30 [DBG] Declare queue: name: SubscriptionStatusChanged, durable, consumer-count: 0 message-count: 0
2025-05-19 11:12:39.274 +05:30 [DBG] Declare queue: name: VendorRegistered, durable, consumer-count: 0 message-count: 0
2025-05-19 11:12:39.274 +05:30 [DBG] Declare queue: name: SubscriptionCreated, durable, consumer-count: 0 message-count: 0
2025-05-19 11:12:39.288 +05:30 [DBG] Declare exchange: name: VendorRegistered, type: fanout, durable
2025-05-19 11:12:39.288 +05:30 [DBG] Declare exchange: name: SubscriptionCreated, type: fanout, durable
2025-05-19 11:12:39.288 +05:30 [DBG] Declare exchange: name: SubscriptionStatusChanged, type: fanout, durable
2025-05-19 11:12:39.298 +05:30 [DBG] Declare exchange: name: VMSContracts.Subscription.Events:SubscriptionCreated, type: fanout, durable
2025-05-19 11:12:39.298 +05:30 [DBG] Declare exchange: name: VMSContracts.Tenant.Events:VendorRegistered, type: fanout, durable
2025-05-19 11:12:39.298 +05:30 [DBG] Declare exchange: name: VMSContracts.Subscription.Events:SubscriptionStatusChanged, type: fanout, durable
2025-05-19 11:12:39.318 +05:30 [DBG] Bind queue: source: SubscriptionCreated, destination: SubscriptionCreated
2025-05-19 11:12:39.319 +05:30 [DBG] Bind queue: source: VendorRegistered, destination: VendorRegistered
2025-05-19 11:12:39.318 +05:30 [DBG] Bind queue: source: SubscriptionStatusChanged, destination: SubscriptionStatusChanged
2025-05-19 11:12:39.345 +05:30 [DBG] Bind exchange: source: VMSContracts.Subscription.Events:SubscriptionStatusChanged, destination: SubscriptionStatusChanged
2025-05-19 11:12:39.345 +05:30 [DBG] Bind exchange: source: VMSContracts.Tenant.Events:VendorRegistered, destination: VendorRegistered
2025-05-19 11:12:39.345 +05:30 [DBG] Bind exchange: source: VMSContracts.Subscription.Events:SubscriptionCreated, destination: SubscriptionCreated
2025-05-19 11:12:39.395 +05:30 [DBG] Consumer Ok: "rabbitmq://localhost/VendorRegistered" - amq.ctag-GNTQvyRV70wy-UQnGMdQmA
2025-05-19 11:12:39.398 +05:30 [DBG] Consumer Ok: "rabbitmq://localhost/SubscriptionCreated" - amq.ctag-E82T84SBVe-lSx26oFP8Tg
2025-05-19 11:12:39.398 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/VendorRegistered"
2025-05-19 11:12:39.398 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/SubscriptionCreated"
2025-05-19 11:12:39.399 +05:30 [DBG] Consumer Ok: "rabbitmq://localhost/SubscriptionStatusChanged" - amq.ctag-VWE3zJXNu42mllhl0UFa0g
2025-05-19 11:12:39.400 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/SubscriptionStatusChanged"
2025-05-19 11:12:39.405 +05:30 [INF] Bus started: "rabbitmq://localhost/"
2025-05-19 11:12:39.647 +05:30 [INF] Executed DbCommand (35ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 11:12:39.702 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 11:12:39.769 +05:30 [INF] Now listening on: http://localhost:5263
2025-05-19 11:12:39.770 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-05-19 11:12:39.770 +05:30 [INF] Hosting environment: Development
2025-05-19 11:12:39.770 +05:30 [INF] Content root path: E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.API
2025-05-19 11:12:40.326 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/swagger/index.html - null null
2025-05-19 11:12:40.416 +05:30 [INF] Expired session "03218d62-0da9-42ed-8648-42ebfb654ac7" for user "849d8d7d-8c16-4125-be2d-e40d6eaa9e7a"
2025-05-19 11:12:40.430 +05:30 [WRN] HttpContext is null
2025-05-19 11:12:40.564 +05:30 [INF] Executed DbCommand (24ms) [Parameters=[@p17='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?', @p2='?', @p3='?', @p4='?' (DbType = DateTime), @p5='?' (DbType = DateTime), @p6='?', @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime), @p9='?', @p18='?' (DbType = Binary), @p10='?' (DbType = DateTime), @p11='?' (DbType = Int32), @p12='?', @p13='?' (DbType = DateTime), @p14='?', @p15='?', @p16='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
UPDATE "UserSessions" SET "CreatedAt" = @p0, "CreatedBy" = @p1, "DeviceInfo" = @p2, "EndReason" = @p3, "EndedAt" = @p4, "ExpiresAt" = @p5, "IpAddress" = @p6, "IsDeleted" = @p7, "LastActiveAt" = @p8, "RefreshToken" = @p9, "StartedAt" = @p10, "Status" = @p11, "Token" = @p12, "UpdatedAt" = @p13, "UpdatedBy" = @p14, "UserAgent" = @p15, "UserId" = @p16
WHERE "Id" = @p17 AND "RowVersion" IS NULL
RETURNING "RowVersion";
2025-05-19 11:12:40.574 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/swagger/index.html - 200 null text/html;charset=utf-8 251.1036ms
2025-05-19 11:12:40.617 +05:30 [INF] Expired 1 idle or expired sessions
2025-05-19 11:12:40.621 +05:30 [DBG] Session cleanup completed
2025-05-19 11:12:40.913 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/swagger/v1/swagger.json - null null
2025-05-19 11:12:41.187 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 273.8394ms
2025-05-19 11:17:40.656 +05:30 [DBG] Starting session cleanup
2025-05-19 11:17:41.736 +05:30 [INF] Executed DbCommand (39ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 11:17:41.771 +05:30 [INF] Executed DbCommand (34ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 11:17:41.772 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 11:17:41.773 +05:30 [DBG] Session cleanup completed
2025-05-19 11:22:41.782 +05:30 [DBG] Starting session cleanup
2025-05-19 11:22:41.806 +05:30 [INF] Executed DbCommand (4ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 11:22:41.817 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 11:22:41.818 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 11:22:41.818 +05:30 [DBG] Session cleanup completed
2025-05-19 11:27:41.825 +05:30 [DBG] Starting session cleanup
2025-05-19 11:27:42.013 +05:30 [INF] Executed DbCommand (8ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 11:27:42.017 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 11:27:42.017 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 11:27:42.017 +05:30 [DBG] Session cleanup completed
2025-05-19 11:32:42.025 +05:30 [DBG] Starting session cleanup
2025-05-19 11:32:42.048 +05:30 [INF] Executed DbCommand (9ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 11:32:42.086 +05:30 [INF] Executed DbCommand (26ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 11:32:42.087 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 11:32:42.087 +05:30 [DBG] Session cleanup completed
2025-05-19 11:37:42.087 +05:30 [DBG] Starting session cleanup
2025-05-19 11:37:42.193 +05:30 [INF] Executed DbCommand (8ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 11:37:42.197 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 11:37:42.198 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 11:37:42.198 +05:30 [DBG] Session cleanup completed
2025-05-19 11:42:42.212 +05:30 [DBG] Starting session cleanup
2025-05-19 11:42:42.237 +05:30 [INF] Executed DbCommand (23ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 11:42:42.265 +05:30 [INF] Executed DbCommand (26ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 11:42:42.266 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 11:42:42.266 +05:30 [DBG] Session cleanup completed
2025-05-19 11:47:42.275 +05:30 [DBG] Starting session cleanup
2025-05-19 11:47:42.361 +05:30 [INF] Executed DbCommand (11ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 11:47:42.366 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 11:47:42.366 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 11:47:42.366 +05:30 [DBG] Session cleanup completed
2025-05-19 11:52:42.398 +05:30 [DBG] Starting session cleanup
2025-05-19 11:52:42.455 +05:30 [INF] Executed DbCommand (54ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 11:52:42.471 +05:30 [INF] Executed DbCommand (13ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 11:52:42.471 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 11:52:42.471 +05:30 [DBG] Session cleanup completed
2025-05-19 11:53:12.733 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/api/Test/token - null null
2025-05-19 11:53:12.810 +05:30 [WRN] Failed to determine the https port for redirect.
2025-05-19 11:53:14.066 +05:30 [INF] Token received and is being processed
2025-05-19 11:53:14.079 +05:30 [WRN] User is null or not authenticated
2025-05-19 11:53:14.079 +05:30 [WRN] No user ID found in token
2025-05-19 11:53:14.081 +05:30 [INF] Executing endpoint 'IdentityService.API.Controllers.TestController.GenerateTestToken (IdentityService.API)'
2025-05-19 11:53:14.103 +05:30 [INF] Route matched with {action = "GenerateTestToken", controller = "Test"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GenerateTestToken() on controller IdentityService.API.Controllers.TestController (IdentityService.API).
2025-05-19 11:53:14.205 +05:30 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType3`3[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-05-19 11:53:14.239 +05:30 [INF] Executed action IdentityService.API.Controllers.TestController.GenerateTestToken (IdentityService.API) in 129.3216ms
2025-05-19 11:53:14.239 +05:30 [INF] Executed endpoint 'IdentityService.API.Controllers.TestController.GenerateTestToken (IdentityService.API)'
2025-05-19 11:53:14.241 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/api/Test/token - 200 null application/json; charset=utf-8 1508.1027ms
2025-05-19 11:53:46.012 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/api/Test/user-id - null null
2025-05-19 11:53:46.018 +05:30 [INF] Token received and is being processed
2025-05-19 11:53:46.077 +05:30 [INF] Token validated successfully with claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: f8711437-03bc-4057-9640-c11fb6d7c830, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: f8711437-03bc-4057-9640-c11fb6d7c830, jti: c5e88c79-d679-4c0c-be1a-7c7c7e4831d6, http://schemas.microsoft.com/ws/2008/06/identity/claims/role: Admin, http://schemas.microsoft.com/ws/2008/06/identity/claims/role: User, permission: test.permission, nbf: 1747635794, exp: 1747639394, iss: identity-service, aud: vms-api
2025-05-19 11:53:46.078 +05:30 [INF] NameIdentifier claim found: f8711437-03bc-4057-9640-c11fb6d7c830
2025-05-19 11:53:46.325 +05:30 [INF] Executed DbCommand (15ms) [Parameters=[@__token_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Token" = @__token_0
LIMIT 1
2025-05-19 11:53:46.327 +05:30 [WRN] Session with token eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.I6_CtB-Ja6yd6DkII4xzMy-GxcFHmMwxe5wY2CFinD4 not found
2025-05-19 11:53:46.327 +05:30 [WRN] Invalid session for token eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.I6_CtB-Ja6yd6DkII4xzMy-GxcFHmMwxe5wY2CFinD4
2025-05-19 11:53:46.340 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/api/Test/user-id - 401 null application/json; charset=utf-8 327.9522ms
2025-05-19 11:57:27.360 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5263/api/Users - application/json 221
2025-05-19 11:57:27.365 +05:30 [INF] Token received and is being processed
2025-05-19 11:57:27.367 +05:30 [INF] Token validated successfully with claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: f8711437-03bc-4057-9640-c11fb6d7c830, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: f8711437-03bc-4057-9640-c11fb6d7c830, jti: c5e88c79-d679-4c0c-be1a-7c7c7e4831d6, http://schemas.microsoft.com/ws/2008/06/identity/claims/role: Admin, http://schemas.microsoft.com/ws/2008/06/identity/claims/role: User, permission: test.permission, nbf: 1747635794, exp: 1747639394, iss: identity-service, aud: vms-api
2025-05-19 11:57:27.367 +05:30 [INF] NameIdentifier claim found: f8711437-03bc-4057-9640-c11fb6d7c830
2025-05-19 11:57:27.384 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__token_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Token" = @__token_0
LIMIT 1
2025-05-19 11:57:27.389 +05:30 [WRN] Session with token eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.I6_CtB-Ja6yd6DkII4xzMy-GxcFHmMwxe5wY2CFinD4 not found
2025-05-19 11:57:27.393 +05:30 [WRN] Invalid session for token eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.I6_CtB-Ja6yd6DkII4xzMy-GxcFHmMwxe5wY2CFinD4
2025-05-19 11:57:27.395 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5263/api/Users - 401 null application/json; charset=utf-8 34.5614ms
2025-05-19 11:57:42.472 +05:30 [DBG] Starting session cleanup
2025-05-19 11:57:42.478 +05:30 [INF] Executed DbCommand (4ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 11:57:42.488 +05:30 [INF] Executed DbCommand (9ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 11:57:42.489 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 11:57:42.489 +05:30 [DBG] Session cleanup completed
2025-05-19 11:57:42.678 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/api/Test/token - null null
2025-05-19 11:57:42.679 +05:30 [INF] Token received and is being processed
2025-05-19 11:57:42.679 +05:30 [INF] Token validated successfully with claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: f8711437-03bc-4057-9640-c11fb6d7c830, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: f8711437-03bc-4057-9640-c11fb6d7c830, jti: c5e88c79-d679-4c0c-be1a-7c7c7e4831d6, http://schemas.microsoft.com/ws/2008/06/identity/claims/role: Admin, http://schemas.microsoft.com/ws/2008/06/identity/claims/role: User, permission: test.permission, nbf: 1747635794, exp: 1747639394, iss: identity-service, aud: vms-api
2025-05-19 11:57:42.679 +05:30 [INF] NameIdentifier claim found: f8711437-03bc-4057-9640-c11fb6d7c830
2025-05-19 11:57:42.684 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__token_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Token" = @__token_0
LIMIT 1
2025-05-19 11:57:42.684 +05:30 [WRN] Session with token eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.I6_CtB-Ja6yd6DkII4xzMy-GxcFHmMwxe5wY2CFinD4 not found
2025-05-19 11:57:42.684 +05:30 [WRN] Invalid session for token eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.I6_CtB-Ja6yd6DkII4xzMy-GxcFHmMwxe5wY2CFinD4
2025-05-19 11:57:42.684 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/api/Test/token - 401 null application/json; charset=utf-8 5.9461ms
2025-05-19 11:57:46.279 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/api/Test/token - null null
2025-05-19 11:57:46.280 +05:30 [INF] Token received and is being processed
2025-05-19 11:57:46.281 +05:30 [INF] Token validated successfully with claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: f8711437-03bc-4057-9640-c11fb6d7c830, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: f8711437-03bc-4057-9640-c11fb6d7c830, jti: c5e88c79-d679-4c0c-be1a-7c7c7e4831d6, http://schemas.microsoft.com/ws/2008/06/identity/claims/role: Admin, http://schemas.microsoft.com/ws/2008/06/identity/claims/role: User, permission: test.permission, nbf: 1747635794, exp: 1747639394, iss: identity-service, aud: vms-api
2025-05-19 11:57:46.281 +05:30 [INF] NameIdentifier claim found: f8711437-03bc-4057-9640-c11fb6d7c830
2025-05-19 11:57:46.284 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__token_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Token" = @__token_0
LIMIT 1
2025-05-19 11:57:46.284 +05:30 [WRN] Session with token eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.I6_CtB-Ja6yd6DkII4xzMy-GxcFHmMwxe5wY2CFinD4 not found
2025-05-19 11:57:46.284 +05:30 [WRN] Invalid session for token eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.I6_CtB-Ja6yd6DkII4xzMy-GxcFHmMwxe5wY2CFinD4
2025-05-19 11:57:46.284 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/api/Test/token - 401 null application/json; charset=utf-8 5.0593ms
2025-05-19 11:58:00.855 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5263/api/Users - application/json 221
2025-05-19 11:58:00.855 +05:30 [INF] Token received and is being processed
2025-05-19 11:58:00.857 +05:30 [WRN] User is null or not authenticated
2025-05-19 11:58:00.858 +05:30 [WRN] No user ID found in token
2025-05-19 11:58:00.858 +05:30 [INF] Executing endpoint 'IdentityService.API.Controllers.UsersController.CreateUser (IdentityService.API)'
2025-05-19 11:58:00.884 +05:30 [INF] Route matched with {action = "CreateUser", controller = "Users"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[IdentityService.Application.DTOs.Users.UserResponse]] CreateUser(IdentityService.Application.DTOs.Users.CreateUserRequest) on controller IdentityService.API.Controllers.UsersController (IdentityService.API).
2025-05-19 11:58:00.952 +05:30 [WRN] User ID not found in HttpContext.Items
2025-05-19 11:58:00.952 +05:30 [INF] Setting UserId "00000000-0000-0000-0000-000000000000" on request of type CreateUserCommand
2025-05-19 11:58:00.953 +05:30 [WRN] User ID not found in HttpContext.Items
2025-05-19 11:58:00.956 +05:30 [INF] Validating request of type CreateUserCommand
2025-05-19 11:58:00.965 +05:30 [INF] Request content: {
  "Request": {
    "Email": "<EMAIL>",
    "PhoneNumber": "8088020875",
    "Password": "Shri@1122",
    "ConfirmPassword": "Shri@1122",
    "RoleIds": [
      "638e2592-0e7f-45da-b423-f5b22325166d"
    ],
    "IsActive": true
  },
  "UserId": "00000000-0000-0000-0000-000000000000"
}
2025-05-19 11:58:00.965 +05:30 [INF] Request UserId property value: "00000000-0000-0000-0000-000000000000"
2025-05-19 11:58:00.965 +05:30 [WRN] UserId is empty GUID (00000000-0000-0000-0000-000000000000)
2025-05-19 11:58:00.965 +05:30 [INF] Validation passed for request of type CreateUserCommand
2025-05-19 11:58:01.006 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__email_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__email_0)
2025-05-19 11:58:01.466 +05:30 [WRN] Invalid operation during user creation
System.InvalidOperationException: Email <EMAIL> is already in use
   at IdentityService.Application.Features.Users.Commands.CreateUserCommandHandler.Handle(CreateUserCommand command, CancellationToken cancellationToken) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.Application\Features\Users\Commands\CreateUserCommand.cs:line 41
   at IdentityService.Application.Behaviors.ValidationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.Application\Behaviors\ValidationBehavior.cs:line 95
   at IdentityService.Application.Behaviors.UserIdPropagationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.Application\Behaviors\UserIdPropagationBehavior.cs:line 39
   at MediatR.Pipeline.RequestExceptionProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestExceptionProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestExceptionActionProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestExceptionActionProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestPostProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestPreProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at IdentityService.API.Controllers.UsersController.CreateUser(CreateUserRequest request) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.API\Controllers\UsersController.cs:line 74
2025-05-19 11:58:01.551 +05:30 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-05-19 11:58:01.553 +05:30 [INF] Executed action IdentityService.API.Controllers.UsersController.CreateUser (IdentityService.API) in 668.2348ms
2025-05-19 11:58:01.554 +05:30 [INF] Executed endpoint 'IdentityService.API.Controllers.UsersController.CreateUser (IdentityService.API)'
2025-05-19 11:58:01.554 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5263/api/Users - 400 null application/json; charset=utf-8 699.2123ms
2025-05-19 11:58:14.425 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5263/api/Users - application/json 223
2025-05-19 11:58:14.425 +05:30 [INF] Token received and is being processed
2025-05-19 11:58:14.427 +05:30 [WRN] User is null or not authenticated
2025-05-19 11:58:14.427 +05:30 [WRN] No user ID found in token
2025-05-19 11:58:14.427 +05:30 [INF] Executing endpoint 'IdentityService.API.Controllers.UsersController.CreateUser (IdentityService.API)'
2025-05-19 11:58:14.427 +05:30 [INF] Route matched with {action = "CreateUser", controller = "Users"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[IdentityService.Application.DTOs.Users.UserResponse]] CreateUser(IdentityService.Application.DTOs.Users.CreateUserRequest) on controller IdentityService.API.Controllers.UsersController (IdentityService.API).
2025-05-19 11:58:14.435 +05:30 [WRN] User ID not found in HttpContext.Items
2025-05-19 11:58:14.435 +05:30 [INF] Setting UserId "00000000-0000-0000-0000-000000000000" on request of type CreateUserCommand
2025-05-19 11:58:14.435 +05:30 [WRN] User ID not found in HttpContext.Items
2025-05-19 11:58:14.436 +05:30 [INF] Validating request of type CreateUserCommand
2025-05-19 11:58:14.438 +05:30 [INF] Request content: {
  "Request": {
    "Email": "<EMAIL>",
    "PhoneNumber": "8088020875",
    "Password": "Shri@1122",
    "ConfirmPassword": "Shri@1122",
    "RoleIds": [
      "638e2592-0e7f-45da-b423-f5b22325166d"
    ],
    "IsActive": true
  },
  "UserId": "00000000-0000-0000-0000-000000000000"
}
2025-05-19 11:58:14.439 +05:30 [INF] Request UserId property value: "00000000-0000-0000-0000-000000000000"
2025-05-19 11:58:14.439 +05:30 [WRN] UserId is empty GUID (00000000-0000-0000-0000-000000000000)
2025-05-19 11:58:14.439 +05:30 [INF] Validation passed for request of type CreateUserCommand
2025-05-19 11:58:14.446 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__email_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__email_0)
2025-05-19 11:58:14.454 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__phoneNumber_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."PhoneNumber" = @__phoneNumber_0)
2025-05-19 11:58:14.556 +05:30 [WRN] User is null or not authenticated
2025-05-19 11:58:14.584 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__get_Item_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."Description", r."IsDeleted", r."Name", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "Roles" AS r
WHERE r."Id" = @__get_Item_0
LIMIT 1
2025-05-19 11:58:14.668 +05:30 [WRN] User is null or not authenticated
2025-05-19 11:58:14.668 +05:30 [WRN] User is null or not authenticated
2025-05-19 11:58:14.721 +05:30 [INF] Executed DbCommand (17ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = DateTime), @p2='?', @p3='?', @p4='?' (DbType = DateTime), @p5='?', @p6='?' (DbType = Guid), @p7='?', @p8='?' (DbType = DateTime), @p9='?' (DbType = DateTime), @p10='?' (DbType = DateTime), @p11='?', @p12='?' (DbType = Guid), @p13='?', @p14='?' (DbType = Guid), @p15='?' (DbType = DateTime), @p16='?', @p17='?' (DbType = Boolean), @p18='?' (DbType = Guid), @p19='?' (DbType = Binary), @p20='?' (DbType = DateTime), @p21='?', @p22='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Users" ("Id", "CreatedAt", "CreatedBy", "Email", "LastLoginAt", "PhoneNumber", "PrimaryBranchId", "RefreshToken", "RefreshTokenExpiryTime", "SecurityStamp", "UpdatedAt", "UpdatedBy", "VendorId", "PasswordHash")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13)
RETURNING "EmailVerified", "IsDeleted", "PhoneNumberVerified", "RememberMe", "RowVersion";
INSERT INTO "UserRoles" ("Id", "CreatedAt", "CreatedBy", "IsDeleted", "RoleId", "RowVersion", "UpdatedAt", "UpdatedBy", "UserId")
VALUES (@p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22);
2025-05-19 11:58:14.756 +05:30 [INF] Executed DbCommand (5ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Name"
FROM "UserRoles" AS u
INNER JOIN "Roles" AS r ON u."RoleId" = r."Id"
WHERE u."UserId" = @__userId_0
2025-05-19 11:58:14.764 +05:30 [INF] Executing CreatedAtActionResult, writing value of type 'IdentityService.Application.DTOs.Users.UserResponse'.
2025-05-19 11:58:14.804 +05:30 [INF] Executed action IdentityService.API.Controllers.UsersController.CreateUser (IdentityService.API) in 376.947ms
2025-05-19 11:58:14.804 +05:30 [INF] Executed endpoint 'IdentityService.API.Controllers.UsersController.CreateUser (IdentityService.API)'
2025-05-19 11:58:14.805 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5263/api/Users - 201 null application/json; charset=utf-8 379.9689ms
2025-05-19 11:58:54.422 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5263/api/Auth/login - application/json 145
2025-05-19 11:58:54.423 +05:30 [INF] Token received and is being processed
2025-05-19 11:58:54.424 +05:30 [WRN] User is null or not authenticated
2025-05-19 11:58:54.424 +05:30 [WRN] No user ID found in token
2025-05-19 11:58:54.424 +05:30 [INF] Executing endpoint 'IdentityService.API.Controllers.AuthController.Login (IdentityService.API)'
2025-05-19 11:58:54.431 +05:30 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[IdentityService.Application.DTOs.Auth.LoginResponse]] Login(IdentityService.Application.DTOs.Auth.LoginRequest) on controller IdentityService.API.Controllers.AuthController (IdentityService.API).
2025-05-19 11:58:54.475 +05:30 [WRN] User ID not found in HttpContext.Items
2025-05-19 11:58:54.475 +05:30 [INF] Setting UserId "00000000-0000-0000-0000-000000000000" on request of type LoginCommand
2025-05-19 11:58:54.475 +05:30 [WRN] User ID not found in HttpContext.Items
2025-05-19 11:58:54.476 +05:30 [INF] Validating request of type LoginCommand
2025-05-19 11:58:54.478 +05:30 [INF] Request content: {
  "Request": {
    "Email": "<EMAIL>",
    "Password": "Shri@1122",
    "PhoneNumber": "8088020875",
    "Otp": "1234",
    "RememberMe": true,
    "IsEmailLogin": true,
    "IsPhoneLogin": true
  },
  "UserId": "00000000-0000-0000-0000-000000000000"
}
2025-05-19 11:58:54.478 +05:30 [INF] Request UserId property value: "00000000-0000-0000-0000-000000000000"
2025-05-19 11:58:54.478 +05:30 [WRN] UserId is empty GUID (00000000-0000-0000-0000-000000000000)
2025-05-19 11:58:54.497 +05:30 [INF] Validation passed for request of type LoginCommand
2025-05-19 11:58:54.545 +05:30 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-05-19 11:58:54.766 +05:30 [INF] Executed DbCommand (15ms) [Parameters=[@__email_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash", t0."Id", t0."CreatedAt", t0."CreatedBy", t0."IsDeleted", t0."RoleId", t0."RowVersion", t0."UpdatedAt", t0."UpdatedBy", t0."UserId", t0."Id0", t0."CreatedAt0", t0."CreatedBy0", t0."Description", t0."IsDeleted0", t0."Name", t0."RowVersion0", t0."UpdatedAt0", t0."UpdatedBy0", t0."Id1", t0."CreatedAt1", t0."CreatedBy1", t0."IsDeleted1", t0."PermissionId", t0."RoleId0", t0."RowVersion1", t0."UpdatedAt1", t0."UpdatedBy1", t0."Id00", t0."Action", t0."CreatedAt00", t0."CreatedBy00", t0."Description0", t0."IsDeleted00", t0."Name0", t0."Resource", t0."RowVersion00", t0."UpdatedAt00", t0."UpdatedBy00"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."Email", u."EmailVerified", u."IsDeleted", u."LastLoginAt", u."PhoneNumber", u."PhoneNumberVerified", u."PrimaryBranchId", u."RefreshToken", u."RefreshTokenExpiryTime", u."RememberMe", u."RowVersion", u."SecurityStamp", u."UpdatedAt", u."UpdatedBy", u."VendorId", u."PasswordHash"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__email_0
    LIMIT 1
) AS t
LEFT JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."IsDeleted", u0."RoleId", u0."RowVersion", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", r."Id" AS "Id0", r."CreatedAt" AS "CreatedAt0", r."CreatedBy" AS "CreatedBy0", r."Description", r."IsDeleted" AS "IsDeleted0", r."Name", r."RowVersion" AS "RowVersion0", r."UpdatedAt" AS "UpdatedAt0", r."UpdatedBy" AS "UpdatedBy0", t1."Id" AS "Id1", t1."CreatedAt" AS "CreatedAt1", t1."CreatedBy" AS "CreatedBy1", t1."IsDeleted" AS "IsDeleted1", t1."PermissionId", t1."RoleId" AS "RoleId0", t1."RowVersion" AS "RowVersion1", t1."UpdatedAt" AS "UpdatedAt1", t1."UpdatedBy" AS "UpdatedBy1", t1."Id0" AS "Id00", t1."Action", t1."CreatedAt0" AS "CreatedAt00", t1."CreatedBy0" AS "CreatedBy00", t1."Description" AS "Description0", t1."IsDeleted0" AS "IsDeleted00", t1."Name" AS "Name0", t1."Resource", t1."RowVersion0" AS "RowVersion00", t1."UpdatedAt0" AS "UpdatedAt00", t1."UpdatedBy0" AS "UpdatedBy00"
    FROM "UserRoles" AS u0
    INNER JOIN "Roles" AS r ON u0."RoleId" = r."Id"
    LEFT JOIN (
        SELECT r0."Id", r0."CreatedAt", r0."CreatedBy", r0."IsDeleted", r0."PermissionId", r0."RoleId", r0."RowVersion", r0."UpdatedAt", r0."UpdatedBy", p."Id" AS "Id0", p."Action", p."CreatedAt" AS "CreatedAt0", p."CreatedBy" AS "CreatedBy0", p."Description", p."IsDeleted" AS "IsDeleted0", p."Name", p."Resource", p."RowVersion" AS "RowVersion0", p."UpdatedAt" AS "UpdatedAt0", p."UpdatedBy" AS "UpdatedBy0"
        FROM "RolePermissions" AS r0
        INNER JOIN "Permissions" AS p ON r0."PermissionId" = p."Id"
    ) AS t1 ON r."Id" = t1."RoleId"
) AS t0 ON t."Id" = t0."UserId"
ORDER BY t."Id", t0."Id", t0."Id0", t0."Id1"
2025-05-19 11:58:54.981 +05:30 [INF] Executed DbCommand (5ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT count(*)::int
FROM "UserSessions" AS u
WHERE u."UserId" = @__userId_0 AND u."Status" = 0 AND u."ExpiresAt" > now()
2025-05-19 11:58:54.986 +05:30 [WRN] User is null or not authenticated
2025-05-19 11:58:55.020 +05:30 [WRN] User is null or not authenticated
2025-05-19 11:58:55.020 +05:30 [WRN] User is null or not authenticated
2025-05-19 11:58:55.042 +05:30 [INF] Executed DbCommand (13ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = DateTime), @p2='?', @p3='?', @p4='?', @p5='?', @p6='?', @p7='?' (DbType = Boolean), @p8='?' (DbType = Boolean), @p9='?', @p10='?' (DbType = Int32), @p11='?', @p12='?' (DbType = Binary), @p13='?' (DbType = DateTime), @p14='?', @p15='?', @p16='?' (DbType = Guid), @p17='?' (DbType = Guid), @p18='?' (DbType = DateTime), @p19='?', @p20='?', @p21='?', @p22='?' (DbType = DateTime), @p23='?' (DbType = DateTime), @p24='?', @p25='?' (DbType = Boolean), @p26='?' (DbType = DateTime), @p27='?', @p28='?' (DbType = DateTime), @p29='?' (DbType = Int32), @p30='?', @p31='?' (DbType = DateTime), @p32='?', @p33='?', @p34='?' (DbType = Guid), @p41='?' (DbType = Guid), @p35='?' (DbType = DateTime), @p36='?', @p37='?' (DbType = DateTime), @p38='?' (DbType = Boolean), @p42='?' (DbType = Binary), @p39='?' (DbType = DateTime), @p40='?'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "UserLoginHistories" ("Id", "CreatedAt", "CreatedBy", "DeviceInfo", "Email", "FailureReason", "IpAddress", "IsDeleted", "IsSuccessful", "Location", "LoginMethod", "PhoneNumber", "RowVersion", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16);
INSERT INTO "UserSessions" ("Id", "CreatedAt", "CreatedBy", "DeviceInfo", "EndReason", "EndedAt", "ExpiresAt", "IpAddress", "IsDeleted", "LastActiveAt", "RefreshToken", "StartedAt", "Status", "Token", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34)
RETURNING "RowVersion";
UPDATE "Users" SET "LastLoginAt" = @p35, "RefreshToken" = @p36, "RefreshTokenExpiryTime" = @p37, "RememberMe" = @p38, "UpdatedAt" = @p39, "UpdatedBy" = @p40
WHERE "Id" = @p41 AND "RowVersion" = @p42
RETURNING "RowVersion";
2025-05-19 11:58:55.047 +05:30 [INF] Created new session "5ffd6bd0-50c1-409f-b374-275f5132da7f" for user "816fb80f-3835-42e3-85bb-2dd493f98915"
2025-05-19 11:58:55.051 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Name"
FROM "UserRoles" AS u
INNER JOIN "Roles" AS r ON u."RoleId" = r."Id"
WHERE u."UserId" = @__userId_0
2025-05-19 11:58:55.063 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."Description", r."IsDeleted", r."Name", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "UserRoles" AS u
INNER JOIN "Roles" AS r ON u."RoleId" = r."Id"
WHERE u."UserId" = @__userId_0
2025-05-19 11:58:55.072 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__role_Id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "RolePermissions" AS r
INNER JOIN "Permissions" AS p ON r."PermissionId" = p."Id"
WHERE r."RoleId" = @__role_Id_0
2025-05-19 11:58:55.079 +05:30 [INF] Executing OkObjectResult, writing value of type 'IdentityService.Application.DTOs.Auth.LoginResponse'.
2025-05-19 11:58:55.085 +05:30 [INF] Executed action IdentityService.API.Controllers.AuthController.Login (IdentityService.API) in 653.4572ms
2025-05-19 11:58:55.085 +05:30 [INF] Executed endpoint 'IdentityService.API.Controllers.AuthController.Login (IdentityService.API)'
2025-05-19 11:58:55.086 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5263/api/Auth/login - 200 null application/json; charset=utf-8 663.4407ms
2025-05-19 11:59:31.909 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/api/service/users/by-email?email=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.VOOY5ljPzmRkCefXl-L4iZLfxi_TXSM_SVPQ7bv-qKI%22%2C%20%20%20%22refreshToken%22%3A%20%221e0f8fc0-ddd2-4d3f-939a-ccbc32a5eda2 - null null
2025-05-19 11:59:31.909 +05:30 [INF] Token received and is being processed
2025-05-19 11:59:32.036 +05:30 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignature(JsonWebToken jwtToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignatureAndIssuerSecurityKey(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-05-19 11:59:32.037 +05:30 [ERR] Authentication failed: Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignature(JsonWebToken jwtToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignatureAndIssuerSecurityKey(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-05-19 11:59:32.038 +05:30 [INF] Bearer was not authenticated. Failure message: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
2025-05-19 11:59:32.043 +05:30 [INF] Bearer was not authenticated. Failure message: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
2025-05-19 11:59:32.075 +05:30 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-05-19 11:59:32.080 +05:30 [WRN] OnChallenge: invalid_token
2025-05-19 11:59:32.081 +05:30 [INF] AuthenticationScheme: Bearer was challenged.
2025-05-19 11:59:32.083 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/api/service/users/by-email?email=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.VOOY5ljPzmRkCefXl-L4iZLfxi_TXSM_SVPQ7bv-qKI%22%2C%20%20%20%22refreshToken%22%3A%20%221e0f8fc0-ddd2-4d3f-939a-ccbc32a5eda2 - 401 0 null 174.5284ms
2025-05-19 12:00:01.982 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/api/service/users/by-email?email=shriprasad.patil12%40svvatech.com - null null
2025-05-19 12:00:01.987 +05:30 [INF] Token received and is being processed
2025-05-19 12:00:02.077 +05:30 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignature(JsonWebToken jwtToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignatureAndIssuerSecurityKey(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-05-19 12:00:02.078 +05:30 [ERR] Authentication failed: Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignature(JsonWebToken jwtToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignatureAndIssuerSecurityKey(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-05-19 12:00:02.078 +05:30 [INF] Bearer was not authenticated. Failure message: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
2025-05-19 12:00:02.083 +05:30 [INF] Bearer was not authenticated. Failure message: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
2025-05-19 12:00:02.083 +05:30 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-05-19 12:00:02.084 +05:30 [WRN] OnChallenge: invalid_token
2025-05-19 12:00:02.084 +05:30 [INF] AuthenticationScheme: Bearer was challenged.
2025-05-19 12:00:02.084 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/api/service/users/by-email?email=shriprasad.patil12%40svvatech.com - 401 0 null 102.4781ms
2025-05-19 12:00:23.913 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/api/service/users/by-email?email=shriprasad.patil%40svvatech.com - null null
2025-05-19 12:00:23.914 +05:30 [INF] Token received and is being processed
2025-05-19 12:00:23.994 +05:30 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignature(JsonWebToken jwtToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignatureAndIssuerSecurityKey(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-05-19 12:00:23.995 +05:30 [ERR] Authentication failed: Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignature(JsonWebToken jwtToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignatureAndIssuerSecurityKey(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-05-19 12:00:23.995 +05:30 [INF] Bearer was not authenticated. Failure message: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
2025-05-19 12:00:23.995 +05:30 [INF] Bearer was not authenticated. Failure message: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
2025-05-19 12:00:23.996 +05:30 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-05-19 12:00:23.996 +05:30 [WRN] OnChallenge: invalid_token
2025-05-19 12:00:23.996 +05:30 [INF] AuthenticationScheme: Bearer was challenged.
2025-05-19 12:00:23.996 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/api/service/users/by-email?email=shriprasad.patil%40svvatech.com - 401 0 null 82.5907ms
2025-05-19 12:00:29.071 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/api/service/users/by-email?email=shriprasad.patil%40svvatech.com - null null
2025-05-19 12:00:29.071 +05:30 [INF] Token received and is being processed
2025-05-19 12:00:29.155 +05:30 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignature(JsonWebToken jwtToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignatureAndIssuerSecurityKey(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-05-19 12:00:29.156 +05:30 [ERR] Authentication failed: Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignature(JsonWebToken jwtToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignatureAndIssuerSecurityKey(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-05-19 12:00:29.156 +05:30 [INF] Bearer was not authenticated. Failure message: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
2025-05-19 12:00:29.157 +05:30 [INF] Bearer was not authenticated. Failure message: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
2025-05-19 12:00:29.157 +05:30 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-05-19 12:00:29.157 +05:30 [WRN] OnChallenge: invalid_token
2025-05-19 12:00:29.157 +05:30 [INF] AuthenticationScheme: Bearer was challenged.
2025-05-19 12:00:29.157 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/api/service/users/by-email?email=shriprasad.patil%40svvatech.com - 401 0 null 86.1362ms
2025-05-19 12:00:41.908 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/api/service/users/by-email?email=shriprasad.patil12%40svvatech.com - null null
2025-05-19 12:00:41.909 +05:30 [INF] Token received and is being processed
2025-05-19 12:00:41.993 +05:30 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignature(JsonWebToken jwtToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignatureAndIssuerSecurityKey(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-05-19 12:00:41.993 +05:30 [ERR] Authentication failed: Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignature(JsonWebToken jwtToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignatureAndIssuerSecurityKey(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-05-19 12:00:41.994 +05:30 [INF] Bearer was not authenticated. Failure message: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
2025-05-19 12:00:41.994 +05:30 [INF] Bearer was not authenticated. Failure message: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
2025-05-19 12:00:41.995 +05:30 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-05-19 12:00:41.995 +05:30 [WRN] OnChallenge: invalid_token
2025-05-19 12:00:41.995 +05:30 [INF] AuthenticationScheme: Bearer was challenged.
2025-05-19 12:00:41.995 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/api/service/users/by-email?email=shriprasad.patil12%40svvatech.com - 401 0 null 86.5905ms
2025-05-19 12:02:42.496 +05:30 [DBG] Starting session cleanup
2025-05-19 12:02:42.530 +05:30 [INF] Executed DbCommand (12ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 12:02:42.536 +05:30 [INF] Executed DbCommand (4ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 12:02:42.536 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 12:02:42.536 +05:30 [DBG] Session cleanup completed
2025-05-19 12:02:52.073 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/api/service/users/816fb80f-3835-42e3-85bb-2dd493f98915/roles - null null
2025-05-19 12:02:52.073 +05:30 [INF] Token received and is being processed
2025-05-19 12:02:52.151 +05:30 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignature(JsonWebToken jwtToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignatureAndIssuerSecurityKey(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-05-19 12:02:52.152 +05:30 [ERR] Authentication failed: Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignature(JsonWebToken jwtToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignatureAndIssuerSecurityKey(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-05-19 12:02:52.152 +05:30 [INF] Bearer was not authenticated. Failure message: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
2025-05-19 12:02:52.153 +05:30 [INF] Bearer was not authenticated. Failure message: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
2025-05-19 12:02:52.153 +05:30 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-05-19 12:02:52.153 +05:30 [WRN] OnChallenge: invalid_token
2025-05-19 12:02:52.153 +05:30 [INF] AuthenticationScheme: Bearer was challenged.
2025-05-19 12:02:52.153 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/api/service/users/816fb80f-3835-42e3-85bb-2dd493f98915/roles - 401 0 null 80.4421ms
2025-05-19 12:03:07.795 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5263/api/Auth/login - application/json 145
2025-05-19 12:03:07.797 +05:30 [INF] Token received and is being processed
2025-05-19 12:03:07.869 +05:30 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignature(JsonWebToken jwtToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignatureAndIssuerSecurityKey(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-05-19 12:03:07.869 +05:30 [ERR] Authentication failed: Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignature(JsonWebToken jwtToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignatureAndIssuerSecurityKey(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-05-19 12:03:07.869 +05:30 [INF] Bearer was not authenticated. Failure message: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
2025-05-19 12:03:07.870 +05:30 [WRN] User is null or not authenticated
2025-05-19 12:03:07.870 +05:30 [WRN] No user ID found in token
2025-05-19 12:03:07.870 +05:30 [INF] Executing endpoint 'IdentityService.API.Controllers.AuthController.Login (IdentityService.API)'
2025-05-19 12:03:07.870 +05:30 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[IdentityService.Application.DTOs.Auth.LoginResponse]] Login(IdentityService.Application.DTOs.Auth.LoginRequest) on controller IdentityService.API.Controllers.AuthController (IdentityService.API).
2025-05-19 12:03:07.873 +05:30 [WRN] User ID not found in HttpContext.Items
2025-05-19 12:03:07.873 +05:30 [INF] Setting UserId "00000000-0000-0000-0000-000000000000" on request of type LoginCommand
2025-05-19 12:03:07.873 +05:30 [WRN] User ID not found in HttpContext.Items
2025-05-19 12:03:07.873 +05:30 [INF] Validating request of type LoginCommand
2025-05-19 12:03:07.873 +05:30 [INF] Request content: {
  "Request": {
    "Email": "<EMAIL>",
    "Password": "Shri@1122",
    "PhoneNumber": "8088020875",
    "Otp": "1234",
    "RememberMe": true,
    "IsEmailLogin": true,
    "IsPhoneLogin": true
  },
  "UserId": "00000000-0000-0000-0000-000000000000"
}
2025-05-19 12:03:07.873 +05:30 [INF] Request UserId property value: "00000000-0000-0000-0000-000000000000"
2025-05-19 12:03:07.873 +05:30 [WRN] UserId is empty GUID (00000000-0000-0000-0000-000000000000)
2025-05-19 12:03:07.874 +05:30 [INF] Validation passed for request of type LoginCommand
2025-05-19 12:03:07.885 +05:30 [INF] Executed DbCommand (9ms) [Parameters=[@__email_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash", t0."Id", t0."CreatedAt", t0."CreatedBy", t0."IsDeleted", t0."RoleId", t0."RowVersion", t0."UpdatedAt", t0."UpdatedBy", t0."UserId", t0."Id0", t0."CreatedAt0", t0."CreatedBy0", t0."Description", t0."IsDeleted0", t0."Name", t0."RowVersion0", t0."UpdatedAt0", t0."UpdatedBy0", t0."Id1", t0."CreatedAt1", t0."CreatedBy1", t0."IsDeleted1", t0."PermissionId", t0."RoleId0", t0."RowVersion1", t0."UpdatedAt1", t0."UpdatedBy1", t0."Id00", t0."Action", t0."CreatedAt00", t0."CreatedBy00", t0."Description0", t0."IsDeleted00", t0."Name0", t0."Resource", t0."RowVersion00", t0."UpdatedAt00", t0."UpdatedBy00"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."Email", u."EmailVerified", u."IsDeleted", u."LastLoginAt", u."PhoneNumber", u."PhoneNumberVerified", u."PrimaryBranchId", u."RefreshToken", u."RefreshTokenExpiryTime", u."RememberMe", u."RowVersion", u."SecurityStamp", u."UpdatedAt", u."UpdatedBy", u."VendorId", u."PasswordHash"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__email_0
    LIMIT 1
) AS t
LEFT JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."IsDeleted", u0."RoleId", u0."RowVersion", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", r."Id" AS "Id0", r."CreatedAt" AS "CreatedAt0", r."CreatedBy" AS "CreatedBy0", r."Description", r."IsDeleted" AS "IsDeleted0", r."Name", r."RowVersion" AS "RowVersion0", r."UpdatedAt" AS "UpdatedAt0", r."UpdatedBy" AS "UpdatedBy0", t1."Id" AS "Id1", t1."CreatedAt" AS "CreatedAt1", t1."CreatedBy" AS "CreatedBy1", t1."IsDeleted" AS "IsDeleted1", t1."PermissionId", t1."RoleId" AS "RoleId0", t1."RowVersion" AS "RowVersion1", t1."UpdatedAt" AS "UpdatedAt1", t1."UpdatedBy" AS "UpdatedBy1", t1."Id0" AS "Id00", t1."Action", t1."CreatedAt0" AS "CreatedAt00", t1."CreatedBy0" AS "CreatedBy00", t1."Description" AS "Description0", t1."IsDeleted0" AS "IsDeleted00", t1."Name" AS "Name0", t1."Resource", t1."RowVersion0" AS "RowVersion00", t1."UpdatedAt0" AS "UpdatedAt00", t1."UpdatedBy0" AS "UpdatedBy00"
    FROM "UserRoles" AS u0
    INNER JOIN "Roles" AS r ON u0."RoleId" = r."Id"
    LEFT JOIN (
        SELECT r0."Id", r0."CreatedAt", r0."CreatedBy", r0."IsDeleted", r0."PermissionId", r0."RoleId", r0."RowVersion", r0."UpdatedAt", r0."UpdatedBy", p."Id" AS "Id0", p."Action", p."CreatedAt" AS "CreatedAt0", p."CreatedBy" AS "CreatedBy0", p."Description", p."IsDeleted" AS "IsDeleted0", p."Name", p."Resource", p."RowVersion" AS "RowVersion0", p."UpdatedAt" AS "UpdatedAt0", p."UpdatedBy" AS "UpdatedBy0"
        FROM "RolePermissions" AS r0
        INNER JOIN "Permissions" AS p ON r0."PermissionId" = p."Id"
    ) AS t1 ON r."Id" = t1."RoleId"
) AS t0 ON t."Id" = t0."UserId"
ORDER BY t."Id", t0."Id", t0."Id0", t0."Id1"
2025-05-19 12:03:07.993 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT count(*)::int
FROM "UserSessions" AS u
WHERE u."UserId" = @__userId_0 AND u."Status" = 0 AND u."ExpiresAt" > now()
2025-05-19 12:03:07.996 +05:30 [WRN] User is null or not authenticated
2025-05-19 12:03:07.997 +05:30 [WRN] User is null or not authenticated
2025-05-19 12:03:07.997 +05:30 [WRN] User is null or not authenticated
2025-05-19 12:03:08.004 +05:30 [INF] Executed DbCommand (4ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = DateTime), @p2='?', @p3='?', @p4='?', @p5='?', @p6='?', @p7='?' (DbType = Boolean), @p8='?' (DbType = Boolean), @p9='?', @p10='?' (DbType = Int32), @p11='?', @p12='?' (DbType = Binary), @p13='?' (DbType = DateTime), @p14='?', @p15='?', @p16='?' (DbType = Guid), @p17='?' (DbType = Guid), @p18='?' (DbType = DateTime), @p19='?', @p20='?', @p21='?', @p22='?' (DbType = DateTime), @p23='?' (DbType = DateTime), @p24='?', @p25='?' (DbType = Boolean), @p26='?' (DbType = DateTime), @p27='?', @p28='?' (DbType = DateTime), @p29='?' (DbType = Int32), @p30='?', @p31='?' (DbType = DateTime), @p32='?', @p33='?', @p34='?' (DbType = Guid), @p39='?' (DbType = Guid), @p35='?' (DbType = DateTime), @p36='?', @p37='?' (DbType = DateTime), @p40='?' (DbType = Binary), @p38='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "UserLoginHistories" ("Id", "CreatedAt", "CreatedBy", "DeviceInfo", "Email", "FailureReason", "IpAddress", "IsDeleted", "IsSuccessful", "Location", "LoginMethod", "PhoneNumber", "RowVersion", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16);
INSERT INTO "UserSessions" ("Id", "CreatedAt", "CreatedBy", "DeviceInfo", "EndReason", "EndedAt", "ExpiresAt", "IpAddress", "IsDeleted", "LastActiveAt", "RefreshToken", "StartedAt", "Status", "Token", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34)
RETURNING "RowVersion";
UPDATE "Users" SET "LastLoginAt" = @p35, "RefreshToken" = @p36, "RefreshTokenExpiryTime" = @p37, "UpdatedAt" = @p38
WHERE "Id" = @p39 AND "RowVersion" = @p40
RETURNING "RowVersion";
2025-05-19 12:03:08.006 +05:30 [INF] Created new session "e14053e9-fc09-448a-b134-75300853315a" for user "816fb80f-3835-42e3-85bb-2dd493f98915"
2025-05-19 12:03:08.008 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Name"
FROM "UserRoles" AS u
INNER JOIN "Roles" AS r ON u."RoleId" = r."Id"
WHERE u."UserId" = @__userId_0
2025-05-19 12:03:08.012 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."Description", r."IsDeleted", r."Name", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "UserRoles" AS u
INNER JOIN "Roles" AS r ON u."RoleId" = r."Id"
WHERE u."UserId" = @__userId_0
2025-05-19 12:03:08.015 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__role_Id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "RolePermissions" AS r
INNER JOIN "Permissions" AS p ON r."PermissionId" = p."Id"
WHERE r."RoleId" = @__role_Id_0
2025-05-19 12:03:08.015 +05:30 [INF] Executing OkObjectResult, writing value of type 'IdentityService.Application.DTOs.Auth.LoginResponse'.
2025-05-19 12:03:08.016 +05:30 [INF] Executed action IdentityService.API.Controllers.AuthController.Login (IdentityService.API) in 145.5987ms
2025-05-19 12:03:08.016 +05:30 [INF] Executed endpoint 'IdentityService.API.Controllers.AuthController.Login (IdentityService.API)'
2025-05-19 12:03:08.016 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5263/api/Auth/login - 200 null application/json; charset=utf-8 221.0667ms
2025-05-19 12:03:25.962 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/api/service/users/816fb80f-3835-42e3-85bb-2dd493f98915/roles - null null
2025-05-19 12:03:25.963 +05:30 [INF] Token received and is being processed
2025-05-19 12:03:26.029 +05:30 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignature(JsonWebToken jwtToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignatureAndIssuerSecurityKey(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-05-19 12:03:26.030 +05:30 [ERR] Authentication failed: Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignature(JsonWebToken jwtToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignatureAndIssuerSecurityKey(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-05-19 12:03:26.030 +05:30 [INF] Bearer was not authenticated. Failure message: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
2025-05-19 12:03:26.030 +05:30 [INF] Bearer was not authenticated. Failure message: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
2025-05-19 12:03:26.030 +05:30 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-05-19 12:03:26.031 +05:30 [WRN] OnChallenge: invalid_token
2025-05-19 12:03:26.031 +05:30 [INF] AuthenticationScheme: Bearer was challenged.
2025-05-19 12:03:26.031 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/api/service/users/816fb80f-3835-42e3-85bb-2dd493f98915/roles - 401 0 null 68.8642ms
2025-05-19 12:03:43.892 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/api/service/users/816fb80f-3835-42e3-85bb-2dd493f98915/roles - null null
2025-05-19 12:03:43.893 +05:30 [INF] Token received and is being processed
2025-05-19 12:03:43.975 +05:30 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignature(JsonWebToken jwtToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignatureAndIssuerSecurityKey(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-05-19 12:03:43.975 +05:30 [ERR] Authentication failed: Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignature(JsonWebToken jwtToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignatureAndIssuerSecurityKey(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-05-19 12:03:43.975 +05:30 [INF] Bearer was not authenticated. Failure message: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
2025-05-19 12:03:43.976 +05:30 [INF] Bearer was not authenticated. Failure message: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
2025-05-19 12:03:43.976 +05:30 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-05-19 12:03:43.976 +05:30 [WRN] OnChallenge: invalid_token
2025-05-19 12:03:43.977 +05:30 [INF] AuthenticationScheme: Bearer was challenged.
2025-05-19 12:03:43.977 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/api/service/users/816fb80f-3835-42e3-85bb-2dd493f98915/roles - 401 0 null 84.4386ms
2025-05-19 12:04:09.215 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/api/Sessions/active - null null
2025-05-19 12:04:09.215 +05:30 [INF] Token received and is being processed
2025-05-19 12:04:09.340 +05:30 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignature(JsonWebToken jwtToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignatureAndIssuerSecurityKey(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-05-19 12:04:09.341 +05:30 [ERR] Authentication failed: Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignature(JsonWebToken jwtToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignatureAndIssuerSecurityKey(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-05-19 12:04:09.341 +05:30 [INF] Bearer was not authenticated. Failure message: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
2025-05-19 12:04:09.342 +05:30 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-05-19 12:04:09.342 +05:30 [WRN] OnChallenge: invalid_token
2025-05-19 12:04:09.342 +05:30 [INF] AuthenticationScheme: Bearer was challenged.
2025-05-19 12:04:09.343 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/api/Sessions/active - 401 0 null 127.6251ms
2025-05-19 12:04:38.275 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/api/Sessions/history?pageNumber=1&pageSize=10 - null null
2025-05-19 12:04:38.281 +05:30 [INF] Token received and is being processed
2025-05-19 12:04:38.348 +05:30 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignature(JsonWebToken jwtToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignatureAndIssuerSecurityKey(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-05-19 12:04:38.348 +05:30 [ERR] Authentication failed: Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignature(JsonWebToken jwtToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignatureAndIssuerSecurityKey(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-05-19 12:04:38.348 +05:30 [INF] Bearer was not authenticated. Failure message: IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.Text.StringBuilder' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
2025-05-19 12:04:38.349 +05:30 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-05-19 12:04:38.349 +05:30 [WRN] OnChallenge: invalid_token
2025-05-19 12:04:38.349 +05:30 [INF] AuthenticationScheme: Bearer was challenged.
2025-05-19 12:04:38.349 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/api/Sessions/history?pageNumber=1&pageSize=10 - 401 0 null 74.2956ms
2025-05-19 12:07:42.547 +05:30 [DBG] Starting session cleanup
2025-05-19 12:07:42.745 +05:30 [INF] Executed DbCommand (11ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 12:07:42.747 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 12:07:42.748 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 12:07:42.748 +05:30 [DBG] Session cleanup completed
2025-05-19 12:12:42.757 +05:30 [DBG] Starting session cleanup
2025-05-19 12:12:42.792 +05:30 [INF] Executed DbCommand (17ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 12:12:42.796 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 12:12:42.797 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 12:12:42.797 +05:30 [DBG] Session cleanup completed
2025-05-19 12:29:51.427 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'AuditLog'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-19 12:29:51.470 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'PasswordResetToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-19 12:29:51.470 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserBranch'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-19 12:29:51.470 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserRole'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-19 12:29:51.471 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserSession'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-19 12:29:51.471 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserSubscription'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-19 12:29:52.420 +05:30 [INF] Executed DbCommand (143ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-05-19 12:29:52.479 +05:30 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-05-19 12:29:52.593 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-05-19 12:29:52.677 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-05-19 12:29:52.678 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-05-19 12:29:52.698 +05:30 [INF] No migrations were applied. The database is already up to date.
2025-05-19 12:29:52.744 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-19 12:29:52.916 +05:30 [INF] Configured endpoint SubscriptionCreated, Consumer: IdentityService.Infrastructure.Messaging.Consumers.SubscriptionCreatedConsumer
2025-05-19 12:29:52.919 +05:30 [INF] Configured endpoint SubscriptionStatusChanged, Consumer: IdentityService.Infrastructure.Messaging.Consumers.SubscriptionStatusChangedConsumer
2025-05-19 12:29:52.921 +05:30 [INF] Configured endpoint VendorRegistered, Consumer: IdentityService.Infrastructure.Messaging.Consumers.VendorRegisteredConsumer
2025-05-19 12:29:53.079 +05:30 [DBG] Starting bus instances: IBus
2025-05-19 12:29:53.083 +05:30 [DBG] Starting bus: "rabbitmq://localhost/"
2025-05-19 12:29:53.110 +05:30 [INF] Session cleanup service is starting
2025-05-19 12:29:53.112 +05:30 [DBG] Starting session cleanup
2025-05-19 12:29:53.148 +05:30 [DBG] Connect: guest@localhost:5672/
2025-05-19 12:29:53.266 +05:30 [DBG] Connected: guest@localhost:5672/ (address: amqp://localhost:5672, local: 51427)
2025-05-19 12:29:53.308 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/DESKTOPI7D4M6U_IdentityServiceAPI_bus_7oayyyfpkikqnyfabdq3pesbnm?temporary=true"
2025-05-19 12:29:53.338 +05:30 [DBG] Declare queue: name: SubscriptionCreated, durable, consumer-count: 0 message-count: 0
2025-05-19 12:29:53.338 +05:30 [DBG] Declare queue: name: SubscriptionStatusChanged, durable, consumer-count: 0 message-count: 0
2025-05-19 12:29:53.338 +05:30 [DBG] Declare queue: name: VendorRegistered, durable, consumer-count: 0 message-count: 0
2025-05-19 12:29:53.348 +05:30 [DBG] Declare exchange: name: VendorRegistered, type: fanout, durable
2025-05-19 12:29:53.348 +05:30 [DBG] Declare exchange: name: SubscriptionCreated, type: fanout, durable
2025-05-19 12:29:53.348 +05:30 [DBG] Declare exchange: name: SubscriptionStatusChanged, type: fanout, durable
2025-05-19 12:29:53.355 +05:30 [DBG] Declare exchange: name: VMSContracts.Subscription.Events:SubscriptionStatusChanged, type: fanout, durable
2025-05-19 12:29:53.355 +05:30 [DBG] Declare exchange: name: VMSContracts.Tenant.Events:VendorRegistered, type: fanout, durable
2025-05-19 12:29:53.355 +05:30 [DBG] Declare exchange: name: VMSContracts.Subscription.Events:SubscriptionCreated, type: fanout, durable
2025-05-19 12:29:53.363 +05:30 [DBG] Bind queue: source: SubscriptionStatusChanged, destination: SubscriptionStatusChanged
2025-05-19 12:29:53.363 +05:30 [DBG] Bind queue: source: SubscriptionCreated, destination: SubscriptionCreated
2025-05-19 12:29:53.363 +05:30 [DBG] Bind queue: source: VendorRegistered, destination: VendorRegistered
2025-05-19 12:29:53.381 +05:30 [DBG] Bind exchange: source: VMSContracts.Subscription.Events:SubscriptionStatusChanged, destination: SubscriptionStatusChanged
2025-05-19 12:29:53.381 +05:30 [DBG] Bind exchange: source: VMSContracts.Tenant.Events:VendorRegistered, destination: VendorRegistered
2025-05-19 12:29:53.381 +05:30 [DBG] Bind exchange: source: VMSContracts.Subscription.Events:SubscriptionCreated, destination: SubscriptionCreated
2025-05-19 12:29:53.446 +05:30 [DBG] Consumer Ok: "rabbitmq://localhost/SubscriptionCreated" - amq.ctag-w1sumZ58gYEZJjbAhG4Inw
2025-05-19 12:29:53.446 +05:30 [DBG] Consumer Ok: "rabbitmq://localhost/SubscriptionStatusChanged" - amq.ctag-FxIj0USMxjQlXGe0Dde9VA
2025-05-19 12:29:53.446 +05:30 [DBG] Consumer Ok: "rabbitmq://localhost/VendorRegistered" - amq.ctag-kRse4w0ZYylo_1z-w6pPcw
2025-05-19 12:29:53.449 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/SubscriptionCreated"
2025-05-19 12:29:53.449 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/SubscriptionStatusChanged"
2025-05-19 12:29:53.449 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/VendorRegistered"
2025-05-19 12:29:53.453 +05:30 [INF] Bus started: "rabbitmq://localhost/"
2025-05-19 12:29:53.687 +05:30 [INF] Executed DbCommand (24ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 12:29:53.781 +05:30 [INF] Now listening on: http://localhost:5263
2025-05-19 12:29:53.782 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-05-19 12:29:53.782 +05:30 [INF] Hosting environment: Development
2025-05-19 12:29:53.782 +05:30 [INF] Content root path: E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.API
2025-05-19 12:29:54.182 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/swagger/index.html - null null
2025-05-19 12:29:54.191 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 12:29:54.297 +05:30 [INF] Expired session "5ffd6bd0-50c1-409f-b374-275f5132da7f" for user "816fb80f-3835-42e3-85bb-2dd493f98915"
2025-05-19 12:29:54.310 +05:30 [WRN] HttpContext is null
2025-05-19 12:29:54.376 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/swagger/index.html - 200 null text/html;charset=utf-8 199.1247ms
2025-05-19 12:29:54.472 +05:30 [INF] Executed DbCommand (7ms) [Parameters=[@p17='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?', @p2='?', @p3='?', @p4='?' (DbType = DateTime), @p5='?' (DbType = DateTime), @p6='?', @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime), @p9='?', @p18='?' (DbType = Binary), @p10='?' (DbType = DateTime), @p11='?' (DbType = Int32), @p12='?', @p13='?' (DbType = DateTime), @p14='?', @p15='?', @p16='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
UPDATE "UserSessions" SET "CreatedAt" = @p0, "CreatedBy" = @p1, "DeviceInfo" = @p2, "EndReason" = @p3, "EndedAt" = @p4, "ExpiresAt" = @p5, "IpAddress" = @p6, "IsDeleted" = @p7, "LastActiveAt" = @p8, "RefreshToken" = @p9, "StartedAt" = @p10, "Status" = @p11, "Token" = @p12, "UpdatedAt" = @p13, "UpdatedBy" = @p14, "UserAgent" = @p15, "UserId" = @p16
WHERE "Id" = @p17 AND "RowVersion" IS NULL
RETURNING "RowVersion";
2025-05-19 12:29:54.501 +05:30 [INF] Expired 1 idle or expired sessions
2025-05-19 12:29:54.501 +05:30 [DBG] Session cleanup completed
2025-05-19 12:29:54.656 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/swagger/v1/swagger.json - null null
2025-05-19 12:29:54.682 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/swagger/favicon-32x32.png - null null
2025-05-19 12:29:54.701 +05:30 [INF] Sending file. Request path: '/favicon-32x32.png'. Physical path: 'N/A'
2025-05-19 12:29:54.702 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/swagger/favicon-32x32.png - 200 628 image/png 19.5511ms
2025-05-19 12:29:54.990 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 334.5472ms
2025-05-19 12:30:59.341 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5263/api/Auth/login - application/json 145
2025-05-19 12:30:59.352 +05:30 [WRN] Failed to determine the https port for redirect.
2025-05-19 12:30:59.451 +05:30 [INF] Token received and is being processed
2025-05-19 12:30:59.545 +05:30 [WRN] User is null or not authenticated
2025-05-19 12:30:59.545 +05:30 [WRN] No user ID found in token
2025-05-19 12:30:59.550 +05:30 [INF] Executing endpoint 'IdentityService.API.Controllers.AuthController.Login (IdentityService.API)'
2025-05-19 12:30:59.571 +05:30 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[IdentityService.Application.DTOs.Auth.LoginResponse]] Login(IdentityService.Application.DTOs.Auth.LoginRequest) on controller IdentityService.API.Controllers.AuthController (IdentityService.API).
2025-05-19 12:30:59.656 +05:30 [WRN] User ID not found in HttpContext.Items
2025-05-19 12:30:59.657 +05:30 [INF] Setting UserId "00000000-0000-0000-0000-000000000000" on request of type LoginCommand
2025-05-19 12:30:59.658 +05:30 [WRN] User ID not found in HttpContext.Items
2025-05-19 12:30:59.661 +05:30 [INF] Validating request of type LoginCommand
2025-05-19 12:30:59.672 +05:30 [INF] Request content: {
  "Request": {
    "Email": "<EMAIL>",
    "Password": "Shri@1122",
    "PhoneNumber": "8088020875",
    "Otp": "1234",
    "RememberMe": true,
    "IsEmailLogin": true,
    "IsPhoneLogin": true
  },
  "UserId": "00000000-0000-0000-0000-000000000000"
}
2025-05-19 12:30:59.672 +05:30 [INF] Request UserId property value: "00000000-0000-0000-0000-000000000000"
2025-05-19 12:30:59.672 +05:30 [WRN] UserId is empty GUID (00000000-0000-0000-0000-000000000000)
2025-05-19 12:30:59.689 +05:30 [INF] Validation passed for request of type LoginCommand
2025-05-19 12:30:59.775 +05:30 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-05-19 12:30:59.797 +05:30 [INF] Executed DbCommand (11ms) [Parameters=[@__email_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash", t0."Id", t0."CreatedAt", t0."CreatedBy", t0."IsDeleted", t0."RoleId", t0."RowVersion", t0."UpdatedAt", t0."UpdatedBy", t0."UserId", t0."Id0", t0."CreatedAt0", t0."CreatedBy0", t0."Description", t0."IsDeleted0", t0."Name", t0."RowVersion0", t0."UpdatedAt0", t0."UpdatedBy0", t0."Id1", t0."CreatedAt1", t0."CreatedBy1", t0."IsDeleted1", t0."PermissionId", t0."RoleId0", t0."RowVersion1", t0."UpdatedAt1", t0."UpdatedBy1", t0."Id00", t0."Action", t0."CreatedAt00", t0."CreatedBy00", t0."Description0", t0."IsDeleted00", t0."Name0", t0."Resource", t0."RowVersion00", t0."UpdatedAt00", t0."UpdatedBy00"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."Email", u."EmailVerified", u."IsDeleted", u."LastLoginAt", u."PhoneNumber", u."PhoneNumberVerified", u."PrimaryBranchId", u."RefreshToken", u."RefreshTokenExpiryTime", u."RememberMe", u."RowVersion", u."SecurityStamp", u."UpdatedAt", u."UpdatedBy", u."VendorId", u."PasswordHash"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__email_0
    LIMIT 1
) AS t
LEFT JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."IsDeleted", u0."RoleId", u0."RowVersion", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", r."Id" AS "Id0", r."CreatedAt" AS "CreatedAt0", r."CreatedBy" AS "CreatedBy0", r."Description", r."IsDeleted" AS "IsDeleted0", r."Name", r."RowVersion" AS "RowVersion0", r."UpdatedAt" AS "UpdatedAt0", r."UpdatedBy" AS "UpdatedBy0", t1."Id" AS "Id1", t1."CreatedAt" AS "CreatedAt1", t1."CreatedBy" AS "CreatedBy1", t1."IsDeleted" AS "IsDeleted1", t1."PermissionId", t1."RoleId" AS "RoleId0", t1."RowVersion" AS "RowVersion1", t1."UpdatedAt" AS "UpdatedAt1", t1."UpdatedBy" AS "UpdatedBy1", t1."Id0" AS "Id00", t1."Action", t1."CreatedAt0" AS "CreatedAt00", t1."CreatedBy0" AS "CreatedBy00", t1."Description" AS "Description0", t1."IsDeleted0" AS "IsDeleted00", t1."Name" AS "Name0", t1."Resource", t1."RowVersion0" AS "RowVersion00", t1."UpdatedAt0" AS "UpdatedAt00", t1."UpdatedBy0" AS "UpdatedBy00"
    FROM "UserRoles" AS u0
    INNER JOIN "Roles" AS r ON u0."RoleId" = r."Id"
    LEFT JOIN (
        SELECT r0."Id", r0."CreatedAt", r0."CreatedBy", r0."IsDeleted", r0."PermissionId", r0."RoleId", r0."RowVersion", r0."UpdatedAt", r0."UpdatedBy", p."Id" AS "Id0", p."Action", p."CreatedAt" AS "CreatedAt0", p."CreatedBy" AS "CreatedBy0", p."Description", p."IsDeleted" AS "IsDeleted0", p."Name", p."Resource", p."RowVersion" AS "RowVersion0", p."UpdatedAt" AS "UpdatedAt0", p."UpdatedBy" AS "UpdatedBy0"
        FROM "RolePermissions" AS r0
        INNER JOIN "Permissions" AS p ON r0."PermissionId" = p."Id"
    ) AS t1 ON r."Id" = t1."RoleId"
) AS t0 ON t."Id" = t0."UserId"
ORDER BY t."Id", t0."Id", t0."Id0", t0."Id1"
2025-05-19 12:31:00.485 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT count(*)::int
FROM "UserSessions" AS u
WHERE u."UserId" = @__userId_0 AND u."Status" = 0 AND u."ExpiresAt" > now()
2025-05-19 12:31:00.491 +05:30 [WRN] User is null or not authenticated
2025-05-19 12:31:00.532 +05:30 [WRN] User is null or not authenticated
2025-05-19 12:31:00.533 +05:30 [WRN] User is null or not authenticated
2025-05-19 12:31:00.584 +05:30 [INF] Executed DbCommand (8ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = DateTime), @p2='?', @p3='?', @p4='?', @p5='?', @p6='?', @p7='?' (DbType = Boolean), @p8='?' (DbType = Boolean), @p9='?', @p10='?' (DbType = Int32), @p11='?', @p12='?' (DbType = Binary), @p13='?' (DbType = DateTime), @p14='?', @p15='?', @p16='?' (DbType = Guid), @p17='?' (DbType = Guid), @p18='?' (DbType = DateTime), @p19='?', @p20='?', @p21='?', @p22='?' (DbType = DateTime), @p23='?' (DbType = DateTime), @p24='?', @p25='?' (DbType = Boolean), @p26='?' (DbType = DateTime), @p27='?', @p28='?' (DbType = DateTime), @p29='?' (DbType = Int32), @p30='?', @p31='?' (DbType = DateTime), @p32='?', @p33='?', @p34='?' (DbType = Guid), @p39='?' (DbType = Guid), @p35='?' (DbType = DateTime), @p36='?', @p37='?' (DbType = DateTime), @p40='?' (DbType = Binary), @p38='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "UserLoginHistories" ("Id", "CreatedAt", "CreatedBy", "DeviceInfo", "Email", "FailureReason", "IpAddress", "IsDeleted", "IsSuccessful", "Location", "LoginMethod", "PhoneNumber", "RowVersion", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16);
INSERT INTO "UserSessions" ("Id", "CreatedAt", "CreatedBy", "DeviceInfo", "EndReason", "EndedAt", "ExpiresAt", "IpAddress", "IsDeleted", "LastActiveAt", "RefreshToken", "StartedAt", "Status", "Token", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34)
RETURNING "RowVersion";
UPDATE "Users" SET "LastLoginAt" = @p35, "RefreshToken" = @p36, "RefreshTokenExpiryTime" = @p37, "UpdatedAt" = @p38
WHERE "Id" = @p39 AND "RowVersion" = @p40
RETURNING "RowVersion";
2025-05-19 12:31:00.604 +05:30 [INF] Created new session "7d313753-e840-426f-b1f7-b8b9a3dc650c" for user "816fb80f-3835-42e3-85bb-2dd493f98915"
2025-05-19 12:31:00.612 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Name"
FROM "UserRoles" AS u
INNER JOIN "Roles" AS r ON u."RoleId" = r."Id"
WHERE u."UserId" = @__userId_0
2025-05-19 12:31:00.625 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."Description", r."IsDeleted", r."Name", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "UserRoles" AS u
INNER JOIN "Roles" AS r ON u."RoleId" = r."Id"
WHERE u."UserId" = @__userId_0
2025-05-19 12:31:00.636 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__role_Id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "RolePermissions" AS r
INNER JOIN "Permissions" AS p ON r."PermissionId" = p."Id"
WHERE r."RoleId" = @__role_Id_0
2025-05-19 12:31:00.654 +05:30 [INF] Executing OkObjectResult, writing value of type 'IdentityService.Application.DTOs.Auth.LoginResponse'.
2025-05-19 12:31:00.692 +05:30 [INF] Executed action IdentityService.API.Controllers.AuthController.Login (IdentityService.API) in 1117.9355ms
2025-05-19 12:31:00.693 +05:30 [INF] Executed endpoint 'IdentityService.API.Controllers.AuthController.Login (IdentityService.API)'
2025-05-19 12:31:00.706 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5263/api/Auth/login - 200 null application/json; charset=utf-8 1365.0847ms
2025-05-19 12:31:45.805 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/api/service/users/by-email?email=shriprasad.patil12%40svvatech.com - null null
2025-05-19 12:31:45.814 +05:30 [INF] Token received and is being processed
2025-05-19 12:31:45.881 +05:30 [INF] Token validated successfully with claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 816fb80f-3835-42e3-85bb-2dd493f98915, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 816fb80f-3835-42e3-85bb-2dd493f98915, jti: 2f7d08c8-e612-4178-897c-3baad437c210, security_stamp: 638832328945571960, http://schemas.microsoft.com/ws/2008/06/identity/claims/role: Admin, permission: Create, permission: test.permission, nbf: 1747638060, exp: 1747641660, iss: identity-service, aud: vms-api
2025-05-19 12:31:45.881 +05:30 [INF] NameIdentifier claim found: 816fb80f-3835-42e3-85bb-2dd493f98915
2025-05-19 12:31:45.926 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__get_Item_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."Email", u."EmailVerified", u."IsDeleted", u."LastLoginAt", u."PhoneNumber", u."PhoneNumberVerified", u."PrimaryBranchId", u."RefreshToken", u."RefreshTokenExpiryTime", u."RememberMe", u."RowVersion", u."SecurityStamp", u."UpdatedAt", u."UpdatedBy", u."VendorId", u."PasswordHash"
FROM "Users" AS u
WHERE NOT (u."IsDeleted") AND u."Id" = @__get_Item_0
LIMIT 1
2025-05-19 12:31:45.952 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__token_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Token" = @__token_0
LIMIT 1
2025-05-19 12:31:45.960 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 816fb80f-3835-42e3-85bb-2dd493f98915, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 816fb80f-3835-42e3-85bb-2dd493f98915, jti: 2f7d08c8-e612-4178-897c-3baad437c210, security_stamp: 638832328945571960, http://schemas.microsoft.com/ws/2008/06/identity/claims/role: Admin, permission: Create, permission: test.permission, nbf: 1747638060, exp: 1747641660, iss: identity-service, aud: vms-api
2025-05-19 12:31:45.960 +05:30 [INF] Successfully extracted user ID: "816fb80f-3835-42e3-85bb-2dd493f98915"
2025-05-19 12:31:45.964 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@p17='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?', @p2='?', @p3='?', @p4='?' (DbType = DateTime), @p5='?' (DbType = DateTime), @p6='?', @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime), @p9='?', @p18='?' (DbType = Binary), @p10='?' (DbType = DateTime), @p11='?' (DbType = Int32), @p12='?', @p13='?' (DbType = DateTime), @p14='?', @p15='?', @p16='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
UPDATE "UserSessions" SET "CreatedAt" = @p0, "CreatedBy" = @p1, "DeviceInfo" = @p2, "EndReason" = @p3, "EndedAt" = @p4, "ExpiresAt" = @p5, "IpAddress" = @p6, "IsDeleted" = @p7, "LastActiveAt" = @p8, "RefreshToken" = @p9, "StartedAt" = @p10, "Status" = @p11, "Token" = @p12, "UpdatedAt" = @p13, "UpdatedBy" = @p14, "UserAgent" = @p15, "UserId" = @p16
WHERE "Id" = @p17 AND "RowVersion" IS NULL
RETURNING "RowVersion";
2025-05-19 12:31:45.973 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__token_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Token" = @__token_0
LIMIT 1
2025-05-19 12:31:45.975 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 816fb80f-3835-42e3-85bb-2dd493f98915, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 816fb80f-3835-42e3-85bb-2dd493f98915, jti: 2f7d08c8-e612-4178-897c-3baad437c210, security_stamp: 638832328945571960, http://schemas.microsoft.com/ws/2008/06/identity/claims/role: Admin, permission: Create, permission: test.permission, nbf: 1747638060, exp: 1747641660, iss: identity-service, aud: vms-api
2025-05-19 12:31:45.975 +05:30 [INF] Successfully extracted user ID: "816fb80f-3835-42e3-85bb-2dd493f98915"
2025-05-19 12:31:45.978 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@p17='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?', @p2='?', @p3='?', @p4='?' (DbType = DateTime), @p5='?' (DbType = DateTime), @p6='?', @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime), @p9='?', @p18='?' (DbType = Binary), @p10='?' (DbType = DateTime), @p11='?' (DbType = Int32), @p12='?', @p13='?' (DbType = DateTime), @p14='?', @p15='?', @p16='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
UPDATE "UserSessions" SET "CreatedAt" = @p0, "CreatedBy" = @p1, "DeviceInfo" = @p2, "EndReason" = @p3, "EndedAt" = @p4, "ExpiresAt" = @p5, "IpAddress" = @p6, "IsDeleted" = @p7, "LastActiveAt" = @p8, "RefreshToken" = @p9, "StartedAt" = @p10, "Status" = @p11, "Token" = @p12, "UpdatedAt" = @p13, "UpdatedBy" = @p14, "UserAgent" = @p15, "UserId" = @p16
WHERE "Id" = @p17 AND "RowVersion" IS NULL
RETURNING "RowVersion";
2025-05-19 12:31:45.978 +05:30 [DBG] Updated last active time for session "7d313753-e840-426f-b1f7-b8b9a3dc650c"
2025-05-19 12:31:45.978 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 816fb80f-3835-42e3-85bb-2dd493f98915, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 816fb80f-3835-42e3-85bb-2dd493f98915, jti: 2f7d08c8-e612-4178-897c-3baad437c210, security_stamp: 638832328945571960, http://schemas.microsoft.com/ws/2008/06/identity/claims/role: Admin, permission: Create, permission: test.permission, nbf: 1747638060, exp: 1747641660, iss: identity-service, aud: vms-api
2025-05-19 12:31:45.979 +05:30 [INF] Successfully extracted user ID: "816fb80f-3835-42e3-85bb-2dd493f98915"
2025-05-19 12:31:45.979 +05:30 [INF] User ID from token: "816fb80f-3835-42e3-85bb-2dd493f98915"
2025-05-19 12:31:45.979 +05:30 [INF] Executing endpoint 'IdentityService.API.Controllers.ServiceApiController.GetUserByEmail (IdentityService.API)'
2025-05-19 12:31:45.990 +05:30 [INF] Route matched with {action = "GetUserByEmail", controller = "ServiceApi"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetUserByEmail(System.String) on controller IdentityService.API.Controllers.ServiceApiController (IdentityService.API).
2025-05-19 12:31:46.007 +05:30 [INF] Service API: Getting user <NAME_EMAIL>
2025-05-19 12:31:46.011 +05:30 [INF] Request of type GetUserByEmailQuery is not a BaseRequest, skipping UserId propagation
2025-05-19 12:31:46.012 +05:30 [INF] Validating request of type GetUserByEmailQuery
2025-05-19 12:31:46.013 +05:30 [INF] Request content: {
  "Email": "<EMAIL>"
}
2025-05-19 12:31:46.013 +05:30 [INF] Request does not have a UserId property
2025-05-19 12:31:46.013 +05:30 [INF] Validation passed for request of type GetUserByEmailQuery
2025-05-19 12:31:46.016 +05:30 [INF] Getting user <NAME_EMAIL>
2025-05-19 12:31:46.022 +05:30 [INF] Executed DbCommand (5ms) [Parameters=[@__email_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash", t0."Id", t0."CreatedAt", t0."CreatedBy", t0."IsDeleted", t0."RoleId", t0."RowVersion", t0."UpdatedAt", t0."UpdatedBy", t0."UserId", t0."Id0", t0."CreatedAt0", t0."CreatedBy0", t0."Description", t0."IsDeleted0", t0."Name", t0."RowVersion0", t0."UpdatedAt0", t0."UpdatedBy0", t0."Id1", t0."CreatedAt1", t0."CreatedBy1", t0."IsDeleted1", t0."PermissionId", t0."RoleId0", t0."RowVersion1", t0."UpdatedAt1", t0."UpdatedBy1", t0."Id00", t0."Action", t0."CreatedAt00", t0."CreatedBy00", t0."Description0", t0."IsDeleted00", t0."Name0", t0."Resource", t0."RowVersion00", t0."UpdatedAt00", t0."UpdatedBy00"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."Email", u."EmailVerified", u."IsDeleted", u."LastLoginAt", u."PhoneNumber", u."PhoneNumberVerified", u."PrimaryBranchId", u."RefreshToken", u."RefreshTokenExpiryTime", u."RememberMe", u."RowVersion", u."SecurityStamp", u."UpdatedAt", u."UpdatedBy", u."VendorId", u."PasswordHash"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__email_0
    LIMIT 1
) AS t
LEFT JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."IsDeleted", u0."RoleId", u0."RowVersion", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", r."Id" AS "Id0", r."CreatedAt" AS "CreatedAt0", r."CreatedBy" AS "CreatedBy0", r."Description", r."IsDeleted" AS "IsDeleted0", r."Name", r."RowVersion" AS "RowVersion0", r."UpdatedAt" AS "UpdatedAt0", r."UpdatedBy" AS "UpdatedBy0", t1."Id" AS "Id1", t1."CreatedAt" AS "CreatedAt1", t1."CreatedBy" AS "CreatedBy1", t1."IsDeleted" AS "IsDeleted1", t1."PermissionId", t1."RoleId" AS "RoleId0", t1."RowVersion" AS "RowVersion1", t1."UpdatedAt" AS "UpdatedAt1", t1."UpdatedBy" AS "UpdatedBy1", t1."Id0" AS "Id00", t1."Action", t1."CreatedAt0" AS "CreatedAt00", t1."CreatedBy0" AS "CreatedBy00", t1."Description" AS "Description0", t1."IsDeleted0" AS "IsDeleted00", t1."Name" AS "Name0", t1."Resource", t1."RowVersion0" AS "RowVersion00", t1."UpdatedAt0" AS "UpdatedAt00", t1."UpdatedBy0" AS "UpdatedBy00"
    FROM "UserRoles" AS u0
    INNER JOIN "Roles" AS r ON u0."RoleId" = r."Id"
    LEFT JOIN (
        SELECT r0."Id", r0."CreatedAt", r0."CreatedBy", r0."IsDeleted", r0."PermissionId", r0."RoleId", r0."RowVersion", r0."UpdatedAt", r0."UpdatedBy", p."Id" AS "Id0", p."Action", p."CreatedAt" AS "CreatedAt0", p."CreatedBy" AS "CreatedBy0", p."Description", p."IsDeleted" AS "IsDeleted0", p."Name", p."Resource", p."RowVersion" AS "RowVersion0", p."UpdatedAt" AS "UpdatedAt0", p."UpdatedBy" AS "UpdatedBy0"
        FROM "RolePermissions" AS r0
        INNER JOIN "Permissions" AS p ON r0."PermissionId" = p."Id"
    ) AS t1 ON r."Id" = t1."RoleId"
) AS t0 ON t."Id" = t0."UserId"
ORDER BY t."Id", t0."Id", t0."Id0", t0."Id1"
2025-05-19 12:31:46.027 +05:30 [INF] Successfully retrieved user <NAME_EMAIL>
2025-05-19 12:31:46.029 +05:30 [INF] Executing OkObjectResult, writing value of type 'IdentityService.Application.DTOs.Users.UserResponse'.
2025-05-19 12:31:46.034 +05:30 [INF] Executed action IdentityService.API.Controllers.ServiceApiController.GetUserByEmail (IdentityService.API) in 44.1242ms
2025-05-19 12:31:46.034 +05:30 [INF] Executed endpoint 'IdentityService.API.Controllers.ServiceApiController.GetUserByEmail (IdentityService.API)'
2025-05-19 12:31:46.035 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/api/service/users/by-email?email=shriprasad.patil12%40svvatech.com - 200 null application/json; charset=utf-8 229.9846ms
2025-05-19 12:31:59.879 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/api/Sessions/active - null null
2025-05-19 12:31:59.907 +05:30 [INF] Token received and is being processed
2025-05-19 12:31:59.909 +05:30 [INF] Token validated successfully with claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 816fb80f-3835-42e3-85bb-2dd493f98915, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 816fb80f-3835-42e3-85bb-2dd493f98915, jti: 2f7d08c8-e612-4178-897c-3baad437c210, security_stamp: 638832328945571960, http://schemas.microsoft.com/ws/2008/06/identity/claims/role: Admin, permission: Create, permission: test.permission, nbf: 1747638060, exp: 1747641660, iss: identity-service, aud: vms-api
2025-05-19 12:31:59.910 +05:30 [INF] NameIdentifier claim found: 816fb80f-3835-42e3-85bb-2dd493f98915
2025-05-19 12:31:59.927 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__get_Item_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."Email", u."EmailVerified", u."IsDeleted", u."LastLoginAt", u."PhoneNumber", u."PhoneNumberVerified", u."PrimaryBranchId", u."RefreshToken", u."RefreshTokenExpiryTime", u."RememberMe", u."RowVersion", u."SecurityStamp", u."UpdatedAt", u."UpdatedBy", u."VendorId", u."PasswordHash"
FROM "Users" AS u
WHERE NOT (u."IsDeleted") AND u."Id" = @__get_Item_0
LIMIT 1
2025-05-19 12:31:59.932 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__token_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Token" = @__token_0
LIMIT 1
2025-05-19 12:31:59.940 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 816fb80f-3835-42e3-85bb-2dd493f98915, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 816fb80f-3835-42e3-85bb-2dd493f98915, jti: 2f7d08c8-e612-4178-897c-3baad437c210, security_stamp: 638832328945571960, http://schemas.microsoft.com/ws/2008/06/identity/claims/role: Admin, permission: Create, permission: test.permission, nbf: 1747638060, exp: 1747641660, iss: identity-service, aud: vms-api
2025-05-19 12:31:59.940 +05:30 [INF] Successfully extracted user ID: "816fb80f-3835-42e3-85bb-2dd493f98915"
2025-05-19 12:31:59.942 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@p17='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?', @p2='?', @p3='?', @p4='?' (DbType = DateTime), @p5='?' (DbType = DateTime), @p6='?', @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime), @p9='?', @p18='?' (DbType = Binary), @p10='?' (DbType = DateTime), @p11='?' (DbType = Int32), @p12='?', @p13='?' (DbType = DateTime), @p14='?', @p15='?', @p16='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
UPDATE "UserSessions" SET "CreatedAt" = @p0, "CreatedBy" = @p1, "DeviceInfo" = @p2, "EndReason" = @p3, "EndedAt" = @p4, "ExpiresAt" = @p5, "IpAddress" = @p6, "IsDeleted" = @p7, "LastActiveAt" = @p8, "RefreshToken" = @p9, "StartedAt" = @p10, "Status" = @p11, "Token" = @p12, "UpdatedAt" = @p13, "UpdatedBy" = @p14, "UserAgent" = @p15, "UserId" = @p16
WHERE "Id" = @p17 AND "RowVersion" IS NULL
RETURNING "RowVersion";
2025-05-19 12:31:59.953 +05:30 [INF] Executed DbCommand (6ms) [Parameters=[@__token_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Token" = @__token_0
LIMIT 1
2025-05-19 12:31:59.987 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 816fb80f-3835-42e3-85bb-2dd493f98915, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 816fb80f-3835-42e3-85bb-2dd493f98915, jti: 2f7d08c8-e612-4178-897c-3baad437c210, security_stamp: 638832328945571960, http://schemas.microsoft.com/ws/2008/06/identity/claims/role: Admin, permission: Create, permission: test.permission, nbf: 1747638060, exp: 1747641660, iss: identity-service, aud: vms-api
2025-05-19 12:31:59.987 +05:30 [INF] Successfully extracted user ID: "816fb80f-3835-42e3-85bb-2dd493f98915"
2025-05-19 12:31:59.989 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@p17='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?', @p2='?', @p3='?', @p4='?' (DbType = DateTime), @p5='?' (DbType = DateTime), @p6='?', @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime), @p9='?', @p18='?' (DbType = Binary), @p10='?' (DbType = DateTime), @p11='?' (DbType = Int32), @p12='?', @p13='?' (DbType = DateTime), @p14='?', @p15='?', @p16='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
UPDATE "UserSessions" SET "CreatedAt" = @p0, "CreatedBy" = @p1, "DeviceInfo" = @p2, "EndReason" = @p3, "EndedAt" = @p4, "ExpiresAt" = @p5, "IpAddress" = @p6, "IsDeleted" = @p7, "LastActiveAt" = @p8, "RefreshToken" = @p9, "StartedAt" = @p10, "Status" = @p11, "Token" = @p12, "UpdatedAt" = @p13, "UpdatedBy" = @p14, "UserAgent" = @p15, "UserId" = @p16
WHERE "Id" = @p17 AND "RowVersion" IS NULL
RETURNING "RowVersion";
2025-05-19 12:31:59.989 +05:30 [DBG] Updated last active time for session "7d313753-e840-426f-b1f7-b8b9a3dc650c"
2025-05-19 12:31:59.989 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 816fb80f-3835-42e3-85bb-2dd493f98915, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 816fb80f-3835-42e3-85bb-2dd493f98915, jti: 2f7d08c8-e612-4178-897c-3baad437c210, security_stamp: 638832328945571960, http://schemas.microsoft.com/ws/2008/06/identity/claims/role: Admin, permission: Create, permission: test.permission, nbf: 1747638060, exp: 1747641660, iss: identity-service, aud: vms-api
2025-05-19 12:31:59.989 +05:30 [INF] Successfully extracted user ID: "816fb80f-3835-42e3-85bb-2dd493f98915"
2025-05-19 12:31:59.989 +05:30 [INF] User ID from token: "816fb80f-3835-42e3-85bb-2dd493f98915"
2025-05-19 12:31:59.989 +05:30 [INF] Executing endpoint 'IdentityService.API.Controllers.SessionsController.GetActiveSessions (IdentityService.API)'
2025-05-19 12:31:59.995 +05:30 [INF] Route matched with {action = "GetActiveSessions", controller = "Sessions"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[IdentityService.Domain.Entities.UserSession]]] GetActiveSessions() on controller IdentityService.API.Controllers.SessionsController (IdentityService.API).
2025-05-19 12:31:59.996 +05:30 [INF] All claims: http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 816fb80f-3835-42e3-85bb-2dd493f98915, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name: <EMAIL>, http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier: 816fb80f-3835-42e3-85bb-2dd493f98915, jti: 2f7d08c8-e612-4178-897c-3baad437c210, security_stamp: 638832328945571960, http://schemas.microsoft.com/ws/2008/06/identity/claims/role: Admin, permission: Create, permission: test.permission, nbf: 1747638060, exp: 1747641660, iss: identity-service, aud: vms-api
2025-05-19 12:31:59.997 +05:30 [INF] Successfully extracted user ID: "816fb80f-3835-42e3-85bb-2dd493f98915"
2025-05-19 12:32:00.155 +05:30 [INF] Executed DbCommand (137ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."UserId" = @__userId_0 AND u."Status" = 0 AND u."ExpiresAt" > now()
ORDER BY u."LastActiveAt" DESC
2025-05-19 12:32:00.157 +05:30 [INF] Executing OkObjectResult, writing value of type 'System.Collections.Generic.List`1[[IdentityService.Domain.Entities.UserSession, IdentityService.Domain, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-19 12:32:00.208 +05:30 [INF] Executed action IdentityService.API.Controllers.SessionsController.GetActiveSessions (IdentityService.API) in 212.8483ms
2025-05-19 12:32:00.208 +05:30 [INF] Executed endpoint 'IdentityService.API.Controllers.SessionsController.GetActiveSessions (IdentityService.API)'
2025-05-19 12:32:00.209 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/api/Sessions/active - 200 null application/json; charset=utf-8 329.8295ms
2025-05-19 12:34:54.516 +05:30 [DBG] Starting session cleanup
2025-05-19 12:34:54.656 +05:30 [INF] Executed DbCommand (23ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 12:34:54.660 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 12:34:54.663 +05:30 [INF] Expired session "e14053e9-fc09-448a-b134-75300853315a" for user "816fb80f-3835-42e3-85bb-2dd493f98915"
2025-05-19 12:34:54.664 +05:30 [WRN] HttpContext is null
2025-05-19 12:34:54.668 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@p17='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?', @p2='?', @p3='?', @p4='?' (DbType = DateTime), @p5='?' (DbType = DateTime), @p6='?', @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime), @p9='?', @p18='?' (DbType = Binary), @p10='?' (DbType = DateTime), @p11='?' (DbType = Int32), @p12='?', @p13='?' (DbType = DateTime), @p14='?', @p15='?', @p16='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
UPDATE "UserSessions" SET "CreatedAt" = @p0, "CreatedBy" = @p1, "DeviceInfo" = @p2, "EndReason" = @p3, "EndedAt" = @p4, "ExpiresAt" = @p5, "IpAddress" = @p6, "IsDeleted" = @p7, "LastActiveAt" = @p8, "RefreshToken" = @p9, "StartedAt" = @p10, "Status" = @p11, "Token" = @p12, "UpdatedAt" = @p13, "UpdatedBy" = @p14, "UserAgent" = @p15, "UserId" = @p16
WHERE "Id" = @p17 AND "RowVersion" IS NULL
RETURNING "RowVersion";
2025-05-19 12:34:54.669 +05:30 [INF] Expired 1 idle or expired sessions
2025-05-19 12:34:54.669 +05:30 [DBG] Session cleanup completed
2025-05-19 12:39:22.208 +05:30 [INF] Received VendorRegistered event for vendor "24a5963a-2878-4df8-be11-2336021df5d4"
2025-05-19 12:39:22.227 +05:30 [INF] Executed DbCommand (17ms) [Parameters=[@__email_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash", t0."Id", t0."CreatedAt", t0."CreatedBy", t0."IsDeleted", t0."RoleId", t0."RowVersion", t0."UpdatedAt", t0."UpdatedBy", t0."UserId", t0."Id0", t0."CreatedAt0", t0."CreatedBy0", t0."Description", t0."IsDeleted0", t0."Name", t0."RowVersion0", t0."UpdatedAt0", t0."UpdatedBy0", t0."Id1", t0."CreatedAt1", t0."CreatedBy1", t0."IsDeleted1", t0."PermissionId", t0."RoleId0", t0."RowVersion1", t0."UpdatedAt1", t0."UpdatedBy1", t0."Id00", t0."Action", t0."CreatedAt00", t0."CreatedBy00", t0."Description0", t0."IsDeleted00", t0."Name0", t0."Resource", t0."RowVersion00", t0."UpdatedAt00", t0."UpdatedBy00"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."Email", u."EmailVerified", u."IsDeleted", u."LastLoginAt", u."PhoneNumber", u."PhoneNumberVerified", u."PrimaryBranchId", u."RefreshToken", u."RefreshTokenExpiryTime", u."RememberMe", u."RowVersion", u."SecurityStamp", u."UpdatedAt", u."UpdatedBy", u."VendorId", u."PasswordHash"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__email_0
    LIMIT 1
) AS t
LEFT JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."IsDeleted", u0."RoleId", u0."RowVersion", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", r."Id" AS "Id0", r."CreatedAt" AS "CreatedAt0", r."CreatedBy" AS "CreatedBy0", r."Description", r."IsDeleted" AS "IsDeleted0", r."Name", r."RowVersion" AS "RowVersion0", r."UpdatedAt" AS "UpdatedAt0", r."UpdatedBy" AS "UpdatedBy0", t1."Id" AS "Id1", t1."CreatedAt" AS "CreatedAt1", t1."CreatedBy" AS "CreatedBy1", t1."IsDeleted" AS "IsDeleted1", t1."PermissionId", t1."RoleId" AS "RoleId0", t1."RowVersion" AS "RowVersion1", t1."UpdatedAt" AS "UpdatedAt1", t1."UpdatedBy" AS "UpdatedBy1", t1."Id0" AS "Id00", t1."Action", t1."CreatedAt0" AS "CreatedAt00", t1."CreatedBy0" AS "CreatedBy00", t1."Description" AS "Description0", t1."IsDeleted0" AS "IsDeleted00", t1."Name" AS "Name0", t1."Resource", t1."RowVersion0" AS "RowVersion00", t1."UpdatedAt0" AS "UpdatedAt00", t1."UpdatedBy0" AS "UpdatedBy00"
    FROM "UserRoles" AS u0
    INNER JOIN "Roles" AS r ON u0."RoleId" = r."Id"
    LEFT JOIN (
        SELECT r0."Id", r0."CreatedAt", r0."CreatedBy", r0."IsDeleted", r0."PermissionId", r0."RoleId", r0."RowVersion", r0."UpdatedAt", r0."UpdatedBy", p."Id" AS "Id0", p."Action", p."CreatedAt" AS "CreatedAt0", p."CreatedBy" AS "CreatedBy0", p."Description", p."IsDeleted" AS "IsDeleted0", p."Name", p."Resource", p."RowVersion" AS "RowVersion0", p."UpdatedAt" AS "UpdatedAt0", p."UpdatedBy" AS "UpdatedBy0"
        FROM "RolePermissions" AS r0
        INNER JOIN "Permissions" AS p ON r0."PermissionId" = p."Id"
    ) AS t1 ON r."Id" = t1."RoleId"
) AS t0 ON t."Id" = t0."UserId"
ORDER BY t."Id", t0."Id", t0."Id0", t0."Id1"
2025-05-19 12:39:22.474 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."Description", r."IsDeleted", r."Name", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "Roles" AS r
WHERE r."Name" = 'Vendor'
2025-05-19 12:39:22.481 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
2025-05-19 12:39:22.504 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = DateTime), @p2='?', @p3='?', @p4='?' (DbType = Boolean), @p5='?', @p6='?' (DbType = Binary), @p7='?' (DbType = DateTime), @p8='?', @p9='?' (DbType = Guid), @p10='?' (DbType = DateTime), @p11='?', @p12='?', @p13='?' (DbType = DateTime), @p14='?', @p15='?' (DbType = Guid), @p16='?', @p17='?' (DbType = DateTime), @p18='?' (DbType = DateTime), @p19='?' (DbType = DateTime), @p20='?', @p21='?' (DbType = Guid), @p22='?', @p23='?' (DbType = Guid), @p24='?' (DbType = DateTime), @p25='?', @p26='?' (DbType = Boolean), @p27='?' (DbType = Guid), @p28='?' (DbType = Binary), @p29='?' (DbType = DateTime), @p30='?', @p31='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Roles" ("Id", "CreatedAt", "CreatedBy", "Description", "IsDeleted", "Name", "RowVersion", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8);
INSERT INTO "Users" ("Id", "CreatedAt", "CreatedBy", "Email", "LastLoginAt", "PhoneNumber", "PrimaryBranchId", "RefreshToken", "RefreshTokenExpiryTime", "SecurityStamp", "UpdatedAt", "UpdatedBy", "VendorId", "PasswordHash")
VALUES (@p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22)
RETURNING "EmailVerified", "IsDeleted", "PhoneNumberVerified", "RememberMe", "RowVersion";
INSERT INTO "UserRoles" ("Id", "CreatedAt", "CreatedBy", "IsDeleted", "RoleId", "RowVersion", "UpdatedAt", "UpdatedBy", "UserId")
VALUES (@p23, @p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31);
2025-05-19 12:39:22.512 +05:30 [INF] Updating vendor "24a5963a-2878-4df8-be11-2336021df5d4" with user ID "5d907570-add2-4f96-b3df-2377d16ffc47"
2025-05-19 12:39:22.524 +05:30 [INF] Updating vendor "24a5963a-2878-4df8-be11-2336021df5d4" with user ID "5d907570-add2-4f96-b3df-2377d16ffc47" in Tenant Management Service
2025-05-19 12:39:22.535 +05:30 [INF] Start processing HTTP request PUT http://localhost:5003/api/vendor/24a5963a-2878-4df8-be11-2336021df5d4/user
2025-05-19 12:39:22.553 +05:30 [INF] Sending HTTP request PUT http://localhost:5003/api/vendor/24a5963a-2878-4df8-be11-2336021df5d4/user
2025-05-19 12:39:22.639 +05:30 [INF] Received HTTP response headers after 83.313ms - 404
2025-05-19 12:39:22.643 +05:30 [INF] End processing HTTP request after 110.0529ms - 404
2025-05-19 12:39:22.770 +05:30 [ERR] Error updating vendor "24a5963a-2878-4df8-be11-2336021df5d4" with user ID "5d907570-add2-4f96-b3df-2377d16ffc47" in Tenant Management Service
System.Net.Http.HttpRequestException: Response status code does not indicate success: 404 (Not Found).
   at System.Net.Http.HttpResponseMessage.EnsureSuccessStatusCode()
   at VMSContracts.ServiceClients.TenantManagementServiceClient.UpdateVendorUserIdAsync(Guid vendorId, Guid userId) in E:\Shri\AutomobilesGenerative\VMS-API\VMSContracts\ServiceClients\TenantManagementServiceClient.cs:line 166
2025-05-19 12:39:23.077 +05:30 [ERR] Error updating vendor "24a5963a-2878-4df8-be11-2336021df5d4" with user ID "5d907570-add2-4f96-b3df-2377d16ffc47"
System.Net.Http.HttpRequestException: Response status code does not indicate success: 404 (Not Found).
   at System.Net.Http.HttpResponseMessage.EnsureSuccessStatusCode()
   at VMSContracts.ServiceClients.TenantManagementServiceClient.UpdateVendorUserIdAsync(Guid vendorId, Guid userId) in E:\Shri\AutomobilesGenerative\VMS-API\VMSContracts\ServiceClients\TenantManagementServiceClient.cs:line 166
   at IdentityService.Infrastructure.Messaging.Consumers.VendorRegisteredConsumer.Consume(ConsumeContext`1 context) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.Infrastructure\Messaging\Consumers\VendorRegisteredConsumer.cs:line 93
2025-05-19 12:39:23.081 +05:30 [INF] Sent credentials email to string
2025-05-19 12:39:23.085 +05:30 [INF] Publishing event UserCreatedEvent with ID "231c26ae-c1ec-41dd-aeb3-4a20c717a3cb"
2025-05-19 12:39:23.279 +05:30 [DBG] Declare exchange: name: IdentityService.Infrastructure.Messaging.Events:UserCreatedEvent, type: fanout, durable
2025-05-19 12:39:23.280 +05:30 [DBG] Declare exchange: name: VMSContracts.Identity.Events:UserCreated, type: fanout, durable
2025-05-19 12:39:23.280 +05:30 [DBG] Declare exchange: name: VMSContracts.Common:IEvent, type: fanout, durable
2025-05-19 12:39:23.280 +05:30 [DBG] Declare exchange: name: IdentityService.Infrastructure.Messaging.Events:BaseEvent, type: fanout, durable
2025-05-19 12:39:23.307 +05:30 [DBG] Bind exchange: source: IdentityService.Infrastructure.Messaging.Events:UserCreatedEvent, destination: VMSContracts.Identity.Events:UserCreated
2025-05-19 12:39:23.308 +05:30 [DBG] Bind exchange: source: IdentityService.Infrastructure.Messaging.Events:UserCreatedEvent, destination: VMSContracts.Common:IEvent
2025-05-19 12:39:23.309 +05:30 [DBG] Bind exchange: source: IdentityService.Infrastructure.Messaging.Events:UserCreatedEvent, destination: IdentityService.Infrastructure.Messaging.Events:BaseEvent
2025-05-19 12:39:23.434 +05:30 [DBG] SEND "rabbitmq://localhost/IdentityService.Infrastructure.Messaging.Events:UserCreatedEvent" "ec300000-ad55-54e1-9fc4-08dd96a4153e" IdentityService.Infrastructure.Messaging.Events.UserCreatedEvent
2025-05-19 12:39:23.442 +05:30 [INF] Published event UserCreatedEvent with ID "231c26ae-c1ec-41dd-aeb3-4a20c717a3cb"
2025-05-19 12:39:23.442 +05:30 [INF] Published UserCreated event for user "5d907570-add2-4f96-b3df-2377d16ffc47"
2025-05-19 12:39:23.443 +05:30 [INF] Successfully created user "5d907570-add2-4f96-b3df-2377d16ffc47" for vendor "24a5963a-2878-4df8-be11-2336021df5d4"
2025-05-19 12:39:23.459 +05:30 [DBG] RECEIVE "rabbitmq://localhost/VendorRegistered" "904e0000-ad55-54e1-7d98-08dd96a41479" VMSContracts.Tenant.Events.VendorRegistered IdentityService.Infrastructure.Messaging.Consumers.VendorRegisteredConsumer("00:00:01.3240195")
2025-05-19 12:39:54.673 +05:30 [DBG] Starting session cleanup
2025-05-19 12:39:54.686 +05:30 [INF] Executed DbCommand (8ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 12:39:54.690 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 12:39:54.690 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 12:39:54.690 +05:30 [DBG] Session cleanup completed
2025-05-19 12:44:07.853 +05:30 [INF] Received VendorRegistered event for vendor "7772f9c6-f734-495b-9796-f5307db5d428"
2025-05-19 12:44:08.124 +05:30 [INF] Executed DbCommand (18ms) [Parameters=[@__email_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash", t0."Id", t0."CreatedAt", t0."CreatedBy", t0."IsDeleted", t0."RoleId", t0."RowVersion", t0."UpdatedAt", t0."UpdatedBy", t0."UserId", t0."Id0", t0."CreatedAt0", t0."CreatedBy0", t0."Description", t0."IsDeleted0", t0."Name", t0."RowVersion0", t0."UpdatedAt0", t0."UpdatedBy0", t0."Id1", t0."CreatedAt1", t0."CreatedBy1", t0."IsDeleted1", t0."PermissionId", t0."RoleId0", t0."RowVersion1", t0."UpdatedAt1", t0."UpdatedBy1", t0."Id00", t0."Action", t0."CreatedAt00", t0."CreatedBy00", t0."Description0", t0."IsDeleted00", t0."Name0", t0."Resource", t0."RowVersion00", t0."UpdatedAt00", t0."UpdatedBy00"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."Email", u."EmailVerified", u."IsDeleted", u."LastLoginAt", u."PhoneNumber", u."PhoneNumberVerified", u."PrimaryBranchId", u."RefreshToken", u."RefreshTokenExpiryTime", u."RememberMe", u."RowVersion", u."SecurityStamp", u."UpdatedAt", u."UpdatedBy", u."VendorId", u."PasswordHash"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__email_0
    LIMIT 1
) AS t
LEFT JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."IsDeleted", u0."RoleId", u0."RowVersion", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", r."Id" AS "Id0", r."CreatedAt" AS "CreatedAt0", r."CreatedBy" AS "CreatedBy0", r."Description", r."IsDeleted" AS "IsDeleted0", r."Name", r."RowVersion" AS "RowVersion0", r."UpdatedAt" AS "UpdatedAt0", r."UpdatedBy" AS "UpdatedBy0", t1."Id" AS "Id1", t1."CreatedAt" AS "CreatedAt1", t1."CreatedBy" AS "CreatedBy1", t1."IsDeleted" AS "IsDeleted1", t1."PermissionId", t1."RoleId" AS "RoleId0", t1."RowVersion" AS "RowVersion1", t1."UpdatedAt" AS "UpdatedAt1", t1."UpdatedBy" AS "UpdatedBy1", t1."Id0" AS "Id00", t1."Action", t1."CreatedAt0" AS "CreatedAt00", t1."CreatedBy0" AS "CreatedBy00", t1."Description" AS "Description0", t1."IsDeleted0" AS "IsDeleted00", t1."Name" AS "Name0", t1."Resource", t1."RowVersion0" AS "RowVersion00", t1."UpdatedAt0" AS "UpdatedAt00", t1."UpdatedBy0" AS "UpdatedBy00"
    FROM "UserRoles" AS u0
    INNER JOIN "Roles" AS r ON u0."RoleId" = r."Id"
    LEFT JOIN (
        SELECT r0."Id", r0."CreatedAt", r0."CreatedBy", r0."IsDeleted", r0."PermissionId", r0."RoleId", r0."RowVersion", r0."UpdatedAt", r0."UpdatedBy", p."Id" AS "Id0", p."Action", p."CreatedAt" AS "CreatedAt0", p."CreatedBy" AS "CreatedBy0", p."Description", p."IsDeleted" AS "IsDeleted0", p."Name", p."Resource", p."RowVersion" AS "RowVersion0", p."UpdatedAt" AS "UpdatedAt0", p."UpdatedBy" AS "UpdatedBy0"
        FROM "RolePermissions" AS r0
        INNER JOIN "Permissions" AS p ON r0."PermissionId" = p."Id"
    ) AS t1 ON r."Id" = t1."RoleId"
) AS t0 ON t."Id" = t0."UserId"
ORDER BY t."Id", t0."Id", t0."Id0", t0."Id1"
2025-05-19 12:44:08.349 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."Description", r."IsDeleted", r."Name", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "Roles" AS r
WHERE r."Name" = 'Vendor'
2025-05-19 12:44:08.355 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = DateTime), @p2='?', @p3='?', @p4='?' (DbType = DateTime), @p5='?', @p6='?' (DbType = Guid), @p7='?', @p8='?' (DbType = DateTime), @p9='?' (DbType = DateTime), @p10='?' (DbType = DateTime), @p11='?', @p12='?' (DbType = Guid), @p13='?', @p14='?' (DbType = Guid), @p15='?' (DbType = DateTime), @p16='?', @p17='?' (DbType = Boolean), @p18='?' (DbType = Guid), @p19='?' (DbType = Binary), @p20='?' (DbType = DateTime), @p21='?', @p22='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Users" ("Id", "CreatedAt", "CreatedBy", "Email", "LastLoginAt", "PhoneNumber", "PrimaryBranchId", "RefreshToken", "RefreshTokenExpiryTime", "SecurityStamp", "UpdatedAt", "UpdatedBy", "VendorId", "PasswordHash")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13)
RETURNING "EmailVerified", "IsDeleted", "PhoneNumberVerified", "RememberMe", "RowVersion";
INSERT INTO "UserRoles" ("Id", "CreatedAt", "CreatedBy", "IsDeleted", "RoleId", "RowVersion", "UpdatedAt", "UpdatedBy", "UserId")
VALUES (@p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22);
2025-05-19 12:44:08.359 +05:30 [INF] Updating vendor "7772f9c6-f734-495b-9796-f5307db5d428" with user ID "cfeade9f-8334-42b6-a00d-df6e0227851b"
2025-05-19 12:44:08.359 +05:30 [INF] Updating vendor "7772f9c6-f734-495b-9796-f5307db5d428" with user ID "cfeade9f-8334-42b6-a00d-df6e0227851b" in Tenant Management Service
2025-05-19 12:44:08.360 +05:30 [INF] Start processing HTTP request PUT http://localhost:5003/api/vendor/7772f9c6-f734-495b-9796-f5307db5d428/user
2025-05-19 12:44:08.361 +05:30 [INF] Sending HTTP request PUT http://localhost:5003/api/vendor/7772f9c6-f734-495b-9796-f5307db5d428/user
2025-05-19 12:44:08.422 +05:30 [INF] Received HTTP response headers after 60.7152ms - 404
2025-05-19 12:44:08.423 +05:30 [INF] End processing HTTP request after 62.413ms - 404
2025-05-19 12:44:08.527 +05:30 [ERR] Error updating vendor "7772f9c6-f734-495b-9796-f5307db5d428" with user ID "cfeade9f-8334-42b6-a00d-df6e0227851b" in Tenant Management Service
System.Net.Http.HttpRequestException: Response status code does not indicate success: 404 (Not Found).
   at System.Net.Http.HttpResponseMessage.EnsureSuccessStatusCode()
   at VMSContracts.ServiceClients.TenantManagementServiceClient.UpdateVendorUserIdAsync(Guid vendorId, Guid userId) in E:\Shri\AutomobilesGenerative\VMS-API\VMSContracts\ServiceClients\TenantManagementServiceClient.cs:line 166
2025-05-19 12:44:08.693 +05:30 [ERR] Error updating vendor "7772f9c6-f734-495b-9796-f5307db5d428" with user ID "cfeade9f-8334-42b6-a00d-df6e0227851b"
System.Net.Http.HttpRequestException: Response status code does not indicate success: 404 (Not Found).
   at System.Net.Http.HttpResponseMessage.EnsureSuccessStatusCode()
   at VMSContracts.ServiceClients.TenantManagementServiceClient.UpdateVendorUserIdAsync(Guid vendorId, Guid userId) in E:\Shri\AutomobilesGenerative\VMS-API\VMSContracts\ServiceClients\TenantManagementServiceClient.cs:line 166
   at IdentityService.Infrastructure.Messaging.Consumers.VendorRegisteredConsumer.Consume(ConsumeContext`1 context) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.Infrastructure\Messaging\Consumers\VendorRegisteredConsumer.cs:line 93
2025-05-19 12:44:08.695 +05:30 [INF] Sent credentials <NAME_EMAIL>
2025-05-19 12:44:08.695 +05:30 [INF] Publishing event UserCreatedEvent with ID "5daa008f-7251-4b8f-8587-60fba52a05c9"
2025-05-19 12:44:08.705 +05:30 [DBG] SEND "rabbitmq://localhost/IdentityService.Infrastructure.Messaging.Events:UserCreatedEvent" "ec300000-ad55-54e1-8666-08dd96a4bf6e" IdentityService.Infrastructure.Messaging.Events.UserCreatedEvent
2025-05-19 12:44:08.705 +05:30 [INF] Published event UserCreatedEvent with ID "5daa008f-7251-4b8f-8587-60fba52a05c9"
2025-05-19 12:44:08.705 +05:30 [INF] Published UserCreated event for user "cfeade9f-8334-42b6-a00d-df6e0227851b"
2025-05-19 12:44:08.705 +05:30 [INF] Successfully created user "cfeade9f-8334-42b6-a00d-df6e0227851b" for vendor "7772f9c6-f734-495b-9796-f5307db5d428"
2025-05-19 12:44:08.706 +05:30 [DBG] RECEIVE "rabbitmq://localhost/VendorRegistered" "04430000-ad55-54e1-bbf6-08dd96a4bed9" VMSContracts.Tenant.Events.VendorRegistered IdentityService.Infrastructure.Messaging.Consumers.VendorRegisteredConsumer("00:00:00.8570068")
2025-05-19 12:44:54.700 +05:30 [DBG] Starting session cleanup
2025-05-19 12:44:54.747 +05:30 [INF] Executed DbCommand (40ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 12:44:54.756 +05:30 [INF] Executed DbCommand (6ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 12:44:54.756 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 12:44:54.756 +05:30 [DBG] Session cleanup completed
2025-05-19 12:46:37.152 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5263/api/Auth/login - application/json 128
2025-05-19 12:46:37.154 +05:30 [INF] Token received and is being processed
2025-05-19 12:46:37.154 +05:30 [WRN] User is null or not authenticated
2025-05-19 12:46:37.155 +05:30 [WRN] No user ID found in token
2025-05-19 12:46:37.155 +05:30 [INF] Executing endpoint 'IdentityService.API.Controllers.AuthController.Login (IdentityService.API)'
2025-05-19 12:46:37.155 +05:30 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[IdentityService.Application.DTOs.Auth.LoginResponse]] Login(IdentityService.Application.DTOs.Auth.LoginRequest) on controller IdentityService.API.Controllers.AuthController (IdentityService.API).
2025-05-19 12:46:37.161 +05:30 [WRN] User ID not found in HttpContext.Items
2025-05-19 12:46:37.161 +05:30 [INF] Setting UserId "00000000-0000-0000-0000-000000000000" on request of type LoginCommand
2025-05-19 12:46:37.161 +05:30 [WRN] User ID not found in HttpContext.Items
2025-05-19 12:46:37.161 +05:30 [INF] Validating request of type LoginCommand
2025-05-19 12:46:37.161 +05:30 [INF] Request content: {
  "Request": {
    "Email": "<EMAIL>",
    "Password": "Admin@123",
    "PhoneNumber": "8088020875",
    "Otp": "1234",
    "RememberMe": true,
    "IsEmailLogin": true,
    "IsPhoneLogin": true
  },
  "UserId": "00000000-0000-0000-0000-000000000000"
}
2025-05-19 12:46:37.161 +05:30 [INF] Request UserId property value: "00000000-0000-0000-0000-000000000000"
2025-05-19 12:46:37.161 +05:30 [WRN] UserId is empty GUID (00000000-0000-0000-0000-000000000000)
2025-05-19 12:46:37.163 +05:30 [INF] Validation passed for request of type LoginCommand
2025-05-19 12:46:37.168 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__email_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash", t0."Id", t0."CreatedAt", t0."CreatedBy", t0."IsDeleted", t0."RoleId", t0."RowVersion", t0."UpdatedAt", t0."UpdatedBy", t0."UserId", t0."Id0", t0."CreatedAt0", t0."CreatedBy0", t0."Description", t0."IsDeleted0", t0."Name", t0."RowVersion0", t0."UpdatedAt0", t0."UpdatedBy0", t0."Id1", t0."CreatedAt1", t0."CreatedBy1", t0."IsDeleted1", t0."PermissionId", t0."RoleId0", t0."RowVersion1", t0."UpdatedAt1", t0."UpdatedBy1", t0."Id00", t0."Action", t0."CreatedAt00", t0."CreatedBy00", t0."Description0", t0."IsDeleted00", t0."Name0", t0."Resource", t0."RowVersion00", t0."UpdatedAt00", t0."UpdatedBy00"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."Email", u."EmailVerified", u."IsDeleted", u."LastLoginAt", u."PhoneNumber", u."PhoneNumberVerified", u."PrimaryBranchId", u."RefreshToken", u."RefreshTokenExpiryTime", u."RememberMe", u."RowVersion", u."SecurityStamp", u."UpdatedAt", u."UpdatedBy", u."VendorId", u."PasswordHash"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__email_0
    LIMIT 1
) AS t
LEFT JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."IsDeleted", u0."RoleId", u0."RowVersion", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", r."Id" AS "Id0", r."CreatedAt" AS "CreatedAt0", r."CreatedBy" AS "CreatedBy0", r."Description", r."IsDeleted" AS "IsDeleted0", r."Name", r."RowVersion" AS "RowVersion0", r."UpdatedAt" AS "UpdatedAt0", r."UpdatedBy" AS "UpdatedBy0", t1."Id" AS "Id1", t1."CreatedAt" AS "CreatedAt1", t1."CreatedBy" AS "CreatedBy1", t1."IsDeleted" AS "IsDeleted1", t1."PermissionId", t1."RoleId" AS "RoleId0", t1."RowVersion" AS "RowVersion1", t1."UpdatedAt" AS "UpdatedAt1", t1."UpdatedBy" AS "UpdatedBy1", t1."Id0" AS "Id00", t1."Action", t1."CreatedAt0" AS "CreatedAt00", t1."CreatedBy0" AS "CreatedBy00", t1."Description" AS "Description0", t1."IsDeleted0" AS "IsDeleted00", t1."Name" AS "Name0", t1."Resource", t1."RowVersion0" AS "RowVersion00", t1."UpdatedAt0" AS "UpdatedAt00", t1."UpdatedBy0" AS "UpdatedBy00"
    FROM "UserRoles" AS u0
    INNER JOIN "Roles" AS r ON u0."RoleId" = r."Id"
    LEFT JOIN (
        SELECT r0."Id", r0."CreatedAt", r0."CreatedBy", r0."IsDeleted", r0."PermissionId", r0."RoleId", r0."RowVersion", r0."UpdatedAt", r0."UpdatedBy", p."Id" AS "Id0", p."Action", p."CreatedAt" AS "CreatedAt0", p."CreatedBy" AS "CreatedBy0", p."Description", p."IsDeleted" AS "IsDeleted0", p."Name", p."Resource", p."RowVersion" AS "RowVersion0", p."UpdatedAt" AS "UpdatedAt0", p."UpdatedBy" AS "UpdatedBy0"
        FROM "RolePermissions" AS r0
        INNER JOIN "Permissions" AS p ON r0."PermissionId" = p."Id"
    ) AS t1 ON r."Id" = t1."RoleId"
) AS t0 ON t."Id" = t0."UserId"
ORDER BY t."Id", t0."Id", t0."Id0", t0."Id1"
2025-05-19 12:46:37.351 +05:30 [WRN] Login failed: Invalid email or password
System.UnauthorizedAccessException: Invalid email or password
   at IdentityService.Application.Features.Auth.Commands.LoginCommandHandler.Handle(LoginCommand request, CancellationToken cancellationToken) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.Application\Features\Auth\Commands\LoginCommand.cs:line 73
2025-05-19 12:46:37.362 +05:30 [INF] Executed DbCommand (9ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = DateTime), @p2='?', @p3='?', @p4='?', @p5='?', @p6='?', @p7='?' (DbType = Boolean), @p8='?' (DbType = Boolean), @p9='?', @p10='?' (DbType = Int32), @p11='?', @p12='?' (DbType = Binary), @p13='?' (DbType = DateTime), @p14='?', @p15='?', @p16='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "UserLoginHistories" ("Id", "CreatedAt", "CreatedBy", "DeviceInfo", "Email", "FailureReason", "IpAddress", "IsDeleted", "IsSuccessful", "Location", "LoginMethod", "PhoneNumber", "RowVersion", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16);
2025-05-19 12:46:38.284 +05:30 [WRN] Login failed for user
System.UnauthorizedAccessException: Invalid email or password
   at IdentityService.Application.Features.Auth.Commands.LoginCommandHandler.Handle(LoginCommand request, CancellationToken cancellationToken) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.Application\Features\Auth\Commands\LoginCommand.cs:line 73
   at IdentityService.Application.Features.Auth.Commands.LoginCommandHandler.Handle(LoginCommand request, CancellationToken cancellationToken) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.Application\Features\Auth\Commands\LoginCommand.cs:line 216
   at IdentityService.Application.Behaviors.ValidationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.Application\Behaviors\ValidationBehavior.cs:line 95
   at IdentityService.Application.Behaviors.UserIdPropagationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.Application\Behaviors\UserIdPropagationBehavior.cs:line 39
   at MediatR.Pipeline.RequestExceptionProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestExceptionProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestExceptionActionProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestExceptionActionProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestPostProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestPreProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at IdentityService.API.Controllers.AuthController.Login(LoginRequest request) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.API\Controllers\AuthController.cs:line 31
2025-05-19 12:46:38.287 +05:30 [INF] Executing UnauthorizedObjectResult, writing value of type '<>f__AnonymousType0`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-05-19 12:46:38.288 +05:30 [INF] Executed action IdentityService.API.Controllers.AuthController.Login (IdentityService.API) in 1133.1551ms
2025-05-19 12:46:38.288 +05:30 [INF] Executed endpoint 'IdentityService.API.Controllers.AuthController.Login (IdentityService.API)'
2025-05-19 12:46:38.289 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5263/api/Auth/login - 401 null application/json; charset=utf-8 1137.0543ms
2025-05-19 12:47:54.317 +05:30 [INF] Request starting HTTP/1.1 POST http://localhost:5263/api/Auth/login - application/json 128
2025-05-19 12:47:54.317 +05:30 [INF] Token received and is being processed
2025-05-19 12:47:54.318 +05:30 [WRN] User is null or not authenticated
2025-05-19 12:47:54.318 +05:30 [WRN] No user ID found in token
2025-05-19 12:47:54.318 +05:30 [INF] Executing endpoint 'IdentityService.API.Controllers.AuthController.Login (IdentityService.API)'
2025-05-19 12:47:54.319 +05:30 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[IdentityService.Application.DTOs.Auth.LoginResponse]] Login(IdentityService.Application.DTOs.Auth.LoginRequest) on controller IdentityService.API.Controllers.AuthController (IdentityService.API).
2025-05-19 12:47:54.322 +05:30 [WRN] User ID not found in HttpContext.Items
2025-05-19 12:47:54.322 +05:30 [INF] Setting UserId "00000000-0000-0000-0000-000000000000" on request of type LoginCommand
2025-05-19 12:47:54.322 +05:30 [WRN] User ID not found in HttpContext.Items
2025-05-19 12:47:54.322 +05:30 [INF] Validating request of type LoginCommand
2025-05-19 12:47:54.322 +05:30 [INF] Request content: {
  "Request": {
    "Email": "<EMAIL>",
    "Password": "Shri@1122",
    "PhoneNumber": "8088020875",
    "Otp": "1234",
    "RememberMe": true,
    "IsEmailLogin": true,
    "IsPhoneLogin": true
  },
  "UserId": "00000000-0000-0000-0000-000000000000"
}
2025-05-19 12:47:54.322 +05:30 [INF] Request UserId property value: "00000000-0000-0000-0000-000000000000"
2025-05-19 12:47:54.322 +05:30 [WRN] UserId is empty GUID (00000000-0000-0000-0000-000000000000)
2025-05-19 12:47:54.322 +05:30 [INF] Validation passed for request of type LoginCommand
2025-05-19 12:47:54.328 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__email_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash", t0."Id", t0."CreatedAt", t0."CreatedBy", t0."IsDeleted", t0."RoleId", t0."RowVersion", t0."UpdatedAt", t0."UpdatedBy", t0."UserId", t0."Id0", t0."CreatedAt0", t0."CreatedBy0", t0."Description", t0."IsDeleted0", t0."Name", t0."RowVersion0", t0."UpdatedAt0", t0."UpdatedBy0", t0."Id1", t0."CreatedAt1", t0."CreatedBy1", t0."IsDeleted1", t0."PermissionId", t0."RoleId0", t0."RowVersion1", t0."UpdatedAt1", t0."UpdatedBy1", t0."Id00", t0."Action", t0."CreatedAt00", t0."CreatedBy00", t0."Description0", t0."IsDeleted00", t0."Name0", t0."Resource", t0."RowVersion00", t0."UpdatedAt00", t0."UpdatedBy00"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."Email", u."EmailVerified", u."IsDeleted", u."LastLoginAt", u."PhoneNumber", u."PhoneNumberVerified", u."PrimaryBranchId", u."RefreshToken", u."RefreshTokenExpiryTime", u."RememberMe", u."RowVersion", u."SecurityStamp", u."UpdatedAt", u."UpdatedBy", u."VendorId", u."PasswordHash"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__email_0
    LIMIT 1
) AS t
LEFT JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."IsDeleted", u0."RoleId", u0."RowVersion", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", r."Id" AS "Id0", r."CreatedAt" AS "CreatedAt0", r."CreatedBy" AS "CreatedBy0", r."Description", r."IsDeleted" AS "IsDeleted0", r."Name", r."RowVersion" AS "RowVersion0", r."UpdatedAt" AS "UpdatedAt0", r."UpdatedBy" AS "UpdatedBy0", t1."Id" AS "Id1", t1."CreatedAt" AS "CreatedAt1", t1."CreatedBy" AS "CreatedBy1", t1."IsDeleted" AS "IsDeleted1", t1."PermissionId", t1."RoleId" AS "RoleId0", t1."RowVersion" AS "RowVersion1", t1."UpdatedAt" AS "UpdatedAt1", t1."UpdatedBy" AS "UpdatedBy1", t1."Id0" AS "Id00", t1."Action", t1."CreatedAt0" AS "CreatedAt00", t1."CreatedBy0" AS "CreatedBy00", t1."Description" AS "Description0", t1."IsDeleted0" AS "IsDeleted00", t1."Name" AS "Name0", t1."Resource", t1."RowVersion0" AS "RowVersion00", t1."UpdatedAt0" AS "UpdatedAt00", t1."UpdatedBy0" AS "UpdatedBy00"
    FROM "UserRoles" AS u0
    INNER JOIN "Roles" AS r ON u0."RoleId" = r."Id"
    LEFT JOIN (
        SELECT r0."Id", r0."CreatedAt", r0."CreatedBy", r0."IsDeleted", r0."PermissionId", r0."RoleId", r0."RowVersion", r0."UpdatedAt", r0."UpdatedBy", p."Id" AS "Id0", p."Action", p."CreatedAt" AS "CreatedAt0", p."CreatedBy" AS "CreatedBy0", p."Description", p."IsDeleted" AS "IsDeleted0", p."Name", p."Resource", p."RowVersion" AS "RowVersion0", p."UpdatedAt" AS "UpdatedAt0", p."UpdatedBy" AS "UpdatedBy0"
        FROM "RolePermissions" AS r0
        INNER JOIN "Permissions" AS p ON r0."PermissionId" = p."Id"
    ) AS t1 ON r."Id" = t1."RoleId"
) AS t0 ON t."Id" = t0."UserId"
ORDER BY t."Id", t0."Id", t0."Id0", t0."Id1"
2025-05-19 12:47:54.484 +05:30 [WRN] Login failed: Invalid email or password
System.UnauthorizedAccessException: Invalid email or password
   at IdentityService.Application.Features.Auth.Commands.LoginCommandHandler.Handle(LoginCommand request, CancellationToken cancellationToken) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.Application\Features\Auth\Commands\LoginCommand.cs:line 73
2025-05-19 12:47:54.487 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = DateTime), @p2='?', @p3='?', @p4='?', @p5='?', @p6='?', @p7='?' (DbType = Boolean), @p8='?' (DbType = Boolean), @p9='?', @p10='?' (DbType = Int32), @p11='?', @p12='?' (DbType = Binary), @p13='?' (DbType = DateTime), @p14='?', @p15='?', @p16='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "UserLoginHistories" ("Id", "CreatedAt", "CreatedBy", "DeviceInfo", "Email", "FailureReason", "IpAddress", "IsDeleted", "IsSuccessful", "Location", "LoginMethod", "PhoneNumber", "RowVersion", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16);
2025-05-19 12:47:55.162 +05:30 [WRN] Login failed for user
System.UnauthorizedAccessException: Invalid email or password
   at IdentityService.Application.Features.Auth.Commands.LoginCommandHandler.Handle(LoginCommand request, CancellationToken cancellationToken) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.Application\Features\Auth\Commands\LoginCommand.cs:line 73
   at IdentityService.Application.Features.Auth.Commands.LoginCommandHandler.Handle(LoginCommand request, CancellationToken cancellationToken) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.Application\Features\Auth\Commands\LoginCommand.cs:line 216
   at IdentityService.Application.Behaviors.ValidationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.Application\Behaviors\ValidationBehavior.cs:line 95
   at IdentityService.Application.Behaviors.UserIdPropagationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.Application\Behaviors\UserIdPropagationBehavior.cs:line 39
   at MediatR.Pipeline.RequestExceptionProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestExceptionProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestExceptionActionProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestExceptionActionProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestPostProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at MediatR.Pipeline.RequestPreProcessorBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken)
   at IdentityService.API.Controllers.AuthController.Login(LoginRequest request) in E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.API\Controllers\AuthController.cs:line 31
2025-05-19 12:47:55.164 +05:30 [INF] Executing UnauthorizedObjectResult, writing value of type '<>f__AnonymousType0`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-05-19 12:47:55.164 +05:30 [INF] Executed action IdentityService.API.Controllers.AuthController.Login (IdentityService.API) in 845.1319ms
2025-05-19 12:47:55.164 +05:30 [INF] Executed endpoint 'IdentityService.API.Controllers.AuthController.Login (IdentityService.API)'
2025-05-19 12:47:55.164 +05:30 [INF] Request finished HTTP/1.1 POST http://localhost:5263/api/Auth/login - 401 null application/json; charset=utf-8 847.1136ms
2025-05-19 12:49:54.783 +05:30 [DBG] Starting session cleanup
2025-05-19 12:49:55.058 +05:30 [INF] Executed DbCommand (14ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 12:49:55.062 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 12:49:55.063 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 12:49:55.063 +05:30 [DBG] Session cleanup completed
2025-05-19 12:54:55.068 +05:30 [DBG] Starting session cleanup
2025-05-19 12:54:55.118 +05:30 [INF] Executed DbCommand (48ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 12:54:55.123 +05:30 [INF] Executed DbCommand (4ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 12:54:55.124 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 12:54:55.124 +05:30 [DBG] Session cleanup completed
2025-05-19 12:59:55.390 +05:30 [DBG] Starting session cleanup
2025-05-19 12:59:56.225 +05:30 [INF] Executed DbCommand (16ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 12:59:56.230 +05:30 [INF] Executed DbCommand (4ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 12:59:56.230 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 12:59:56.230 +05:30 [DBG] Session cleanup completed
2025-05-19 13:04:56.239 +05:30 [DBG] Starting session cleanup
2025-05-19 13:04:56.253 +05:30 [INF] Executed DbCommand (8ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 13:04:56.288 +05:30 [INF] Executed DbCommand (18ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 13:04:56.289 +05:30 [INF] Expired session "7d313753-e840-426f-b1f7-b8b9a3dc650c" for user "816fb80f-3835-42e3-85bb-2dd493f98915"
2025-05-19 13:04:56.289 +05:30 [WRN] HttpContext is null
2025-05-19 13:04:56.313 +05:30 [INF] Executed DbCommand (22ms) [Parameters=[@p17='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?', @p2='?', @p3='?', @p4='?' (DbType = DateTime), @p5='?' (DbType = DateTime), @p6='?', @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime), @p9='?', @p18='?' (DbType = Binary), @p10='?' (DbType = DateTime), @p11='?' (DbType = Int32), @p12='?', @p13='?' (DbType = DateTime), @p14='?', @p15='?', @p16='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
UPDATE "UserSessions" SET "CreatedAt" = @p0, "CreatedBy" = @p1, "DeviceInfo" = @p2, "EndReason" = @p3, "EndedAt" = @p4, "ExpiresAt" = @p5, "IpAddress" = @p6, "IsDeleted" = @p7, "LastActiveAt" = @p8, "RefreshToken" = @p9, "StartedAt" = @p10, "Status" = @p11, "Token" = @p12, "UpdatedAt" = @p13, "UpdatedBy" = @p14, "UserAgent" = @p15, "UserId" = @p16
WHERE "Id" = @p17 AND "RowVersion" IS NULL
RETURNING "RowVersion";
2025-05-19 13:04:56.313 +05:30 [INF] Expired 1 idle or expired sessions
2025-05-19 13:04:56.313 +05:30 [DBG] Session cleanup completed
2025-05-19 13:09:56.318 +05:30 [DBG] Starting session cleanup
2025-05-19 13:09:56.473 +05:30 [INF] Executed DbCommand (12ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 13:09:56.483 +05:30 [INF] Executed DbCommand (9ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 13:09:56.483 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 13:09:56.483 +05:30 [DBG] Session cleanup completed
2025-05-19 13:14:56.484 +05:30 [DBG] Starting session cleanup
2025-05-19 13:14:56.517 +05:30 [INF] Executed DbCommand (21ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 13:14:56.524 +05:30 [INF] Executed DbCommand (5ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 13:14:56.525 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 13:14:56.525 +05:30 [DBG] Session cleanup completed
2025-05-19 13:19:56.529 +05:30 [DBG] Starting session cleanup
2025-05-19 13:19:56.622 +05:30 [INF] Executed DbCommand (9ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 13:19:56.624 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 13:19:56.625 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 13:19:56.625 +05:30 [DBG] Session cleanup completed
2025-05-19 13:24:56.617 +05:30 [DBG] Starting session cleanup
2025-05-19 13:24:56.626 +05:30 [INF] Executed DbCommand (4ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 13:24:56.631 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 13:24:56.631 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 13:24:56.631 +05:30 [DBG] Session cleanup completed
2025-05-19 13:29:56.631 +05:30 [DBG] Starting session cleanup
2025-05-19 13:29:56.710 +05:30 [INF] Executed DbCommand (10ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 13:29:56.713 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 13:29:56.714 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 13:29:56.714 +05:30 [DBG] Session cleanup completed
2025-05-19 13:34:56.722 +05:30 [DBG] Starting session cleanup
2025-05-19 13:34:56.728 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 13:34:56.731 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 13:34:56.731 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 13:34:56.731 +05:30 [DBG] Session cleanup completed
2025-05-19 13:39:56.725 +05:30 [DBG] Starting session cleanup
2025-05-19 13:39:56.911 +05:30 [INF] Executed DbCommand (8ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 13:39:56.913 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 13:39:56.914 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 13:39:56.914 +05:30 [DBG] Session cleanup completed
2025-05-19 13:44:56.911 +05:30 [DBG] Starting session cleanup
2025-05-19 13:44:56.919 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 13:44:56.927 +05:30 [INF] Executed DbCommand (5ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 13:44:56.927 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 13:44:56.927 +05:30 [DBG] Session cleanup completed
2025-05-19 13:49:56.921 +05:30 [DBG] Starting session cleanup
2025-05-19 13:49:56.989 +05:30 [INF] Executed DbCommand (7ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 13:49:56.992 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 13:49:56.992 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 13:49:56.992 +05:30 [DBG] Session cleanup completed
2025-05-19 14:49:00.777 +05:30 [DBG] Starting session cleanup
2025-05-19 14:49:00.790 +05:30 [INF] Executed DbCommand (7ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 14:49:00.814 +05:30 [INF] Executed DbCommand (13ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 14:49:00.816 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 14:49:00.816 +05:30 [DBG] Session cleanup completed
2025-05-19 14:54:00.811 +05:30 [DBG] Starting session cleanup
2025-05-19 14:54:01.082 +05:30 [INF] Executed DbCommand (10ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 14:54:01.085 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 14:54:01.087 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 14:54:01.089 +05:30 [DBG] Session cleanup completed
2025-05-19 14:59:01.094 +05:30 [DBG] Starting session cleanup
2025-05-19 14:59:01.124 +05:30 [INF] Executed DbCommand (20ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 14:59:01.172 +05:30 [INF] Executed DbCommand (28ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 14:59:01.173 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 14:59:01.174 +05:30 [DBG] Session cleanup completed
2025-05-19 15:04:01.177 +05:30 [DBG] Starting session cleanup
2025-05-19 15:04:01.473 +05:30 [INF] Executed DbCommand (14ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 15:04:01.481 +05:30 [INF] Executed DbCommand (7ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 15:04:01.482 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 15:04:01.483 +05:30 [DBG] Session cleanup completed
2025-05-19 15:09:01.498 +05:30 [DBG] Starting session cleanup
2025-05-19 15:09:01.526 +05:30 [INF] Executed DbCommand (13ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 15:09:01.551 +05:30 [INF] Executed DbCommand (8ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 15:09:01.551 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 15:09:01.552 +05:30 [DBG] Session cleanup completed
2025-05-19 15:14:01.557 +05:30 [DBG] Starting session cleanup
2025-05-19 15:14:01.881 +05:30 [INF] Executed DbCommand (12ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 15:14:01.894 +05:30 [INF] Executed DbCommand (12ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 15:14:01.895 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 15:14:01.895 +05:30 [DBG] Session cleanup completed
2025-05-19 15:19:01.902 +05:30 [DBG] Starting session cleanup
2025-05-19 15:19:01.923 +05:30 [INF] Executed DbCommand (6ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 15:19:01.945 +05:30 [INF] Executed DbCommand (5ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 15:19:01.948 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 15:19:01.949 +05:30 [DBG] Session cleanup completed
2025-05-19 15:24:01.956 +05:30 [DBG] Starting session cleanup
2025-05-19 15:24:02.155 +05:30 [INF] Executed DbCommand (9ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 15:24:02.162 +05:30 [INF] Executed DbCommand (4ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 15:24:02.163 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 15:24:02.163 +05:30 [DBG] Session cleanup completed
2025-05-19 15:29:02.176 +05:30 [DBG] Starting session cleanup
2025-05-19 15:29:02.209 +05:30 [INF] Executed DbCommand (15ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 15:29:02.264 +05:30 [INF] Executed DbCommand (52ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 15:29:02.284 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 15:29:02.284 +05:30 [DBG] Session cleanup completed
2025-05-19 15:34:02.287 +05:30 [DBG] Starting session cleanup
2025-05-19 15:34:02.483 +05:30 [INF] Executed DbCommand (16ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 15:34:02.489 +05:30 [INF] Executed DbCommand (4ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 15:34:02.490 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 15:34:02.490 +05:30 [DBG] Session cleanup completed
2025-05-19 15:39:02.506 +05:30 [DBG] Starting session cleanup
2025-05-19 15:39:02.540 +05:30 [INF] Executed DbCommand (14ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 15:39:02.552 +05:30 [INF] Executed DbCommand (7ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 15:39:02.553 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 15:39:02.553 +05:30 [DBG] Session cleanup completed
2025-05-19 16:55:51.617 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'AuditLog'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-19 16:55:51.675 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'PasswordResetToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-19 16:55:51.675 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserBranch'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-19 16:55:51.675 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserRole'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-19 16:55:51.676 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserSession'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-19 16:55:51.676 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserSubscription'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-05-19 16:55:52.693 +05:30 [INF] Executed DbCommand (86ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-05-19 16:55:52.721 +05:30 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-05-19 16:55:52.841 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-05-19 16:55:52.959 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-05-19 16:55:52.960 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-05-19 16:55:52.982 +05:30 [INF] No migrations were applied. The database is already up to date.
2025-05-19 16:55:53.046 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-19 16:55:53.280 +05:30 [INF] Configured endpoint SubscriptionCreated, Consumer: IdentityService.Infrastructure.Messaging.Consumers.SubscriptionCreatedConsumer
2025-05-19 16:55:53.285 +05:30 [INF] Configured endpoint SubscriptionStatusChanged, Consumer: IdentityService.Infrastructure.Messaging.Consumers.SubscriptionStatusChangedConsumer
2025-05-19 16:55:53.288 +05:30 [INF] Configured endpoint VendorRegistered, Consumer: IdentityService.Infrastructure.Messaging.Consumers.VendorRegisteredConsumer
2025-05-19 16:55:53.563 +05:30 [DBG] Starting bus instances: IBus
2025-05-19 16:55:53.571 +05:30 [DBG] Starting bus: "rabbitmq://localhost/"
2025-05-19 16:55:53.611 +05:30 [INF] Session cleanup service is starting
2025-05-19 16:55:53.612 +05:30 [DBG] Starting session cleanup
2025-05-19 16:55:53.661 +05:30 [DBG] Connect: guest@localhost:5672/
2025-05-19 16:55:53.996 +05:30 [DBG] Connected: guest@localhost:5672/ (address: amqp://localhost:5672, local: 61641)
2025-05-19 16:55:54.044 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/DESKTOPI7D4M6U_IdentityServiceAPI_bus_kbyoyyfpkikqngn5bdq3pt9kdf?temporary=true"
2025-05-19 16:55:54.082 +05:30 [DBG] Declare queue: name: SubscriptionStatusChanged, durable, consumer-count: 0 message-count: 0
2025-05-19 16:55:54.082 +05:30 [DBG] Declare queue: name: VendorRegistered, durable, consumer-count: 0 message-count: 0
2025-05-19 16:55:54.082 +05:30 [DBG] Declare queue: name: SubscriptionCreated, durable, consumer-count: 0 message-count: 0
2025-05-19 16:55:54.096 +05:30 [DBG] Declare exchange: name: SubscriptionCreated, type: fanout, durable
2025-05-19 16:55:54.096 +05:30 [DBG] Declare exchange: name: SubscriptionStatusChanged, type: fanout, durable
2025-05-19 16:55:54.096 +05:30 [DBG] Declare exchange: name: VendorRegistered, type: fanout, durable
2025-05-19 16:55:54.103 +05:30 [DBG] Declare exchange: name: VMSContracts.Tenant.Events:VendorRegistered, type: fanout, durable
2025-05-19 16:55:54.103 +05:30 [DBG] Declare exchange: name: VMSContracts.Subscription.Events:SubscriptionCreated, type: fanout, durable
2025-05-19 16:55:54.103 +05:30 [DBG] Declare exchange: name: VMSContracts.Subscription.Events:SubscriptionStatusChanged, type: fanout, durable
2025-05-19 16:55:54.113 +05:30 [DBG] Bind queue: source: SubscriptionCreated, destination: SubscriptionCreated
2025-05-19 16:55:54.118 +05:30 [DBG] Bind queue: source: SubscriptionStatusChanged, destination: SubscriptionStatusChanged
2025-05-19 16:55:54.118 +05:30 [DBG] Bind queue: source: VendorRegistered, destination: VendorRegistered
2025-05-19 16:55:54.137 +05:30 [DBG] Bind exchange: source: VMSContracts.Subscription.Events:SubscriptionStatusChanged, destination: SubscriptionStatusChanged
2025-05-19 16:55:54.137 +05:30 [DBG] Bind exchange: source: VMSContracts.Subscription.Events:SubscriptionCreated, destination: SubscriptionCreated
2025-05-19 16:55:54.137 +05:30 [DBG] Bind exchange: source: VMSContracts.Tenant.Events:VendorRegistered, destination: VendorRegistered
2025-05-19 16:55:54.199 +05:30 [DBG] Consumer Ok: "rabbitmq://localhost/VendorRegistered" - amq.ctag-lgHNsIe54c5CsNf1oeFB3g
2025-05-19 16:55:54.199 +05:30 [DBG] Consumer Ok: "rabbitmq://localhost/SubscriptionStatusChanged" - amq.ctag-U5tVPAIuw8NMVLrK1jQXXQ
2025-05-19 16:55:54.199 +05:30 [DBG] Consumer Ok: "rabbitmq://localhost/SubscriptionCreated" - amq.ctag-Gxm9_ZOvbK6I87slZSxyzg
2025-05-19 16:55:54.203 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/SubscriptionStatusChanged"
2025-05-19 16:55:54.203 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/SubscriptionCreated"
2025-05-19 16:55:54.203 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/VendorRegistered"
2025-05-19 16:55:54.208 +05:30 [INF] Bus started: "rabbitmq://localhost/"
2025-05-19 16:55:54.446 +05:30 [INF] Executed DbCommand (52ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 16:55:54.488 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 16:55:54.527 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 16:55:54.528 +05:30 [DBG] Session cleanup completed
2025-05-19 16:55:54.542 +05:30 [INF] Now listening on: http://localhost:5263
2025-05-19 16:55:54.542 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-05-19 16:55:54.543 +05:30 [INF] Hosting environment: Development
2025-05-19 16:55:54.543 +05:30 [INF] Content root path: E:\Shri\AutomobilesGenerative\VMS-API\IdentityService\src\IdentityService.API
2025-05-19 16:55:54.941 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/swagger/index.html - null null
2025-05-19 16:55:55.136 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/swagger/index.html - 200 null text/html;charset=utf-8 196.9816ms
2025-05-19 16:55:55.416 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/swagger/v1/swagger.json - null null
2025-05-19 16:55:55.717 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 300.0969ms
2025-05-19 17:00:54.536 +05:30 [DBG] Starting session cleanup
2025-05-19 17:00:54.706 +05:30 [INF] Executed DbCommand (8ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 17:00:54.710 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 17:00:54.711 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 17:00:54.711 +05:30 [DBG] Session cleanup completed
2025-05-19 17:05:54.731 +05:30 [DBG] Starting session cleanup
2025-05-19 17:05:54.749 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 17:05:54.757 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 17:05:54.757 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 17:05:54.757 +05:30 [DBG] Session cleanup completed
2025-05-19 17:10:54.765 +05:30 [DBG] Starting session cleanup
2025-05-19 17:10:54.911 +05:30 [INF] Executed DbCommand (14ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 17:10:54.915 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 17:10:54.916 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 17:10:54.916 +05:30 [DBG] Session cleanup completed
2025-05-19 17:15:54.916 +05:30 [DBG] Starting session cleanup
2025-05-19 17:15:54.941 +05:30 [INF] Executed DbCommand (10ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 17:15:54.988 +05:30 [INF] Executed DbCommand (31ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 17:15:54.988 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 17:15:54.989 +05:30 [DBG] Session cleanup completed
2025-05-19 17:20:55.002 +05:30 [DBG] Starting session cleanup
2025-05-19 17:20:55.232 +05:30 [INF] Executed DbCommand (8ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 17:20:55.235 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 17:20:55.235 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 17:20:55.236 +05:30 [DBG] Session cleanup completed
2025-05-19 17:25:55.244 +05:30 [DBG] Starting session cleanup
2025-05-19 17:25:55.262 +05:30 [INF] Executed DbCommand (6ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 17:25:55.265 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 17:25:55.266 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 17:25:55.266 +05:30 [DBG] Session cleanup completed
2025-05-19 17:30:55.266 +05:30 [DBG] Starting session cleanup
2025-05-19 17:30:55.348 +05:30 [INF] Executed DbCommand (10ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 17:30:55.353 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 17:30:55.353 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 17:30:55.353 +05:30 [DBG] Session cleanup completed
2025-05-19 17:35:55.361 +05:30 [DBG] Starting session cleanup
2025-05-19 17:35:55.371 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 17:35:55.377 +05:30 [INF] Executed DbCommand (4ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 17:35:55.377 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 17:35:55.377 +05:30 [DBG] Session cleanup completed
2025-05-19 17:40:55.387 +05:30 [DBG] Starting session cleanup
2025-05-19 17:40:55.453 +05:30 [INF] Executed DbCommand (6ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 17:40:55.455 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 17:40:55.455 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 17:40:55.455 +05:30 [DBG] Session cleanup completed
2025-05-19 17:45:55.464 +05:30 [DBG] Starting session cleanup
2025-05-19 17:45:55.484 +05:30 [INF] Executed DbCommand (11ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 17:45:55.529 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 17:45:55.533 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 17:45:55.533 +05:30 [DBG] Session cleanup completed
2025-05-19 17:50:55.538 +05:30 [DBG] Starting session cleanup
2025-05-19 17:50:55.776 +05:30 [INF] Executed DbCommand (7ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 17:50:55.779 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 17:50:55.779 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 17:50:55.779 +05:30 [DBG] Session cleanup completed
2025-05-19 17:55:55.787 +05:30 [DBG] Starting session cleanup
2025-05-19 17:55:55.794 +05:30 [INF] Executed DbCommand (5ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 17:55:55.797 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 17:55:55.797 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 17:55:55.797 +05:30 [DBG] Session cleanup completed
2025-05-19 18:00:55.808 +05:30 [DBG] Starting session cleanup
2025-05-19 18:00:55.897 +05:30 [INF] Executed DbCommand (8ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 18:00:55.900 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 18:00:55.900 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 18:00:55.900 +05:30 [DBG] Session cleanup completed
2025-05-19 18:05:55.911 +05:30 [DBG] Starting session cleanup
2025-05-19 18:05:55.922 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 18:05:55.926 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 18:05:55.926 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 18:05:55.927 +05:30 [DBG] Session cleanup completed
2025-05-19 18:10:55.931 +05:30 [DBG] Starting session cleanup
2025-05-19 18:10:56.009 +05:30 [INF] Executed DbCommand (7ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 18:10:56.011 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 18:10:56.012 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 18:10:56.012 +05:30 [DBG] Session cleanup completed
2025-05-19 18:15:56.027 +05:30 [DBG] Starting session cleanup
2025-05-19 18:15:56.038 +05:30 [INF] Executed DbCommand (4ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 18:15:56.080 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 18:15:56.080 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 18:15:56.080 +05:30 [DBG] Session cleanup completed
2025-05-19 18:20:56.089 +05:30 [DBG] Starting session cleanup
2025-05-19 18:20:56.162 +05:30 [INF] Executed DbCommand (8ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 18:20:56.166 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 18:20:56.170 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 18:20:56.170 +05:30 [DBG] Session cleanup completed
2025-05-19 18:25:56.169 +05:30 [DBG] Starting session cleanup
2025-05-19 18:25:56.173 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 18:25:56.189 +05:30 [INF] Executed DbCommand (14ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 18:25:56.191 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 18:25:56.191 +05:30 [DBG] Session cleanup completed
2025-05-19 18:30:56.198 +05:30 [DBG] Starting session cleanup
2025-05-19 18:30:56.397 +05:30 [INF] Executed DbCommand (7ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 18:30:56.399 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 18:30:56.399 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 18:30:56.399 +05:30 [DBG] Session cleanup completed
2025-05-19 21:41:58.733 +05:30 [DBG] Starting session cleanup
2025-05-19 21:41:58.739 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 21:41:58.744 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 21:41:58.744 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 21:41:58.744 +05:30 [DBG] Session cleanup completed
2025-05-19 21:46:58.743 +05:30 [DBG] Starting session cleanup
2025-05-19 21:46:58.991 +05:30 [INF] Executed DbCommand (11ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 21:46:58.994 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 21:46:58.995 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 21:46:58.995 +05:30 [DBG] Session cleanup completed
2025-05-19 21:51:59.001 +05:30 [DBG] Starting session cleanup
2025-05-19 21:51:59.026 +05:30 [INF] Executed DbCommand (10ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 21:51:59.048 +05:30 [INF] Executed DbCommand (7ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 21:51:59.049 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 21:51:59.049 +05:30 [DBG] Session cleanup completed
2025-05-19 21:56:59.060 +05:30 [DBG] Starting session cleanup
2025-05-19 21:56:59.341 +05:30 [INF] Executed DbCommand (26ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 21:56:59.348 +05:30 [INF] Executed DbCommand (5ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 21:56:59.348 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 21:56:59.349 +05:30 [DBG] Session cleanup completed
2025-05-19 22:01:59.351 +05:30 [DBG] Starting session cleanup
2025-05-19 22:01:59.371 +05:30 [INF] Executed DbCommand (11ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 22:01:59.416 +05:30 [INF] Executed DbCommand (4ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 22:01:59.417 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 22:01:59.417 +05:30 [DBG] Session cleanup completed
2025-05-19 22:06:59.421 +05:30 [DBG] Starting session cleanup
2025-05-19 22:06:59.546 +05:30 [INF] Executed DbCommand (8ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 22:06:59.548 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 22:06:59.549 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 22:06:59.549 +05:30 [DBG] Session cleanup completed
2025-05-19 22:11:59.550 +05:30 [DBG] Starting session cleanup
2025-05-19 22:11:59.572 +05:30 [INF] Executed DbCommand (9ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 22:11:59.590 +05:30 [INF] Executed DbCommand (11ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 22:11:59.591 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 22:11:59.591 +05:30 [DBG] Session cleanup completed
2025-05-19 22:16:59.592 +05:30 [DBG] Starting session cleanup
2025-05-19 22:16:59.675 +05:30 [INF] Executed DbCommand (10ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 22:16:59.678 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 22:16:59.678 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 22:16:59.678 +05:30 [DBG] Session cleanup completed
2025-05-19 22:24:26.350 +05:30 [DBG] Starting session cleanup
2025-05-19 22:24:26.461 +05:30 [INF] Executed DbCommand (59ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 22:24:26.478 +05:30 [INF] Executed DbCommand (10ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 22:24:26.478 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 22:24:26.478 +05:30 [DBG] Session cleanup completed
2025-05-19 22:29:26.482 +05:30 [DBG] Starting session cleanup
2025-05-19 22:29:26.726 +05:30 [INF] Executed DbCommand (10ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 22:29:26.729 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 22:29:26.729 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 22:29:26.729 +05:30 [DBG] Session cleanup completed
2025-05-19 22:34:26.732 +05:30 [DBG] Starting session cleanup
2025-05-19 22:34:26.744 +05:30 [INF] Executed DbCommand (6ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 22:34:26.751 +05:30 [INF] Executed DbCommand (5ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 22:34:26.753 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 22:34:26.753 +05:30 [DBG] Session cleanup completed
2025-05-19 22:39:26.759 +05:30 [DBG] Starting session cleanup
2025-05-19 22:39:27.053 +05:30 [INF] Executed DbCommand (9ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 22:39:27.056 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 22:39:27.056 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 22:39:27.056 +05:30 [DBG] Session cleanup completed
2025-05-19 22:44:27.067 +05:30 [DBG] Starting session cleanup
2025-05-19 22:44:27.073 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 22:44:27.077 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 22:44:27.077 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 22:44:27.077 +05:30 [DBG] Session cleanup completed
2025-05-19 22:49:27.082 +05:30 [DBG] Starting session cleanup
2025-05-19 22:49:27.190 +05:30 [INF] Executed DbCommand (10ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 22:49:27.192 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 22:49:27.192 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 22:49:27.192 +05:30 [DBG] Session cleanup completed
2025-05-19 22:54:27.204 +05:30 [DBG] Starting session cleanup
2025-05-19 22:54:27.235 +05:30 [INF] Executed DbCommand (13ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 22:54:27.242 +05:30 [INF] Executed DbCommand (6ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 22:54:27.243 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 22:54:27.243 +05:30 [DBG] Session cleanup completed
2025-05-19 22:59:27.249 +05:30 [DBG] Starting session cleanup
2025-05-19 22:59:27.563 +05:30 [INF] Executed DbCommand (11ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 22:59:27.569 +05:30 [INF] Executed DbCommand (5ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 22:59:27.569 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 22:59:27.569 +05:30 [DBG] Session cleanup completed
2025-05-19 23:04:27.577 +05:30 [DBG] Starting session cleanup
2025-05-19 23:04:27.605 +05:30 [INF] Executed DbCommand (19ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 23:04:27.615 +05:30 [INF] Executed DbCommand (7ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 23:04:27.615 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 23:04:27.616 +05:30 [DBG] Session cleanup completed
2025-05-19 23:09:27.624 +05:30 [DBG] Starting session cleanup
2025-05-19 23:09:27.789 +05:30 [INF] Executed DbCommand (9ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 23:09:27.791 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 23:09:27.792 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 23:09:27.792 +05:30 [DBG] Session cleanup completed
2025-05-19 23:14:27.800 +05:30 [DBG] Starting session cleanup
2025-05-19 23:14:27.834 +05:30 [INF] Executed DbCommand (29ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 23:14:27.842 +05:30 [INF] Executed DbCommand (7ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 23:14:27.843 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 23:14:27.843 +05:30 [DBG] Session cleanup completed
2025-05-19 23:19:27.844 +05:30 [DBG] Starting session cleanup
2025-05-19 23:19:28.101 +05:30 [INF] Executed DbCommand (16ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 23:19:28.105 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 23:19:28.105 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 23:19:28.106 +05:30 [DBG] Session cleanup completed
2025-05-19 23:24:28.115 +05:30 [DBG] Starting session cleanup
2025-05-19 23:24:28.128 +05:30 [INF] Executed DbCommand (9ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 23:24:28.157 +05:30 [INF] Executed DbCommand (7ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 23:24:28.158 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 23:24:28.159 +05:30 [DBG] Session cleanup completed
2025-05-19 23:29:28.171 +05:30 [DBG] Starting session cleanup
2025-05-19 23:29:28.419 +05:30 [INF] Executed DbCommand (11ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 23:29:28.422 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 23:29:28.423 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 23:29:28.423 +05:30 [DBG] Session cleanup completed
2025-05-19 23:34:28.430 +05:30 [DBG] Starting session cleanup
2025-05-19 23:34:28.437 +05:30 [INF] Executed DbCommand (5ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 23:34:28.450 +05:30 [INF] Executed DbCommand (5ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 23:34:28.452 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 23:34:28.455 +05:30 [DBG] Session cleanup completed
2025-05-19 23:39:28.466 +05:30 [DBG] Starting session cleanup
2025-05-19 23:39:28.876 +05:30 [INF] Executed DbCommand (43ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 23:39:28.891 +05:30 [INF] Executed DbCommand (13ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 23:39:28.892 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 23:39:28.893 +05:30 [DBG] Session cleanup completed
2025-05-19 23:44:28.901 +05:30 [DBG] Starting session cleanup
2025-05-19 23:44:28.913 +05:30 [INF] Executed DbCommand (7ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 23:44:28.936 +05:30 [INF] Executed DbCommand (9ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 23:44:28.937 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 23:44:28.937 +05:30 [DBG] Session cleanup completed
2025-05-19 23:49:28.945 +05:30 [DBG] Starting session cleanup
2025-05-19 23:49:29.170 +05:30 [INF] Executed DbCommand (13ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-05-19 23:49:29.174 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-05-19 23:49:29.174 +05:30 [INF] Expired 0 idle or expired sessions
2025-05-19 23:49:29.174 +05:30 [DBG] Session cleanup completed
