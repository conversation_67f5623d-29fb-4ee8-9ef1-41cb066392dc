using System;
using System.Threading;
using System.Threading.Tasks;
using IdentityService.Application.DTOs.Menus;
using IdentityService.Application.Interfaces;
using IdentityService.Domain.Interfaces;
using MediatR;

namespace IdentityService.Application.Features.Menus.Commands;

public class UpdateMenuCommand : IRequest<MenuResponse>
{
    public Guid MenuId { get; set; }
    public UpdateMenuRequest Request { get; set; }
}

public class UpdateMenuCommandHandler : IRequestHandler<UpdateMenuCommand, MenuResponse>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICurrentUserService _currentUserService;
    private readonly IAuditLogService _auditLogService;

    public UpdateMenuCommandHandler(
        IUnitOfWork unitOfWork,
        ICurrentUserService currentUserService,
        IAuditLogService auditLogService)
    {
        _unitOfWork = unitOfWork;
        _currentUserService = currentUserService;
        _auditLogService = auditLogService;
    }

    public async Task<MenuResponse> Handle(UpdateMenuCommand command, CancellationToken cancellationToken)
    {
        var menu = await _unitOfWork.MenuRepository.GetByIdAsync(command.MenuId);
        if (menu == null)
            throw new InvalidOperationException($"Menu with ID {command.MenuId} not found");
            
        var request = command.Request;
        var updatedBy = _currentUserService.UserId.ToString() ?? "System";
        
        // Validate parent menu if provided
        if (request.ParentId.HasValue)
        {
            // Prevent circular reference
            if (request.ParentId.Value == command.MenuId)
                throw new InvalidOperationException("A menu cannot be its own parent");
                
            var parentMenu = await _unitOfWork.MenuRepository.GetByIdAsync(request.ParentId.Value);
            if (parentMenu == null)
                throw new InvalidOperationException($"Parent menu with ID {request.ParentId.Value} not found");
        }
        
        // Track old values for audit log
        var oldValues = System.Text.Json.JsonSerializer.Serialize(new { 
            menu.DisplayName, 
            menu.Path, 
            menu.Icon, 
            menu.Order, 
            menu.ParentId 
        });
        
        // Update menu properties
        menu.Update(
            request.DisplayName,
            request.Path,
            request.Icon,
            request.Order,
            request.ParentId,
            updatedBy);
        
        // Save changes
        await _unitOfWork.MenuRepository.UpdateAsync(menu);
        await _unitOfWork.SaveChangesAsync(cancellationToken);
        
        // Track new values for audit log
        var newValues = System.Text.Json.JsonSerializer.Serialize(new { 
            menu.DisplayName, 
            menu.Path, 
            menu.Icon, 
            menu.Order, 
            menu.ParentId 
        });
        
        // Create audit log
        await _auditLogService.CreateAuditLogAsync(
            "Update",
            "Menu",
            menu.Id.ToString(),
            oldValues,
            newValues,
            "DisplayName,Path,Icon,Order,ParentId",
            _currentUserService.UserId ?? Guid.Empty);
        
        // Create response
        var response = new MenuResponse
        {
            Id = menu.Id,
            Name = menu.Name,
            DisplayName = menu.DisplayName,
            Path = menu.Path,
            Icon = menu.Icon,
            Order = menu.Order,
            ParentId = menu.ParentId,
            CreatedAt = menu.CreatedAt,
            CreatedBy = menu.CreatedBy,
            UpdatedAt = menu.UpdatedAt,
            UpdatedBy = menu.UpdatedBy
        };
        
        return response;
    }
}
