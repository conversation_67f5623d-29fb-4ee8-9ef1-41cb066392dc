using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using SubscriptionService.Application.Services;
using System.Security.Claims;

namespace SubscriptionService.API.Filters;

/// <summary>
/// Action filter to validate subscription terms acceptance before subscription actions
/// </summary>
public class SubscriptionTermsValidationFilter : IAsyncActionFilter
{
    private readonly ISubscriptionTermsValidationService _validationService;
    private readonly ILogger<SubscriptionTermsValidationFilter> _logger;

    public SubscriptionTermsValidationFilter(
        ISubscriptionTermsValidationService validationService,
        ILogger<SubscriptionTermsValidationFilter> logger)
    {
        _validationService = validationService;
        _logger = logger;
    }

    public async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
    {
        try
        {
            // Skip validation for SuperAdmin users
            var user = context.HttpContext.User;
            if (user.IsInRole("SuperAdmin"))
            {
                _logger.LogInformation("Skipping subscription terms validation for SuperAdmin user");
                await next();
                return;
            }

            // Extract vendor ID from various possible sources
            Guid? vendorId = ExtractVendorId(context);

            if (!vendorId.HasValue)
            {
                _logger.LogWarning("Could not extract vendor ID for subscription terms validation");
                context.Result = new BadRequestObjectResult(new
                {
                    Success = false,
                    Message = "Vendor ID is required for this operation"
                });
                return;
            }

            // Check if vendor can perform subscription actions
            var canPerformActions = await _validationService.CanVendorPerformSubscriptionActionsAsync(vendorId.Value);

            if (!canPerformActions)
            {
                var blockReason = await _validationService.GetSubscriptionActionBlockReasonAsync(vendorId.Value);
                
                _logger.LogWarning("Blocking subscription action for vendor {VendorId}: {Reason}", 
                    vendorId.Value, blockReason);

                context.Result = new BadRequestObjectResult(new
                {
                    Success = false,
                    Message = blockReason ?? "You must accept the current Subscription Terms and Conditions before performing this action",
                    ErrorCode = "TERMS_ACCEPTANCE_REQUIRED"
                });
                return;
            }

            // Vendor has accepted terms, proceed with action
            await next();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during subscription terms validation");
            context.Result = new StatusCodeResult(500);
        }
    }

    private static Guid? ExtractVendorId(ActionExecutingContext context)
    {
        // Try to extract vendor ID from action parameters
        foreach (var parameter in context.ActionArguments)
        {
            if (parameter.Value == null) continue;

            // Check if parameter is directly a Guid named vendorId
            if (parameter.Key.Equals("vendorId", StringComparison.OrdinalIgnoreCase) && 
                parameter.Value is Guid directVendorId)
            {
                return directVendorId;
            }

            // Check if parameter has a VendorId property
            var vendorIdProperty = parameter.Value.GetType().GetProperty("VendorId");
            if (vendorIdProperty?.GetValue(parameter.Value) is Guid propertyVendorId)
            {
                return propertyVendorId;
            }
        }

        // Try to extract from route values
        if (context.RouteData.Values.TryGetValue("vendorId", out var routeVendorId) && 
            Guid.TryParse(routeVendorId?.ToString(), out var parsedVendorId))
        {
            return parsedVendorId;
        }

        // Try to extract from query parameters
        if (context.HttpContext.Request.Query.TryGetValue("vendorId", out var queryVendorId) && 
            Guid.TryParse(queryVendorId.FirstOrDefault(), out var parsedQueryVendorId))
        {
            return parsedQueryVendorId;
        }

        return null;
    }
}

/// <summary>
/// Attribute to apply subscription terms validation to controller actions
/// </summary>
[AttributeUsage(AttributeTargets.Method | AttributeTargets.Class)]
public class RequireSubscriptionTermsAcceptanceAttribute : ServiceFilterAttribute
{
    public RequireSubscriptionTermsAcceptanceAttribute() : base(typeof(SubscriptionTermsValidationFilter))
    {
    }
}
