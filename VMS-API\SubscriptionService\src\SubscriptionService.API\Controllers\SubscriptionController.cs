using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using SubscriptionService.Application.DTOs;
using SubscriptionService.Application.Features.Subscriptions.Commands;
using SubscriptionService.Application.Features.Subscriptions.Queries;
using SubscriptionService.Domain.Entities;
using SubscriptionService.Domain.Enums;
using SubscriptionService.Domain.Interfaces;
using SubscriptionService.Infrastructure.Persistence;
using MediatR;

namespace SubscriptionService.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class SubscriptionController : ControllerBase
    {
        private readonly SubscriptionDbContext _context;
        private readonly IMediator _mediator;
        private readonly ISubscriptionChangeService _subscriptionChangeService;
        private readonly IBillingService _billingService;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<SubscriptionController> _logger;

        public SubscriptionController(
            SubscriptionDbContext context,
            IMediator mediator,
            ISubscriptionChangeService subscriptionChangeService,
            IBillingService billingService,
            IUnitOfWork unitOfWork,
            ILogger<SubscriptionController> logger)
        {
            _context = context;
            _mediator = mediator;
            _subscriptionChangeService = subscriptionChangeService;
            _billingService = billingService;
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        [HttpGet]
        [Authorize(Roles = "SuperAdmin,VendorAdmin")]
        public async Task<ActionResult<IEnumerable<Subscription>>> GetSubscriptions()
        {
            var subscriptions = await _context.Subscriptions
                .Include(s => s.SubscriptionPlan)
                .ToListAsync();
            return Ok(subscriptions);
        }

        [HttpGet("{id}")]
        [Authorize(Policy = "Subscriptions.View")]
        public async Task<ActionResult<SubscriptionDto>> GetSubscription(Guid id)
        {
            try
            {
                var query = new GetSubscriptionByIdQuery { SubscriptionId = id };
                var subscription = await _mediator.Send(query);

                if (subscription == null)
                {
                    return NotFound();
                }

                return Ok(subscription);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving subscription {SubscriptionId}", id);
                return StatusCode(500, new { message = "An error occurred while retrieving the subscription" });
            }
        }

        [HttpGet("vendor/{vendorId}")]
        [Authorize(Roles = "SuperAdmin,VendorAdmin")]
        public async Task<ActionResult<IEnumerable<Subscription>>> GetVendorSubscriptions(Guid vendorId)
        {
            var subscriptions = await _context.Subscriptions
                .Include(s => s.SubscriptionPlan)
                .Where(s => s.UserId == vendorId)
                .ToListAsync();
            return Ok(subscriptions);
        }

        [HttpGet("status/{status}")]
        [Authorize(Roles = "SuperAdmin,VendorAdmin")]
        public async Task<ActionResult<IEnumerable<Subscription>>> GetSubscriptionsByStatus(SubscriptionStatus status)
        {
            var subscriptions = await _context.Subscriptions
                .Include(s => s.SubscriptionPlan)
                .Where(s => s.Status == status)
                .ToListAsync();
            return Ok(subscriptions);
        }

        [HttpPost]
        [Authorize(Policy = "Subscriptions.Create")]
        public async Task<ActionResult<SubscriptionDto>> CreateSubscription(CreateSubscriptionCommand command)
        {
            try
            {
                var subscription = await _mediator.Send(command);
                return CreatedAtAction(nameof(GetSubscription), new { id = subscription.Id }, subscription);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating subscription for vendor {VendorId}", command.VendorId);
                return StatusCode(500, new { message = "An error occurred while creating the subscription" });
            }
        }



        [HttpPut("{id}/cancel")]
        [Authorize(Policy = "Subscriptions.Update")]
        public async Task<IActionResult> CancelSubscription(Guid id)
        {
            try
            {
                var command = new CancelSubscriptionCommand { SubscriptionId = id };
                var result = await _mediator.Send(command);

                if (!result)
                {
                    return NotFound($"Subscription with ID {id} not found");
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cancelling subscription {SubscriptionId}", id);
                return StatusCode(500, "An error occurred while cancelling the subscription");
            }
        }

        [HttpDelete("{id}")]
        [Authorize(Roles = "SuperAdmin")]
        public async Task<IActionResult> DeleteSubscription(Guid id)
        {
            var subscription = await _context.Subscriptions.FindAsync(id);
            if (subscription == null)
            {
                return NotFound();
            }

            _context.Subscriptions.Remove(subscription);
            await _context.SaveChangesAsync();

            return NoContent();
        }

        [HttpPost("{id}/upgrade")]
        [Authorize(Policy = "Subscriptions.Update")]
        public async Task<ActionResult<SubscriptionChange>> UpgradeSubscription(Guid id, [FromBody] UpgradeSubscriptionRequest request)
        {
            try
            {
                var change = await _subscriptionChangeService.UpgradeSubscriptionAsync(
                    id,
                    request.NewPlanId,
                    request.PromotionCode);

                return Ok(change);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error upgrading subscription {SubscriptionId} to plan {PlanId}",
                    id, request.NewPlanId);
                return StatusCode(500, "An error occurred while upgrading the subscription");
            }
        }

        [HttpPost("{id}/downgrade")]
        [Authorize(Policy = "Subscriptions.Update")]
        public async Task<ActionResult<SubscriptionChange>> DowngradeSubscription(Guid id, [FromBody] DowngradeSubscriptionRequest request)
        {
            try
            {
                var change = await _subscriptionChangeService.DowngradeSubscriptionAsync(
                    id,
                    request.NewPlanId);

                return Ok(change);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error downgrading subscription {SubscriptionId} to plan {PlanId}",
                    id, request.NewPlanId);
                return StatusCode(500, "An error occurred while downgrading the subscription");
            }
        }

        [HttpPost("{id}/renew")]
        [Authorize(Policy = "Subscriptions.Update")]
        public async Task<ActionResult<SubscriptionChange>> RenewSubscription(Guid id, [FromBody] RenewSubscriptionRequest request)
        {
            try
            {
                var change = await _subscriptionChangeService.RenewSubscriptionAsync(
                    id,
                    request.PromotionCode);

                return Ok(change);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error renewing subscription {SubscriptionId}", id);
                return StatusCode(500, "An error occurred while renewing the subscription");
            }
        }

        [HttpPost("{id}/convert-trial")]
        [Authorize(Policy = "Subscriptions.Update")]
        public async Task<ActionResult<SubscriptionChange>> ConvertTrial(Guid id, [FromBody] ConvertTrialRequest request)
        {
            try
            {
                var change = await _subscriptionChangeService.ConvertTrialAsync(
                    id,
                    request.PlanId,
                    request.PromotionCode);

                return Ok(change);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error converting trial subscription {SubscriptionId} to plan {PlanId}",
                    id, request.PlanId);
                return StatusCode(500, "An error occurred while converting the trial subscription");
            }
        }

        [HttpGet("{id}/changes")]
        [Authorize(Policy = "Subscriptions.View")]
        public async Task<ActionResult<IEnumerable<SubscriptionChange>>> GetSubscriptionChanges(Guid id)
        {
            try
            {
                var changes = await _subscriptionChangeService.GetSubscriptionChangeHistoryAsync(id);
                return Ok(changes);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting change history for subscription {SubscriptionId}", id);
                return StatusCode(500, "An error occurred while retrieving the subscription change history");
            }
        }

        [HttpGet("{id}/invoices")]
        [Authorize(Policy = "Subscriptions.View")]
        public async Task<ActionResult<IEnumerable<Invoice>>> GetSubscriptionInvoices(Guid id)
        {
            try
            {
                var invoices = await _billingService.GetInvoiceHistoryAsync(id);
                return Ok(invoices);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting invoices for subscription {SubscriptionId}", id);
                return StatusCode(500, "An error occurred while retrieving the subscription invoices");
            }
        }

        private bool SubscriptionExists(Guid id)
        {
            return _context.Subscriptions.Any(e => e.Id == id);
        }
    }

    public class UpgradeSubscriptionRequest
    {
        public Guid NewPlanId { get; set; }
        public string? PromotionCode { get; set; }
    }

    public class DowngradeSubscriptionRequest
    {
        public Guid NewPlanId { get; set; }
    }

    public class RenewSubscriptionRequest
    {
        public string? PromotionCode { get; set; }
    }

    public class ConvertTrialRequest
    {
        public Guid PlanId { get; set; }
        public string? PromotionCode { get; set; }
    }
}