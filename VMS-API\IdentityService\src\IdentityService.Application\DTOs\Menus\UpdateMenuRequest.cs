using System;
using System.ComponentModel.DataAnnotations;

namespace IdentityService.Application.DTOs.Menus;

public class UpdateMenuRequest
{
    [Required]
    public string DisplayName { get; set; }
    
    [Required]
    public string Path { get; set; }
    
   // [Required]
    public string Icon { get; set; }
    
    [Required]
    public int Order { get; set; }
    
    public Guid? ParentId { get; set; }
}
