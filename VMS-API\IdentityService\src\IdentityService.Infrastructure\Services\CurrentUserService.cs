using System.Security.Claims;
using IdentityService.Domain.Interfaces;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace IdentityService.Infrastructure.Services
{
   public class CurrentUserService : ICurrentUserService
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ILogger<CurrentUserService> _logger;

        public CurrentUserService(IHttpContextAccessor httpContextAccessor, ILogger<CurrentUserService> logger)
        {
            _httpContextAccessor = httpContextAccessor;
            _logger = logger;
        }

        public Guid? UserId
        {
            get
            {
                try
                {
                    var httpContext = _httpContextAccessor.HttpContext;
                    if (httpContext == null)
                    {
                        _logger.LogWarning("HttpContext is null");
                        return null;
                    }

                    var user = httpContext.User;
                    if (user == null || !user.Identity.IsAuthenticated)
                    {
                        _logger.LogWarning("User is null or not authenticated");
                        return null;
                    }

                    // Log all claims for debugging
                    var allClaims = user.Claims.Select(c => $"{c.Type}: {c.Value}").ToList();
                    _logger.LogInformation("All claims: {Claims}", string.Join(", ", allClaims));

                    // Try to find the NameIdentifier claim (short form)
                    var userIdClaim = user.FindFirst(ClaimTypes.NameIdentifier);

                    // Try to find the NameIdentifier claim (full URI form)
                    if (userIdClaim == null)
                    {
                        _logger.LogWarning("User ID claim (ClaimTypes.NameIdentifier) not found in token");
                        userIdClaim = user.FindFirst("http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier");
                    }

                    // Try to find the 'sub' claim as a fallback
                    if (userIdClaim == null)
                    {
                        _logger.LogWarning("User ID claim (full URI NameIdentifier) not found in token");
                        userIdClaim = user.FindFirst("sub");
                    }

                    if (userIdClaim == null)
                    {
                        _logger.LogWarning("No user ID claim found in token (tried NameIdentifier and sub)");
                        return null;
                    }

                    if (Guid.TryParse(userIdClaim.Value, out var userId))
                    {
                        _logger.LogInformation("Successfully extracted user ID: {UserId}", userId);
                        return userId;
                    }

                    _logger.LogWarning("Failed to parse user ID from claim: {ClaimValue}", userIdClaim.Value);
                    return null;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error getting user ID from claims");
                    return null;
                }
            }
        }

        public string? UserName
        {
            get
            {
                try
                {
                    var httpContext = _httpContextAccessor.HttpContext;
                    if (httpContext == null)
                    {
                        _logger.LogWarning("HttpContext is null");
                        return null;
                    }

                    var user = httpContext.User;
                    if (user == null || !user.Identity.IsAuthenticated)
                    {
                        _logger.LogWarning("User is null or not authenticated");
                        return null;
                    }

                    // Try to find the Name claim (short form)
                    var userNameClaim = user.FindFirst(ClaimTypes.Name);

                    // Try to find the Name claim (full URI form)
                    if (userNameClaim == null)
                    {
                        _logger.LogWarning("User name claim (ClaimTypes.Name) not found in token");
                        userNameClaim = user.FindFirst("http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name");
                    }

                    // Try to find the Email claim as a fallback (short form)
                    if (userNameClaim == null)
                    {
                        _logger.LogWarning("User name claim (full URI Name) not found in token");
                        userNameClaim = user.FindFirst(ClaimTypes.Email);
                    }

                    // Try to find the Email claim as a fallback (full URI form)
                    if (userNameClaim == null)
                    {
                        _logger.LogWarning("User name claim (Email) not found in token");
                        userNameClaim = user.FindFirst("http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress");
                    }

                    if (userNameClaim == null)
                    {
                        _logger.LogWarning("No user name claim found in token (tried Name and Email)");
                        return null;
                    }

                    _logger.LogInformation("Successfully extracted user name: {UserName}", userNameClaim.Value);
                    return userNameClaim.Value;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error getting username from claims");
                    return null;
                }
            }
        }
    }
}