FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY ["src/SubscriptionService.API/SubscriptionService.API.csproj", "SubscriptionService.API/"]
COPY ["src/SubscriptionService.Application/SubscriptionService.Application.csproj", "SubscriptionService.Application/"]
COPY ["src/SubscriptionService.Domain/SubscriptionService.Domain.csproj", "SubscriptionService.Domain/"]
COPY ["src/SubscriptionService.Infrastructure/SubscriptionService.Infrastructure.csproj", "SubscriptionService.Infrastructure/"]
COPY ["src/SubscriptionService.Shared/SubscriptionService.Shared.csproj", "SubscriptionService.Shared/"]
RUN dotnet restore "SubscriptionService.API/SubscriptionService.API.csproj"
COPY src/ .
WORKDIR "/src/SubscriptionService.API"
RUN dotnet build "SubscriptionService.API.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "SubscriptionService.API.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "SubscriptionService.API.dll"] 