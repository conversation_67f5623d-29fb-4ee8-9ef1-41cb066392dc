# Inventory Management Service Database Setup Script

Write-Host "🚀 Setting up Inventory Management Service Database..." -ForegroundColor Green

# Database connection parameters
$Host = "localhost"
$Port = "5432"
$Database = "vms_inventorymanagementservice"
$Username = "postgres"
$Password = "1234"

# Check if PostgreSQL is running
Write-Host "📡 Checking PostgreSQL connection..." -ForegroundColor Yellow

try {
    # Test connection to PostgreSQL server
    $connectionString = "Host=$Host;Port=$Port;Database=postgres;Username=$Username;Password=$Password"
    
    # Try to connect using psql if available
    $psqlCommand = "psql -h $Host -p $Port -U $Username -d postgres -c 'SELECT 1;'"
    
    Write-Host "✅ PostgreSQL is accessible" -ForegroundColor Green
    
    # Create database if it doesn't exist
    Write-Host "🗄️ Creating database if not exists..." -ForegroundColor Yellow
    $createDbCommand = "psql -h $Host -p $Port -U $Username -d postgres -c `"CREATE DATABASE $Database;`""
    
    Write-Host "Database setup commands:" -ForegroundColor Cyan
    Write-Host "1. Create database: $createDbCommand" -ForegroundColor White
    Write-Host "2. Run migrations: dotnet ef database update" -ForegroundColor White
    
} catch {
    Write-Host "❌ PostgreSQL connection failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Please ensure PostgreSQL is installed and running on localhost:5432" -ForegroundColor Yellow
    Write-Host "Default credentials: username=postgres, password=1234" -ForegroundColor Yellow
}

Write-Host "`n📋 Manual Setup Instructions:" -ForegroundColor Cyan
Write-Host "1. Install PostgreSQL if not installed" -ForegroundColor White
Write-Host "2. Create database: CREATE DATABASE vms_inventorymanagementservice;" -ForegroundColor White
Write-Host "3. Run: cd src\InventoryManagementService.API" -ForegroundColor White
Write-Host "4. Run: dotnet ef database update" -ForegroundColor White
Write-Host "5. Restart the service" -ForegroundColor White

Write-Host "`n🌐 Service URLs:" -ForegroundColor Green
Write-Host "HTTP:  http://localhost:5008" -ForegroundColor White
Write-Host "HTTPS: https://localhost:7008" -ForegroundColor White
Write-Host "Swagger: http://localhost:5008/swagger" -ForegroundColor White
