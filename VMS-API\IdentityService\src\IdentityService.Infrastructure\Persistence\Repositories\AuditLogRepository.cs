using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using IdentityService.Domain.Entities;
using IdentityService.Domain.Interfaces;
using IdentityService.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;

namespace IdentityService.Infrastructure.Persistence.Repositories;

public class AuditLogRepository : GenericRepository<AuditLog>, IAuditLogRepository
{
    public AuditLogRepository(ApplicationDbContext context) : base(context)
    {
    }

    public async Task<(IReadOnlyList<AuditLog> Items, int TotalCount)> GetFilteredAsync(
        string? action = null,
        string? entityName = null,
        string? entityId = null,
        Guid? userId = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        int pageNumber = 1,
        int pageSize = 10)
    {
        var query = _dbSet.AsQueryable();

        // Apply filters
        if (!string.IsNullOrEmpty(action))
            query = query.Where(a => a.Action.Contains(action));

        if (!string.IsNullOrEmpty(entityName))
            query = query.Where(a => a.EntityName.Contains(entityName));

        if (!string.IsNullOrEmpty(entityId))
            query = query.Where(a => a.EntityId.Contains(entityId));

        if (userId.HasValue)
            query = query.Where(a => a.UserId == userId.Value);

        if (fromDate.HasValue)
            query = query.Where(a => a.CreatedAt >= fromDate.Value);

        if (toDate.HasValue)
            query = query.Where(a => a.CreatedAt <= toDate.Value);

        // Get total count
        var totalCount = await query.CountAsync();

        // Apply pagination
        var items = await query
            .OrderByDescending(a => a.CreatedAt)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .Include(a => a.User)
            .ToListAsync();

        return (items, totalCount);
    }

    public async Task<IReadOnlyList<AuditLog>> GetByUserIdAsync(Guid userId)
    {
        return await _dbSet
            .Where(a => a.UserId == userId)
            .OrderByDescending(a => a.CreatedAt)
            .Include(a => a.User)
            .ToListAsync();
    }

    public async Task<IReadOnlyList<AuditLog>> GetByEntityAsync(string entityName, string entityId)
    {
        return await _dbSet
            .Where(a => a.EntityName == entityName && a.EntityId == entityId)
            .OrderByDescending(a => a.CreatedAt)
            .Include(a => a.User)
            .ToListAsync();
    }

    public async Task<byte[]> ExportToCsvAsync(IReadOnlyList<AuditLog> auditLogs)
    {
        var csv = new StringBuilder();
        
        // Add header
        csv.AppendLine("Id,Action,EntityName,EntityId,OldValues,NewValues,AffectedColumns,IpAddress,UserAgent,UserId,UserEmail,CreatedAt,CreatedBy");
        
        // Add rows
        foreach (var log in auditLogs)
        {
            csv.AppendLine(
                $"{log.Id}," +
                $"\"{EscapeCsvField(log.Action)}\"," +
                $"\"{EscapeCsvField(log.EntityName)}\"," +
                $"\"{EscapeCsvField(log.EntityId)}\"," +
                $"\"{EscapeCsvField(log.OldValues)}\"," +
                $"\"{EscapeCsvField(log.NewValues)}\"," +
                $"\"{EscapeCsvField(log.AffectedColumns)}\"," +
                $"\"{EscapeCsvField(log.IpAddress)}\"," +
                $"\"{EscapeCsvField(log.UserAgent)}\"," +
                $"{log.UserId}," +
                $"\"{EscapeCsvField(log.User?.Email ?? string.Empty)}\"," +
                $"\"{log.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss")}\"," +
                $"\"{EscapeCsvField(log.CreatedBy)}\""
            );
        }
        
        return Encoding.UTF8.GetBytes(csv.ToString());
    }

    public async Task<byte[]> ExportToPdfAsync(IReadOnlyList<AuditLog> auditLogs)
    {
        // In a real implementation, you would use a PDF library like iTextSharp or PDFsharp
        // For this example, we'll just return a placeholder
        var text = "This is a placeholder for PDF export functionality. In a real implementation, you would use a PDF library.";
        return Encoding.UTF8.GetBytes(text);
    }
    
    private string EscapeCsvField(string field)
    {
        if (string.IsNullOrEmpty(field))
            return string.Empty;
            
        return field.Replace("\"", "\"\"");
    }
}
