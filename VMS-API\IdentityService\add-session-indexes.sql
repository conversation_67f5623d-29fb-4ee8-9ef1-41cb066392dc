-- S<PERSON> Script to add session performance indexes
-- Run this directly on your PostgreSQL database

-- Check if indexes already exist before creating them
DO $$
BEGIN
    -- Add LastActiveAt index if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE tablename = 'UserSessions' 
        AND indexname = 'IX_UserSessions_LastActiveAt'
    ) THEN
        CREATE INDEX "IX_UserSessions_LastActiveAt" ON "UserSessions" ("LastActiveAt");
        RAISE NOTICE 'Created index: IX_UserSessions_LastActiveAt';
    ELSE
        RAISE NOTICE 'Index IX_UserSessions_LastActiveAt already exists';
    END IF;

    -- Add composite index for Status + ExpiresAt
    IF NOT EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE tablename = 'UserSessions' 
        AND indexname = 'IX_UserSessions_Status_ExpiresAt'
    ) THEN
        CREATE INDEX "IX_UserSessions_Status_ExpiresAt" ON "UserSessions" ("Status", "ExpiresAt");
        RAISE NOTICE 'Created index: IX_UserSessions_Status_ExpiresAt';
    ELSE
        RAISE NOTICE 'Index IX_UserSessions_Status_ExpiresAt already exists';
    END IF;

    -- Add composite index for Status + LastActiveAt + ExpiresAt
    IF NOT EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE tablename = 'UserSessions' 
        AND indexname = 'IX_UserSessions_Status_LastActiveAt_ExpiresAt'
    ) THEN
        CREATE INDEX "IX_UserSessions_Status_LastActiveAt_ExpiresAt" ON "UserSessions" ("Status", "LastActiveAt", "ExpiresAt");
        RAISE NOTICE 'Created index: IX_UserSessions_Status_LastActiveAt_ExpiresAt';
    ELSE
        RAISE NOTICE 'Index IX_UserSessions_Status_LastActiveAt_ExpiresAt already exists';
    END IF;

    -- Add composite index for UserId + Status + ExpiresAt
    IF NOT EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE tablename = 'UserSessions' 
        AND indexname = 'IX_UserSessions_UserId_Status_ExpiresAt'
    ) THEN
        CREATE INDEX "IX_UserSessions_UserId_Status_ExpiresAt" ON "UserSessions" ("UserId", "Status", "ExpiresAt");
        RAISE NOTICE 'Created index: IX_UserSessions_UserId_Status_ExpiresAt';
    ELSE
        RAISE NOTICE 'Index IX_UserSessions_UserId_Status_ExpiresAt already exists';
    END IF;

END $$;

-- Verify the indexes were created
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'UserSessions'
ORDER BY indexname;

-- Show index usage statistics (run this after some time to see if indexes are being used)
-- SELECT 
--     schemaname,
--     tablename,
--     indexname,
--     idx_scan as "Times Used",
--     idx_tup_read as "Tuples Read",
--     idx_tup_fetch as "Tuples Fetched"
-- FROM pg_stat_user_indexes 
-- WHERE tablename = 'UserSessions'
-- ORDER BY idx_scan DESC;
