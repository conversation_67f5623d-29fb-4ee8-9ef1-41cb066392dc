using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using IdentityService.Application.DTOs.Permissions;
using IdentityService.Application.Features.Permissions.Commands;
using IdentityService.Application.Features.Permissions.Queries;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace IdentityService.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class PermissionsController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<PermissionsController> _logger;

    public PermissionsController(IMediator mediator, ILogger<PermissionsController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    [HttpGet]
    [Authorize(Policy = "Permissions.View")]
    public async Task<ActionResult<List<PermissionResponse>>> GetPermissions()
    {
        try
        {
            var query = new GetPermissionsQuery();
            var permissions = await _mediator.Send(query);
            return Ok(permissions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving permissions");
            return StatusCode(500, new { message = "An error occurred while retrieving permissions" });
        }
    }

    [HttpGet("{id}")]
    [Authorize(Policy = "Permissions.View")]
    public async Task<ActionResult<PermissionResponse>> GetPermissionById(Guid id)
    {
        try
        {
            var query = new GetPermissionByIdQuery { PermissionId = id };
            var permission = await _mediator.Send(query);
            return Ok(permission);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Permission not found");
            return NotFound(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving permission");
            return StatusCode(500, new { message = "An error occurred while retrieving the permission" });
        }
    }

    [HttpPost]
    [Authorize(Roles = "SuperAdmin")]
    public async Task<ActionResult<PermissionResponse>> CreatePermission([FromBody] CreatePermissionRequest request)
    {
        try
        {
            var command = new CreatePermissionCommand { Request = request };
            var permission = await _mediator.Send(command);
            return CreatedAtAction(nameof(GetPermissionById), new { id = permission.Id }, permission);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation during permission creation");
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating permission");
            return StatusCode(500, new { message = "An error occurred while creating the permission" });
        }
    }

    [HttpPut("{id}")]
    [Authorize(Policy = "Permissions.Update")]
    public async Task<ActionResult<PermissionResponse>> UpdatePermission(Guid id, [FromBody] UpdatePermissionRequest request)
    {
        try
        {
            var command = new UpdatePermissionCommand { PermissionId = id, Request = request };
            var permission = await _mediator.Send(command);
            return Ok(permission);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation during permission update");
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating permission");
            return StatusCode(500, new { message = "An error occurred while updating the permission" });
        }
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = "Permissions.Delete")]
    public async Task<ActionResult> DeletePermission(Guid id)
    {
        try
        {
            var command = new DeletePermissionCommand { PermissionId = id };
            await _mediator.Send(command);
            return NoContent();
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation during permission deletion");
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting permission");
            return StatusCode(500, new { message = "An error occurred while deleting the permission" });
        }
    }

    [HttpPost("standard")]
    [Authorize(Roles = "SuperAdmin")]
    public async Task<ActionResult<List<PermissionResponse>>> CreateStandardPermissions([FromBody] CreateStandardPermissionsRequest request)
    {
        try
        {
            var command = new CreateStandardPermissionsCommand { Request = request };
            var permissions = await _mediator.Send(command);
            return Ok(permissions);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation during standard permissions creation");
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating standard permissions");
            return StatusCode(500, new { message = "An error occurred while creating standard permissions" });
        }
    }
}
