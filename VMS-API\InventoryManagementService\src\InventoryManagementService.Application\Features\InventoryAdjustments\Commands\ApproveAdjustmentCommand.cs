using FluentValidation;
using InventoryManagementService.Domain.Interfaces;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using VMS.Contracts.Common.Authorization;

namespace InventoryManagementService.Application.Features.InventoryAdjustments.Commands;

public class ApproveAdjustmentCommand : IRequest
{
    public Guid AdjustmentId { get; set; }
    public string ApprovedBy { get; set; } = string.Empty;
}

public class ApproveAdjustmentCommandValidator : AbstractValidator<ApproveAdjustmentCommand>
{
    public ApproveAdjustmentCommandValidator()
    {
        RuleFor(x => x.AdjustmentId)
            .NotEmpty().WithMessage("Adjustment ID is required");

        RuleFor(x => x.ApprovedBy)
            .NotEmpty().WithMessage("ApprovedBy is required");
    }
}

public class ApproveAdjustmentCommandHandler : IRequestHandler<ApproveAdjustmentCommand>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<ApproveAdjustmentCommandHandler> _logger;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly IEventPublisher _eventPublisher;

    public ApproveAdjustmentCommandHandler(
        IUnitOfWork unitOfWork,
        ILogger<ApproveAdjustmentCommandHandler> logger,
        IHttpContextAccessor httpContextAccessor,
        IEventPublisher eventPublisher)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
        _httpContextAccessor = httpContextAccessor;
        _eventPublisher = eventPublisher;
    }

    public async Task Handle(ApproveAdjustmentCommand request, CancellationToken cancellationToken)
    {
        // Check permissions - Admin roles bypass, others need explicit permission
        var user = _httpContextAccessor.HttpContext?.User;
        if (!PermissionHelper.IsAdmin(user))
        {
            PermissionHelper.ValidateAccess(user, "Adjustments.Approve");
        }

        _logger.LogInformation("Approving adjustment {AdjustmentId}", request.AdjustmentId);

        await _unitOfWork.BeginTransactionAsync();

        try
        {
            var adjustment = await _unitOfWork.InventoryAdjustments.GetByIdAsync(request.AdjustmentId);

            if (adjustment == null)
            {
                throw new KeyNotFoundException($"Inventory adjustment with ID {request.AdjustmentId} not found");
            }

            // Apply adjustments to inventory
            foreach (var item in adjustment.Items)
            {
                var inventoryItem = await _unitOfWork.Inventory.GetByPartAndBranchAsync(item.PartId, adjustment.BranchId);
                
                if (inventoryItem != null)
                {
                    // Update the inventory with the new quantity
                    var adjustmentQuantity = item.NewQuantity - item.PreviousQuantity;
                    var reason = $"Inventory Adjustment - {adjustment.AdjustmentNumber} ({adjustment.ReasonDescription})";
                    
                    inventoryItem.UpdateStock(item.NewQuantity, request.ApprovedBy, reason);
                }
                else
                {
                    // Log warning if inventory item doesn't exist
                    _logger.LogWarning("Inventory item for part {PartId} not found at branch {BranchId} during adjustment approval", 
                        item.PartId, adjustment.BranchId);
                }
            }

            adjustment.Approve(request.ApprovedBy);

            await _unitOfWork.SaveChangesAsync();
            await _unitOfWork.CommitTransactionAsync();

            _logger.LogInformation("Adjustment {AdjustmentId} approved successfully", adjustment.Id);

            // Publish event
            await _eventPublisher.PublishAdjustmentApprovedEvent(
                adjustment.Id,
                adjustment.AdjustmentNumber,
                adjustment.ApprovedDate!.Value);
        }
        catch
        {
            await _unitOfWork.RollbackTransactionAsync();
            throw;
        }
    }
}
