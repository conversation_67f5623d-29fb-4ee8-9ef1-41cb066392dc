using InventoryManagementService.Domain.Common;
using InventoryManagementService.Domain.Enums;

namespace InventoryManagementService.Domain.Entities;

public class StockTransfer : BaseEntity
{
    public Guid VendorId { get; private set; }
    public Guid SourceBranchId { get; private set; }
    public Guid DestinationBranchId { get; private set; }
    public string TransferNumber { get; private set; } = string.Empty;
    public TransferStatus Status { get; private set; }
    public DateTime RequestedDate { get; private set; }
    public DateTime? ShippedDate { get; private set; }
    public DateTime? ReceivedDate { get; private set; }
    public string? Notes { get; private set; }
    public string? ShippedBy { get; private set; }
    public string? ReceivedBy { get; private set; }

    // Navigation properties
    public List<StockTransferItem> Items { get; private set; } = new();

    private StockTransfer() { }

    public static StockTransfer Create(
        Guid vendorId,
        Guid sourceBranchId,
        Guid destinationBranchId,
        string transferNumber,
        string createdBy,
        string? notes = null)
    {
        return new StockTransfer
        {
            VendorId = vendorId,
            SourceBranchId = sourceBranchId,
            DestinationBranchId = destinationBranchId,
            TransferNumber = transferNumber.Trim(),
            Status = TransferStatus.Pending,
            RequestedDate = DateTime.UtcNow,
            Notes = notes?.Trim(),
            CreatedBy = createdBy
        };
    }

    public void AddItem(Guid partId, int quantity, string createdBy)
    {
        if (Status != TransferStatus.Pending)
            throw new InvalidOperationException("Cannot add items to a transfer that is not pending");

        var existingItem = Items.FirstOrDefault(i => i.PartId == partId);
        if (existingItem != null)
        {
            existingItem.UpdateQuantity(existingItem.RequestedQuantity + quantity, createdBy);
        }
        else
        {
            var item = StockTransferItem.Create(Id, partId, quantity, createdBy);
            Items.Add(item);
        }
    }

    public void RemoveItem(Guid itemId)
    {
        if (Status != TransferStatus.Pending)
            throw new InvalidOperationException("Cannot remove items from a transfer that is not pending");

        var item = Items.FirstOrDefault(i => i.Id == itemId);
        if (item != null)
        {
            Items.Remove(item);
        }
    }

    public void Ship(string shippedBy)
    {
        if (Status != TransferStatus.Pending)
            throw new InvalidOperationException("Only pending transfers can be shipped");

        if (!Items.Any())
            throw new InvalidOperationException("Cannot ship transfer without items");

        Status = TransferStatus.InTransit;
        ShippedDate = DateTime.UtcNow;
        ShippedBy = shippedBy;
        SetUpdatedBy(shippedBy);
    }

    public void Complete(string receivedBy)
    {
        if (Status != TransferStatus.InTransit)
            throw new InvalidOperationException("Only in-transit transfers can be completed");

        Status = TransferStatus.Completed;
        ReceivedDate = DateTime.UtcNow;
        ReceivedBy = receivedBy;
        SetUpdatedBy(receivedBy);
    }

    public void Cancel(string cancelledBy, string reason)
    {
        if (Status == TransferStatus.Completed)
            throw new InvalidOperationException("Cannot cancel completed transfers");

        Status = TransferStatus.Cancelled;
        Notes = $"{Notes}\nCancelled: {reason}".Trim();
        SetUpdatedBy(cancelledBy);
    }

    public void UpdateNotes(string notes, string updatedBy)
    {
        Notes = notes?.Trim();
        SetUpdatedBy(updatedBy);
    }
}
