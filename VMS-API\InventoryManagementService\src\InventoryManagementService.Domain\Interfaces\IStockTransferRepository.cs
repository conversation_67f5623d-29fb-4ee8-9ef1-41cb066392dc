using InventoryManagementService.Domain.Entities;
using InventoryManagementService.Domain.Enums;
using System.Linq.Expressions;

namespace InventoryManagementService.Domain.Interfaces;

public interface IStockTransferRepository
{
    Task<StockTransfer?> GetByIdAsync(Guid id);
    Task<StockTransfer?> GetByTransferNumberAsync(string transferNumber);
    Task<List<StockTransfer>> GetAllAsync();
    Task<List<StockTransfer>> GetBySourceBranchAsync(Guid sourceBranchId);
    Task<List<StockTransfer>> GetByDestinationBranchAsync(Guid destinationBranchId);
    Task<List<StockTransfer>> GetByVendorAsync(Guid vendorId);
    Task<List<StockTransfer>> GetByStatusAsync(TransferStatus status);
    Task<List<StockTransfer>> GetPendingTransfersAsync();
    Task<List<StockTransfer>> GetAsync(Expression<Func<StockTransfer, bool>> predicate);
    Task AddAsync(StockTransfer stockTransfer);
    Task UpdateAsync(StockTransfer stockTransfer);
    Task DeleteAsync(Guid id);
    Task<bool> ExistsAsync(Guid id);
    Task<bool> TransferNumberExistsAsync(string transferNumber, Guid? excludeId = null);
}
