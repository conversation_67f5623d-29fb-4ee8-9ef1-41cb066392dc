using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using IdentityService.Domain.Common;
using IdentityService.Domain.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace IdentityService.Infrastructure.Persistence.Repositories;

public class GenericRepository<T> : IGenericRepository<T> where T : BaseEntity
{
    protected readonly ApplicationDbContext _context;
    protected readonly DbSet<T> _dbSet;

    protected GenericRepository(ApplicationDbContext context)
    {
        _context = context;
        _dbSet = context.Set<T>();
    }

    public virtual async Task<T?> GetByIdAsync(Guid id)
    {
        return await _dbSet.FindAsync(id);
    }
public async Task<bool> ExistsAsync(Expression<Func<T, bool>> predicate)
{
    return await _context.Set<T>().AnyAsync(predicate);
}
    public async Task<IReadOnlyList<T>> GetAllAsync()
    {
        return await _dbSet.ToListAsync();
    }

    public async Task<IReadOnlyList<T>> GetAsync(Expression<Func<T, bool>> predicate)
    {
        return await _dbSet.Where(predicate).ToListAsync();
    }

    public async Task<T?> GetFirstOrDefaultAsync(Expression<Func<T, bool>> predicate)
    {
        return await _dbSet.FirstOrDefaultAsync(predicate);
    }

    public async Task<T> AddAsync(T entity)
    {
        await _dbSet.AddAsync(entity);
        return entity;
    }

    public async Task UpdateAsync(T entity)
    {
        try
        {
            // Ensure the entity is being tracked
            if (_context.Entry(entity).State == EntityState.Detached)
            {
                // If the entity is detached, attach it and mark it as modified
                _dbSet.Attach(entity);
            }

            _context.Entry(entity).State = EntityState.Modified;
        }
        catch (DbUpdateConcurrencyException ex)
        {
            // Handle concurrency conflict
            var entry = ex.Entries.Single();
            var databaseValues = await entry.GetDatabaseValuesAsync();

            if (databaseValues == null)
            {
                // The entity was deleted by another user
                throw new InvalidOperationException("The record you attempted to edit was deleted by another user.");
            }

            // Refresh the original values to reflect the database values
            entry.OriginalValues.SetValues(databaseValues);

            // Retry the update
            _context.Entry(entity).State = EntityState.Modified;
        }
    }

    public Task DeleteAsync(T entity)
    {
        _dbSet.Remove(entity);
        return Task.CompletedTask;
    }

    public async Task<int> CountAsync(Expression<Func<T, bool>> predicate)
    {
        return await _dbSet.CountAsync(predicate);
    }

    public async Task<IReadOnlyList<T>> ListAllAsync()
    {
        return await _dbSet.ToListAsync();
    }

    public async Task<IReadOnlyList<T>> ListAsync(Expression<Func<T, bool>> predicate)
    {
        return await _dbSet.Where(predicate).ToListAsync();
    }
}

// Concrete implementation to allow instantiation
public class GenericRepositoryImpl<T> : GenericRepository<T> where T : BaseEntity
{
    public GenericRepositoryImpl(ApplicationDbContext context) : base(context) { }
}