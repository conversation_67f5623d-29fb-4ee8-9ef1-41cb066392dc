using InventoryManagementService.Domain.Common;
using InventoryManagementService.Domain.Enums;

namespace InventoryManagementService.Domain.Entities;

public class Part : BaseEntity
{
    public string SKU { get; private set; } = string.Empty;
    public string Name { get; private set; } = string.Empty;
    public string Description { get; private set; } = string.Empty;
    public string Manufacturer { get; private set; } = string.Empty;
    public decimal CostPrice { get; private set; }
    public decimal RetailPrice { get; private set; }
    public PartCategory Category { get; private set; }
    public string? PartNumber { get; private set; }
    public string? Barcode { get; private set; }
    public int? WarrantyMonths { get; private set; }
    public string? ImageUrl { get; private set; }
    public string? Notes { get; private set; }

    // Navigation properties
    public List<PartCompatibility> Compatibilities { get; private set; } = new();
    public List<InventoryItem> InventoryItems { get; private set; } = new();

    private Part() { }

    public static Part Create(
        string sku,
        string name,
        string description,
        string manufacturer,
        decimal costPrice,
        decimal retailPrice,
        PartCategory category,
        string createdBy,
        string? partNumber = null,
        string? barcode = null,
        int? warrantyMonths = null,
        string? imageUrl = null,
        string? notes = null)
    {
        var part = new Part
        {
            SKU = sku.ToUpper().Trim(),
            Name = name.Trim(),
            Description = description.Trim(),
            Manufacturer = manufacturer.Trim(),
            CostPrice = costPrice,
            RetailPrice = retailPrice,
            Category = category,
            PartNumber = partNumber?.Trim(),
            Barcode = barcode?.Trim(),
            WarrantyMonths = warrantyMonths,
            ImageUrl = imageUrl?.Trim(),
            Notes = notes?.Trim(),
            CreatedBy = createdBy
        };

        return part;
    }

    public void Update(
        string name,
        string description,
        string manufacturer,
        decimal costPrice,
        decimal retailPrice,
        PartCategory category,
        string updatedBy,
        string? partNumber = null,
        string? barcode = null,
        int? warrantyMonths = null,
        string? imageUrl = null,
        string? notes = null)
    {
        Name = name.Trim();
        Description = description.Trim();
        Manufacturer = manufacturer.Trim();
        CostPrice = costPrice;
        RetailPrice = retailPrice;
        Category = category;
        PartNumber = partNumber?.Trim();
        Barcode = barcode?.Trim();
        WarrantyMonths = warrantyMonths;
        ImageUrl = imageUrl?.Trim();
        Notes = notes?.Trim();
        
        SetUpdatedBy(updatedBy);
    }

    public void AddCompatibility(string make, string model, int yearFrom, int yearTo, string createdBy)
    {
        var compatibility = PartCompatibility.Create(Id, make, model, yearFrom, yearTo, createdBy);
        Compatibilities.Add(compatibility);
    }

    public void RemoveCompatibility(Guid compatibilityId)
    {
        var compatibility = Compatibilities.FirstOrDefault(c => c.Id == compatibilityId);
        if (compatibility != null)
        {
            Compatibilities.Remove(compatibility);
        }
    }
}
