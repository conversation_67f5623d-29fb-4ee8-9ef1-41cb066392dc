using MassTransit;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;
using VMSContracts.Subscription.Events;

namespace BranchManagementService.Infrastructure.Messaging.Consumers
{
    public class SubscriptionCreatedConsumer : IConsumer<SubscriptionCreated>
    {
        private readonly ILogger<SubscriptionCreatedConsumer> _logger;

        public SubscriptionCreatedConsumer(ILogger<SubscriptionCreatedConsumer> logger)
        {
            _logger = logger;
        }

        public Task Consume(ConsumeContext<SubscriptionCreated> context)
        {
            try
            {
                var @event = context.Message;
                _logger.LogInformation("Received SubscriptionCreated event for subscription {SubscriptionId}, user {UserId}",
                    @event.SubscriptionId, @event.UserId);

                // TODO: Implement logic to handle subscription creation
                // This could include updating branch limits for the vendor

                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error consuming SubscriptionCreated event");
                throw;
            }
        }
    }
}
