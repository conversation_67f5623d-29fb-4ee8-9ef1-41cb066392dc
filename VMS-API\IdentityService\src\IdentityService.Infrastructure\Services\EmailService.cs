using IdentityService.Application.Interfaces;
using Microsoft.Extensions.Configuration;

namespace IdentityService.Infrastructure.Services;

public class EmailService : IEmailService
{
    private readonly IConfiguration _configuration;

    public EmailService(IConfiguration configuration)
    {
        _configuration = configuration;
    }

    public async Task SendEmailAsync(string to, string subject, string body)
    {
        // TODO: Implement actual email sending logic
        Console.WriteLine($"Sending email to: {to}");
        Console.WriteLine($"Subject: {subject}");
        Console.WriteLine($"Body: {body}");

        await Task.CompletedTask;
    }

    public async Task SendPasswordResetEmailAsync(string email, string resetToken)
    {
        var subject = "Password Reset Request";
        var body = $"Your password reset token is: {resetToken}";
        await SendEmailAsync(email, subject, body);
    }

    public async Task SendEmailVerificationAsync(string email, string verificationToken)
    {
        var subject = "Email Verification";
        var body = $"Your email verification token is: {verificationToken}";
        await <PERSON><PERSON><PERSON>Async(email, subject, body);
    }

    public async Task SendOtpAsync(string email, string otp)
    {
        var subject = "One-Time Password";
        var body = $"Your OTP is: {otp}";
        await SendEmailAsync(email, subject, body);
    }
}