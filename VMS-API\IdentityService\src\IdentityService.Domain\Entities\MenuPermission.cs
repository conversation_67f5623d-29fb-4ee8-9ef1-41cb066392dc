using System;
using IdentityService.Domain.Common;

namespace IdentityService.Domain.Entities;

public class MenuPermission : BaseEntity
{
    public Guid MenuId { get; private set; }
    public Guid? PermissionId { get; private set; }
    public Menu Menu { get; private set; }
    public Permission Permission { get; private set; }

    private MenuPermission()
    {
        Menu = null!;
        Permission = null!;
    }

    public static MenuPermission Create(Menu menu, Permission? permission, string createdBy)
    {
        return new MenuPermission
        {
            MenuId = menu.Id,
            PermissionId = permission?.Id,
            Menu = menu,
            Permission = permission!,
            CreatedBy = createdBy
        };
    }
} 