using InventoryManagementService.Domain.Common;

namespace InventoryManagementService.Domain.Entities;

public class StockTransferItem : BaseEntity
{
    public Guid StockTransferId { get; private set; }
    public Guid PartId { get; private set; }
    public int RequestedQuantity { get; private set; }
    public int? ShippedQuantity { get; private set; }
    public int? ReceivedQuantity { get; private set; }
    public string? Notes { get; private set; }

    // Navigation properties
    public StockTransfer StockTransfer { get; private set; } = null!;
    public Part Part { get; private set; } = null!;

    private StockTransferItem() { }

    public static StockTransferItem Create(
        Guid stockTransferId,
        Guid partId,
        int requestedQuantity,
        string createdBy,
        string? notes = null)
    {
        return new StockTransferItem
        {
            StockTransferId = stockTransferId,
            PartId = partId,
            RequestedQuantity = requestedQuantity,
            Notes = notes?.Trim(),
            CreatedBy = createdBy
        };
    }

    public void UpdateQuantity(int requestedQuantity, string updatedBy)
    {
        RequestedQuantity = requestedQuantity;
        SetUpdatedBy(updatedBy);
    }

    public void SetShippedQuantity(int shippedQuantity, string updatedBy)
    {
        ShippedQuantity = shippedQuantity;
        SetUpdatedBy(updatedBy);
    }

    public void SetReceivedQuantity(int receivedQuantity, string updatedBy)
    {
        ReceivedQuantity = receivedQuantity;
        SetUpdatedBy(updatedBy);
    }

    public void UpdateNotes(string notes, string updatedBy)
    {
        Notes = notes?.Trim();
        SetUpdatedBy(updatedBy);
    }
}
