2025-06-07 22:42:09.895 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'AuditLog'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-07 22:42:10.048 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'PasswordResetToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-07 22:42:10.052 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserBranch'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-07 22:42:10.054 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-07 22:42:10.057 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserRole'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-07 22:42:10.061 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserSession'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-07 22:42:10.064 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserSubscription'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-07 22:42:11.408 +05:30 [INF] Executed DbCommand (94ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-06-07 22:42:11.454 +05:30 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-06-07 22:42:11.603 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-06-07 22:42:11.821 +05:30 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-06-07 22:42:11.839 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-06-07 22:42:12.644 +05:30 [INF] No migrations were applied. The database is already up to date.
2025-06-07 22:42:12.651 +05:30 [INF] Database migrations applied successfully.
2025-06-07 22:42:13.214 +05:30 [INF] Executed DbCommand (43ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT count(*)::int
FROM "Roles" AS r
2025-06-07 22:42:13.278 +05:30 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT count(*)::int
FROM "Permissions" AS p
2025-06-07 22:42:13.341 +05:30 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT count(*)::int
FROM "Users" AS u
WHERE NOT (u."IsDeleted")
2025-06-07 22:42:13.365 +05:30 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT count(*)::int
FROM "Menus" AS m
2025-06-07 22:42:13.585 +05:30 [INF] Executed DbCommand (15ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."Description", r."IsDeleted", r."Name", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "Roles" AS r
WHERE r."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:14.385 +05:30 [INF] Executed DbCommand (69ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."Description", r."IsDeleted", r."Name", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "Roles" AS r
WHERE r."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:16.931 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."Description", r."IsDeleted", r."Name", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "Roles" AS r
WHERE r."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:16.989 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."Description", r."IsDeleted", r."Name", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "Roles" AS r
WHERE r."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:17.016 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."Description", r."IsDeleted", r."Name", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "Roles" AS r
WHERE r."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:17.085 +05:30 [INF] Executed DbCommand (11ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."Description", r."IsDeleted", r."Name", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "Roles" AS r
WHERE r."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:17.205 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."Description", r."IsDeleted", r."Name", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "Roles" AS r
WHERE r."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:17.313 +05:30 [INF] Executed DbCommand (9ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:17.485 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:17.516 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:17.569 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:17.628 +05:30 [INF] Executed DbCommand (5ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:17.701 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:17.715 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:17.760 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:17.817 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:17.848 +05:30 [INF] Executed DbCommand (4ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:17.899 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:17.920 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:17.938 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:17.964 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:17.983 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:17.999 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:18.010 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:18.031 +05:30 [INF] Executed DbCommand (8ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:18.049 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:18.067 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:18.082 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:18.101 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:18.171 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:18.203 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:18.217 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:18.236 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:18.252 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:18.267 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:18.282 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:18.304 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:18.326 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:18.347 +05:30 [INF] Executed DbCommand (8ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:18.367 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:18.390 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:18.404 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:18.431 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:18.449 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:18.467 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:18.486 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:18.513 +05:30 [INF] Executed DbCommand (7ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:18.532 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:18.551 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:18.567 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:18.587 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:18.604 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:18.638 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:18.655 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:18.686 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:18.711 +05:30 [INF] Executed DbCommand (4ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:18.720 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:18.733 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:18.747 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:18.761 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:18.773 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:18.797 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:18.805 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:18.866 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:18.890 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:18.905 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:18.919 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__name_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__name_0
LIMIT 1
2025-06-07 22:42:19.015 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM "Menus" AS m)
2025-06-07 22:42:19.237 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."Description", r."IsDeleted", r."Name", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "Roles" AS r
WHERE r."Name" = 'SuperAdmin'
LIMIT 1
2025-06-07 22:42:19.286 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
2025-06-07 22:42:19.372 +05:30 [INF] Executed DbCommand (6ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:19.514 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:19.534 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:19.618 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:19.653 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:19.687 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:19.706 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:19.727 +05:30 [INF] Executed DbCommand (6ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:19.749 +05:30 [INF] Executed DbCommand (6ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:19.767 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:19.801 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:19.816 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:19.838 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:19.883 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:19.920 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:19.939 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:19.955 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:19.985 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:20.006 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:20.033 +05:30 [INF] Executed DbCommand (10ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:20.064 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:20.080 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:20.102 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:20.152 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:20.167 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:20.181 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:20.197 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:20.209 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:20.219 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:20.234 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:20.248 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:20.255 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:20.273 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:20.289 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:20.306 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:20.332 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:20.386 +05:30 [INF] Executed DbCommand (13ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:20.402 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:20.414 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:20.466 +05:30 [INF] Executed DbCommand (10ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:20.480 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:20.488 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:20.502 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:20.519 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:20.538 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:20.567 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:20.622 +05:30 [INF] Executed DbCommand (11ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:20.670 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:20.735 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:20.804 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:20.830 +05:30 [INF] Executed DbCommand (9ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:20.853 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:20.915 +05:30 [INF] Executed DbCommand (10ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:20.939 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:21.021 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:21.051 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:21.114 +05:30 [INF] Executed DbCommand (25ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:21.133 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:21.154 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:21.169 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals1_superAdminRole_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals1_superAdminRole_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:21.199 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__roleName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."Description", r."IsDeleted", r."Name", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "Roles" AS r
WHERE r."Name" = @__roleName_0
LIMIT 1
2025-06-07 22:42:21.207 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__roleName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."Description", r."IsDeleted", r."Name", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "Roles" AS r
WHERE r."Name" = @__roleName_0
LIMIT 1
2025-06-07 22:42:21.235 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:21.265 +05:30 [INF] Executed DbCommand (9ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:21.279 +05:30 [INF] Executed DbCommand (5ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:21.286 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:21.300 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:21.317 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:21.331 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:21.339 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:21.355 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:21.368 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:21.388 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:21.402 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:21.415 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:21.430 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:21.439 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:21.452 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:21.464 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:21.480 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:21.488 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:21.505 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:21.518 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:21.533 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:21.551 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:21.567 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:21.588 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:21.613 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:21.636 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:21.651 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:21.678 +05:30 [INF] Executed DbCommand (6ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:21.883 +05:30 [INF] Executed DbCommand (27ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:21.968 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:22.019 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:22.039 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:22.053 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:22.069 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:22.085 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:22.100 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:22.119 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:22.735 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:22.772 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:22.896 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:23.168 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:23.188 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__roleName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."Description", r."IsDeleted", r."Name", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "Roles" AS r
WHERE r."Name" = @__roleName_0
LIMIT 1
2025-06-07 22:42:23.208 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:23.363 +05:30 [INF] Executed DbCommand (7ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:23.598 +05:30 [INF] Executed DbCommand (10ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:23.654 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:23.698 +05:30 [INF] Executed DbCommand (9ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:23.730 +05:30 [INF] Executed DbCommand (8ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:23.748 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:23.760 +05:30 [INF] Executed DbCommand (5ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:23.795 +05:30 [INF] Executed DbCommand (6ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:23.850 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:23.915 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:23.955 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:23.981 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:23.996 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:24.014 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:24.029 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:24.048 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:24.064 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:24.081 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:24.098 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:24.117 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:24.135 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:24.153 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:24.168 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:24.184 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:24.195 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:24.205 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:24.216 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:24.237 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:24.254 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:24.304 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:24.319 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:24.339 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:24.371 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:24.420 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:24.448 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:24.478 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:24.499 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:24.520 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__roleName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."Description", r."IsDeleted", r."Name", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "Roles" AS r
WHERE r."Name" = @__roleName_0
LIMIT 1
2025-06-07 22:42:24.536 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:24.564 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:24.600 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:24.619 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:24.635 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:24.651 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:24.664 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:24.675 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:24.685 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:24.700 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:24.716 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:24.727 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:24.736 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:24.752 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:24.764 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:24.771 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:24.794 +05:30 [INF] Executed DbCommand (6ms) [Parameters=[@__roleName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."Description", r."IsDeleted", r."Name", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "Roles" AS r
WHERE r."Name" = @__roleName_0
LIMIT 1
2025-06-07 22:42:24.802 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:24.813 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:24.828 +05:30 [INF] Executed DbCommand (6ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:24.836 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:24.850 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:24.868 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:24.880 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:24.887 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:24.918 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:24.930 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:24.949 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:24.964 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:24.983 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:25.000 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:25.012 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__roleName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."Description", r."IsDeleted", r."Name", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "Roles" AS r
WHERE r."Name" = @__roleName_0
LIMIT 1
2025-06-07 22:42:25.019 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:25.031 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:25.048 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:25.068 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:25.083 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:25.112 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:25.138 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:25.155 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:25.168 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:25.184 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:25.199 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:25.209 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:25.218 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:25.232 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:25.249 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:25.282 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:25.302 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:25.315 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:25.330 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:25.367 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:25.383 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:25.396 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:25.407 +05:30 [INF] Executed DbCommand (3ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:25.416 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:25.432 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__roleName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."Description", r."IsDeleted", r."Name", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "Roles" AS r
WHERE r."Name" = @__roleName_0
LIMIT 1
2025-06-07 22:42:25.448 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:25.465 +05:30 [INF] Executed DbCommand (9ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:25.484 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:25.502 +05:30 [INF] Executed DbCommand (2ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:25.518 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:25.529 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:25.547 +05:30 [INF] Executed DbCommand (8ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:25.555 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:25.568 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:25.583 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:25.599 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:25.612 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:25.633 +05:30 [INF] Executed DbCommand (11ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:25.649 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:25.667 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:25.682 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:25.696 +05:30 [INF] Executed DbCommand (9ms) [Parameters=[@__8__locals3_permissionName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Action", p."CreatedAt", p."CreatedBy", p."Description", p."IsDeleted", p."Name", p."Resource", p."RowVersion", p."UpdatedAt", p."UpdatedBy"
FROM "Permissions" AS p
WHERE p."Name" = @__8__locals3_permissionName_0
LIMIT 1
2025-06-07 22:42:25.713 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_role_Id_0='?' (DbType = Guid), @__permission_Id_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."IsDeleted", r."PermissionId", r."RoleId", r."RowVersion", r."UpdatedAt", r."UpdatedBy"
FROM "RolePermissions" AS r
WHERE r."RoleId" = @__8__locals2_role_Id_0 AND r."PermissionId" = @__permission_Id_1
LIMIT 1
2025-06-07 22:42:25.736 +05:30 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM "MenuPermissions" AS m)
2025-06-07 22:42:25.804 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted"))
2025-06-07 22:42:25.832 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT count(*)::int
FROM "Roles" AS r
2025-06-07 22:42:25.838 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT count(*)::int
FROM "Permissions" AS p
2025-06-07 22:42:25.848 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT count(*)::int
FROM "Users" AS u
WHERE NOT (u."IsDeleted")
2025-06-07 22:42:25.876 +05:30 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT count(*)::int
FROM "UserRoles" AS u
2025-06-07 22:42:25.881 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT count(*)::int
FROM "Menus" AS m
2025-06-07 22:42:25.895 +05:30 [INF] Roles and permissions seeding completed successfully.
2025-06-07 22:42:25.961 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-07 22:42:26.566 +05:30 [INF] Configured endpoint SubscriptionCreated, Consumer: IdentityService.Infrastructure.Messaging.Consumers.SubscriptionCreatedConsumer
2025-06-07 22:42:26.603 +05:30 [INF] Configured endpoint SubscriptionStatusChanged, Consumer: IdentityService.Infrastructure.Messaging.Consumers.SubscriptionStatusChangedConsumer
2025-06-07 22:42:26.618 +05:30 [INF] Configured endpoint VendorRegistered, Consumer: IdentityService.Infrastructure.Messaging.Consumers.VendorRegisteredConsumer
2025-06-07 22:42:27.340 +05:30 [DBG] Starting bus instances: IBus
2025-06-07 22:42:27.353 +05:30 [DBG] Starting bus: "rabbitmq://localhost/"
2025-06-07 22:42:27.435 +05:30 [INF] Session cleanup service is starting
2025-06-07 22:42:27.443 +05:30 [DBG] Starting session cleanup
2025-06-07 22:42:27.516 +05:30 [DBG] Connect: guest@localhost:5672/
2025-06-07 22:42:27.614 +05:30 [INF] Executed DbCommand (27ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId"
FROM "UserSessions" AS u
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-06-07 22:42:27.679 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId"
FROM "UserSessions" AS u
WHERE u."Status" = 0 AND u."ExpiresAt" <= @__now_0
2025-06-07 22:42:27.687 +05:30 [DBG] Connected: guest@localhost:5672/ (address: amqp://localhost:5672, local: 61057)
2025-06-07 22:42:27.715 +05:30 [INF] Expired 0 idle or expired sessions
2025-06-07 22:42:27.718 +05:30 [DBG] Session cleanup completed
2025-06-07 22:42:27.810 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/DESKTOPI7D4M6U_IdentityServiceAPI_bus_cnnyyyfpkikqdoyybdq4m3u38y?temporary=true"
2025-06-07 22:42:27.864 +05:30 [DBG] Declare queue: name: SubscriptionStatusChanged, durable, consumer-count: 1 message-count: 0
2025-06-07 22:42:27.864 +05:30 [DBG] Declare queue: name: VendorRegistered, durable, consumer-count: 1 message-count: 0
2025-06-07 22:42:27.864 +05:30 [DBG] Declare queue: name: SubscriptionCreated, durable, consumer-count: 2 message-count: 0
2025-06-07 22:42:27.886 +05:30 [DBG] Declare exchange: name: SubscriptionStatusChanged, type: fanout, durable
2025-06-07 22:42:27.916 +05:30 [DBG] Declare exchange: name: SubscriptionCreated, type: fanout, durable
2025-06-07 22:42:27.918 +05:30 [DBG] Declare exchange: name: VendorRegistered, type: fanout, durable
2025-06-07 22:42:27.932 +05:30 [DBG] Declare exchange: name: VMSContracts.Subscription.Events:SubscriptionStatusChanged, type: fanout, durable
2025-06-07 22:42:27.932 +05:30 [DBG] Declare exchange: name: VMSContracts.Tenant.Events:VendorRegistered, type: fanout, durable
2025-06-07 22:42:27.932 +05:30 [DBG] Declare exchange: name: VMSContracts.Subscription.Events:SubscriptionCreated, type: fanout, durable
2025-06-07 22:42:27.960 +05:30 [DBG] Bind queue: source: SubscriptionStatusChanged, destination: SubscriptionStatusChanged
2025-06-07 22:42:27.961 +05:30 [DBG] Bind queue: source: VendorRegistered, destination: VendorRegistered
2025-06-07 22:42:27.963 +05:30 [DBG] Bind queue: source: SubscriptionCreated, destination: SubscriptionCreated
2025-06-07 22:42:27.998 +05:30 [DBG] Bind exchange: source: VMSContracts.Subscription.Events:SubscriptionStatusChanged, destination: SubscriptionStatusChanged
2025-06-07 22:42:27.998 +05:30 [DBG] Bind exchange: source: VMSContracts.Tenant.Events:VendorRegistered, destination: VendorRegistered
2025-06-07 22:42:27.998 +05:30 [DBG] Bind exchange: source: VMSContracts.Subscription.Events:SubscriptionCreated, destination: SubscriptionCreated
2025-06-07 22:42:28.099 +05:30 [DBG] Consumer Ok: "rabbitmq://localhost/VendorRegistered" - amq.ctag-JvnWWFiODWHM2SOl7COOWw
2025-06-07 22:42:28.099 +05:30 [DBG] Consumer Ok: "rabbitmq://localhost/SubscriptionCreated" - amq.ctag-NYs83a9kcdEId0DAhF3fuQ
2025-06-07 22:42:28.099 +05:30 [DBG] Consumer Ok: "rabbitmq://localhost/SubscriptionStatusChanged" - amq.ctag-A4NEBDsH6c6x5yu7tdUPkA
2025-06-07 22:42:28.111 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/SubscriptionStatusChanged"
2025-06-07 22:42:28.109 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/VendorRegistered"
2025-06-07 22:42:28.109 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/SubscriptionCreated"
2025-06-07 22:42:28.133 +05:30 [INF] Bus started: "rabbitmq://localhost/"
2025-06-07 22:42:28.200 +05:30 [ERR] Hosting failed to start
System.IO.IOException: Failed to bind to address https://127.0.0.1:5263: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: Only one usage of each socket address (protocol/network address/port) is normally permitted.
 ---> System.Net.Sockets.SocketException (10048): Only one usage of each socket address (protocol/network address/port) is normally permitted.
   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
2025-06-07 22:42:28.293 +05:30 [INF] Session cleanup service is stopping
2025-06-07 22:42:28.297 +05:30 [DBG] Stopping bus instances: IBus
2025-06-07 22:42:28.315 +05:30 [DBG] Stopping bus: "rabbitmq://localhost/"
2025-06-07 22:42:28.336 +05:30 [DBG] Endpoint Stopping: "rabbitmq://localhost/SubscriptionCreated"
2025-06-07 22:42:28.384 +05:30 [DBG] Consumer Stopping: "rabbitmq://localhost/SubscriptionCreated" (Stop Receive Transport)
2025-06-07 22:42:28.404 +05:30 [DBG] Consumer Cancel Ok: "rabbitmq://localhost/SubscriptionCreated" - amq.ctag-NYs83a9kcdEId0DAhF3fuQ
2025-06-07 22:42:28.416 +05:30 [DBG] Endpoint Stopping: "rabbitmq://localhost/SubscriptionStatusChanged"
2025-06-07 22:42:28.419 +05:30 [DBG] Endpoint Completed: "rabbitmq://localhost/SubscriptionCreated"
2025-06-07 22:42:28.420 +05:30 [DBG] Consumer Stopping: "rabbitmq://localhost/SubscriptionStatusChanged" (Stop Receive Transport)
2025-06-07 22:42:28.436 +05:30 [DBG] Endpoint Stopping: "rabbitmq://localhost/VendorRegistered"
2025-06-07 22:42:28.444 +05:30 [DBG] Consumer Stopping: "rabbitmq://localhost/VendorRegistered" (Stop Receive Transport)
2025-06-07 22:42:28.532 +05:30 [DBG] Consumer Completed: "rabbitmq://localhost/SubscriptionCreated": 0 received, 0 concurrent, amq.ctag-NYs83a9kcdEId0DAhF3fuQ
2025-06-07 22:42:28.568 +05:30 [DBG] Consumer Cancel Ok: "rabbitmq://localhost/SubscriptionStatusChanged" - amq.ctag-A4NEBDsH6c6x5yu7tdUPkA
2025-06-07 22:42:28.579 +05:30 [DBG] Consumer Cancel Ok: "rabbitmq://localhost/VendorRegistered" - amq.ctag-JvnWWFiODWHM2SOl7COOWw
2025-06-07 22:42:28.581 +05:30 [DBG] Endpoint Completed: "rabbitmq://localhost/VendorRegistered"
2025-06-07 22:42:28.582 +05:30 [DBG] Endpoint Completed: "rabbitmq://localhost/SubscriptionStatusChanged"
2025-06-07 22:42:28.584 +05:30 [DBG] Consumer Completed: "rabbitmq://localhost/VendorRegistered": 0 received, 0 concurrent, amq.ctag-JvnWWFiODWHM2SOl7COOWw
2025-06-07 22:42:28.586 +05:30 [DBG] Consumer Completed: "rabbitmq://localhost/SubscriptionStatusChanged": 0 received, 0 concurrent, amq.ctag-A4NEBDsH6c6x5yu7tdUPkA
2025-06-07 22:42:28.601 +05:30 [DBG] Endpoint Stopping: "rabbitmq://localhost/DESKTOPI7D4M6U_IdentityServiceAPI_bus_cnnyyyfpkikqdoyybdq4m3u38y?temporary=true"
2025-06-07 22:42:28.610 +05:30 [DBG] Endpoint Completed: "rabbitmq://localhost/DESKTOPI7D4M6U_IdentityServiceAPI_bus_cnnyyyfpkikqdoyybdq4m3u38y?temporary=true"
2025-06-07 22:42:28.620 +05:30 [DBG] Disconnect: guest@localhost:5672/
2025-06-07 22:42:28.644 +05:30 [DBG] Disconnected: guest@localhost:5672/
2025-06-07 22:42:28.647 +05:30 [INF] Bus stopped: "rabbitmq://localhost/"
