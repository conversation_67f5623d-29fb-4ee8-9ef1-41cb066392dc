using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using IdentityService.Domain.Entities;
using Microsoft.EntityFrameworkCore;

namespace IdentityService.Infrastructure.Persistence.Migrations;

public static class SeedData
{
    public static async Task SeedAsync(ApplicationDbContext context)
    {
        if (!context.Roles.Any())
        {
            await SeedRolesAsync(context);
        }

        if (!context.Permissions.Any())
        {
            await SeedPermissionsAsync(context);
        }

        if (!context.RolePermissions.Any())
        {
            await SeedRolePermissionsAsync(context);
        }

        if (!context.Menus.Any())
        {
            await SeedMenusAsync(context);
        }

        if (!context.MenuPermissions.Any())
        {
            await SeedMenuPermissionsAsync(context);
        }

        await context.SaveChangesAsync();
    }

    private static async Task SeedRolesAsync(ApplicationDbContext context)
    {
        var roles = new List<Role>
        {
            // Admin Roles
            Role.Create("Super Admin", "Super administrator with full access", "System"),
            Role.Create("Admin", "Administrator with limited access", "System"),
            Role.Create("VendorAdmin", "Vendor administrator with vendor-level access", "System"),
            Role.Create("BranchAdmin", "Branch administrator with branch-level access", "System"),

            // Branch Roles
            Role.Create("Service Advisor", "Service advisor role for customer interaction and service management", "System"),
            Role.Create("Technician", "Technician role for vehicle maintenance and repair", "System"),
            Role.Create("Inventory Manager", "Inventory manager role for parts and supplies management", "System"),
            Role.Create("Cashier", "Cashier role for payment processing and billing", "System"),

            // General Roles
            Role.Create("User", "Regular user with basic access", "System")
        };

        await context.Roles.AddRangeAsync(roles);
    }

    private static async Task SeedPermissionsAsync(ApplicationDbContext context)
    {
        var permissions = new List<Permission>
        {
            Permission.Create("Users.View", "View users", "Users", "View", "System"),
            Permission.Create("Users.Create", "Create users", "Users", "Create", "System"),
            Permission.Create("Users.Update", "Update users", "Users", "Update", "System"),
            Permission.Create("Users.Delete", "Delete users", "Users", "Delete", "System"),
            Permission.Create("Roles.View", "View roles", "Roles", "View", "System"),
            Permission.Create("Roles.Create", "Create roles", "Roles", "Create", "System"),
            Permission.Create("Roles.Update", "Update roles", "Roles", "Update", "System"),
            Permission.Create("Roles.Delete", "Delete roles", "Roles", "Delete", "System"),
            Permission.Create("Permissions.View", "View permissions", "Permissions", "View", "System"),
            Permission.Create("Permissions.Assign", "Assign permissions", "Permissions", "Assign", "System"),
            Permission.Create("AuditLogs.View", "View audit logs", "AuditLogs", "View", "System")
        };

        await context.Permissions.AddRangeAsync(permissions);
    }

    private static async Task SeedRolePermissionsAsync(ApplicationDbContext context)
    {
        var superAdminRole = await context.Roles.FirstAsync(r => r.Name == "Super Admin");
        var adminRole = await context.Roles.FirstAsync(r => r.Name == "Admin");
        var vendorAdminRole = await context.Roles.FirstAsync(r => r.Name == "VendorAdmin");
        var branchAdminRole = await context.Roles.FirstAsync(r => r.Name == "BranchAdmin");
        var serviceAdvisorRole = await context.Roles.FirstAsync(r => r.Name == "Service Advisor");
        var technicianRole = await context.Roles.FirstAsync(r => r.Name == "Technician");
        var inventoryManagerRole = await context.Roles.FirstAsync(r => r.Name == "Inventory Manager");
        var cashierRole = await context.Roles.FirstAsync(r => r.Name == "Cashier");
        var userRole = await context.Roles.FirstAsync(r => r.Name == "User");

        var allPermissions = await context.Permissions.ToListAsync();
        var adminPermissions = allPermissions.Where(p => !p.Name.Contains("Delete")).ToList();
        var userPermissions = allPermissions.Where(p => p.Name.EndsWith(".View")).ToList();
        var vendorPermissions = allPermissions.Where(p =>
            p.Name.StartsWith("Vendor.") ||
            p.Name.StartsWith("Branch.") ||
            p.Name.StartsWith("Vehicle.") ||
            p.Name.StartsWith("Profile.")).ToList();
        var branchPermissions = allPermissions.Where(p =>
            p.Name.StartsWith("Vehicle.") ||
            p.Name.StartsWith("Service.") ||
            p.Name.StartsWith("Profile.")).ToList();

        var rolePermissions = new List<RolePermission>();

        // Super Admin gets all permissions
        rolePermissions.AddRange(allPermissions.Select(p =>
            RolePermission.Create(superAdminRole, p, "System")));

        // Admin gets all permissions except Delete
        rolePermissions.AddRange(adminPermissions.Select(p =>
            RolePermission.Create(adminRole, p, "System")));

        // VendorAdmin gets vendor-related permissions
        rolePermissions.AddRange(vendorPermissions.Select(p =>
            RolePermission.Create(vendorAdminRole, p, "System")));

        // BranchAdmin gets branch-related permissions
        rolePermissions.AddRange(branchPermissions.Select(p =>
            RolePermission.Create(branchAdminRole, p, "System")));

        // Service Advisor gets service and vehicle view/update permissions
        var serviceAdvisorPermissions = allPermissions.Where(p =>
            p.Name.StartsWith("Vehicle.View") ||
            p.Name.StartsWith("Vehicle.Update") ||
            p.Name.StartsWith("Service.") ||
            p.Name.StartsWith("Customer.")).ToList();
        rolePermissions.AddRange(serviceAdvisorPermissions.Select(p =>
            RolePermission.Create(serviceAdvisorRole, p, "System")));

        // Technician gets vehicle and service permissions
        var technicianPermissions = allPermissions.Where(p =>
            p.Name.StartsWith("Vehicle.") ||
            p.Name.StartsWith("Service.") ||
            p.Name.StartsWith("Maintenance.")).ToList();
        rolePermissions.AddRange(technicianPermissions.Select(p =>
            RolePermission.Create(technicianRole, p, "System")));

        // Inventory Manager gets inventory and parts permissions
        var inventoryPermissions = allPermissions.Where(p =>
            p.Name.StartsWith("Inventory.") ||
            p.Name.StartsWith("Parts.") ||
            p.Name.StartsWith("Stock.")).ToList();
        rolePermissions.AddRange(inventoryPermissions.Select(p =>
            RolePermission.Create(inventoryManagerRole, p, "System")));

        // Cashier gets billing and payment permissions
        var cashierPermissions = allPermissions.Where(p =>
            p.Name.StartsWith("Billing.") ||
            p.Name.StartsWith("Payment.") ||
            p.Name.StartsWith("Invoice.")).ToList();
        rolePermissions.AddRange(cashierPermissions.Select(p =>
            RolePermission.Create(cashierRole, p, "System")));

        // User gets only View permissions
        rolePermissions.AddRange(userPermissions.Select(p =>
            RolePermission.Create(userRole, p, "System")));

        await context.RolePermissions.AddRangeAsync(rolePermissions);
    }

    private static async Task SeedMenusAsync(ApplicationDbContext context)
    {
        var menus = new List<Menu>
        {
            Menu.Create("Dashboard", "Dashboard", "/dashboard", "dashboard", 1, null, "System"),
            Menu.Create("User Management", "User Management", "/users", "users", 2, null, "System"),
            Menu.Create("Role Management", "Role Management", "/roles", "roles", 3, null, "System"),
            Menu.Create("Permission Management", "Permission Management", "/permissions", "permissions", 4, null, "System"),
            Menu.Create("Audit Logs", "Audit Logs", "/audit-logs", "audit-logs", 5, null, "System")
        };

        await context.Menus.AddRangeAsync(menus);
    }

    private static async Task SeedMenuPermissionsAsync(ApplicationDbContext context)
    {
        var menus = await context.Menus.ToListAsync();
        var permissions = await context.Permissions.ToListAsync();

        var menuPermissions = new List<MenuPermission>();

        // Dashboard - accessible by all authenticated users
        var dashboardMenu = menus.First(m => m.Name == "Dashboard");
        menuPermissions.Add(MenuPermission.Create(dashboardMenu, null, "System"));

        // User Management
        var userMenu = menus.First(m => m.Name == "User Management");
        var userPermissions = permissions.Where(p => p.Name.StartsWith("Users.")).ToList();
        menuPermissions.AddRange(userPermissions.Select(p => 
            MenuPermission.Create(userMenu, p, "System")));

        // Role Management
        var roleMenu = menus.First(m => m.Name == "Role Management");
        var rolePermissions = permissions.Where(p => p.Name.StartsWith("Roles.")).ToList();
        menuPermissions.AddRange(rolePermissions.Select(p => 
            MenuPermission.Create(roleMenu, p, "System")));

        // Permission Management
        var permissionMenu = menus.First(m => m.Name == "Permission Management");
        var permissionPermissions = permissions.Where(p => p.Name.StartsWith("Permissions.")).ToList();
        menuPermissions.AddRange(permissionPermissions.Select(p => 
            MenuPermission.Create(permissionMenu, p, "System")));

        // Audit Logs
        var auditMenu = menus.First(m => m.Name == "Audit Logs");
        var auditPermission = permissions.First(p => p.Name == "AuditLogs.View");
        menuPermissions.Add(MenuPermission.Create(auditMenu, auditPermission, "System"));

        await context.MenuPermissions.AddRangeAsync(menuPermissions);
    }
} 