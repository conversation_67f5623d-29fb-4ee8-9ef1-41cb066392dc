{"Routes": [{"DownstreamPathTemplate": "/api/{everything}", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "localhost", "Port": 5263}], "UpstreamPathTemplate": "/api/{everything}", "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE"]}, {"DownstreamPathTemplate": "/swagger/v1/swagger.json", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "localhost", "Port": 5004}], "UpstreamPathTemplate": "/swagger/branch/swagger.json", "UpstreamHttpMethod": ["GET"]}, {"DownstreamPathTemplate": "/swagger/v1/swagger.json", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "localhost", "Port": 5263}], "UpstreamPathTemplate": "/swagger/identity/swagger.json", "UpstreamHttpMethod": ["GET"]}, {"DownstreamPathTemplate": "/swagger/v1/swagger.json", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "localhost", "Port": 5114}], "UpstreamPathTemplate": "/swagger/subscription/swagger.json", "UpstreamHttpMethod": ["GET"]}, {"DownstreamPathTemplate": "/swagger/v1/swagger.json", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "localhost", "Port": 5002}], "UpstreamPathTemplate": "/swagger/tenant/swagger.json", "UpstreamHttpMethod": ["GET"]}, {"DownstreamPathTemplate": "/swagger/v1/swagger.json", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "localhost", "Port": 5005}], "UpstreamPathTemplate": "/swagger/vehicle/swagger.json", "UpstreamHttpMethod": ["GET"]}, {"DownstreamPathTemplate": "/api/{everything}", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "localhost", "Port": 5004}], "UpstreamPathTemplate": "/branch/{everything}", "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE"]}, {"DownstreamPathTemplate": "/api/{everything}", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "localhost", "Port": 5114}], "UpstreamPathTemplate": "/subscription/{everything}", "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE"]}, {"DownstreamPathTemplate": "/api/{everything}", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "localhost", "Port": 5002}], "UpstreamPathTemplate": "/tenant/{everything}", "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE"]}, {"DownstreamPathTemplate": "/api/{everything}", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "localhost", "Port": 5005}], "UpstreamPathTemplate": "/vehicle/{everything}", "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE"]}], "GlobalConfiguration": {"BaseUrl": "http://localhost:5124"}}