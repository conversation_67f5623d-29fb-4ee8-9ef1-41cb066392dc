version: '3.8'

services:
  identity-service:
    build:
      context: .
      dockerfile: docker/Dockerfile
    ports:
      - "5001:80"
      - "5002:443"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__DefaultConnection=Host=postgres;Port=5432;Database=identity_service;Username=postgres;Password=postgres
      - RabbitMQ__HostName=rabbitmq
    depends_on:
      - postgres
      - rabbitmq
    networks:
      - vms-network

  postgres:
    image: postgres:16
    environment:
      - POSTGRES_DB=identity_service
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data
    networks:
      - vms-network

  rabbitmq:
    image: rabbitmq:3-management
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      - RABBITMQ_DEFAULT_USER=guest
      - RABBITMQ_DEFAULT_PASS=guest
    volumes:
      - rabbitmq-data:/var/lib/rabbitmq
    networks:
      - vms-network

  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
    networks:
      - vms-network

  grafana:
    image: grafana/grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana-data:/var/lib/grafana
    depends_on:
      - prometheus
    networks:
      - vms-network

volumes:
  postgres-data:
  rabbitmq-data:
  grafana-data:

networks:
  vms-network:
    driver: bridge 