using System;
using IdentityService.Domain.Common;

namespace IdentityService.Domain.Entities;

public class UserBranch : BaseEntity
{
    public Guid UserId { get; private set; }
    public Guid BranchId { get; private set; }
    public bool IsPrimary { get; private set; }
    public User User { get; private set; }
    public Branch Branch { get; private set; }

    private UserBranch() { }

    public static UserBranch Create(User user, Branch branch, bool isPrimary, string createdBy)
    {
        return new UserBranch
        {
            UserId = user.Id,
            BranchId = branch.Id,
            IsPrimary = isPrimary,
            User = user,
            Branch = branch,
            CreatedBy = createdBy
        };
    }

    public void SetAsPrimary(string updatedBy)
    {
        IsPrimary = true;
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;
    }

    public void SetAsNonPrimary(string updatedBy)
    {
        IsPrimary = false;
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;
    }
} 