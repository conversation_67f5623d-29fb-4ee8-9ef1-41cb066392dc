using InventoryManagementService.Domain.Entities;
using System.Linq.Expressions;

namespace InventoryManagementService.Domain.Interfaces;

public interface IInventoryRepository
{
    Task<InventoryItem?> GetByIdAsync(Guid id);
    Task<InventoryItem?> GetByPartAndBranchAsync(Guid partId, Guid branchId);
    Task<List<InventoryItem>> GetAllAsync();
    Task<List<InventoryItem>> GetByBranchAsync(Guid branchId);
    Task<List<InventoryItem>> GetByPartAsync(Guid partId);
    Task<List<InventoryItem>> GetByVendorAsync(Guid vendorId);
    Task<List<InventoryItem>> GetLowStockItemsAsync(Guid branchId);
    Task<List<InventoryItem>> GetOutOfStockItemsAsync(Guid branchId);
    Task<List<InventoryItem>> GetAsync(Expression<Func<InventoryItem, bool>> predicate);
    Task AddAsync(InventoryItem inventoryItem);
    Task UpdateAsync(InventoryItem inventoryItem);
    Task DeleteAsync(Guid id);
    Task<bool> ExistsAsync(Guid id);
    Task<bool> ExistsForPartAndBranchAsync(Guid partId, Guid branchId);
}
