using IdentityService.Domain.Interfaces;
using IdentityService.Domain.Services;
using MediatR;
using Microsoft.Extensions.Logging;

namespace IdentityService.Application.Features.Menus.Queries;

/// <summary>
/// Query to get admin menu (all menus regardless of permissions)
/// </summary>
public class GetAdminMenuQuery : IRequest<List<MenuItemResponse>>
{
}

/// <summary>
/// Handler for getting admin menu
/// </summary>
public class GetAdminMenuQueryHandler : IRequestHandler<GetAdminMenuQuery, List<MenuItemResponse>>
{
    private readonly IMenuService _menuService;
    private readonly ICurrentUserService _currentUserService;
    private readonly ILogger<GetAdminMenuQueryHandler> _logger;

    public GetAdminMenuQueryHandler(
        IMenuService menuService,
        ICurrentUserService currentUserService,
        ILogger<GetAdminMenuQueryHandler> logger)
    {
        _menuService = menuService;
        _currentUserService = currentUserService;
        _logger = logger;
    }

    public async Task<List<MenuItemResponse>> Handle(GetAdminMenuQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var userId = _currentUserService.UserId;
            if (!userId.HasValue)
            {
                throw new UnauthorizedAccessException("User not authenticated");
            }

            _logger.LogInformation("Getting admin menu for user {UserId}", userId.Value);

            // Get admin menu (all menus regardless of permissions)
            var menuItems = await _menuService.GetAdminMenuAsync();

            // Map to response
            var response = menuItems.Select(MapToResponse).ToList();

            _logger.LogInformation("Successfully retrieved admin menu with {Count} items for user {UserId}",
                response.Count, userId.Value);

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting admin menu");
            throw;
        }
    }

    private static MenuItemResponse MapToResponse(MenuItemDto menuItem)
    {
        return new MenuItemResponse
        {
            Id = menuItem.Id,
            Name = menuItem.Name,
            DisplayName = menuItem.DisplayName,
            Path = menuItem.Path,
            Icon = menuItem.Icon,
            Order = menuItem.Order,
            ParentId = menuItem.ParentId,
            IsVisible = menuItem.IsVisible,
            HasAccess = menuItem.HasAccess,
            RequiredPermissions = menuItem.RequiredPermissions,
            Children = menuItem.Children.Select(MapToResponse).ToList()
        };
    }
}
