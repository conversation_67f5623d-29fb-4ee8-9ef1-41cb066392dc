using InventoryManagementService.Domain.Interfaces;
using MassTransit;
using Microsoft.Extensions.Logging;

namespace InventoryManagementService.Infrastructure.Messaging;

public class EventPublisher : IEventPublisher
{
    private readonly IPublishEndpoint _publishEndpoint;
    private readonly ILogger<EventPublisher> _logger;

    public EventPublisher(IPublishEndpoint publishEndpoint, ILogger<EventPublisher> logger)
    {
        _publishEndpoint = publishEndpoint;
        _logger = logger;
    }

    public async Task PublishPartCreatedEvent(Guid partId, string sku, string name, string manufacturer)
    {
        try
        {
            var eventData = new
            {
                PartId = partId,
                SKU = sku,
                Name = name,
                Manufacturer = manufacturer,
                EventType = "PartCreated",
                Timestamp = DateTime.UtcNow
            };

            await _publishEndpoint.Publish(eventData);
            _logger.LogInformation("Published PartCreated event for Part {PartId}", partId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to publish PartCreated event for Part {PartId}", partId);
        }
    }

    public async Task PublishPartUpdatedEvent(Guid partId, string sku, string name, string manufacturer)
    {
        try
        {
            var eventData = new
            {
                PartId = partId,
                SKU = sku,
                Name = name,
                Manufacturer = manufacturer,
                EventType = "PartUpdated",
                Timestamp = DateTime.UtcNow
            };

            await _publishEndpoint.Publish(eventData);
            _logger.LogInformation("Published PartUpdated event for Part {PartId}", partId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to publish PartUpdated event for Part {PartId}", partId);
        }
    }

    public async Task PublishPartActivatedEvent(Guid partId, string sku)
    {
        try
        {
            var eventData = new
            {
                PartId = partId,
                SKU = sku,
                EventType = "PartActivated",
                Timestamp = DateTime.UtcNow
            };

            await _publishEndpoint.Publish(eventData);
            _logger.LogInformation("Published PartActivated event for Part {PartId}", partId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to publish PartActivated event for Part {PartId}", partId);
        }
    }

    public async Task PublishPartDeactivatedEvent(Guid partId, string sku)
    {
        try
        {
            var eventData = new
            {
                PartId = partId,
                SKU = sku,
                EventType = "PartDeactivated",
                Timestamp = DateTime.UtcNow
            };

            await _publishEndpoint.Publish(eventData);
            _logger.LogInformation("Published PartDeactivated event for Part {PartId}", partId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to publish PartDeactivated event for Part {PartId}", partId);
        }
    }

    public async Task PublishInventoryCreatedEvent(Guid inventoryItemId, Guid partId, Guid branchId, int initialStock)
    {
        try
        {
            var eventData = new
            {
                InventoryItemId = inventoryItemId,
                PartId = partId,
                BranchId = branchId,
                InitialStock = initialStock,
                EventType = "InventoryCreated",
                Timestamp = DateTime.UtcNow
            };

            await _publishEndpoint.Publish(eventData);
            _logger.LogInformation("Published InventoryCreated event for InventoryItem {InventoryItemId}", inventoryItemId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to publish InventoryCreated event for InventoryItem {InventoryItemId}", inventoryItemId);
        }
    }

    public async Task PublishStockUpdatedEvent(Guid inventoryItemId, Guid partId, Guid branchId, int previousStock, int newStock, string reason)
    {
        try
        {
            var eventData = new
            {
                InventoryItemId = inventoryItemId,
                PartId = partId,
                BranchId = branchId,
                PreviousStock = previousStock,
                NewStock = newStock,
                Reason = reason,
                EventType = "StockUpdated",
                Timestamp = DateTime.UtcNow
            };

            await _publishEndpoint.Publish(eventData);
            _logger.LogInformation("Published StockUpdated event for InventoryItem {InventoryItemId}", inventoryItemId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to publish StockUpdated event for InventoryItem {InventoryItemId}", inventoryItemId);
        }
    }

    public async Task PublishLowStockAlertEvent(Guid inventoryItemId, Guid partId, Guid branchId, int currentStock, int reorderLevel)
    {
        try
        {
            var eventData = new
            {
                InventoryItemId = inventoryItemId,
                PartId = partId,
                BranchId = branchId,
                CurrentStock = currentStock,
                ReorderLevel = reorderLevel,
                EventType = "LowStockAlert",
                Timestamp = DateTime.UtcNow
            };

            await _publishEndpoint.Publish(eventData);
            _logger.LogInformation("Published LowStockAlert event for InventoryItem {InventoryItemId}", inventoryItemId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to publish LowStockAlert event for InventoryItem {InventoryItemId}", inventoryItemId);
        }
    }

    public async Task PublishTransferCreatedEvent(Guid transferId, string transferNumber, Guid sourceBranchId, Guid destinationBranchId)
    {
        try
        {
            var eventData = new
            {
                TransferId = transferId,
                TransferNumber = transferNumber,
                SourceBranchId = sourceBranchId,
                DestinationBranchId = destinationBranchId,
                EventType = "TransferCreated",
                Timestamp = DateTime.UtcNow
            };

            await _publishEndpoint.Publish(eventData);
            _logger.LogInformation("Published TransferCreated event for Transfer {TransferId}", transferId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to publish TransferCreated event for Transfer {TransferId}", transferId);
        }
    }

    public async Task PublishTransferShippedEvent(Guid transferId, string transferNumber, DateTime shippedDate)
    {
        try
        {
            var eventData = new
            {
                TransferId = transferId,
                TransferNumber = transferNumber,
                ShippedDate = shippedDate,
                EventType = "TransferShipped",
                Timestamp = DateTime.UtcNow
            };

            await _publishEndpoint.Publish(eventData);
            _logger.LogInformation("Published TransferShipped event for Transfer {TransferId}", transferId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to publish TransferShipped event for Transfer {TransferId}", transferId);
        }
    }

    public async Task PublishTransferCompletedEvent(Guid transferId, string transferNumber, DateTime completedDate)
    {
        try
        {
            var eventData = new
            {
                TransferId = transferId,
                TransferNumber = transferNumber,
                CompletedDate = completedDate,
                EventType = "TransferCompleted",
                Timestamp = DateTime.UtcNow
            };

            await _publishEndpoint.Publish(eventData);
            _logger.LogInformation("Published TransferCompleted event for Transfer {TransferId}", transferId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to publish TransferCompleted event for Transfer {TransferId}", transferId);
        }
    }

    public async Task PublishTransferCancelledEvent(Guid transferId, string transferNumber, string reason)
    {
        try
        {
            var eventData = new
            {
                TransferId = transferId,
                TransferNumber = transferNumber,
                Reason = reason,
                EventType = "TransferCancelled",
                Timestamp = DateTime.UtcNow
            };

            await _publishEndpoint.Publish(eventData);
            _logger.LogInformation("Published TransferCancelled event for Transfer {TransferId}", transferId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to publish TransferCancelled event for Transfer {TransferId}", transferId);
        }
    }

    public async Task PublishAdjustmentCreatedEvent(Guid adjustmentId, string adjustmentNumber, Guid branchId)
    {
        try
        {
            var eventData = new
            {
                AdjustmentId = adjustmentId,
                AdjustmentNumber = adjustmentNumber,
                BranchId = branchId,
                EventType = "AdjustmentCreated",
                Timestamp = DateTime.UtcNow
            };

            await _publishEndpoint.Publish(eventData);
            _logger.LogInformation("Published AdjustmentCreated event for Adjustment {AdjustmentId}", adjustmentId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to publish AdjustmentCreated event for Adjustment {AdjustmentId}", adjustmentId);
        }
    }

    public async Task PublishAdjustmentApprovedEvent(Guid adjustmentId, string adjustmentNumber, DateTime approvedDate)
    {
        try
        {
            var eventData = new
            {
                AdjustmentId = adjustmentId,
                AdjustmentNumber = adjustmentNumber,
                ApprovedDate = approvedDate,
                EventType = "AdjustmentApproved",
                Timestamp = DateTime.UtcNow
            };

            await _publishEndpoint.Publish(eventData);
            _logger.LogInformation("Published AdjustmentApproved event for Adjustment {AdjustmentId}", adjustmentId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to publish AdjustmentApproved event for Adjustment {AdjustmentId}", adjustmentId);
        }
    }
}
