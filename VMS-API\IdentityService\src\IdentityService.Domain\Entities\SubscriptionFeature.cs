using System;
using IdentityService.Domain.Common;

namespace IdentityService.Domain.Entities;

public class SubscriptionFeature : BaseEntity
{
    public Guid UserSubscriptionId { get; private set; }
    public UserSubscription UserSubscription { get; private set; }
    public string FeatureName { get; private set; }
    public bool IsEnabled { get; private set; }
    public int? UsageLimit { get; private set; }

    private SubscriptionFeature() { }

    public static SubscriptionFeature Create(
        Guid userSubscriptionId,
        string featureName,
        bool isEnabled,
        int? usageLimit,
        string createdBy)
    {
        return new SubscriptionFeature
        {
            UserSubscriptionId = userSubscriptionId,
            FeatureName = featureName,
            IsEnabled = isEnabled,
            UsageLimit = usageLimit,
            CreatedBy = createdBy
        };
    }

    public void UpdateAccess(bool isEnabled, int? usageLimit, string updatedBy)
    {
        IsEnabled = isEnabled;
        UsageLimit = usageLimit;
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;
    }
}
