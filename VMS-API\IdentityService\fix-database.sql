-- Fix database migration issues for Identity Service
-- This script handles the PrimaryBranchId column issue and ensures proper migration state

-- Step 1: Check and handle PrimaryBranchId column
DO $$
BEGIN
    -- Check if PrimaryBranchId column exists
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'Users'
        AND column_name = 'PrimaryBranchId'
    ) THEN
        RAISE NOTICE 'PrimaryBranchId column already exists in Users table';

        -- Mark the migration as applied if it exists but isn't recorded
        IF NOT EXISTS (
            SELECT 1 FROM "__EFMigrationsHistory"
            WHERE "MigrationId" = '20250606072859_AddUserPermissionSupport'
        ) THEN
            INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
            VALUES ('20250606072859_AddUserPermissionSupport', '8.0.0');
            RAISE NOTICE 'Marked AddUserPermissionSupport migration as applied';
        END IF;
    ELSE
        -- Add the column if it doesn't exist
        ALTER TABLE "Users" ADD COLUMN "PrimaryBranchId" uuid NULL;
        RAISE NOTICE 'Added PrimaryBranchId column to Users table';
    END IF;
END $$;

-- Step 2: Check and handle VendorId column
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'Users'
        AND column_name = 'VendorId'
    ) THEN
        ALTER TABLE "Users" ADD COLUMN "VendorId" uuid NULL;
        RAISE NOTICE 'Added VendorId column to Users table';
    ELSE
        RAISE NOTICE 'VendorId column already exists in Users table';
    END IF;
END $$;

-- Step 3: Ensure UserPermission table exists
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.tables
        WHERE table_name = 'UserPermission'
    ) THEN
        CREATE TABLE "UserPermission" (
            "Id" uuid NOT NULL,
            "UserId" uuid NOT NULL,
            "PermissionId" uuid NOT NULL,
            "Type" integer NOT NULL,
            "ExpiresAt" timestamp with time zone NULL,
            "Reason" text NULL,
            "IsActive" boolean NOT NULL,
            "CreatedAt" timestamp with time zone NOT NULL,
            "UpdatedAt" timestamp with time zone NULL,
            "CreatedBy" text NOT NULL,
            "UpdatedBy" text NULL,
            "IsDeleted" boolean NOT NULL,
            "RowVersion" bytea NULL,
            CONSTRAINT "PK_UserPermission" PRIMARY KEY ("Id"),
            CONSTRAINT "FK_UserPermission_Permissions_PermissionId" FOREIGN KEY ("PermissionId") REFERENCES "Permissions" ("Id") ON DELETE CASCADE,
            CONSTRAINT "FK_UserPermission_Users_UserId" FOREIGN KEY ("UserId") REFERENCES "Users" ("Id") ON DELETE CASCADE
        );

        CREATE INDEX "IX_UserPermission_PermissionId" ON "UserPermission" ("PermissionId");
        CREATE INDEX "IX_UserPermission_UserId" ON "UserPermission" ("UserId");

        RAISE NOTICE 'Created UserPermission table with indexes';
    ELSE
        RAISE NOTICE 'UserPermission table already exists';
    END IF;
END $$;

-- Step 4: Ensure Vendor table exists
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.tables
        WHERE table_name = 'Vendor'
    ) THEN
        CREATE TABLE "Vendor" (
            "Id" uuid NOT NULL,
            "Name" text NOT NULL,
            "Email" text NOT NULL,
            "PhoneNumber" text NOT NULL,
            "IsActive" boolean NOT NULL,
            "CreatedAt" timestamp with time zone NOT NULL,
            "UpdatedAt" timestamp with time zone NULL,
            "CreatedBy" text NOT NULL,
            "UpdatedBy" text NULL,
            "IsDeleted" boolean NOT NULL,
            "RowVersion" bytea NULL,
            CONSTRAINT "PK_Vendor" PRIMARY KEY ("Id")
        );

        RAISE NOTICE 'Created Vendor table';
    ELSE
        RAISE NOTICE 'Vendor table already exists';
    END IF;
END $$;

-- Step 5: Ensure foreign key constraint exists for Users.VendorId
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.table_constraints
        WHERE constraint_name = 'FK_Users_Vendor_VendorId'
    ) THEN
        ALTER TABLE "Users" ADD CONSTRAINT "FK_Users_Vendor_VendorId"
        FOREIGN KEY ("VendorId") REFERENCES "Vendor" ("Id");
        RAISE NOTICE 'Added foreign key constraint FK_Users_Vendor_VendorId';
    ELSE
        RAISE NOTICE 'Foreign key constraint FK_Users_Vendor_VendorId already exists';
    END IF;
END $$;

-- Step 6: Ensure index exists for Users.VendorId
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM pg_indexes
        WHERE indexname = 'IX_Users_VendorId'
    ) THEN
        CREATE INDEX "IX_Users_VendorId" ON "Users" ("VendorId");
        RAISE NOTICE 'Created index IX_Users_VendorId';
    ELSE
        RAISE NOTICE 'Index IX_Users_VendorId already exists';
    END IF;
END $$;

-- Step 7: Update RowVersion columns to be nullable if needed
DO $$
BEGIN
    -- Make RowVersion nullable for Users table
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'Users'
        AND column_name = 'RowVersion'
        AND is_nullable = 'NO'
    ) THEN
        ALTER TABLE "Users" ALTER COLUMN "RowVersion" DROP NOT NULL;
        RAISE NOTICE 'Made Users.RowVersion nullable';
    END IF;
END $$;

RAISE NOTICE 'Database fix script completed successfully!';
