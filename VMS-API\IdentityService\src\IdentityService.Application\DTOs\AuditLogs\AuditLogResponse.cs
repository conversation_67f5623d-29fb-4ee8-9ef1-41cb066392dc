using System;

namespace IdentityService.Application.DTOs.AuditLogs;

public class AuditLogResponse
{
    public Guid Id { get; set; }
    public string Action { get; set; }
    public string EntityName { get; set; }
    public string EntityId { get; set; }
    public string OldValues { get; set; }
    public string NewValues { get; set; }
    public string AffectedColumns { get; set; }
    public string IpAddress { get; set; }
    public string UserAgent { get; set; }
    public Guid UserId { get; set; }
    public string UserEmail { get; set; }
    public DateTime CreatedAt { get; set; }
    public string CreatedBy { get; set; }
}
