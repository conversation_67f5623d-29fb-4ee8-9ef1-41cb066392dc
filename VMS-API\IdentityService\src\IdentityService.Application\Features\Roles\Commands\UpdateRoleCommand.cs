using System;
using System.Threading;
using System.Threading.Tasks;
using IdentityService.Application.Common;
using IdentityService.Application.DTOs.Roles;
using IdentityService.Application.Interfaces;
using IdentityService.Domain.Interfaces;
using MediatR;

namespace IdentityService.Application.Features.Roles.Commands;

public class UpdateRoleCommand : BaseRequest<RoleResponse>
{
    public Guid RoleId { get; set; }
    public UpdateRoleRequest Request { get; set; }
}

public class UpdateRoleCommandHandler : IRequestHandler<UpdateRoleCommand, RoleResponse>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICurrentUserService _currentUserService;
    private readonly IAuditLogService _auditLogService;

    public UpdateRoleCommandHandler(
        IUnitOfWork unitOfWork,
        ICurrentUserService currentUserService,
        IAuditLogService auditLogService)
    {
        _unitOfWork = unitOfWork;
        _currentUserService = currentUserService;
        _auditLogService = auditLogService;
    }

    public async Task<RoleResponse> Handle(UpdateRoleCommand command, CancellationToken cancellationToken)
    {
        var role = await _unitOfWork.RoleRepository.GetByIdAsync(command.RoleId);
        if (role == null)
            throw new InvalidOperationException($"Role with ID {command.RoleId} not found");

        var request = command.Request;
        var updatedBy = _currentUserService.UserId.ToString() ?? "System";

        // Track changes for audit log
        var oldValues = System.Text.Json.JsonSerializer.Serialize(new
        {
            role.Name,
            role.Description
        });

        // Update role
        role.UpdateDetails(request.Name, request.Description, updatedBy);

        // Save changes
        await _unitOfWork.RoleRepository.UpdateAsync(role);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        // Create audit log
        var newValues = System.Text.Json.JsonSerializer.Serialize(new
        {
            role.Name,
            role.Description
        });

        await _auditLogService.CreateAuditLogAsync(
            "Update",
            "Role",
            role.Id.ToString(),
            oldValues,
            newValues,
            "Name,Description",
            _currentUserService.UserId ?? Guid.Empty);

        // Get permissions for response
        var permissions = new List<RolePermissionResponse>();
        foreach (var rp in role.RolePermissions)
        {
            var permission = await _unitOfWork.PermissionRepository.GetByIdAsync(rp.PermissionId);
            if (permission != null)
            {
                permissions.Add(new RolePermissionResponse
                {
                    Id = permission.Id,
                    Name = permission.Name,
                    Description = permission.Description,
                    Resource = permission.Resource,
                    Action = permission.Action
                });
            }
        }

        // Create response
        var response = new RoleResponse
        {
            Id = role.Id,
            Name = role.Name,
            Description = role.Description,
            CreatedAt = role.CreatedAt,
            CreatedBy = role.CreatedBy,
            UpdatedAt = role.UpdatedAt,
            UpdatedBy = role.UpdatedBy,
            Permissions = permissions
        };

        return response;
    }
}
