using System;
using System.Threading;
using System.Threading.Tasks;
using IdentityService.Application.DTOs.Users;
using IdentityService.Domain.Interfaces;
using MediatR;
using Microsoft.Extensions.Logging;

namespace IdentityService.Application.Features.Users.Queries
{
    /// <summary>
    /// Query to get a user by email
    /// </summary>
    public class GetUserByEmailQuery : IRequest<UserResponse>
    {
        /// <summary>
        /// The user's email
        /// </summary>
        public required string Email { get; set; }
    }

    /// <summary>
    /// Handler for <see cref="GetUserByEmailQuery"/>
    /// </summary>
    public class GetUserByEmailQueryHandler : IRequestHandler<GetUserByEmailQuery, UserResponse>
    {
        private readonly IUserRepository _userRepository;
        private readonly ILogger<GetUserByEmailQueryHandler> _logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="GetUserByEmailQueryHandler"/> class
        /// </summary>
        /// <param name="userRepository">The user repository</param>
        /// <param name="logger">The logger</param>
        public GetUserByEmailQueryHandler(
            IUserRepository userRepository,
            ILogger<GetUserByEmailQueryHandler> logger)
        {
            _userRepository = userRepository ?? throw new ArgumentNullException(nameof(userRepository));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Handles the query
        /// </summary>
        /// <param name="request">The request</param>
        /// <param name="cancellationToken">The cancellation token</param>
        /// <returns>The user response</returns>
        public async Task<UserResponse> Handle(GetUserByEmailQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Getting user by email {Email}", request.Email);

                var user = await _userRepository.GetByEmailAsync(request.Email);

                if (user == null)
                {
                    _logger.LogWarning("User with email {Email} not found", request.Email);
                    return null;
                }

                var response = new UserResponse
                {
                    Id = user.Id,
                    Email = user.Email,
                    PhoneNumber = user.PhoneNumber,
                    IsActive = !user.IsDeleted,
                    EmailVerified = user.EmailVerified,
                    PhoneNumberVerified = user.PhoneNumberVerified,
                    CreatedAt = user.CreatedAt,
                    UpdatedAt = user.UpdatedAt,
                    CreatedBy = user.CreatedBy,
                    UpdatedBy = user.UpdatedBy
                };

                _logger.LogInformation("Successfully retrieved user with email {Email}", request.Email);

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user by email {Email}", request.Email);
                throw;
            }
        }
    }
}
