using IdentityService.Application.Interfaces;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Logging;
using System;

namespace IdentityService.Infrastructure.Services;

public class PasswordHasher : IPasswordHasher
{
    private readonly PasswordHasher<object> _passwordHasher;
    private readonly ILogger<PasswordHasher> _logger;

    public PasswordHasher(ILogger<PasswordHasher> logger = null)
    {
        _passwordHasher = new PasswordHasher<object>();
        _logger = logger;
    }

    public string HashPassword(string password)
    {
        return _passwordHasher.HashPassword(null, password);
    }

    public bool VerifyHashedPassword(string hashedPassword, string providedPassword)
    {
        try
        {
            if (string.IsNullOrEmpty(hashedPassword))
            {
                _logger?.LogWarning("Password hash is null or empty");
                return false;
            }

            var result = _passwordHasher.VerifyHashedPassword(null, hashedPassword, providedPassword);
            return result == PasswordVerificationResult.Success;
        }
        catch (FormatException ex)
        {
            // If the hash is not in a valid format, log the error and return false
            _logger?.LogError(ex, "Invalid password hash format: {HashLength} chars",
                hashedPassword?.Length ?? 0);
            return false;
        }
        catch (Exception ex)
        {
            // Log any other exceptions
            _logger?.LogError(ex, "Error verifying password hash");
            return false;
        }
    }

    public bool VerifyPassword(string providedPassword, string hashedPassword)
    {
        // Fix parameter order - VerifyHashedPassword expects (hashedPassword, providedPassword)
        return VerifyHashedPassword(hashedPassword, providedPassword);
    }
}