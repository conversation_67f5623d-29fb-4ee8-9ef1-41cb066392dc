using System;
using System.Threading;
using System.Threading.Tasks;
using IdentityService.Domain.Interfaces;
using MediatR;
using Microsoft.Extensions.Logging;

namespace IdentityService.Application.Features.Roles.Queries
{
    /// <summary>
    /// Query to check if a user has a specific role
    /// </summary>
    public class CheckUserHasRoleQuery : IRequest<bool>
    {
        /// <summary>
        /// The user ID
        /// </summary>
        public Guid UserId { get; set; }

        /// <summary>
        /// The role name
        /// </summary>
        public required string RoleName { get; set; }
    }

    /// <summary>
    /// Handler for <see cref="CheckUserHasRoleQuery"/>
    /// </summary>
    public class CheckUserHasRoleQueryHandler : IRequestHandler<CheckUserHasRoleQuery, bool>
    {
        private readonly IUserRepository _userRepository;
        private readonly ILogger<CheckUserHasRoleQueryHandler> _logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="CheckUserHasRoleQueryHandler"/> class
        /// </summary>
        /// <param name="userRepository">The user repository</param>
        /// <param name="logger">The logger</param>
        public CheckUserHasRoleQueryHandler(
            IUserRepository userRepository,
            ILogger<CheckUserHasRoleQueryHandler> logger)
        {
            _userRepository = userRepository ?? throw new ArgumentNullException(nameof(userRepository));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Handles the query
        /// </summary>
        /// <param name="request">The request</param>
        /// <param name="cancellationToken">The cancellation token</param>
        /// <returns>True if the user has the role, false otherwise</returns>
        public async Task<bool> Handle(CheckUserHasRoleQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Checking if user {UserId} has role {RoleName}", request.UserId, request.RoleName);

                var user = await _userRepository.GetByIdAsync(request.UserId);

                if (user == null)
                {
                    _logger.LogWarning("User {UserId} not found", request.UserId);
                    return false;
                }

                var hasRole = await _userRepository.UserHasRoleAsync(request.UserId, request.RoleName);

                _logger.LogInformation("User {UserId} has role {RoleName}: {HasRole}", request.UserId, request.RoleName, hasRole);

                return hasRole;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if user {UserId} has role {RoleName}", request.UserId, request.RoleName);
                throw;
            }
        }
    }
}
