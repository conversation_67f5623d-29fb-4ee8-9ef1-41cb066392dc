using System;

namespace IdentityService.Domain.Services;

/// <summary>
/// Options for password complexity requirements
/// </summary>
public class PasswordComplexityOptions
{
    /// <summary>
    /// Minimum required length for passwords
    /// </summary>
    public int MinimumLength { get; set; } = 8;

    /// <summary>
    /// Whether passwords must contain at least one uppercase letter
    /// </summary>
    public bool RequireUppercase { get; set; } = true;

    /// <summary>
    /// Whether passwords must contain at least one lowercase letter
    /// </summary>
    public bool RequireLowercase { get; set; } = true;

    /// <summary>
    /// Whether passwords must contain at least one digit
    /// </summary>
    public bool RequireDigit { get; set; } = true;

    /// <summary>
    /// Whether passwords must contain at least one special character
    /// </summary>
    public bool RequireSpecialCharacter { get; set; } = true;

    /// <summary>
    /// Maximum number of repeated characters allowed
    /// </summary>
    public int MaximumRepeatedCharacters { get; set; } = 3;

    /// <summary>
    /// Whether to prevent common passwords
    /// </summary>
    public bool PreventCommonPasswords { get; set; } = true;

    /// <summary>
    /// Whether to prevent passwords containing the username
    /// </summary>
    public bool PreventUsernameInPassword { get; set; } = true;
}
