using Microsoft.AspNetCore.Authorization;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;
using System.Linq;

namespace IdentityService.API.Filters
{
    public class SecurityRequirementsOperationFilter : IOperationFilter
    {
        public void Apply(OpenApiOperation operation, OperationFilterContext context)
        {
            // Policy names map to scopes
            var requiredScopes = context.MethodInfo
                .GetCustomAttributes(true)
                .OfType<AuthorizeAttribute>()
                .Select(attr => attr.Policy)
                .Distinct();

            if (!requiredScopes.Any())
            {
                // Check controller level attributes
                if (context.MethodInfo.DeclaringType != null)
                {
                    requiredScopes = context.MethodInfo.DeclaringType
                        .GetCustomAttributes(true)
                        .OfType<AuthorizeAttribute>()
                        .Select(attr => attr.Policy)
                        .Distinct();
                }
            }

            if (requiredScopes.Any())
            {
                // Add the "Bearer" security requirement
                operation.Security = new List<OpenApiSecurityRequirement>
                {
                    new OpenApiSecurityRequirement
                    {
                        {
                            new OpenApiSecurityScheme
                            {
                                Reference = new OpenApiReference
                                {
                                    Type = ReferenceType.SecurityScheme,
                                    Id = "Bearer"
                                }
                            },
                            requiredScopes.ToList()
                        }
                    }
                };
            }
        }
    }
}
