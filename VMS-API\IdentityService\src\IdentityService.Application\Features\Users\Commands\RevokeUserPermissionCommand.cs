using System.ComponentModel.DataAnnotations;
using IdentityService.Application.Interfaces;
using IdentityService.Domain.Interfaces;
using IdentityService.Domain.Services;
using MediatR;
using Microsoft.Extensions.Logging;

namespace IdentityService.Application.Features.Users.Commands;

/// <summary>
/// Command to revoke a user permission
/// </summary>
public class RevokeUserPermissionCommand : IRequest
{
    /// <summary>
    /// User ID
    /// </summary>
    [Required]
    public Guid UserId { get; set; }

    /// <summary>
    /// Permission ID to revoke
    /// </summary>
    [Required]
    public Guid PermissionId { get; set; }
}

/// <summary>
/// Handler for revoking user permission
/// </summary>
public class RevokeUserPermissionCommandHandler : IRequestHandler<RevokeUserPermissionCommand, Unit>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IPermissionService _permissionService;
    private readonly ICurrentUserService _currentUserService;
    private readonly ILogger<RevokeUserPermissionCommandHandler> _logger;

    public RevokeUserPermissionCommandHandler(
        IUnitOfWork unitOfWork,
        IPermissionService permissionService,
        ICurrentUserService currentUserService,
        ILogger<RevokeUserPermissionCommandHandler> logger)
    {
        _unitOfWork = unitOfWork;
        _permissionService = permissionService;
        _currentUserService = currentUserService;
        _logger = logger;
    }

    public async Task<Unit> Handle(RevokeUserPermissionCommand command, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Revoking permission {PermissionId} from user {UserId}",
                command.PermissionId, command.UserId);

            // Get the user
            var user = await _unitOfWork.UserRepository.GetByIdAsync(command.UserId);
            if (user == null)
            {
                throw new ArgumentException($"User with ID {command.UserId} not found");
            }

            // Get the permission
            var permission = await _unitOfWork.PermissionRepository.GetByIdAsync(command.PermissionId);
            if (permission == null)
            {
                throw new ArgumentException($"Permission with ID {command.PermissionId} not found");
            }

            // Find the user permission
            var userPermission = user.UserPermissions
                .FirstOrDefault(up => up.PermissionId == command.PermissionId && up.IsCurrentlyActive());

            if (userPermission == null)
            {
                throw new ArgumentException($"Active permission {command.PermissionId} not found for user {command.UserId}");
            }

            // Revoke the permission
            var currentUserName = _currentUserService.UserName ?? "System";
            userPermission.Revoke(currentUserName);

            // Save changes
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Successfully revoked permission {PermissionId} from user {UserId}",
                command.PermissionId, command.UserId);

            return Unit.Value;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error revoking permission {PermissionId} from user {UserId}",
                command.PermissionId, command.UserId);
            throw;
        }
    }
}
