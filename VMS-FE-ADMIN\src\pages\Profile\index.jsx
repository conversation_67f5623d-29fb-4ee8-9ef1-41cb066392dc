import React, { useState, useEffect } from "react";
import { useLocation } from "react-router-dom";
import { <PERSON><PERSON><PERSON>, <PERSON>, But<PERSON> } from "reactstrap";
import {
  FaUser,
  FaEnvelope,
  FaPhone,
  FaMapMarkerAlt,
  FaCalendarAlt,
  FaEdit,
  FaBuilding,
  FaCog,
} from "react-icons/fa";
import { useSelector } from "react-redux";
import { useUserById } from "@api/useAuthHooks";
import { useFilter } from "@contexts/FilterContext";
import AccountSettings from "./components/AccountSettings";
import EditProfileForm from "./components/EditProfileForm";
// CSS moved to styles/custom.css - use vm-profile-* classes

const Profile = () => {
  const location = useLocation();
  const [activeTab, setActiveTab] = useState("profile");
  const { openSidebar } = useFilter();

  // Get user ID from Redux store
  const currentUser = useSelector((state) => state.user.user);
  const userId = currentUser?.id;

  // Set active tab based on route
  useEffect(() => {
    if (location.pathname === "/account-settings") {
      setActiveTab("settings");
    } else {
      setActiveTab("profile");
    }
  }, [location.pathname]);

  // Fetch user data from API
  const {
    data: userResponse,
    isLoading: userLoading,
    error: userError,
    refetch: refetchUser,
  } = useUserById(userId, {
    enabled: !!userId,
  });

  const userData = userResponse?.data;

  // Format user data for display
  const adminData = userData ? {
    id: userData.id,
    name: `${userData.firstName} ${userData.lastName}`,
    email: userData.email,
    phone: userData.phoneNumber,
    role: userData.userType === 1 ? "Super Admin" : "System Administrator",
    department: "IT Operations",
    location: "India",
    joinDate: new Date(userData.createdAt).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }),
    lastLogin: userData.lastLoginAt ? new Date(userData.lastLoginAt).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }) : "Never",
    avatar: null, // Will use icon placeholder
    permissions: [
      "User Management",
      "Vendor Management",
      "System Configuration",
      "Reports & Analytics",
      "Security Settings"
    ],
    stats: {
      totalVendors: 1247,
      activeUsers: 89,
      pendingApprovals: 23,
      systemUptime: "99.9%"
    }
  } : null;

  const handleEditProfile = () => {
    if (userData) {
      openSidebar(
        <EditProfileForm
          userData={userData}
          onClose={() => {}}
          onSuccess={() => {
            refetchUser(); // Refresh user data after successful update
          }}
        />,
        "Edit Profile",
        "edit"
      );
    }
  };

  // Loading state
  if (userLoading) {
    return (
      <div className="simple-profile-page">
        <div className="simple-profile-container">
          <div className="simple-page-header">
            <h1 className="simple-page-title">Admin Profile</h1>
          </div>
          <div className="text-center p-4">
            <div className="spinner-border text-primary" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
            <p className="mt-2">Loading profile...</p>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (userError || !adminData) {
    return (
      <div className="simple-profile-page">
        <div className="simple-profile-container">
          <div className="simple-page-header">
            <h1 className="simple-page-title">Admin Profile</h1>
          </div>
          <div className="text-center p-4">
            <div className="alert alert-danger">
              <h5>Error Loading Profile</h5>
              <p>{userError?.message || "Failed to load profile data"}</p>
              <button
                className="btn btn-primary"
                onClick={() => refetchUser()}
              >
                Retry
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const renderProfileInfo = () => (
    <div className="simple-profile-content">
      {/* Simple Profile Header */}
      <div className="simple-profile-header">
        <div className="profile-basic-info">
          <div className="profile-avatar-simple">
            {adminData.avatar ? (
              <img src={adminData.avatar} alt="Profile" className="avatar-img" />
            ) : (
              <FaUser className="avatar-icon" />
            )}
          </div>
          <div className="profile-text">
            <h1 className="profile-name-simple">{adminData.name}</h1>
            <p className="profile-role-simple">{adminData.role}</p>
            <p className="profile-department-simple">{adminData.department}</p>
          </div>
        </div>
        <div className="profile-actions-simple">
          <button className="btn btn-outline-primary" onClick={handleEditProfile}>
            <FaEdit />
            Edit Profile
          </button>
        </div>
      </div>

      {/* Simple Information Cards */}
      <div className="simple-profile-grid">
        {/* Personal Information */}
        <div className="simple-info-card">
          <div className="card-header-simple">
            <h3 className="card-title-simple">Personal Information</h3>
          </div>
          <div className="card-body-simple">
            <div className="info-row-simple">
              <span className="info-label-simple">Email Address</span>
              <span className="info-value-simple">{adminData.email}</span>
            </div>
            <div className="info-row-simple">
              <span className="info-label-simple">Phone Number</span>
              <span className="info-value-simple">{adminData.phone}</span>
            </div>
            <div className="info-row-simple">
              <span className="info-label-simple">Location</span>
              <span className="info-value-simple">{adminData.location}</span>
            </div>
            <div className="info-row-simple">
              <span className="info-label-simple">Join Date</span>
              <span className="info-value-simple">{adminData.joinDate}</span>
            </div>
          </div>
        </div>

        {/* System Overview */}
        <div className="simple-info-card">
          <div className="card-header-simple">
            <h3 className="card-title-simple">System Overview</h3>
          </div>
          <div className="card-body-simple">
            <div className="stats-grid-simple">
              <div className="stat-item-simple">
                <span className="stat-label-simple">Total Vendors</span>
                <span className="stat-value-simple">{adminData.stats.totalVendors}</span>
              </div>
              <div className="stat-item-simple">
                <span className="stat-label-simple">Active Users</span>
                <span className="stat-value-simple">{adminData.stats.activeUsers}</span>
              </div>
              <div className="stat-item-simple">
                <span className="stat-label-simple">Pending Approvals</span>
                <span className="stat-value-simple">{adminData.stats.pendingApprovals}</span>
              </div>
              <div className="stat-item-simple">
                <span className="stat-label-simple">System Uptime</span>
                <span className="stat-value-simple">{adminData.stats.systemUptime}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="simple-info-card">
          <div className="card-header-simple">
            <h3 className="card-title-simple">Recent Activity</h3>
          </div>
          <div className="card-body-simple">
            <div className="activity-list-simple">
              <div className="activity-item-simple">
                <span className="activity-label-simple">Last Login</span>
                <span className="activity-value-simple">{adminData.lastLogin}</span>
              </div>
              <div className="activity-item-simple">
                <span className="activity-label-simple">Profile Updated</span>
                <span className="activity-value-simple">2 days ago</span>
              </div>
              <div className="activity-item-simple">
                <span className="activity-label-simple">Password Changed</span>
                <span className="activity-value-simple">1 week ago</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="simple-profile-page">
      <div className="simple-profile-container">
        <div className="simple-page-header">
          <h1 className="simple-page-title">Admin Profile</h1>
        </div>

        <div className="simple-profile-tabs">
          <button
            className={`simple-tab-button ${activeTab === 'profile' ? 'active' : ''}`}
            onClick={() => setActiveTab('profile')}
          >
            Profile Information
          </button>
          <button
            className={`simple-tab-button ${activeTab === 'settings' ? 'active' : ''}`}
            onClick={() => setActiveTab('settings')}
          >
            Account Settings
          </button>
        </div>

        <div className="simple-tab-content">
          {activeTab === 'profile' && renderProfileInfo()}
          {activeTab === 'settings' && <AccountSettings />}
        </div>
      </div>
    </div>
  );
};

export default Profile;
