using System;
using System.Security.Claims;
using System.Threading.Tasks;
using IdentityService.Domain.Enums;
using IdentityService.Domain.Interfaces;
using IdentityService.Domain.Services;
using IdentityService.Application.Common.Authorization;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;

namespace IdentityService.Infrastructure.Authorization;

public class SubscriptionTierHandler : AuthorizationHandler<SubscriptionTierRequirement>
{
    private readonly IUserRepository _userRepository;
    private readonly ILogger<SubscriptionTierHandler> _logger;

    public SubscriptionTierHandler(
        IUserRepository userRepository,
        ILogger<SubscriptionTierHandler> logger)
    {
        _userRepository = userRepository;
        _logger = logger;
    }

    protected override async Task HandleRequirementAsync(
        AuthorizationHandlerContext context,
        SubscriptionTierRequirement requirement)
    {
        if (!context.User.Identity.IsAuthenticated)
        {
            return;
        }

        var userIdClaim = context.User.FindFirst(ClaimTypes.NameIdentifier);
        if (userIdClaim == null || !Guid.TryParse(userIdClaim.Value, out var userId))
        {
            _logger.LogWarning("User ID claim not found or invalid");
            return;
        }

        try
        {
            var userTier = await _userRepository.GetUserSubscriptionTierAsync(userId);
            
            if ((int)userTier >= (int)requirement.MinimumTier)
            {
                context.Succeed(requirement);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking user subscription tier");
        }
    }
}

public class SubscriptionFeatureHandler : AuthorizationHandler<SubscriptionFeatureRequirement>
{
    private readonly IUserRepository _userRepository;
    private readonly ILogger<SubscriptionFeatureHandler> _logger;

    public SubscriptionFeatureHandler(
        IUserRepository userRepository,
        ILogger<SubscriptionFeatureHandler> logger)
    {
        _userRepository = userRepository;
        _logger = logger;
    }

    protected override async Task HandleRequirementAsync(
        AuthorizationHandlerContext context,
        SubscriptionFeatureRequirement requirement)
    {
        if (!context.User.Identity.IsAuthenticated)
        {
            return;
        }

        var userIdClaim = context.User.FindFirst(ClaimTypes.NameIdentifier);
        if (userIdClaim == null || !Guid.TryParse(userIdClaim.Value, out var userId))
        {
            _logger.LogWarning("User ID claim not found or invalid");
            return;
        }

        try
        {
            var hasFeature = await _userRepository.HasSubscriptionFeatureAsync(userId, requirement.FeatureName);
            
            if (hasFeature)
            {
                context.Succeed(requirement);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking user subscription feature");
        }
    }
}

public class PermissionAuthorizationHandler : AuthorizationHandler<PermissionRequirement>
{
    private readonly IPermissionService _permissionService;
    private readonly ILogger<PermissionAuthorizationHandler> _logger;

    public PermissionAuthorizationHandler(
        IPermissionService permissionService,
        ILogger<PermissionAuthorizationHandler> logger)
    {
        _permissionService = permissionService;
        _logger = logger;
    }

    protected override async Task HandleRequirementAsync(
        AuthorizationHandlerContext context,
        PermissionRequirement requirement)
    {
        if (!context.User.Identity.IsAuthenticated)
        {
            _logger.LogDebug("User is not authenticated");
            return;
        }

        // ✅ CRITICAL: SuperAdmin bypass - this is the fix for the issue
        if (PermissionHelper.IsSuperAdmin(context.User))
        {
            _logger.LogDebug("SuperAdmin user bypassing permission check for {Permission}", requirement.PermissionName);
            context.Succeed(requirement);
            return;
        }

        // ✅ Admin roles bypass permission checks
        if (PermissionHelper.IsAdmin(context.User))
        {
            _logger.LogDebug("Admin user bypassing permission check for {Permission}", requirement.PermissionName);
            context.Succeed(requirement);
            return;
        }

        var userIdClaim = context.User.FindFirst(ClaimTypes.NameIdentifier);
        if (userIdClaim == null || !Guid.TryParse(userIdClaim.Value, out var userId))
        {
            _logger.LogWarning("User ID claim not found or invalid");
            return;
        }

        try
        {
            var hasPermission = await _permissionService.HasPermissionAsync(userId, requirement.PermissionName);

            if (hasPermission)
            {
                _logger.LogDebug("User {UserId} has permission {Permission}", userId, requirement.PermissionName);
                context.Succeed(requirement);
            }
            else
            {
                _logger.LogDebug("User {UserId} does not have permission {Permission}", userId, requirement.PermissionName);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking user permission {Permission} for user {UserId}", requirement.PermissionName, userId);
        }
    }
}
