using Npgsql;

class Program
{
    static async Task Main(string[] args)
    {
        var connectionString = "Host=**************;Port=5432;Database=vms_inventorymanagementservice;Username=postgres;Password=************";
        
        try
        {
            Console.WriteLine("Testing database connection...");
            using var connection = new NpgsqlConnection(connectionString);
            await connection.OpenAsync();
            Console.WriteLine("✅ Database connection successful!");
            
            // Test if database exists
            var command = new NpgsqlCommand("SELECT 1", connection);
            var result = await command.ExecuteScalarAsync();
            Console.WriteLine($"✅ Database query successful! Result: {result}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Database connection failed: {ex.Message}");
        }
    }
}
