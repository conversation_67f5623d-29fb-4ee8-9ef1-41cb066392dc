{"ConnectionStrings": {"DefaultConnection": "Host=**************;Port=5432;Database=vms_inventorymanagementservice;Username=postgres;Password=************;Timeout=30;CommandTimeout=30"}, "Urls": "http://localhost:5008;https://localhost:7008", "RabbitMQ": {"Host": "localhost", "Port": 5672, "Username": "guest", "Password": "guest"}, "JWT": {"SecretKey": "YourSuperSecretKeyThatIsAtLeast32CharactersLong!", "Issuer": "VMS-InventoryManagementService", "Audience": "VMS-Users", "ExpiryInMinutes": 60}, "AllowedOrigins": ["http://localhost:3000", "http://localhost:3001", "http://localhost:3002", "http://localhost:4200", "https://localhost:3000", "https://localhost:3001", "https://localhost:3002", "https://localhost:4200", "http://localhost:5008", "https://localhost:7008"], "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/inventorymanagementservice-.txt", "rollingInterval": "Day", "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}"}}]}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*"}