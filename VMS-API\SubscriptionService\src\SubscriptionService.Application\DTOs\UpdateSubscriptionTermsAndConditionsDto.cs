using System.ComponentModel.DataAnnotations;

namespace SubscriptionService.Application.DTOs;

/// <summary>
/// DTO for updating Subscription Terms and Conditions
/// </summary>
public class UpdateSubscriptionTermsAndConditionsDto
{
    [Required(ErrorMessage = "Title is required")]
    [StringLength(200, ErrorMessage = "Title cannot exceed 200 characters")]
    public string Title { get; set; } = string.Empty;

    [Required(ErrorMessage = "Content is required")]
    [MinLength(10, ErrorMessage = "Content must be at least 10 characters")]
    public string Content { get; set; } = string.Empty;
}
