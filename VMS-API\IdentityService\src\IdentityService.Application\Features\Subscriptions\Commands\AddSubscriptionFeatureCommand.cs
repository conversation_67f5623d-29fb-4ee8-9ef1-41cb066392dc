using System;
using System.Threading;
using System.Threading.Tasks;
using IdentityService.Application.DTOs.Subscriptions;
using IdentityService.Application.Interfaces;
using IdentityService.Domain.Interfaces;
using MediatR;

namespace IdentityService.Application.Features.Subscriptions.Commands;

public class AddSubscriptionFeatureCommand : IRequest<UserSubscriptionResponse>
{
    public Guid SubscriptionId { get; set; }
    public required AddSubscriptionFeatureRequest Request { get; set; }
}

public class AddSubscriptionFeatureCommandHandler : IRequestHandler<AddSubscriptionFeatureCommand, UserSubscriptionResponse>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICurrentUserService _currentUserService;
    private readonly IAuditLogService _auditLogService;

    public AddSubscriptionFeatureCommandHandler(
        IUnitOfWork unitOfWork,
        ICurrentUserService currentUserService,
        IAuditLogService auditLogService)
    {
        _unitOfWork = unitOfWork;
        _currentUserService = currentUserService;
        _auditLogService = auditLogService;
    }

    public async Task<UserSubscriptionResponse> Handle(AddSubscriptionFeatureCommand command, CancellationToken cancellationToken)
    {
        var subscription = await _unitOfWork.UserSubscriptionRepository.GetBySubscriptionIdAsync(command.SubscriptionId);
        if (subscription == null)
            throw new InvalidOperationException($"Subscription with ID {command.SubscriptionId} not found");

        var request = command.Request;
        var updatedBy = _currentUserService.UserId.ToString() ?? "System";

        // Check if feature already exists
        if (subscription.Features.Any(f => f.FeatureName.Equals(request.FeatureName, StringComparison.OrdinalIgnoreCase)))
            throw new InvalidOperationException($"Feature '{request.FeatureName}' already exists for this subscription");

        // Add feature
        subscription.AddFeature(request.FeatureName, request.IsEnabled, request.UsageLimit, updatedBy);

        // Save changes
        await _unitOfWork.UserSubscriptionRepository.UpdateAsync(subscription);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        // Create audit log
        await _auditLogService.CreateAuditLogAsync(
            "AddFeature",
            "UserSubscription",
            subscription.Id.ToString(),
            string.Empty,
            System.Text.Json.JsonSerializer.Serialize(new {
                FeatureName = request.FeatureName,
                IsEnabled = request.IsEnabled,
                UsageLimit = request.UsageLimit
            }),
            "FeatureName,IsEnabled,UsageLimit",
            _currentUserService.UserId ?? Guid.Empty);

        // Create response
        var response = new UserSubscriptionResponse
        {
            Id = subscription.Id,
            SubscriptionId = subscription.SubscriptionId,
            PlanName = subscription.PlanName,
            Tier = subscription.Tier,
            Status = subscription.Status,
            StartDate = subscription.StartDate,
            EndDate = subscription.EndDate,
            TrialEndDate = subscription.TrialEndDate,
            CreatedAt = subscription.CreatedAt,
            CreatedBy = subscription.CreatedBy,
            UpdatedAt = subscription.UpdatedAt,
            UpdatedBy = subscription.UpdatedBy,
            Features = subscription.Features.Select(f => new SubscriptionFeatureResponse
            {
                Id = f.Id,
                FeatureName = f.FeatureName,
                IsEnabled = f.IsEnabled,
                UsageLimit = f.UsageLimit
            }).ToList()
        };

        return response;
    }
}
