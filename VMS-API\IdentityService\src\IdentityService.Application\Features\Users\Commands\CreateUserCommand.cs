using System;
using System.Threading;
using System.Threading.Tasks;
using IdentityService.Application.Common;
using IdentityService.Application.DTOs.Users;
using IdentityService.Application.Interfaces;
using IdentityService.Domain.Entities;
using IdentityService.Domain.Interfaces;
using MediatR;

namespace IdentityService.Application.Features.Users.Commands;

public class CreateUserCommand : BaseRequest<UserResponse>
{
    public CreateUserRequest Request { get; set; }
}

public class CreateUserCommandHandler : IRequestHandler<CreateUserCommand, UserResponse>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IPasswordHasher _passwordHasher;
    private readonly ICurrentUserService _currentUserService;

    public CreateUserCommandHandler(
        IUnitOfWork unitOfWork,
        IPasswordHasher passwordHasher,
        ICurrentUserService currentUserService)
    {
        _unitOfWork = unitOfWork;
        _passwordHasher = passwordHasher;
        _currentUserService = currentUserService;
    }

    public async Task<UserResponse> Handle(CreateUserCommand command, CancellationToken cancellationToken)
    {
        var request = command.Request;

        // Check if email already exists
        var emailExists = await _unitOfWork.UserRepository.ExistsByEmailAsync(request.Email);
        if (emailExists)
            throw new InvalidOperationException($"Email {request.Email} is already in use");

        // Check if phone number already exists
        var phoneExists = await _unitOfWork.UserRepository.ExistsByPhoneNumberAsync(request.PhoneNumber);
        if (phoneExists)
            throw new InvalidOperationException($"Phone number {request.PhoneNumber} is already in use");

        // Hash password
        var passwordHash = _passwordHasher.HashPassword(request.Password);

        // Create user
        var createdBy = _currentUserService.UserId.ToString() ?? "System";
        var user = User.Create(request.Email, request.PhoneNumber, passwordHash, createdBy);

        // Assign roles if provided
        if (request.RoleIds != null && request.RoleIds.Count > 0)
        {
            foreach (var roleId in request.RoleIds)
            {
                var role = await _unitOfWork.RoleRepository.GetByIdAsync(roleId);
                if (role != null)
                {
                    user.AddRole(role, createdBy);
                }
            }
        }

        // Save user
        await _unitOfWork.UserRepository.AddAsync(user);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        // Get user roles for response
        var roles = await _unitOfWork.UserRepository.GetUserRolesAsync(user.Id);

        // Create response
        var response = new UserResponse
        {
            Id = user.Id,
            Email = user.Email,
            PhoneNumber = user.PhoneNumber,
            EmailVerified = user.EmailVerified,
            PhoneNumberVerified = user.PhoneNumberVerified,
            IsActive = true,
            LastLoginAt = user.LastLoginAt,
            CreatedAt = user.CreatedAt,
            CreatedBy = user.CreatedBy,
            UpdatedAt = user.UpdatedAt,
            UpdatedBy = user.UpdatedBy,
            Roles = new List<UserRoleResponse>()
        };

        return response;
    }
}
