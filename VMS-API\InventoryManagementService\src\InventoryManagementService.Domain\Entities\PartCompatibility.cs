using InventoryManagementService.Domain.Common;

namespace InventoryManagementService.Domain.Entities;

public class PartCompatibility : BaseEntity
{
    public Guid PartId { get; private set; }
    public string Make { get; private set; } = string.Empty;
    public string Model { get; private set; } = string.Empty;
    public int YearFrom { get; private set; }
    public int YearTo { get; private set; }
    public string? EngineType { get; private set; }
    public string? Variant { get; private set; }

    // Navigation properties
    public Part Part { get; private set; } = null!;

    private PartCompatibility() { }

    public static PartCompatibility Create(
        Guid partId,
        string make,
        string model,
        int yearFrom,
        int yearTo,
        string createdBy,
        string? engineType = null,
        string? variant = null)
    {
        return new PartCompatibility
        {
            PartId = partId,
            Make = make.Trim(),
            Model = model.Trim(),
            YearFrom = yearFrom,
            YearTo = yearTo,
            EngineType = engineType?.Trim(),
            Variant = variant?.Trim(),
            CreatedBy = createdBy
        };
    }

    public void Update(
        string make,
        string model,
        int yearFrom,
        int yearTo,
        string updatedBy,
        string? engineType = null,
        string? variant = null)
    {
        Make = make.Trim();
        Model = model.Trim();
        YearFrom = yearFrom;
        YearTo = yearTo;
        EngineType = engineType?.Trim();
        Variant = variant?.Trim();
        
        SetUpdatedBy(updatedBy);
    }
}
