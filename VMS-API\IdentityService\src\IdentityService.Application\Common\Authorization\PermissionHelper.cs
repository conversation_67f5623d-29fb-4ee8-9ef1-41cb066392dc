using System;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using IdentityService.Domain.Interfaces;
using IdentityService.Domain.Services;

namespace IdentityService.Application.Common.Authorization
{
    /// <summary>
    /// Helper class for checking user permissions in IdentityService
    /// </summary>
    public static class PermissionHelper
    {
        /// <summary>
        /// Admin roles that bypass permission checks
        /// </summary>
        private static readonly string[] AdminRoles = { "SuperAdmin", "VendorAdmin", "BranchAdmin" };

        /// <summary>
        /// Checks if a user has access to a specific permission using user repository
        /// </summary>
        /// <param name="user">The user claims principal</param>
        /// <param name="permissionKey">The permission key to check</param>
        /// <param name="userRepository">The user repository</param>
        /// <returns>True if user has access, false otherwise</returns>
        public static async Task<bool> HasAccess(ClaimsPrincipal user, string permissionKey, IUserRepository userRepository)
        {
            if (user == null || !user.Identity.IsAuthenticated)
                return false;

            // Check if user is admin - admins bypass permission checks
            if (IsAdmin(user))
                return true;

            // Get user ID
            var userIdClaim = user.FindFirst(ClaimTypes.NameIdentifier);
            if (userIdClaim == null || !Guid.TryParse(userIdClaim.Value, out var userId))
                return false;

            // Get user entity and check permission
            var userEntity = await userRepository.GetByIdAsync(userId);
            if (userEntity == null)
                return false;

            return await userRepository.HasPermissionAsync(userEntity, permissionKey);
        }

        /// <summary>
        /// Enhanced permission check using the new permission service
        /// </summary>
        /// <param name="user">The user claims principal</param>
        /// <param name="permissionKey">The permission key to check</param>
        /// <param name="permissionService">The permission service</param>
        /// <returns>True if user has access, false otherwise</returns>
        public static async Task<bool> HasAccessEnhanced(ClaimsPrincipal user, string permissionKey, IPermissionService permissionService)
        {
            if (user == null || user.Identity == null || !user.Identity.IsAuthenticated)
                return false;

            // Check if user is admin - admins bypass permission checks
            if (IsAdmin(user))
                return true;

            // Get user ID
            var userIdClaim = user.FindFirst(ClaimTypes.NameIdentifier);
            if (userIdClaim == null || !Guid.TryParse(userIdClaim.Value, out var userId))
                return false;

            // Use enhanced permission service that considers both role and user-specific permissions
            return await permissionService.HasPermissionAsync(userId, permissionKey);
        }

        /// <summary>
        /// Checks if the user is in an admin role
        /// </summary>
        /// <param name="user">The user claims principal</param>
        /// <returns>True if user is admin, false otherwise</returns>
        public static bool IsAdmin(ClaimsPrincipal? user)
        {
            if (user == null || user.Identity == null || !user.Identity.IsAuthenticated)
                return false;

            var userRoles = user.Claims
                .Where(c => c.Type == ClaimTypes.Role)
                .Select(c => c.Value)
                .ToList();

            return userRoles.Any(role => AdminRoles.Contains(role, StringComparer.OrdinalIgnoreCase));
        }

        /// <summary>
        /// Checks if the user is a Super Admin
        /// </summary>
        /// <param name="user">The user claims principal</param>
        /// <returns>True if user is Super Admin, false otherwise</returns>
        public static bool IsSuperAdmin(ClaimsPrincipal? user)
        {
            if (user == null || user.Identity == null || !user.Identity.IsAuthenticated)
                return false;

            return user.IsInRole("SuperAdmin");
        }

        /// <summary>
        /// Checks if the user is a Vendor Admin
        /// </summary>
        /// <param name="user">The user claims principal</param>
        /// <returns>True if user is Vendor Admin, false otherwise</returns>
        public static bool IsVendorAdmin(ClaimsPrincipal? user)
        {
            if (user == null || user.Identity == null || !user.Identity.IsAuthenticated)
                return false;

            return user.IsInRole("VendorAdmin");
        }

        /// <summary>
        /// Checks if the user is a Branch Admin
        /// </summary>
        /// <param name="user">The user claims principal</param>
        /// <returns>True if user is Branch Admin, false otherwise</returns>
        public static bool IsBranchAdmin(ClaimsPrincipal? user)
        {
            if (user == null || user.Identity == null || !user.Identity.IsAuthenticated)
                return false;

            return user.IsInRole("BranchAdmin");
        }

        /// <summary>
        /// Validates permission access and throws UnauthorizedAccessException if denied
        /// </summary>
        /// <param name="user">The user claims principal</param>
        /// <param name="permissionKey">The permission key to check</param>
        /// <param name="userRepository">The user repository</param>
        /// <exception cref="UnauthorizedAccessException">Thrown when user lacks permission</exception>
        public static async Task ValidateAccess(ClaimsPrincipal user, string permissionKey, IUserRepository userRepository)
        {
            if (!await HasAccess(user, permissionKey, userRepository))
            {
                throw new UnauthorizedAccessException($"You do not have permission to perform this action. Required permission: {permissionKey}");
            }
        }
    }
}
