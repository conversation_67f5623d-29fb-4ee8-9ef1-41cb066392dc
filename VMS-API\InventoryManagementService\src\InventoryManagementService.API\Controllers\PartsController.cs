using InventoryManagementService.Application.DTOs;
using InventoryManagementService.Application.Features.Parts.Commands;
using InventoryManagementService.Application.Features.Parts.Queries;
using InventoryManagementService.Domain.Enums;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace InventoryManagementService.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class PartsController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<PartsController> _logger;

    public PartsController(IMediator mediator, ILogger<PartsController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Get a part by ID
    /// </summary>
    [HttpGet("{id}")]
    [Authorize(Policy = "Parts.View")]
    public async Task<ActionResult<PartDto>> GetPart(Guid id)
    {
        try
        {
            var query = new GetPartByIdQuery { PartId = id };
            var part = await _mediator.Send(query);

            if (part == null)
            {
                return NotFound($"Part with ID {id} not found");
            }

            return Ok(part);
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access attempt to get part {PartId}", id);
            return Forbid(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving part {PartId}", id);
            return StatusCode(500, new { message = "An error occurred while retrieving the part" });
        }
    }

    /// <summary>
    /// Search parts with various criteria
    /// </summary>
    [HttpGet("search")]
    [Authorize(Policy = "Parts.View")]
    public async Task<ActionResult<List<PartDto>>> SearchParts(
        [FromQuery] string? searchTerm,
        [FromQuery] PartCategory? category,
        [FromQuery] string? manufacturer,
        [FromQuery] string? make,
        [FromQuery] string? model,
        [FromQuery] int? year,
        [FromQuery] bool includeInactive = false)
    {
        try
        {
            var query = new SearchPartsQuery
            {
                SearchTerm = searchTerm,
                Category = category,
                Manufacturer = manufacturer,
                Make = make,
                Model = model,
                Year = year,
                IncludeInactive = includeInactive
            };

            var parts = await _mediator.Send(query);
            return Ok(parts);
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access attempt to search parts");
            return Forbid(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching parts");
            return StatusCode(500, new { message = "An error occurred while searching parts" });
        }
    }

    /// <summary>
    /// Create a new part
    /// </summary>
    [HttpPost]
    [Authorize(Policy = "Parts.Create")]
    public async Task<ActionResult<PartDto>> CreatePart([FromBody] CreatePartDto createPartDto)
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "System";
            
            var command = new CreatePartCommand
            {
                Part = createPartDto,
                CreatedBy = userId
            };

            var part = await _mediator.Send(command);
            return CreatedAtAction(nameof(GetPart), new { id = part.Id }, part);
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access attempt to create part");
            return Forbid(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation during part creation");
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating part");
            return StatusCode(500, new { message = "An error occurred while creating the part" });
        }
    }

    /// <summary>
    /// Update an existing part (excluding SKU)
    /// </summary>
    [HttpPut("{id}")]
    [Authorize(Policy = "Parts.Update")]
    public async Task<ActionResult<PartDto>> UpdatePart(Guid id, [FromBody] UpdatePartDto updatePartDto)
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "System";
            
            var command = new UpdatePartCommand
            {
                PartId = id,
                Part = updatePartDto,
                UpdatedBy = userId
            };

            var part = await _mediator.Send(command);
            return Ok(part);
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access attempt to update part {PartId}", id);
            return Forbid(ex.Message);
        }
        catch (KeyNotFoundException ex)
        {
            _logger.LogWarning(ex, "Part not found for update: {PartId}", id);
            return NotFound(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating part {PartId}", id);
            return StatusCode(500, new { message = "An error occurred while updating the part" });
        }
    }

    /// <summary>
    /// Activate a part
    /// </summary>
    [HttpPatch("{id}/activate")]
    [Authorize(Policy = "Parts.Update")]
    public async Task<ActionResult> ActivatePart(Guid id)
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "System";
            
            var command = new ActivatePartCommand
            {
                PartId = id,
                UpdatedBy = userId
            };

            await _mediator.Send(command);
            return NoContent();
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access attempt to activate part {PartId}", id);
            return Forbid(ex.Message);
        }
        catch (KeyNotFoundException ex)
        {
            _logger.LogWarning(ex, "Part not found for activation: {PartId}", id);
            return NotFound(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error activating part {PartId}", id);
            return StatusCode(500, new { message = "An error occurred while activating the part" });
        }
    }

    /// <summary>
    /// Deactivate a part
    /// </summary>
    [HttpPatch("{id}/deactivate")]
    [Authorize(Policy = "Parts.Update")]
    public async Task<ActionResult> DeactivatePart(Guid id)
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "System";
            
            var command = new DeactivatePartCommand
            {
                PartId = id,
                UpdatedBy = userId
            };

            await _mediator.Send(command);
            return NoContent();
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access attempt to deactivate part {PartId}", id);
            return Forbid(ex.Message);
        }
        catch (KeyNotFoundException ex)
        {
            _logger.LogWarning(ex, "Part not found for deactivation: {PartId}", id);
            return NotFound(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deactivating part {PartId}", id);
            return StatusCode(500, new { message = "An error occurred while deactivating the part" });
        }
    }

    /// <summary>
    /// Add vehicle compatibility to a part
    /// </summary>
    [HttpPost("{id}/compatibility")]
    [Authorize(Policy = "Parts.Update")]
    public async Task<ActionResult> AddPartCompatibility(Guid id, [FromBody] AddPartCompatibilityDto compatibilityDto)
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "System";
            
            var command = new AddPartCompatibilityCommand
            {
                PartId = id,
                Compatibility = compatibilityDto,
                CreatedBy = userId
            };

            await _mediator.Send(command);
            return NoContent();
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access attempt to add compatibility to part {PartId}", id);
            return Forbid(ex.Message);
        }
        catch (KeyNotFoundException ex)
        {
            _logger.LogWarning(ex, "Part not found for adding compatibility: {PartId}", id);
            return NotFound(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding compatibility to part {PartId}", id);
            return StatusCode(500, new { message = "An error occurred while adding part compatibility" });
        }
    }

    /// <summary>
    /// Remove vehicle compatibility from a part
    /// </summary>
    [HttpDelete("{id}/compatibility/{compatibilityId}")]
    [Authorize(Policy = "Parts.Update")]
    public async Task<ActionResult> RemovePartCompatibility(Guid id, Guid compatibilityId)
    {
        try
        {
            var command = new RemovePartCompatibilityCommand
            {
                PartId = id,
                CompatibilityId = compatibilityId
            };

            await _mediator.Send(command);
            return NoContent();
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access attempt to remove compatibility from part {PartId}", id);
            return Forbid(ex.Message);
        }
        catch (KeyNotFoundException ex)
        {
            _logger.LogWarning(ex, "Part or compatibility not found: {PartId}, {CompatibilityId}", id, compatibilityId);
            return NotFound(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing compatibility from part {PartId}", id);
            return StatusCode(500, new { message = "An error occurred while removing part compatibility" });
        }
    }
}
