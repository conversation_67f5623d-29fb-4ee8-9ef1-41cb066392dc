# Script to fix migration state and run the application
Write-Host "Fixing Identity Service Migration State..." -ForegroundColor Cyan

# Set PostgreSQL password
$env:PGPASSWORD = "admin"

# Run the migration fix script
$fixScriptPath = Join-Path $PSScriptRoot "fix-migration-state.sql"

if (Test-Path $fixScriptPath) {
    Write-Host "Executing migration fix script..." -ForegroundColor Yellow
    psql -h localhost -U postgres -d VMS_Identity -f $fixScriptPath
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Migration fix completed successfully!" -ForegroundColor Green
        
        # Now try to run the application
        Write-Host "Starting Identity Service..." -ForegroundColor Yellow
        Set-Location "src\IdentityService.API"
        
        # Start the application
        Write-Host "Application starting - watch for seeding output..." -ForegroundColor Cyan
        Write-Host "Press Ctrl+C to stop when seeding is complete" -ForegroundColor Yellow
        dotnet run
        
    } else {
        Write-Host "Migration fix failed with exit code: $LASTEXITCODE" -ForegroundColor Red
    }
} else {
    Write-Host "Fix script not found: $fixScriptPath" -ForegroundColor Red
}
