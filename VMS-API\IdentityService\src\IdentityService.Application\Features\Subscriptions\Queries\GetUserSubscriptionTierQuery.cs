using System;
using System.Threading;
using System.Threading.Tasks;
using IdentityService.Domain.Enums;
using IdentityService.Domain.Interfaces;
using MediatR;

namespace IdentityService.Application.Features.Subscriptions.Queries;

public class GetUserSubscriptionTierQuery : IRequest<SubscriptionTier>
{
    public Guid UserId { get; set; }
}

public class GetUserSubscriptionTierQueryHandler : IRequestHandler<GetUserSubscriptionTierQuery, SubscriptionTier>
{
    private readonly IUnitOfWork _unitOfWork;

    public GetUserSubscriptionTierQueryHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
    }

    public async Task<SubscriptionTier> Handle(GetUserSubscriptionTierQuery query, CancellationToken cancellationToken)
    {
        return await _unitOfWork.UserRepository.GetUserSubscriptionTierAsync(query.UserId);
    }
}
