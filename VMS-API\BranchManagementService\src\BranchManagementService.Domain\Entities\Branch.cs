using BranchManagementService.Domain.Common;
using BranchManagementService.Domain.Enums;
using System;
using System.Collections.Generic;

namespace BranchManagementService.Domain.Entities
{
    public class Branch : BaseEntity
    {
        public Guid VendorId { get; private set; }
        public string Name { get; private set; }
        public string Email { get; private set; }
        public string ContactPerson { get; private set; }
        public string MobileNumber { get; private set; }
        public string Address { get; private set; }
        public string City { get; private set; }
        public string State { get; private set; }
        public string ZipCode { get; private set; }
        public VehicleTypeSupport VehicleTypeSupport { get; private set; }
        public bool IsActive { get; private set; }

        private Branch()
        {
        }

        public static Branch Create(
            Guid vendorId,
            string name,
            string email,
            string contactPerson,
            string mobileNumber,
            string address,
            string city,
            string state,
            string zipCode,
            VehicleTypeSupport vehicleTypeSupport,
            string createdBy)
        {
            return new Branch
            {
                VendorId = vendorId,
                Name = name,
                Email = email,
                ContactPerson = contactPerson,
                MobileNumber = mobileNumber,
                Address = address,
                City = city,
                State = state,
                ZipCode = zipCode,
                VehicleTypeSupport = vehicleTypeSupport,
                IsActive = true,
                CreatedBy = createdBy
            };
        }

        public void Update(
            string name,
            string email,
            string contactPerson,
            string mobileNumber,
            string address,
            string city,
            string state,
            string zipCode,
            VehicleTypeSupport vehicleTypeSupport,
            string updatedBy)
        {
            Name = name;
            Email = email;
            ContactPerson = contactPerson;
            MobileNumber = mobileNumber;
            Address = address;
            City = city;
            State = state;
            ZipCode = zipCode;
            VehicleTypeSupport = vehicleTypeSupport;
            SetUpdatedBy(updatedBy);
        }

        public void Activate(string updatedBy)
        {
            IsActive = true;
            SetUpdatedBy(updatedBy);
        }

        public void Deactivate(string updatedBy)
        {
            IsActive = false;
            SetUpdatedBy(updatedBy);
        }

        private void SetUpdatedBy(string updatedBy)
        {
            UpdatedBy = updatedBy;
            UpdatedAt = DateTime.UtcNow;
        }
    }
}
