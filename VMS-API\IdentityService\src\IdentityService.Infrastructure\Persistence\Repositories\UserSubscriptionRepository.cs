using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using IdentityService.Domain.Entities;
using IdentityService.Domain.Enums;
using IdentityService.Domain.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace IdentityService.Infrastructure.Persistence.Repositories;

public class UserSubscriptionRepository : GenericRepository<UserSubscription>, IUserSubscriptionRepository
{
    public UserSubscriptionRepository(ApplicationDbContext context) : base(context)
    {
    }

    public async Task<UserSubscription?> GetBySubscriptionIdAsync(Guid subscriptionId)
    {
        return await _dbSet
            .Include(s => s.User)
            .Include(s => s.Features)
            .FirstOrDefaultAsync(s => s.SubscriptionId == subscriptionId);
    }

    public async Task<List<UserSubscription>> GetByUserIdAsync(Guid userId)
    {
        return await _dbSet
            .Include(s => s.Features)
            .Where(s => s.UserId == userId)
            .ToListAsync();
    }

    public async Task<List<UserSubscription>> GetActiveSubscriptionsAsync()
    {
        var now = DateTime.UtcNow;
        return await _dbSet
            .Include(s => s.User)
            .Include(s => s.Features)
            .Where(s => (s.Status == SubscriptionStatus.Active && s.EndDate >= now) ||
                        (s.Status == SubscriptionStatus.Trial && s.TrialEndDate >= now))
            .ToListAsync();
    }

    public async Task<List<UserSubscription>> GetSubscriptionsByStatusAsync(SubscriptionStatus status)
    {
        return await _dbSet
            .Include(s => s.User)
            .Include(s => s.Features)
            .Where(s => s.Status == status)
            .ToListAsync();
    }

    public async Task<List<UserSubscription>> GetSubscriptionsByTierAsync(SubscriptionTier tier)
    {
        return await _dbSet
            .Include(s => s.User)
            .Include(s => s.Features)
            .Where(s => s.Tier == tier)
            .ToListAsync();
    }

    public async Task<bool> AddFeatureAsync(Guid subscriptionId, string featureName, bool isEnabled, int? usageLimit)
    {
        var subscription = await _dbSet
            .Include(s => s.Features)
            .FirstOrDefaultAsync(s => s.SubscriptionId == subscriptionId);
            
        if (subscription == null)
            return false;
            
        subscription.AddFeature(featureName, isEnabled, usageLimit, "System");
        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<bool> UpdateFeatureAsync(Guid subscriptionId, string featureName, bool isEnabled, int? usageLimit)
    {
        var subscription = await _dbSet
            .Include(s => s.Features)
            .FirstOrDefaultAsync(s => s.SubscriptionId == subscriptionId);
            
        if (subscription == null)
            return false;
            
        var feature = subscription.Features.FirstOrDefault(f => f.FeatureName == featureName);
        if (feature == null)
            return false;
            
        feature.UpdateAccess(isEnabled, usageLimit, "System");
        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<bool> RemoveFeatureAsync(Guid subscriptionId, string featureName)
    {
        var subscription = await _dbSet
            .Include(s => s.Features)
            .FirstOrDefaultAsync(s => s.SubscriptionId == subscriptionId);
            
        if (subscription == null)
            return false;
            
        subscription.RemoveFeature(featureName);
        await _context.SaveChangesAsync();
        return true;
    }
}
