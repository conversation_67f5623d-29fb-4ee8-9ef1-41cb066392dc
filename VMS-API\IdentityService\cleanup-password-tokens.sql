-- <PERSON><PERSON>t to clean up password reset tokens
-- Specifically for user: 382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e

-- 1. Check current state of password reset tokens for this user
SELECT 
    'BEFORE CLEANUP' as "Phase",
    prt."Id",
    prt."Token",
    prt."CreatedAt",
    prt."ExpiryTime",
    prt."IsUsed",
    prt."IsDeleted",
    prt."UpdatedAt",
    CASE 
        WHEN prt."IsUsed" = true THEN 'USED'
        WHEN prt."ExpiryTime" <= NOW() THEN 'EXPIRED'
        WHEN prt."IsDeleted" = true THEN 'DELETED'
        ELSE 'ACTIVE'
    END as "Status"
FROM "PasswordResetTokens" prt
WHERE prt."UserId" = '382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e'
ORDER BY prt."CreatedAt" DESC;

-- 2. Count tokens by status
SELECT 
    'BEFORE CLEANUP' as "Phase",
    COUNT(*) as "TotalTokens",
    COUNT(CASE WHEN "IsUsed" = false AND "ExpiryTime" > NOW() AND "IsDeleted" = false THEN 1 END) as "ActiveTokens",
    COUNT(CASE WHEN "IsUsed" = true THEN 1 END) as "UsedTokens",
    COUNT(CASE WHEN "ExpiryTime" <= NOW() THEN 1 END) as "ExpiredTokens",
    COUNT(CASE WHEN "IsDeleted" = true THEN 1 END) as "DeletedTokens"
FROM "PasswordResetTokens"
WHERE "UserId" = '382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e';

-- 3. Clean up expired, used, and deleted tokens
DELETE FROM "PasswordResetTokens" 
WHERE "UserId" = '382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e' 
AND ("IsUsed" = true OR "ExpiryTime" <= NOW() OR "IsDeleted" = true);

-- 4. Check state after cleanup
SELECT 
    'AFTER CLEANUP' as "Phase",
    prt."Id",
    prt."Token",
    prt."CreatedAt",
    prt."ExpiryTime",
    prt."IsUsed",
    prt."IsDeleted",
    prt."UpdatedAt",
    CASE 
        WHEN prt."IsUsed" = true THEN 'USED'
        WHEN prt."ExpiryTime" <= NOW() THEN 'EXPIRED'
        WHEN prt."IsDeleted" = true THEN 'DELETED'
        ELSE 'ACTIVE'
    END as "Status"
FROM "PasswordResetTokens" prt
WHERE prt."UserId" = '382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e'
ORDER BY prt."CreatedAt" DESC;

-- 5. Count tokens after cleanup
SELECT 
    'AFTER CLEANUP' as "Phase",
    COUNT(*) as "TotalTokens",
    COUNT(CASE WHEN "IsUsed" = false AND "ExpiryTime" > NOW() AND "IsDeleted" = false THEN 1 END) as "ActiveTokens",
    COUNT(CASE WHEN "IsUsed" = true THEN 1 END) as "UsedTokens",
    COUNT(CASE WHEN "ExpiryTime" <= NOW() THEN 1 END) as "ExpiredTokens",
    COUNT(CASE WHEN "IsDeleted" = true THEN 1 END) as "DeletedTokens"
FROM "PasswordResetTokens"
WHERE "UserId" = '382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e';

-- 6. If still having issues, uncomment this to remove ALL tokens for this user (NUCLEAR OPTION)
-- DELETE FROM "PasswordResetTokens" 
-- WHERE "UserId" = '382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e';

-- 7. Verify user exists and check their details
SELECT 
    u."Id",
    u."Email",
    u."CreatedAt",
    u."UpdatedAt",
    u."LastLoginAt",
    u."IsDeleted"
FROM "Users" u
WHERE u."Id" = '382426b5-cfe0-4ecb-b7b6-4a3c6abaeb8e';
