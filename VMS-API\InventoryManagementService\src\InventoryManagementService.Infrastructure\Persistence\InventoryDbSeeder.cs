using InventoryManagementService.Domain.Entities;
using InventoryManagementService.Domain.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace InventoryManagementService.Infrastructure.Persistence;

public class InventoryDbSeeder
{
    private readonly InventoryDbContext _context;
    private readonly ILogger<InventoryDbSeeder> _logger;

    public InventoryDbSeeder(InventoryDbContext context, ILogger<InventoryDbSeeder> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task SeedAsync()
    {
        try
        {
            _logger.LogInformation("Starting inventory database seeding...");

            // Always clear existing data for fresh seeding
            await ClearExistingDataAsync();

            _logger.LogInformation("Database cleared. Starting fresh seeding process...");
            await SeedPartsAsync();
            await SeedSampleInventoryAsync();

            await _context.SaveChangesAsync();
            _logger.LogInformation("Inventory database seeding completed successfully.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An error occurred while seeding the inventory database.");
            throw;
        }
    }

    private async Task ClearExistingDataAsync()
    {
        _logger.LogInformation("Clearing existing data...");

        // Clear in proper order to avoid foreign key constraint violations
        _context.InventoryAdjustmentItems.RemoveRange(_context.InventoryAdjustmentItems);
        _context.InventoryAdjustments.RemoveRange(_context.InventoryAdjustments);
        _context.StockTransferItems.RemoveRange(_context.StockTransferItems);
        _context.StockTransfers.RemoveRange(_context.StockTransfers);
        _context.InventoryTransactions.RemoveRange(_context.InventoryTransactions);
        _context.InventoryItems.RemoveRange(_context.InventoryItems);
        _context.PartCompatibilities.RemoveRange(_context.PartCompatibilities);
        _context.Parts.RemoveRange(_context.Parts);

        await _context.SaveChangesAsync();
        _logger.LogInformation("Existing data cleared successfully.");
    }

    private async Task SeedPartsAsync()
    {
        _logger.LogInformation("Seeding parts...");

        var parts = new List<Part>
        {
            // Engine Parts
            Part.Create("ENG-001", "Engine Oil Filter", "High-quality engine oil filter for optimal performance", "Bosch", 15.99m, 29.99m, PartCategory.Engine, "System", "OF-123", "1234567890123", 12, null, "Compatible with most vehicles"),
            Part.Create("ENG-002", "Air Filter", "Premium air filter for clean air intake", "K&N", 25.50m, 45.99m, PartCategory.Engine, "System", "AF-456", "1234567890124", 6, null, "High-flow design"),
            Part.Create("ENG-003", "Spark Plugs Set", "Iridium spark plugs for better ignition", "NGK", 35.00m, 65.99m, PartCategory.Engine, "System", "SP-789", "1234567890125", 24, null, "Set of 4 plugs"),
            
            // Brake Parts
            Part.Create("BRK-001", "Brake Pads Front", "Ceramic brake pads for front wheels", "Brembo", 45.00m, 89.99m, PartCategory.Brakes, "System", "BP-001", "1234567890126", 12, null, "Low dust formula"),
            Part.Create("BRK-002", "Brake Pads Rear", "Ceramic brake pads for rear wheels", "Brembo", 40.00m, 79.99m, PartCategory.Brakes, "System", "BP-002", "1234567890127", 12, null, "Low dust formula"),
            Part.Create("BRK-003", "Brake Rotors Front", "Ventilated brake rotors for front wheels", "ACDelco", 75.00m, 149.99m, PartCategory.Brakes, "System", "BR-001", "1234567890128", 24, null, "Precision machined"),
            
            // Suspension Parts
            Part.Create("SUS-001", "Shock Absorber Front", "Gas-filled shock absorber for front suspension", "Monroe", 65.00m, 129.99m, PartCategory.Suspension, "System", "SA-001", "1234567890129", 12, null, "Smooth ride quality"),
            Part.Create("SUS-002", "Shock Absorber Rear", "Gas-filled shock absorber for rear suspension", "Monroe", 60.00m, 119.99m, PartCategory.Suspension, "System", "SA-002", "1234567890130", 12, null, "Smooth ride quality"),
            
            // Electrical Parts
            Part.Create("ELE-001", "Car Battery", "Maintenance-free car battery", "Optima", 120.00m, 199.99m, PartCategory.Electrical, "System", "BAT-001", "1234567890131", 36, null, "Cold cranking amps: 800"),
            Part.Create("ELE-002", "Alternator", "High-output alternator", "Denso", 180.00m, 299.99m, PartCategory.Electrical, "System", "ALT-001", "1234567890132", 24, null, "120 amp output"),
            
            // Filters
            Part.Create("FIL-001", "Fuel Filter", "Inline fuel filter", "Wix", 12.00m, 24.99m, PartCategory.Filters, "System", "FF-001", "1234567890133", 12, null, "Removes contaminants"),
            Part.Create("FIL-002", "Cabin Air Filter", "HEPA cabin air filter", "Mann", 18.00m, 34.99m, PartCategory.Filters, "System", "CAF-001", "1234567890134", 12, null, "Allergen protection"),
            
            // Fluids
            Part.Create("FLD-001", "Engine Oil 5W-30", "Synthetic engine oil", "Mobil 1", 8.50m, 16.99m, PartCategory.Fluids, "System", "EO-530", "1234567890135", null, null, "5 quart bottle"),
            Part.Create("FLD-002", "Brake Fluid DOT 4", "High-performance brake fluid", "Valvoline", 6.00m, 12.99m, PartCategory.Fluids, "System", "BF-DOT4", "1234567890136", null, null, "32 oz bottle"),
            
            // Belts and Hoses
            Part.Create("BLT-001", "Serpentine Belt", "Multi-ribbed serpentine belt", "Gates", 22.00m, 42.99m, PartCategory.Belts, "System", "SB-001", "1234567890137", 24, null, "EPDM construction"),
            Part.Create("HSE-001", "Radiator Hose Upper", "Molded radiator hose", "Dayco", 28.00m, 54.99m, PartCategory.Hoses, "System", "RH-UP", "1234567890138", 24, null, "Heat resistant"),
            
            // Tires
            Part.Create("TIR-001", "All-Season Tire 225/60R16", "All-season passenger tire", "Michelin", 95.00m, 179.99m, PartCategory.Tires, "System", "AST-225", "1234567890139", 60, null, "80,000 mile warranty"),
            Part.Create("TIR-002", "Winter Tire 225/60R16", "Winter performance tire", "Bridgestone", 110.00m, 199.99m, PartCategory.Tires, "System", "WT-225", "1234567890140", 60, null, "Severe snow rated"),
            
            // Lights
            Part.Create("LGT-001", "Headlight Bulb H7", "Halogen headlight bulb", "Philips", 12.00m, 24.99m, PartCategory.Lights, "System", "HB-H7", "1234567890141", 12, null, "Standard replacement"),
            Part.Create("LGT-002", "LED Headlight H7", "LED headlight conversion", "Sylvania", 45.00m, 89.99m, PartCategory.Lights, "System", "LED-H7", "1234567890142", 36, null, "6000K color temperature")
        };

        await _context.Parts.AddRangeAsync(parts);
        await _context.SaveChangesAsync();

        // Add vehicle compatibility for some parts
        await SeedPartCompatibilityAsync(parts);

        _logger.LogInformation("Seeded {Count} parts", parts.Count);
    }

    private async Task SeedPartCompatibilityAsync(List<Part> parts)
    {
        _logger.LogInformation("Seeding part compatibility...");

        var compatibilities = new List<PartCompatibility>();

        // Add compatibility for engine oil filter
        var oilFilter = parts.First(p => p.SKU == "ENG-001");
        compatibilities.AddRange(new[]
        {
            PartCompatibility.Create(oilFilter.Id, "Toyota", "Camry", 2015, 2023, "System", "2.5L 4-Cyl"),
            PartCompatibility.Create(oilFilter.Id, "Honda", "Accord", 2013, 2023, "System", "2.4L 4-Cyl"),
            PartCompatibility.Create(oilFilter.Id, "Nissan", "Altima", 2013, 2023, "System", "2.5L 4-Cyl")
        });

        // Add compatibility for brake pads
        var frontBrakePads = parts.First(p => p.SKU == "BRK-001");
        compatibilities.AddRange(new[]
        {
            PartCompatibility.Create(frontBrakePads.Id, "Toyota", "Camry", 2012, 2023, "System"),
            PartCompatibility.Create(frontBrakePads.Id, "Honda", "Accord", 2013, 2023, "System"),
            PartCompatibility.Create(frontBrakePads.Id, "Mazda", "6", 2014, 2023, "System")
        });

        // Add compatibility for tires
        var allSeasonTire = parts.First(p => p.SKU == "TIR-001");
        compatibilities.AddRange(new[]
        {
            PartCompatibility.Create(allSeasonTire.Id, "Toyota", "Camry", 2015, 2023, "System"),
            PartCompatibility.Create(allSeasonTire.Id, "Honda", "Accord", 2016, 2023, "System"),
            PartCompatibility.Create(allSeasonTire.Id, "Nissan", "Altima", 2016, 2023, "System"),
            PartCompatibility.Create(allSeasonTire.Id, "Hyundai", "Sonata", 2015, 2023, "System")
        });

        await _context.PartCompatibilities.AddRangeAsync(compatibilities);
        await _context.SaveChangesAsync();

        _logger.LogInformation("Seeded {Count} part compatibilities", compatibilities.Count);
    }

    private async Task SeedSampleInventoryAsync()
    {
        _logger.LogInformation("Seeding sample inventory items...");

        // This would typically be done after branches are created
        // For now, we'll use sample GUIDs that would represent actual branches
        var sampleBranchId1 = Guid.Parse("11111111-1111-1111-1111-111111111111");
        var sampleBranchId2 = Guid.Parse("*************-2222-2222-************");
        var sampleVendorId = Guid.Parse("*************-3333-3333-************");

        var parts = await _context.Parts.ToListAsync();
        var inventoryItems = new List<InventoryItem>();

        // Create inventory for first branch
        foreach (var part in parts.Take(10))
        {
            var random = new Random();
            var currentStock = random.Next(5, 50);
            var minStock = random.Next(1, 5);
            var maxStock = random.Next(50, 100);
            var reorderLevel = random.Next(minStock, minStock + 5);

            var inventoryItem = InventoryItem.Create(
                part.Id,
                sampleBranchId1,
                sampleVendorId,
                currentStock,
                minStock,
                maxStock,
                reorderLevel,
                $"Warehouse-A-{random.Next(1, 10)}",
                "System",
                $"Bin-{random.Next(1, 20)}",
                $"Shelf-{random.Next(1, 5)}");

            inventoryItems.Add(inventoryItem);
        }

        // Create inventory for second branch
        foreach (var part in parts.Skip(5).Take(10))
        {
            var random = new Random();
            var currentStock = random.Next(3, 30);
            var minStock = random.Next(1, 3);
            var maxStock = random.Next(30, 80);
            var reorderLevel = random.Next(minStock, minStock + 3);

            var inventoryItem = InventoryItem.Create(
                part.Id,
                sampleBranchId2,
                sampleVendorId,
                currentStock,
                minStock,
                maxStock,
                reorderLevel,
                $"Warehouse-B-{random.Next(1, 8)}",
                "System",
                $"Bin-{random.Next(1, 15)}",
                $"Shelf-{random.Next(1, 4)}");

            inventoryItems.Add(inventoryItem);
        }

        await _context.InventoryItems.AddRangeAsync(inventoryItems);
        await _context.SaveChangesAsync();

        _logger.LogInformation("Seeded {Count} inventory items", inventoryItems.Count);
    }
}
