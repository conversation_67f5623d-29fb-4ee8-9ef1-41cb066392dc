using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using IdentityService.Domain.Entities;
using IdentityService.Domain.Enums;

namespace IdentityService.Domain.Interfaces;

public interface IUserRepository : IGenericRepository<User>
{
    Task<User?> GetByEmailAsync(string email);
    Task<User?> GetByPhoneNumberAsync(string phoneNumber);
    Task<List<User>> GetByIdsAsync(List<Guid> userIds);
    Task<User?> GetByIdWithRolesAsync(Guid userId);
    Task<List<string>> GetUserRolesAsync(Guid userId);
    Task<bool> UserHasRoleAsync(Guid userId, string roleName);
    Task<List<Permission>> GetUserPermissionsAsync(Guid userId);
    Task<bool> HasPermissionAsync(User user, string permissionName);
    Task<bool> ExistsByEmailAsync(string email);
    Task<bool> ExistsByPhoneNumberAsync(string phoneNumber);
    Task<IReadOnlyList<User>> GetUsersByRoleAsync(string roleName);
    Task UpdatePasswordAsync(Guid userId, string passwordHash);
    Task UpdateLastLoginAsync(Guid userId);
    Task<bool> ValidateRefreshTokenAsync(Guid userId, string refreshToken);
    Task<UserSubscription?> GetActiveSubscriptionAsync(Guid userId);
    Task<List<UserSubscription>> GetUserSubscriptionsAsync(Guid userId);
    Task<bool> HasActiveSubscriptionAsync(Guid userId);
    Task<SubscriptionTier> GetUserSubscriptionTierAsync(Guid userId);
    Task<bool> HasSubscriptionFeatureAsync(Guid userId, string featureName);
}