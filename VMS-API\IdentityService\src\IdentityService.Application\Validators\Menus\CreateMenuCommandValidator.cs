using FluentValidation;
using IdentityService.Application.DTOs.Menus;
using IdentityService.Application.Features.Menus.Commands;

namespace IdentityService.Application.Validators.Menus;

public class CreateMenuCommandValidator : BaseValidator<CreateMenuCommand>
{
    public CreateMenuCommandValidator()
    {
        RuleFor(x => x.Request).NotNull().WithMessage("Request cannot be null.");
        
        When(x => x.Request != null, () =>
        {
            RuleFor(x => x.Request.Name)
                .Cascade(CascadeMode.Stop)
                .NotEmpty().WithMessage("Menu name is required.")
                .MaximumLength(100).WithMessage("Menu name cannot exceed 100 characters.");
                
            RuleFor(x => x.Request.DisplayName)
                .Cascade(CascadeMode.Stop)
                .NotEmpty().WithMessage("Display name is required.")
                .MaximumLength(100).WithMessage("Display name cannot exceed 100 characters.");
                
            RuleFor(x => x.Request.Path)
                .Cascade(CascadeMode.Stop)
                .NotEmpty().WithMessage("Path is required.")
                .MaximumLength(200).WithMessage("Path cannot exceed 200 characters.");
                
            RuleFor(x => x.Request.Icon)
                .Cascade(CascadeMode.Stop)
                .NotEmpty().When(x => x.Request.ParentId == null).WithMessage("Icon is required for parent menus.")
                .MaximumLength(50).WithMessage("Icon cannot exceed 50 characters.");
                
            RuleFor(x => x.Request.Order)
                .GreaterThanOrEqualTo(0).WithMessage("Order must be a non-negative number.");
        });
    }
}
