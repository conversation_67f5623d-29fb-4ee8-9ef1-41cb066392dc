using InventoryManagementService.Domain.Common;

namespace InventoryManagementService.Domain.Entities;

public class InventoryItem : BaseEntity
{
    public Guid PartId { get; private set; }
    public Guid BranchId { get; private set; }
    public Guid VendorId { get; private set; }
    public int CurrentStock { get; private set; }
    public int MinimumStock { get; private set; }
    public int MaximumStock { get; private set; }
    public int ReorderLevel { get; private set; }
    public string StorageLocation { get; private set; } = string.Empty;
    public string? Bin { get; private set; }
    public string? Shelf { get; private set; }
    public DateTime? LastStockUpdate { get; private set; }

    // Navigation properties
    public Part Part { get; private set; } = null!;
    public List<InventoryTransaction> Transactions { get; private set; } = new();

    private InventoryItem() { }

    public static InventoryItem Create(
        Guid partId,
        Guid branchId,
        Guid vendorId,
        int initialStock,
        int minimumStock,
        int maximumStock,
        int reorderLevel,
        string storageLocation,
        string createdBy,
        string? bin = null,
        string? shelf = null)
    {
        return new InventoryItem
        {
            PartId = partId,
            BranchId = branchId,
            VendorId = vendorId,
            CurrentStock = initialStock,
            MinimumStock = minimumStock,
            MaximumStock = maximumStock,
            ReorderLevel = reorderLevel,
            StorageLocation = storageLocation.Trim(),
            Bin = bin?.Trim(),
            Shelf = shelf?.Trim(),
            LastStockUpdate = DateTime.UtcNow,
            CreatedBy = createdBy
        };
    }

    public void UpdateStock(int newStock, string updatedBy, string reason = "Manual Update")
    {
        var previousStock = CurrentStock;
        CurrentStock = newStock;
        LastStockUpdate = DateTime.UtcNow;
        SetUpdatedBy(updatedBy);

        // Create transaction record
        var transaction = InventoryTransaction.Create(
            Id,
            newStock > previousStock ? Enums.TransactionType.StockIn : Enums.TransactionType.StockOut,
            Math.Abs(newStock - previousStock),
            previousStock,
            newStock,
            reason,
            updatedBy);

        Transactions.Add(transaction);
    }

    public void AddStock(int quantity, string updatedBy, string reason = "Stock Addition")
    {
        var previousStock = CurrentStock;
        CurrentStock += quantity;
        LastStockUpdate = DateTime.UtcNow;
        SetUpdatedBy(updatedBy);

        var transaction = InventoryTransaction.Create(
            Id,
            Enums.TransactionType.StockIn,
            quantity,
            previousStock,
            CurrentStock,
            reason,
            updatedBy);

        Transactions.Add(transaction);
    }

    public bool RemoveStock(int quantity, string updatedBy, string reason = "Stock Removal")
    {
        if (CurrentStock < quantity)
            return false;

        var previousStock = CurrentStock;
        CurrentStock -= quantity;
        LastStockUpdate = DateTime.UtcNow;
        SetUpdatedBy(updatedBy);

        var transaction = InventoryTransaction.Create(
            Id,
            Enums.TransactionType.StockOut,
            quantity,
            previousStock,
            CurrentStock,
            reason,
            updatedBy);

        Transactions.Add(transaction);
        return true;
    }

    public void UpdateStorageLocation(string storageLocation, string updatedBy, string? bin = null, string? shelf = null)
    {
        StorageLocation = storageLocation.Trim();
        Bin = bin?.Trim();
        Shelf = shelf?.Trim();
        SetUpdatedBy(updatedBy);
    }

    public void UpdateStockLevels(int minimumStock, int maximumStock, int reorderLevel, string updatedBy)
    {
        MinimumStock = minimumStock;
        MaximumStock = maximumStock;
        ReorderLevel = reorderLevel;
        SetUpdatedBy(updatedBy);
    }

    public bool IsLowStock => CurrentStock <= ReorderLevel;
    public bool IsOutOfStock => CurrentStock <= 0;
}
